﻿using System;
using Serilog;

namespace AutoGameBai
{
    public class UIManager
    {
        private readonly ComboBox _comboBoxRooms;

        public UIManager(ComboBox comboBoxRooms)
        {
            _comboBoxRooms = comboBoxRooms ?? throw new ArgumentNullException(nameof(comboBoxRooms));
        }

        public void AppendLog(string message, LogLevel level, string username = "")
        {
            if (string.IsNullOrEmpty(message)) return;

            string logLevel = level switch
            {
                LogLevel.Debug => "DEBUG",
                LogLevel.Info => "INFO",
                LogLevel.Warning => "WARNING",
                LogLevel.Error => "ERROR",
                _ => "INFO"
            };

            string formattedMessage = string.IsNullOrEmpty(username)
                ? $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{logLevel}]: {message}"
                : $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{logLevel}] [{username}]: {message}";

            switch (level)
            {
                case LogLevel.Debug:
                    Log.Debug(formattedMessage);
                    break;
                case LogLevel.Info:
                    Log.Information(formattedMessage);
                    break;
                case LogLevel.Warning:
                    Log.Warning(formattedMessage);
                    break;
                case LogLevel.Error:
                    Log.Error(formattedMessage);
                    break;
                default:
                    Log.Information(formattedMessage);
                    break;
            }
        }

        public enum LogLevel
        {
            Debug,
            Info,
            Warning,
            Error
        }
    }
}