using System;
using System.Collections.Generic;
using System.Linq;
using AutoGameBai.Gamemaubinh;

namespace AutoGameBai
{
    /// <summary>
    /// Test class để kiểm tra thuật toán mới dựa trên logic game gốc
    /// </summary>
    public class TestGameOriginalLogic
    {
        public static void RunTests()
        {
            Console.WriteLine("🧪 BẮT ĐẦU TEST THUẬT TOÁN MỚI - LOGIC GAME GỐC");
            Console.WriteLine(new string('=', 60));

            // Test 1: Kiểm tra điểm số game gốc
            TestGameOriginalScoring();

            // Test 2: Kiểm tra bài đặc biệt
            TestSpecialHands();

            // Test 3: Kiểm tra gợi ý với bài thường
            TestNormalHandSuggestions();

            // Test 4: So sánh với thuật toán cũ
            CompareWithOldAlgorithm();

            Console.WriteLine("\n✅ HOÀN THÀNH TẤT CẢ TESTS");
        }

        /// <summary>
        /// Test 1: Ki<PERSON><PERSON> tra hệ thống điểm số game gốc
        /// </summary>
        private static void TestGameOriginalScoring()
        {
            Console.WriteLine("\n🎯 TEST 1: HỆ THỐNG ĐIỂM SỐ GAME GỐC");
            Console.WriteLine(new string('-', 40));

            try
            {
                // Test case 1: Thùng phá sảnh (TPS) - A♠, 2♠, 3♠, 4♠, 5♠
                var tpsCards = new List<CardUtilityMaubinh.CardInfo>
                {
                    new(0), new(4), new(8), new(12), new(16) // A♠, 2♠, 3♠, 4♠, 5♠
                };
                int tpsScore = CardUtilityMaubinh.GetGameOriginalScore(tpsCards);
                Console.WriteLine($"TPS A-2-3-4-5♠: {tpsScore} (Expected: 544+5 = 549)");

                // Test case 2: Tứ quý Át
                var fourKindCards = new List<CardUtilityMaubinh.CardInfo>
                {
                    new(0), new(1), new(2), new(3), new(4) // A♠, A♣, A♦, A♥, 2♠
                };
                int fourKindScore = CardUtilityMaubinh.GetGameOriginalScore(fourKindCards);
                Console.WriteLine($"Tứ quý Át: {fourKindScore} (Expected: 476+1 = 477)");

                // Test case 3: Xám (Ba Át)
                var threeKindCards = new List<CardUtilityMaubinh.CardInfo>
                {
                    new(0), new(1), new(2) // A♠, A♣, A♦
                };
                int threeKindScore = CardUtilityMaubinh.GetGameOriginalScore(threeKindCards);
                Console.WriteLine($"Xám Át: {threeKindScore} (Expected: 204+1 = 205)");

                // Test case 4: Đôi Át
                var pairCards = new List<CardUtilityMaubinh.CardInfo>
                {
                    new(0), new(1), new(4) // A♠, A♣, 2♠
                };
                int pairScore = CardUtilityMaubinh.GetGameOriginalScore(pairCards);
                Console.WriteLine($"Đôi Át: {pairScore} (Expected: 68+1 = 69)");

                // Test case 5: Mậu thầu
                var highCardCards = new List<CardUtilityMaubinh.CardInfo>
                {
                    new(0), new(4), new(8) // A♠, 2♠, 3♠
                };
                int highCardScore = CardUtilityMaubinh.GetGameOriginalScore(highCardCards);
                Console.WriteLine($"Mậu thầu A-3-2: {highCardScore} (Expected: < 68)");

                // Test case 6: Sảnh thường
                var straightCards = new List<CardUtilityMaubinh.CardInfo>
                {
                    new(4), new(8), new(12), new(16), new(20) // 2♠, 3♠, 4♠, 5♠, 6♠
                };
                int straightScore = CardUtilityMaubinh.GetGameOriginalScore(straightCards);
                Console.WriteLine($"Sảnh 2-3-4-5-6: {straightScore} (Expected: 272+6 = 278)");

                // Test case 7: Thùng
                var flushCards = new List<CardUtilityMaubinh.CardInfo>
                {
                    new(0), new(8), new(16), new(24), new(32) // A♠, 3♠, 5♠, 7♠, 9♠
                };
                int flushScore = CardUtilityMaubinh.GetGameOriginalScore(flushCards);
                Console.WriteLine($"Thùng ♠: {flushScore} (Expected: 340+9 = 349)");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Lỗi test điểm số: {ex.Message}");
            }
        }

        /// <summary>
        /// Test 2: Kiểm tra các bài đặc biệt
        /// </summary>
        private static void TestSpecialHands()
        {
            Console.WriteLine("\n🏆 TEST 2: BÀI ĐẶC BIỆT");
            Console.WriteLine(new string('-', 40));

            // Test case 1: Sảnh rồng (A-2-3-4-5-6-7-8-9-10-J-Q-K)
            var dragonCards = new List<CardUtilityMaubinh.CardInfo>();
            for (int i = 0; i < 13; i++)
            {
                dragonCards.Add(new CardUtilityMaubinh.CardInfo(i * 4)); // A♠, 2♠, 3♠, ..., K♠
            }
            int dragonType = CardUtilityMaubinh.CheckMauBinhSpecialHands(dragonCards);
            Console.WriteLine($"Sảnh rồng: {dragonType} (Expected: 14)");

            // Test case 2: Đồng hoa đỏ
            var redCards = new List<CardUtilityMaubinh.CardInfo>();
            for (int i = 0; i < 13; i++)
            {
                redCards.Add(new CardUtilityMaubinh.CardInfo(i * 4 + 2)); // Tất cả ♦
            }
            int redType = CardUtilityMaubinh.CheckMauBinhSpecialHands(redCards);
            Console.WriteLine($"Đồng hoa đỏ: {redType} (Expected: 13)");

            // Test case 3: 6 đôi
            var sixPairsCards = new List<CardUtilityMaubinh.CardInfo>
            {
                new(0), new(1), // A♠, A♣
                new(4), new(5), // 2♠, 2♣
                new(8), new(9), // 3♠, 3♣
                new(12), new(13), // 4♠, 4♣
                new(16), new(17), // 5♠, 5♣
                new(20), new(21), // 6♠, 6♣
                new(24) // 7♠
            };
            int sixPairsType = CardUtilityMaubinh.CheckMauBinhSpecialHands(sixPairsCards);
            Console.WriteLine($"6 đôi: {sixPairsType} (Expected: 12)");
        }

        /// <summary>
        /// Test 3: Kiểm tra gợi ý với bài thường
        /// </summary>
        private static void TestNormalHandSuggestions()
        {
            Console.WriteLine("\n💡 TEST 3: GỢI Ý BÀI THƯỜNG");
            Console.WriteLine(new string('-', 40));

            // Tạo bộ bài test: có đôi, xám, và các lá lẻ
            var testCards = new int[]
            {
                0, 1, 4, 5, 8, 9, 12, 16, 20, 24, 28, 32, 36 // A♠A♣, 2♠2♣, 3♠3♣, 4♠, 5♠, 6♠, 7♠, 8♠, 9♠
            };

            try
            {
                Console.WriteLine("Bộ bài test: A♠A♣, 2♠2♣, 3♠3♣, 4♠, 5♠, 6♠, 7♠, 8♠, 9♠");

                // Test điểm số từng chi
                var chi1Test = new int[] { 32, 28, 24, 20, 16 }; // 9♠, 8♠, 7♠, 6♠, 5♠
                var chi2Test = new int[] { 12, 9, 8, 5, 4 }; // 4♠, 3♣, 3♠, 2♣, 2♠
                var chi3Test = new int[] { 1, 0, 36 }; // A♣, A♠, 10♠

                double chi1Score = CardUtilityMaubinh.GetChiStrength(chi1Test);
                double chi2Score = CardUtilityMaubinh.GetChiStrength(chi2Test);
                double chi3Score = CardUtilityMaubinh.GetChiStrength(chi3Test);

                Console.WriteLine($"Chi 1 (5 lá): {chi1Score:F1}");
                Console.WriteLine($"Chi 2 (5 lá): {chi2Score:F1}");
                Console.WriteLine($"Chi 3 (3 lá): {chi3Score:F1}");

                bool isValid = chi1Score >= chi2Score && chi2Score >= chi3Score;
                Console.WriteLine($"Tính hợp lệ (Chi1≥Chi2≥Chi3): {(isValid ? "✅" : "❌")}");

                // Test với UIManager mock
                Console.WriteLine("\nTest tạo gợi ý...");
                TestSuggestionGeneration(testCards);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Lỗi test gợi ý: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Test tạo gợi ý với mock UIManager
        /// </summary>
        private static void TestSuggestionGeneration(int[] testCards)
        {
            try
            {
                // Tạo mock UIManager đơn giản
                var mockUI = new MockUIManager();
                var engine = new MauBinhEngine(mockUI);

                var suggestions = engine.GenerateSuggestions(testCards);

                Console.WriteLine($"Số gợi ý được tạo: {suggestions.Count}");

                for (int i = 0; i < Math.Min(3, suggestions.Count); i++)
                {
                    var suggestion = suggestions[i];
                    Console.WriteLine($"\nGợi ý {i + 1}: {suggestion.Description}");
                    Console.WriteLine($"  Chi 1: {string.Join(",", suggestion.Chi1)}");
                    Console.WriteLine($"  Chi 2: {string.Join(",", suggestion.Chi2)}");
                    Console.WriteLine($"  Chi 3: {string.Join(",", suggestion.Chi3)}");
                    Console.WriteLine($"  Điểm: {suggestion.Score:F1}");

                    // Kiểm tra tính hợp lệ
                    var chi1Strength = CardUtilityMaubinh.GetChiStrength(suggestion.Chi1);
                    var chi2Strength = CardUtilityMaubinh.GetChiStrength(suggestion.Chi2);
                    var chi3Strength = CardUtilityMaubinh.GetChiStrength(suggestion.Chi3);

                    bool valid = chi1Strength >= chi2Strength && chi2Strength >= chi3Strength;
                    Console.WriteLine($"  Hợp lệ: {(valid ? "✅" : "❌")} ({chi1Strength:F1} ≥ {chi2Strength:F1} ≥ {chi3Strength:F1})");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Lỗi tạo gợi ý: {ex.Message}");
            }
        }

        /// <summary>
        /// Mock UIManager để test - tạo instance với ComboBox giả
        /// </summary>
        public class MockUIManager : UIManager
        {
            public MockUIManager() : base(new ComboBox())
            {
                // Constructor với ComboBox giả
            }

            // Override không cần thiết vì UIManager.AppendLog không phải virtual
            // Chỉ cần tạo instance để test
        }

        /// <summary>
        /// Test runner chính - COMMENTED OUT để tránh multiple entry points
        /// </summary>
        /*
        public static void Main(string[] args)
        {
            try
            {
                RunTests();
                Console.WriteLine("\n✅ TẤT CẢ TESTS HOÀN THÀNH THÀNH CÔNG!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ LỖI TRONG QUÁ TRÌNH TEST: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("\nNhấn phím bất kỳ để thoát...");
            Console.ReadKey();
        }
        */

        /// <summary>
        /// Test 4: So sánh với thuật toán cũ
        /// </summary>
        private static void CompareWithOldAlgorithm()
        {
            Console.WriteLine("\n⚖️ TEST 4: SO SÁNH VỚI THUẬT TOÁN CŨ");
            Console.WriteLine(new string('-', 40));

            var testCards = new int[]
            {
                0, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48 // A♠, 2♠, 3♠, ..., K♠
            };

            // Test điểm số Chi
            var chi1Cards = testCards.Take(5).ToArray();
            var chi2Cards = testCards.Skip(5).Take(5).ToArray();
            var chi3Cards = testCards.Skip(10).Take(3).ToArray();

            double newChi1Score = CardUtilityMaubinh.GetChiStrength(chi1Cards);
            double newChi2Score = CardUtilityMaubinh.GetChiStrength(chi2Cards);
            double newChi3Score = CardUtilityMaubinh.GetChiStrength(chi3Cards);

            Console.WriteLine($"Chi 1 (5 lá): {newChi1Score:F1}");
            Console.WriteLine($"Chi 2 (5 lá): {newChi2Score:F1}");
            Console.WriteLine($"Chi 3 (3 lá): {newChi3Score:F1}");

            // Kiểm tra tính hợp lệ
            bool isValid = newChi1Score >= newChi2Score && newChi2Score >= newChi3Score;
            Console.WriteLine($"Tính hợp lệ (Chi1≥Chi2≥Chi3): {(isValid ? "✅" : "❌")}");
        }
    }
}
