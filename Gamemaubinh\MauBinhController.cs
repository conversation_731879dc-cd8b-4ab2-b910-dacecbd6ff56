using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace AutoGameBai.Gamemaubinh
{
    /// <summary>
    /// Controller ch<PERSON>h cho M<PERSON>u <PERSON>h - Kết nối với hệ thống hiện tại
    /// </summary>
    public class MauBinhController
    {
        private readonly UIManager _uiManager;
        private readonly MauBinhUIManager _uiManager2;
        private readonly Dictionary<string, int[]> _userCards;
        private readonly Dictionary<string, string> _userIndexMap;

        // Events
        public event Action<string, int[]> OnCardsSuggested;
        public event Action<Dictionary<string, int[]>> OnTeamSuggestionsGenerated;

        public MauBinhController(UIManager uiManager, ListBox suggestionListBox, Panel cardDisplayPanel)
        {
            _uiManager = uiManager;
            _uiManager2 = new MauBinhUIManager(uiManager, suggestionListBox, cardDisplayPanel);
            _userCards = new Dictionary<string, int[]>();
            _userIndexMap = new Dictionary<string, string>();
        }

        #region Public Methods

        /// <summary>
        /// Cập nhật bài cho user
        /// </summary>
        public void UpdateUserCards(string username, int[] cards, string indexKey)
        {
            try
            {
                _userCards[username] = cards;
                _userIndexMap[username] = indexKey;

                _uiManager.AppendLog($"📝 Cập nhật bài cho {username}: {cards.Length} lá", UIManager.LogLevel.Info);

                // Nếu có đủ 3 users, tính toán team strategy
                if (_userCards.Count >= 3)
                {
                    GenerateTeamStrategy();
                }
                else
                {
                    // Tạo gợi ý cho user hiện tại
                    _uiManager2.GenerateSingleUserSuggestions(cards);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi cập nhật bài: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Tạo gợi ý cho user cụ thể
        /// </summary>
        public void GenerateSuggestionsForUser(string username)
        {
            try
            {
                if (_userCards.ContainsKey(username))
                {
                    _uiManager2.GenerateSingleUserSuggestions(_userCards[username]);
                    _uiManager.AppendLog($"🎯 Tạo gợi ý cho {username}", UIManager.LogLevel.Info);
                }
                else
                {
                    _uiManager.AppendLog($"⚠️ Không tìm thấy bài cho {username}", UIManager.LogLevel.Warning);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo gợi ý: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Tạo chiến thuật team
        /// </summary>
        public void GenerateTeamStrategy()
        {
            try
            {
                if (_userCards.Count < 3)
                {
                    _uiManager.AppendLog("⚠️ Cần ít nhất 3 users để tính team strategy", UIManager.LogLevel.Warning);
                    return;
                }

                _uiManager.AppendLog("🎯 Bắt đầu tính toán team strategy", UIManager.LogLevel.Info);

                // Lấy 3 users đầu tiên
                var teamCards = _userCards.Take(3).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                _uiManager2.GenerateTeamSuggestions(teamCards);

                // Trigger event
                OnTeamSuggestionsGenerated?.Invoke(teamCards);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi team strategy: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Xóa user
        /// </summary>
        public void RemoveUser(string username)
        {
            try
            {
                if (_userCards.ContainsKey(username))
                {
                    _userCards.Remove(username);
                    _userIndexMap.Remove(username);
                    _uiManager.AppendLog($"🗑️ Xóa user {username}", UIManager.LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi xóa user: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Reset tất cả
        /// </summary>
        public void Reset()
        {
            try
            {
                _userCards.Clear();
                _userIndexMap.Clear();
                _uiManager.AppendLog("🔄 Reset Mậu Binh Controller", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi reset: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Áp dụng gợi ý cho user
        /// </summary>
        public void ApplySuggestionToUser(string username, int[] suggestedCards)
        {
            try
            {
                if (_userIndexMap.ContainsKey(username))
                {
                    var indexKey = _userIndexMap[username];
                    OnCardsSuggested?.Invoke(indexKey, suggestedCards);
                    _uiManager.AppendLog($"✅ Áp dụng gợi ý cho {username} ({indexKey})", UIManager.LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi áp dụng gợi ý: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        #endregion

        #region Integration Methods

        /// <summary>
        /// Tích hợp với MauBinhCardManager hiện tại
        /// </summary>
        public void IntegrateWithCardManager(MauBinhCardManager cardManager)
        {
            try
            {
                // Note: MauBinhCardManager doesn't have OnCardsUpdated event
                // Integration will be done through direct method calls
                _uiManager.AppendLog("🔗 Tích hợp với MauBinhCardManager", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tích hợp: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Tích hợp với WebSocket Handler
        /// </summary>
        public void IntegrateWithWebSocket(object webSocketHandler)
        {
            try
            {
                // Có thể tích hợp với WebSocket events ở đây
                _uiManager.AppendLog("🔗 Tích hợp với WebSocket Handler", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tích hợp WebSocket: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Lấy thông tin user hiện tại
        /// </summary>
        public Dictionary<string, object> GetUserInfo()
        {
            return new Dictionary<string, object>
            {
                ["TotalUsers"] = _userCards.Count,
                ["Users"] = _userCards.Keys.ToList(),
                ["HasEnoughForTeam"] = _userCards.Count >= 3
            };
        }

        /// <summary>
        /// Lấy bài của user
        /// </summary>
        public int[] GetUserCards(string username)
        {
            return _userCards.ContainsKey(username) ? _userCards[username] : Array.Empty<int>();
        }

        /// <summary>
        /// Kiểm tra user có bài không
        /// </summary>
        public bool HasUserCards(string username)
        {
            return _userCards.ContainsKey(username) && _userCards[username].Length > 0;
        }

        /// <summary>
        /// Format thông tin để hiển thị
        /// </summary>
        public string GetStatusText()
        {
            var lines = new List<string>();
            lines.Add($"👥 Users: {_userCards.Count}");

            foreach (var kvp in _userCards)
            {
                lines.Add($"  📝 {kvp.Key}: {kvp.Value.Length} lá");
            }

            if (_userCards.Count >= 3)
            {
                lines.Add("✅ Đủ điều kiện team strategy");
            }
            else
            {
                lines.Add($"⚠️ Cần thêm {3 - _userCards.Count} users");
            }

            return string.Join("\n", lines);
        }

        #endregion

        #region Static Factory Methods

        /// <summary>
        /// Tạo instance mới với UI controls
        /// </summary>
        public static MauBinhController Create(UIManager uiManager, Form parentForm)
        {
            // Tìm hoặc tạo controls cần thiết
            var suggestionListBox = FindOrCreateListBox(parentForm, "suggestionListBox");
            var cardDisplayPanel = FindOrCreatePanel(parentForm, "cardDisplayPanel");

            return new MauBinhController(uiManager, suggestionListBox, cardDisplayPanel);
        }

        private static ListBox FindOrCreateListBox(Form form, string name)
        {
            var existing = form.Controls.Find(name, true).FirstOrDefault() as ListBox;
            if (existing != null) return existing;

            var listBox = new ListBox
            {
                Name = name,
                Size = new System.Drawing.Size(400, 200),
                Location = new System.Drawing.Point(10, 10)
            };

            form.Controls.Add(listBox);
            return listBox;
        }

        private static Panel FindOrCreatePanel(Form form, string name)
        {
            var existing = form.Controls.Find(name, true).FirstOrDefault() as Panel;
            if (existing != null) return existing;

            var panel = new Panel
            {
                Name = name,
                Size = new System.Drawing.Size(800, 600),
                Location = new System.Drawing.Point(420, 10),
                BorderStyle = BorderStyle.FixedSingle
            };

            form.Controls.Add(panel);
            return panel;
        }

        #endregion
    }
}
