C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\selenium-manager\linux\selenium-manager
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\selenium-manager\macos\selenium-manager
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\selenium-manager\windows\selenium-manager.exe
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\token.txt
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\AutoGameBai.exe
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\AutoGameBai.deps.json
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\AutoGameBai.runtimeconfig.json
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\AutoGameBai.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\AutoGameBai.pdb
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\AngleSharp.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Azure.Core.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Azure.Identity.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\EntityFramework.SqlServer.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\EntityFramework.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.Windows.Forms.DataVisualization.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Identity.Client.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\OpenCvSharp.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\OpenCvSharp.Extensions.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\WebDriver.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Serilog.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\Serilog.Sinks.File.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\ICSharpCode.SharpZipLib.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\SocketIO.Core.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\SocketIO.Serializer.Core.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\SocketIO.Serializer.SystemTextJson.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\SocketIOClient.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.Data.SQLite.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.CodeDom.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.Data.SqlClient.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.Data.SQLite.EF6.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.Drawing.Common.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.IO.Pipelines.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.Management.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.Net.Http.Json.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\System.Text.Json.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\WebDriverManager.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\websocket-sharp.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\unix\lib\netcoreapp3.1\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win\lib\netcoreapp3.1\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-x64\native\OpenCvSharpExtern.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-x64\native\opencv_videoio_ffmpeg4110_64.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-x86\native\OpenCvSharpExtern.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-x86\native\opencv_videoio_ffmpeg4110.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-arm64\native\sni.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-x64\native\sni.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-x86\native\sni.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\linux-x64\native\SQLite.Interop.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\osx-x64\native\SQLite.Interop.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-x64\native\SQLite.Interop.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win-x86\native\SQLite.Interop.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.Management.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\win\lib\netstandard2.0\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.Forms.AddUserForm.resources
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.Form1.resources
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.Gamemaubinh.MauBinhSuggestionForm.resources
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.Gamephom.PhomSuggestionForm.resources
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.Models.User.resources
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.Properties.Resources.resources
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.csproj.GenerateResource.cache
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.AssemblyInfo.cs
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.sourcelink.json
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGame.BFFEB2B2.Up2Date
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\refint\AutoGameBai.dll
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.pdb
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\AutoGameBai.genruntimeconfig.cache
C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\obj\Debug\net8.0-windows\ref\AutoGameBai.dll
