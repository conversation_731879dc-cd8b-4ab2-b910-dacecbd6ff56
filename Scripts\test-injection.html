<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Profile Buttons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 20px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test Profile Buttons Injection</h1>
        <p>Trang n<PERSON>y để test việc inject profile buttons JavaScript.</p>

        <button class="test-button" onclick="injectButtons()">Inject Profile Buttons</button>
        <button class="test-button" onclick="removeButtons()">Remove Buttons</button>
        <button class="test-button" onclick="clearLog()">Clear Log</button>

        <div id="log" class="log">
            <div>Sẵn sàng test injection...</div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>Log cleared...</div>';
        }

        function removeButtons() {
            const container = document.querySelector('.button-container');
            if (container) {
                container.remove();
                log('✅ Đã xóa profile buttons');
            } else {
                log('❌ Không tìm thấy profile buttons để xóa');
            }
        }

        function injectButtons() {
            log('🔄 Bắt đầu inject profile buttons...');

            // Inline profile buttons script
            const script = `
(function() {
    'use strict';

    // Tránh load multiple lần
    if (window.profileButtonsLoaded) {
        console.log('Profile buttons đã được load, skip...');
        return;
    }
    window.profileButtonsLoaded = true;

    let getKeyButton = null;
    let leaveRoomButton = null;
    let isGetKeyRunning = false;

    // Tạo CSS styles cho buttons
    function createStyles() {
        // Kiểm tra nếu style đã tồn tại
        if (document.getElementById('profile-buttons-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'profile-buttons-styles';
        style.textContent = \`
            .profile-button {
                padding: 6px 12px;
                margin: 3px;
                border: none;
                border-radius: 4px;
                font-size: 11px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 70px;
                height: 28px;
                z-index: 99999;
            }

            .get-key-btn {
                background-color: #007bff;
                color: white;
            }

            .get-key-btn:hover {
                background-color: #0056b3;
            }

            .get-key-btn.stop {
                background-color: #dc3545;
                color: white;
            }

            .get-key-btn.stop:hover {
                background-color: #c82333;
            }

            .leave-room-btn {
                background-color: #dc3545;
                color: white;
            }

            .leave-room-btn:hover {
                background-color: #c82333;
            }

            .button-container {
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 99999;
                display: flex;
                gap: 5px;
            }
        \`;
        document.head.appendChild(style);
    }

    // Tạo Get Key button
    function createGetKeyButton() {
        getKeyButton = document.createElement('button');
        getKeyButton.className = 'profile-button get-key-btn';
        getKeyButton.textContent = 'Get Key';
        getKeyButton.onclick = toggleGetKey;
        return getKeyButton;
    }

    // Tạo Leave Room button
    function createLeaveRoomButton() {
        leaveRoomButton = document.createElement('button');
        leaveRoomButton.className = 'profile-button leave-room-btn';
        leaveRoomButton.textContent = 'Thoát bàn';
        leaveRoomButton.onclick = leaveRoom;
        return leaveRoomButton;
    }

    // Toggle Get Key functionality
    function toggleGetKey() {
        if (!isGetKeyRunning) {
            startGetKey();
        } else {
            stopGetKey();
        }
    }

    // Bắt đầu Get Key
    function startGetKey() {
        isGetKeyRunning = true;
        getKeyButton.textContent = 'Stop';
        getKeyButton.classList.add('stop');
        console.log('🔑 Bắt đầu Get Key Room...');
        if (window.log) window.log('🔑 Bắt đầu Get Key Room...');
    }

    // Dừng Get Key
    function stopGetKey() {
        isGetKeyRunning = false;
        getKeyButton.textContent = 'Get Key';
        getKeyButton.classList.remove('stop');
        console.log('🛑 Đã dừng Get Key Room');
        if (window.log) window.log('🛑 Đã dừng Get Key Room');
    }

    // Thoát phòng
    function leaveRoom() {
        console.log('🚪 Thoát phòng');
        if (window.log) window.log('🚪 Thoát phòng');
    }

    // Khởi tạo buttons
    function initButtons() {
        // Xóa buttons cũ nếu có
        const existingContainer = document.querySelector('.button-container');
        if (existingContainer) {
            existingContainer.remove();
        }

        // Tạo styles
        createStyles();

        // Tạo container
        const container = document.createElement('div');
        container.className = 'button-container';

        // Tạo và thêm buttons
        container.appendChild(createGetKeyButton());
        container.appendChild(createLeaveRoomButton());

        // Thêm vào body
        document.body.appendChild(container);

        console.log('✅ Profile buttons đã được khởi tạo');
        if (window.log) window.log('✅ Profile buttons đã được khởi tạo');
    }

    // Export functions for external access
    window.profileButtons = {
        startGetKey: startGetKey,
        stopGetKey: stopGetKey,
        leaveRoom: leaveRoom,
        isGetKeyRunning: () => isGetKeyRunning,
        reinit: initButtons
    };

    // Khởi tạo ngay lập tức
    initButtons();

})();
            `;

            try {
                // Execute the script
                eval(script);
                log('✅ Đã inject profile buttons thành công!');
                log('🎯 Kiểm tra góc phải trên để thấy 2 buttons');
            } catch (error) {
                log('❌ Lỗi khi inject: ' + error.message);
            }
        }

        // Auto inject khi trang load
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('🔄 Auto-inject profile buttons...');
                injectButtons();
            }, 1000);
        });
    </script>
</body>
</html>
