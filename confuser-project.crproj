<?xml version="1.0" encoding="utf-8"?>
<project outputDir="bin\Obfuscated" baseDir="bin\Release\net8.0-windows\win-x64\publish">
  <rule pattern="true" inherit="false">
    <!-- Name obfuscation -->
    <protection id="rename">
      <argument name="mode" value="unicode" />
      <argument name="renameMode" value="letters" />
    </protection>
    
    <!-- Control flow obfuscation -->
    <protection id="ctrl flow">
      <argument name="predicate" value="expression" />
      <argument name="intensity" value="60" />
    </protection>
    
    <!-- String encryption -->
    <protection id="constants">
      <argument name="mode" value="dynamic" />
      <argument name="decoderCount" value="5" />
    </protection>
    
    <!-- Anti-debug -->
    <protection id="anti debug">
      <argument name="mode" value="safe" />
    </protection>
    
    <!-- Anti-dump -->
    <protection id="anti dump">
    </protection>
    
    <!-- Anti-tamper -->
    <protection id="anti tamper">
      <argument name="key" value="normal" />
    </protection>
    
    <!-- Resource encryption -->
    <protection id="resources">
      <argument name="mode" value="dynamic" />
    </protection>
  </rule>
  
  <module path="AutoGameBai.exe" />
</project>
