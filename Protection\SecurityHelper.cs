using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;

namespace AutoGameBai.Protection
{
    /// <summary>
    /// Helper class for basic security and anti-reverse engineering
    /// </summary>
    public static class SecurityHelper
    {
        private static readonly byte[] Key = { 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF };
        private static readonly byte[] IV = { 0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10 };

        /// <summary>
        /// Check if debugger is attached
        /// </summary>
        public static bool IsDebuggerPresent()
        {
            return Debugger.IsAttached || 
                   Environment.GetEnvironmentVariable("COR_ENABLE_PROFILING") == "1" ||
                   Environment.GetEnvironmentVariable("COR_PROFILER") != null;
        }

        /// <summary>
        /// Encrypt sensitive string
        /// </summary>
        public static string EncryptString(string plainText)
        {
            try
            {
                using (var des = DES.Create())
                {
                    des.Key = Key;
                    des.IV = IV;
                    
                    using (var encryptor = des.CreateEncryptor())
                    using (var ms = new MemoryStream())
                    using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    using (var writer = new StreamWriter(cs))
                    {
                        writer.Write(plainText);
                        writer.Flush();
                        cs.FlushFinalBlock();
                        return Convert.ToBase64String(ms.ToArray());
                    }
                }
            }
            catch
            {
                return plainText; // Fallback
            }
        }

        /// <summary>
        /// Decrypt sensitive string
        /// </summary>
        public static string DecryptString(string cipherText)
        {
            try
            {
                var buffer = Convert.FromBase64String(cipherText);
                
                using (var des = DES.Create())
                {
                    des.Key = Key;
                    des.IV = IV;
                    
                    using (var decryptor = des.CreateDecryptor())
                    using (var ms = new MemoryStream(buffer))
                    using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                    using (var reader = new StreamReader(cs))
                    {
                        return reader.ReadToEnd();
                    }
                }
            }
            catch
            {
                return cipherText; // Fallback
            }
        }

        /// <summary>
        /// Get protected password
        /// </summary>
        public static string GetProtectedPassword()
        {
            // Encrypted "mmobatri"
            return DecryptString("YourEncryptedPasswordHere");
        }

        /// <summary>
        /// Verify assembly integrity
        /// </summary>
        public static bool VerifyIntegrity()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var location = assembly.Location;
                
                if (!File.Exists(location))
                    return false;

                using (var sha256 = SHA256.Create())
                {
                    var fileBytes = File.ReadAllBytes(location);
                    var hash = sha256.ComputeHash(fileBytes);
                    var hashString = Convert.ToBase64String(hash);
                    
                    // Store expected hash somewhere secure
                    // For now, just return true (implement proper checking)
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Anti-tampering check
        /// </summary>
        public static void PerformSecurityChecks()
        {
            if (IsDebuggerPresent())
            {
                Environment.Exit(1);
            }

            if (!VerifyIntegrity())
            {
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// Obfuscated method names helper
        /// </summary>
        public static class ObfuscatedNames
        {
            public const string A1 = "SuggestSmartCard";
            public const string B2 = "FindBestWasteCard";
            public const string C3 = "FilterViableCa";
            public const string D4 = "UpdatePlayedCards";
        }
    }
}
