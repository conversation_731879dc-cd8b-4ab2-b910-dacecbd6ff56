@echo off
echo ========================================
echo   AutoGameBai Safe Distribution Creator
echo ========================================

echo.
echo Step 1: Building release version (without heavy protection)...
echo Setting IsDebugMode = false...
powershell -Command "(Get-Content 'Form1.cs') -replace 'private static readonly bool IsDebugMode = true;', 'private static readonly bool IsDebugMode = false;' | Set-Content 'Form1.cs'"

echo Building Release configuration...
dotnet build AutoGameBai.csproj --configuration Release --no-restore

if %ERRORLEVEL% neq 0 (
    echo ❌ Build failed! Restoring debug mode...
    powershell -Command "(Get-Content 'Form1.cs') -replace 'private static readonly bool IsDebugMode = false;', 'private static readonly bool IsDebugMode = true;' | Set-Content 'Form1.cs'"
    pause
    exit /b 1
)

echo.
echo Step 2: Creating distribution folder...
if exist "Distribution-Safe" rmdir /s /q "Distribution-Safe"
mkdir "Distribution-Safe"
mkdir "Distribution-Safe\AutoGameBai"

echo.
echo Step 3: Copying release files...
copy "bin\Release\net8.0-windows\AutoGameBai.exe" "Distribution-Safe\AutoGameBai\"
copy "bin\Release\net8.0-windows\AutoGameBai.dll" "Distribution-Safe\AutoGameBai\"
copy "bin\Release\net8.0-windows\AutoGameBai.runtimeconfig.json" "Distribution-Safe\AutoGameBai\"
copy "bin\Release\net8.0-windows\AutoGameBai.deps.json" "Distribution-Safe\AutoGameBai\"

echo.
echo Step 4: Copying required dependencies...
xcopy "bin\Release\net8.0-windows\*.dll" "Distribution-Safe\AutoGameBai\" /Y /Q
xcopy "bin\Release\net8.0-windows\*.exe" "Distribution-Safe\AutoGameBai\" /Y /Q
xcopy "bin\Release\net8.0-windows\runtimes" "Distribution-Safe\AutoGameBai\runtimes\" /E /I /Y /Q

echo.
echo Step 5: Copying essential resources...
echo Copying card images...
if exist "bin\Debug\net8.0-windows\card" xcopy "bin\Debug\net8.0-windows\card" "Distribution-Safe\AutoGameBai\card\" /E /I /Y /Q
if exist "bin\Release\net8.0-windows\card" xcopy "bin\Release\net8.0-windows\card" "Distribution-Safe\AutoGameBai\card\" /E /I /Y /Q

echo Copying game images...
if exist "bin\Debug\net8.0-windows\img" xcopy "bin\Debug\net8.0-windows\img" "Distribution-Safe\AutoGameBai\img\" /E /I /Y /Q
if exist "bin\Release\net8.0-windows\img" xcopy "bin\Release\net8.0-windows\img" "Distribution-Safe\AutoGameBai\img\" /E /I /Y /Q

echo Copying selenium-manager...
if exist "bin\Release\net8.0-windows\selenium-manager" xcopy "bin\Release\net8.0-windows\selenium-manager" "Distribution-Safe\AutoGameBai\selenium-manager\" /E /I /Y /Q
if exist "bin\Debug\net8.0-windows\selenium-manager" xcopy "bin\Debug\net8.0-windows\selenium-manager" "Distribution-Safe\AutoGameBai\selenium-manager\" /E /I /Y /Q

echo.
echo Step 6: Copying configuration files...
if exist "bin\Release\net8.0-windows\*.txt" copy "bin\Release\net8.0-windows\*.txt" "Distribution-Safe\AutoGameBai\"
if exist "bin\Release\net8.0-windows\config.json" copy "bin\Release\net8.0-windows\config.json" "Distribution-Safe\AutoGameBai\"
if exist "*.txt" copy "*.txt" "Distribution-Safe\AutoGameBai\"
if exist "config.json" copy "config.json" "Distribution-Safe\AutoGameBai\"

echo.
echo Step 7: Creating README for users...
echo AutoGameBai - Game Assistant Tool (SAFE RELEASE) > "Distribution-Safe\AutoGameBai\README.txt"
echo. >> "Distribution-Safe\AutoGameBai\README.txt"
echo VERSION: Safe Release Build >> "Distribution-Safe\AutoGameBai\README.txt"
echo BUILD DATE: %date% %time% >> "Distribution-Safe\AutoGameBai\README.txt"
echo PROTECTION: BASIC (stable and compatible) >> "Distribution-Safe\AutoGameBai\README.txt"
echo. >> "Distribution-Safe\AutoGameBai\README.txt"
echo INSTALLATION: >> "Distribution-Safe\AutoGameBai\README.txt"
echo 1. Extract all files to a folder >> "Distribution-Safe\AutoGameBai\README.txt"
echo 2. Run AutoGameBai.exe >> "Distribution-Safe\AutoGameBai\README.txt"
echo 3. Follow the setup instructions >> "Distribution-Safe\AutoGameBai\README.txt"
echo. >> "Distribution-Safe\AutoGameBai\README.txt"
echo REQUIREMENTS: >> "Distribution-Safe\AutoGameBai\README.txt"
echo - Windows 10/11 >> "Distribution-Safe\AutoGameBai\README.txt"
echo - .NET 8.0 Runtime (will auto-install if needed) >> "Distribution-Safe\AutoGameBai\README.txt"
echo - Chrome browser >> "Distribution-Safe\AutoGameBai\README.txt"
echo. >> "Distribution-Safe\AutoGameBai\README.txt"
echo FEATURES: >> "Distribution-Safe\AutoGameBai\README.txt"
echo - Phom game assistance >> "Distribution-Safe\AutoGameBai\README.txt"
echo - Mau Binh game assistance (NEW LOGIC) >> "Distribution-Safe\AutoGameBai\README.txt"
echo - Tien Len game assistance >> "Distribution-Safe\AutoGameBai\README.txt"
echo - Smart card suggestions >> "Distribution-Safe\AutoGameBai\README.txt"
echo - Team coordination >> "Distribution-Safe\AutoGameBai\README.txt"

echo.
echo Step 8: Testing the executable...
echo Checking if AutoGameBai.exe can start...
cd "Distribution-Safe\AutoGameBai"
timeout /t 2 /nobreak > nul
start /wait /min AutoGameBai.exe --test-mode 2>nul
if %ERRORLEVEL% equ 0 (
    echo ✅ Executable test passed!
) else (
    echo ⚠️  Executable test inconclusive (may still work)
)
cd ..\..

echo.
echo Step 9: Creating ZIP package...
powershell -command "Compress-Archive -Path 'Distribution-Safe\AutoGameBai\*' -DestinationPath 'Distribution-Safe\AutoGameBai-SAFE-v1.0.zip' -Force"

echo.
echo Step 10: Restoring debug mode...
powershell -Command "(Get-Content 'Form1.cs') -replace 'private static readonly bool IsDebugMode = false;', 'private static readonly bool IsDebugMode = true;' | Set-Content 'Form1.cs'"

echo.
echo ✅ Safe distribution package created successfully!
echo.
echo Files created:
echo - Distribution-Safe\AutoGameBai\ (folder with all files)
echo - Distribution-Safe\AutoGameBai-SAFE-v1.0.zip (ready to distribute)
echo.
echo Package contents:
dir "Distribution-Safe\AutoGameBai" /B

echo.
echo 📦 Ready to distribute: Distribution-Safe\AutoGameBai-SAFE-v1.0.zip
echo 🔒 Protection level: BASIC (stable and compatible)
echo 📁 Package size:
powershell -command "(Get-Item 'Distribution-Safe\AutoGameBai-SAFE-v1.0.zip').Length / 1MB | ForEach-Object { '{0:N2} MB' -f $_ }"

echo.
echo ✅ This version prioritizes stability over maximum protection.
echo 🛡️  Basic protection is still active to prevent casual reverse engineering.

echo.
pause
