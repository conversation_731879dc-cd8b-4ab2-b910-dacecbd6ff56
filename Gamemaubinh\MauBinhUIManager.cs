using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace AutoGameBai.Gamemaubinh
{
    /// <summary>
    /// UI Manager cho <PERSON><PERSON>h - Đơn giản và hiệu quả
    /// </summary>
    public class MauBinhUIManager
    {
        private readonly UIManager _uiManager;
        private readonly MauBinhEngine _engine;
        private readonly TeamStrategyEngine _teamEngine;

        // UI Controls
        private ListBox _suggestionListBox;
        private Panel _cardDisplayPanel;
        private Label _chickenScoreLabel;
        private Dictionary<int, Panel> _userPanels;
        private Dictionary<int, Label> _sapLangLabels;

        public MauBinhUIManager(UIManager uiManager, ListBox suggestionListBox, Panel cardDisplayPanel)
        {
            _uiManager = uiManager;
            _engine = new MauBinhEngine(uiManager);
            _teamEngine = new TeamStrategyEngine(uiManager);
            _suggestionListBox = suggestionListBox;
            _cardDisplayPanel = cardDisplayPanel;
            _userPanels = new Dictionary<int, Panel>();
            _sapLangLabels = new Dictionary<int, Label>();

            InitializeUI();
        }

        #region UI Initialization

        /// <summary>
        /// Khởi tạo UI
        /// </summary>
        private void InitializeUI()
        {
            // Tạo label điểm Gà
            _chickenScoreLabel = new Label
            {
                Size = new Size(100, 40),
                BackColor = Color.Yellow,
                ForeColor = Color.Red,
                Font = new Font("Arial", 12, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                BorderStyle = BorderStyle.FixedSingle,
                Text = "0",
                Visible = false
            };

            // Setup ListBox
            _suggestionListBox.DrawMode = DrawMode.OwnerDrawFixed;
            _suggestionListBox.ItemHeight = 25;
            _suggestionListBox.DrawItem += SuggestionListBox_DrawItem;
            _suggestionListBox.SelectedIndexChanged += SuggestionListBox_SelectedIndexChanged;
        }

        /// <summary>
        /// Custom draw cho ListBox
        /// </summary>
        private void SuggestionListBox_DrawItem(object sender, DrawItemEventArgs e)
        {
            if (e.Index < 0) return;

            var listBox = sender as ListBox;
            var item = listBox.Items[e.Index] as MauBinhEngine.MauBinhSuggestion;
            if (item == null) return;

            e.DrawBackground();

            // Màu nền
            var backColor = e.State.HasFlag(DrawItemState.Selected) ? Color.LightBlue : Color.White;
            if (item.IsSpecial) backColor = Color.Gold;

            using (var brush = new SolidBrush(backColor))
            {
                e.Graphics.FillRectangle(brush, e.Bounds);
            }

            // Text
            var text = FormatSuggestionText(item);
            var textColor = item.IsSpecial ? Color.Red : Color.Black;
            
            using (var brush = new SolidBrush(textColor))
            {
                e.Graphics.DrawString(text, e.Font, brush, e.Bounds.Left + 5, e.Bounds.Top + 5);
            }

            e.DrawFocusRectangle();
        }

        #endregion

        #region Main Methods

        /// <summary>
        /// Tạo gợi ý cho 1 user
        /// </summary>
        public void GenerateSingleUserSuggestions(int[] cards)
        {
            try
            {
                var suggestions = _engine.GenerateSuggestions(cards);
                DisplaySuggestions(suggestions);
                _uiManager.AppendLog($"✅ Tạo {suggestions.Count} gợi ý cho user", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo gợi ý: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Tạo gợi ý team (3 users vs Gà)
        /// </summary>
        public void GenerateTeamSuggestions(Dictionary<string, int[]> userCards)
        {
            try
            {
                var users = userCards.Select((kvp, index) => new TeamStrategyEngine.UserInfo
                {
                    Username = kvp.Key,
                    Cards = kvp.Value,
                    Index = index
                }).ToList();

                var teamResult = _teamEngine.CalculateTeamStrategy(users);
                DisplayTeamResult(teamResult, users);

                _uiManager.AppendLog("✅ Hoàn thành tính toán team strategy", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi team strategy: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Hiển thị gợi ý trong ListBox
        /// </summary>
        private void DisplaySuggestions(List<MauBinhEngine.MauBinhSuggestion> suggestions)
        {
            _suggestionListBox.Items.Clear();
            
            foreach (var suggestion in suggestions)
            {
                _suggestionListBox.Items.Add(suggestion);
            }

            if (suggestions.Any())
            {
                _suggestionListBox.SelectedIndex = 0;
            }
        }

        /// <summary>
        /// Hiển thị kết quả team
        /// </summary>
        private void DisplayTeamResult(TeamStrategyEngine.TeamResult result, List<TeamStrategyEngine.UserInfo> users)
        {
            // Hiển thị điểm thua của Gà
            _chickenScoreLabel.Text = result.ChickenTotalLoss.ToString();
            _chickenScoreLabel.Visible = true;

            // Hiển thị sập làng
            foreach (var user in users)
            {
                var isSapLang = result.SapLangUsers.Contains(user.Username);
                ShowSapLangLabel(user.Index, isSapLang);
            }

            // Log kết quả
            var resultText = _teamEngine.FormatTeamResult(result);
            _uiManager.AppendLog(resultText, UIManager.LogLevel.Info);
        }

        #endregion

        #region Card Display

        /// <summary>
        /// Hiển thị bài theo gợi ý
        /// </summary>
        public void DisplayCardsByIndex(int[] cards, int userIndex = 0)
        {
            var panel = GetOrCreateUserPanel(userIndex);
            panel.Controls.Clear();

            // Hiển thị theo format: Chi3 (top), Chi2 (middle), Chi1 (bottom)
            var chi1Cards = cards.Take(5).ToArray();
            var chi2Cards = cards.Skip(5).Take(5).ToArray();
            var chi3Cards = cards.Skip(10).Take(3).ToArray();

            // Chi3 - Top row
            DisplayChiCards(panel, chi3Cards, 0, "Chi3");

            // Chi2 - Middle row  
            DisplayChiCards(panel, chi2Cards, 1, "Chi2");

            // Chi1 - Bottom row
            DisplayChiCards(panel, chi1Cards, 2, "Chi1");

            // Đặt label điểm Gà nếu là index 3 (Gà)
            if (userIndex == 3)
            {
                _chickenScoreLabel.Parent = panel;
                _chickenScoreLabel.Location = new Point(panel.Width - 120, 10);
                _chickenScoreLabel.BringToFront();
            }
        }

        /// <summary>
        /// Hiển thị cards của 1 chi
        /// </summary>
        private void DisplayChiCards(Panel parentPanel, int[] cards, int row, string chiName)
        {
            var startY = row * 80 + 10;
            var startX = 10;

            // Label tên chi
            var chiLabel = new Label
            {
                Text = chiName,
                Location = new Point(startX, startY),
                Size = new Size(50, 20),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            parentPanel.Controls.Add(chiLabel);

            // Hiển thị cards
            for (int i = 0; i < cards.Length; i++)
            {
                var cardPanel = CreateCardPanel(cards[i]);
                cardPanel.Location = new Point(startX + 60 + i * 65, startY);
                parentPanel.Controls.Add(cardPanel);
            }
        }

        /// <summary>
        /// Tạo panel cho 1 lá bài
        /// </summary>
        private Panel CreateCardPanel(int cardId)
        {
            var panel = new Panel
            {
                Size = new Size(60, 75),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White
            };

            var label = new Label
            {
                Text = CardUtilityMaubinh.GetCardDisplayName(cardId),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Arial", 10, FontStyle.Bold),
                ForeColor = CardUtilityMaubinh.IsRedCard(cardId) ? Color.Red : Color.Black
            };

            panel.Controls.Add(label);
            return panel;
        }

        /// <summary>
        /// Lấy hoặc tạo panel cho user
        /// </summary>
        private Panel GetOrCreateUserPanel(int userIndex)
        {
            if (!_userPanels.ContainsKey(userIndex))
            {
                var panel = new Panel
                {
                    Size = new Size(400, 250),
                    BorderStyle = BorderStyle.FixedSingle,
                    Location = new Point((userIndex % 2) * 420, (userIndex / 2) * 260)
                };
                
                _cardDisplayPanel.Controls.Add(panel);
                _userPanels[userIndex] = panel;
            }

            return _userPanels[userIndex];
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Xử lý khi chọn gợi ý trong ListBox
        /// </summary>
        private void SuggestionListBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            var listBox = sender as ListBox;
            var selectedSuggestion = listBox.SelectedItem as MauBinhEngine.MauBinhSuggestion;
            
            if (selectedSuggestion != null)
            {
                // Hiển thị bài theo gợi ý được chọn
                var allCards = selectedSuggestion.Chi1
                    .Concat(selectedSuggestion.Chi2)
                    .Concat(selectedSuggestion.Chi3)
                    .ToArray();

                DisplayCardsByIndex(allCards, 0);
                
                _uiManager.AppendLog($"📋 Chọn gợi ý: {selectedSuggestion.Description}", UIManager.LogLevel.Info);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Format text cho gợi ý
        /// </summary>
        private string FormatSuggestionText(MauBinhEngine.MauBinhSuggestion suggestion)
        {
            var chi1Text = string.Join(",", suggestion.Chi1.Select(CardUtilityMaubinh.GetCardDisplayName));
            var chi2Text = string.Join(",", suggestion.Chi2.Select(CardUtilityMaubinh.GetCardDisplayName));
            var chi3Text = string.Join(",", suggestion.Chi3.Select(CardUtilityMaubinh.GetCardDisplayName));

            return $"{chi1Text} - {chi2Text} - {chi3Text} | {suggestion.Description}";
        }

        /// <summary>
        /// Hiển thị label sập làng
        /// </summary>
        private void ShowSapLangLabel(int userIndex, bool show)
        {
            if (!_sapLangLabels.ContainsKey(userIndex))
            {
                var label = new Label
                {
                    Text = "💥 SẬP LÀNG",
                    BackColor = Color.Red,
                    ForeColor = Color.White,
                    Font = new Font("Arial", 12, FontStyle.Bold),
                    Size = new Size(100, 30),
                    TextAlign = ContentAlignment.MiddleCenter
                };
                
                var userPanel = GetOrCreateUserPanel(userIndex);
                userPanel.Controls.Add(label);
                label.Location = new Point(userPanel.Width - 110, userPanel.Height - 40);
                label.BringToFront();
                
                _sapLangLabels[userIndex] = label;
            }

            _sapLangLabels[userIndex].Visible = show;
        }

        #endregion
    }
}
