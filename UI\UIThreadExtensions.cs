﻿using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AutoGameBai.UI
{
    public static class UIThreadExtensions
    {
        public static void InvokeIfRequired(this Control control, Action action)
        {
            if (control.InvokeRequired)
            {
                control.Invoke(action);
            }
            else
            {
                action();
            }
        }

        public static async Task InvokeIfRequiredAsync(this Control control, Action action)
        {
            if (control.InvokeRequired)
            {
                await Task.Run(() => control.Invoke(action));
            }
            else
            {
                action();
            }
        }

        public static T InvokeIfRequired<T>(this Control control, Func<T> func)
        {
            if (control.InvokeRequired)
            {
                return (T)control.Invoke(func);
            }
            else
            {
                return func();
            }
        }

        public static async Task<T> InvokeIfRequiredAsync<T>(this Control control, Func<T> func)
        {
            if (control.InvokeRequired)
            {
                return await Task.Run(() => (T)control.Invoke(func));
            }
            else
            {
                return func();
            }
        }

        public static Task InvokeAsync(this Control control, Action action)
        {
            var tcs = new TaskCompletionSource<bool>();
            if (control.InvokeRequired)
            {
                control.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        action();
                        tcs.SetResult(true);
                    }
                    catch (Exception ex)
                    {
                        tcs.SetException(ex);
                    }
                }));
            }
            else
            {
                try
                {
                    action();
                    tcs.SetResult(true);
                }
                catch (Exception ex)
                {
                    tcs.SetException(ex);
                }
            }
            return tcs.Task;
        }

        public static Task<T> InvokeAsync<T>(this Control control, Func<T> func)
        {
            var tcs = new TaskCompletionSource<T>();
            if (control.InvokeRequired)
            {
                control.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        tcs.SetResult(func());
                    }
                    catch (Exception ex)
                    {
                        tcs.SetException(ex);
                    }
                }));
            }
            else
            {
                try
                {
                    tcs.SetResult(func());
                }
                catch (Exception ex)
                {
                    tcs.SetException(ex);
                }
            }
            return tcs.Task;
        }

        public static async Task InvokeAsync(this Control control, Func<Task> func)
        {
            var tcs = new TaskCompletionSource<bool>();
            if (control.InvokeRequired)
            {
                control.BeginInvoke(new Action(async () =>
                {
                    try
                    {
                        await func();
                        tcs.SetResult(true);
                    }
                    catch (Exception ex)
                    {
                        tcs.SetException(ex);
                    }
                }));
            }
            else
            {
                try
                {
                    await func();
                    tcs.SetResult(true);
                }
                catch (Exception ex)
                {
                    tcs.SetException(ex);
                }
            }
            await tcs.Task;
        }

        public static async Task<T> InvokeAsync<T>(this Control control, Func<Task<T>> func)
        {
            var tcs = new TaskCompletionSource<T>();
            if (control.InvokeRequired)
            {
                control.BeginInvoke(new Action(async () =>
                {
                    try
                    {
                        tcs.SetResult(await func());
                    }
                    catch (Exception ex)
                    {
                        tcs.SetException(ex);
                    }
                }));
            }
            else
            {
                try
                {
                    tcs.SetResult(await func());
                }
                catch (Exception ex)
                {
                    tcs.SetException(ex);
                }
            }
            return await tcs.Task;
        }
    }
}