using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoGameBai.Gamemaubinh;

namespace AutoGameBai.WebSocket
{
    /// <summary>
    /// WebSocket handler cho game <PERSON><PERSON>u <PERSON>h
    /// X<PERSON> lý các cmd: 600, 602, 603
    /// </summary>
    public class MauBinhWebSocketHandler : IGameWebSocketHandler
    {
        private readonly GameClientManager _gameClient;
        private readonly UIManager _uiManager;
        private readonly MauBinhSuggestionHandler _suggestionHandler;
        private readonly MauBinhCardManager _cardManager;
        private readonly Dictionary<string, int[]> _userCards;
        private readonly Dictionary<string, (int[] Chi1, int[] Chi2, int[] Chi3)> _opponentRealHands;
        private readonly Dictionary<string, int> _gameResults;
        private readonly object _lock = new object();

        public HashSet<int> SupportedCommands { get; } = new HashSet<int> { 600, 602, 603 };

        public event Action? CardsUpdated;
        public event Action? PlayCardReceived;
        public event Action? GameEnded;
        public event Action? NewGameStarted;

        public MauBinhWebSocketHandler(GameClientManager gameClient, UIManager uiManager, 
            MauBinhSuggestionHandler suggestionHandler, MauBinhCardManager cardManager)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _suggestionHandler = suggestionHandler ?? throw new ArgumentNullException(nameof(suggestionHandler));
            _cardManager = cardManager ?? throw new ArgumentNullException(nameof(cardManager));

            _userCards = new Dictionary<string, int[]>();
            _opponentRealHands = new Dictionary<string, (int[] Chi1, int[] Chi2, int[] Chi3)>();
            _gameResults = new Dictionary<string, int>();
        }

        public void HandleCommand(JObject messageData, string username, int cmd)
        {
            switch (cmd)
            {
                case 600:
                    HandleCmd600(messageData, username);
                    break;
                case 602:
                    HandleCmd602(messageData, username);
                    break;
                case 603:
                    HandleCmd603(messageData, username);
                    break;
                default:
                    _uiManager.AppendLog($"Unsupported Mau Binh command: {cmd}", UIManager.LogLevel.Warning, username);
                    break;
            }
        }

        /// <summary>
        /// Xử lý cmd 600 - Nhận bài Mậu Binh
        /// </summary>
        private void HandleCmd600(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 600 (Mau Binh cards)", UIManager.LogLevel.Debug);

                if (!messageData.ContainsKey("cs") || !messageData.ContainsKey("lpi"))
                {
                    _uiManager.AppendLog($"Cmd 600 for {username} lacks 'cs' or 'lpi' field", UIManager.LogLevel.Warning);
                    return;
                }

                var cardIds = messageData["cs"]?.ToObject<int[]>();
                var lpi = messageData["lpi"]?.ToObject<string[]>();
                
                if (cardIds == null || lpi == null || string.IsNullOrEmpty(username))
                {
                    _uiManager.AppendLog($"Cannot save cards for {username}: Invalid cardIds, lpi, or username", UIManager.LogLevel.Error);
                    return;
                }

                if (cardIds.Length != 13)
                {
                    _uiManager.AppendLog($"Invalid card data for {username}: {cardIds.Length} cards (expected 13)", UIManager.LogLevel.Error);
                    return;
                }

                lock (_lock)
                {
                    _userCards[username] = cardIds;
                    string cardsString = CardUtilityMaubinh.ConvertCardsToString(cardIds);
                    _uiManager.AppendLog($"Saved Mau Binh cards for {username}: {cardsString}", UIManager.LogLevel.Info);
                }

                CardsUpdated?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 600 for {username}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Xử lý cmd 602 - Kết thúc ván Mậu Binh
        /// </summary>
        private async void HandleCmd602(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 602 (Mau Binh game end)", UIManager.LogLevel.Info, username);
                
                if (messageData.ContainsKey("ps"))
                {
                    var playersArray = messageData["ps"] as JArray;
                    if (playersArray != null)
                    {
                        foreach (var player in playersArray)
                        {
                            string playerUsername = player["dn"]?.ToString();
                            if (string.IsNullOrEmpty(playerUsername)) continue;

                            long moneyChange = player["mX"]?.Value<long>() ?? 0;
                            string result;

                            if (_gameClient.SelectedGame == "HitClub")
                            {
                                var gr = player["gr"] as JArray;
                                string chiResult = gr != null ? $"Chi: {gr.Count}" : "No chi";
                                result = moneyChange > 0 ? $"Win: +{moneyChange} ({chiResult})"
                                    : moneyChange < 0 ? $"Lose: {moneyChange} ({chiResult})"
                                    : $"Tie ({chiResult})";
                            }
                            else
                            {
                                int? rank = player["rr"]?.Value<int>();
                                string rankResult = rank.HasValue ? $"Rank: {rank}" : "No rank";
                                result = moneyChange > 0 ? $"Win: +{moneyChange} ({rankResult})"
                                    : moneyChange < 0 ? $"Lose: {moneyChange} ({rankResult})"
                                    : $"Tie ({rankResult})";
                            }

                            if (_gameClient.GetUsers().ContainsKey(playerUsername))
                            {
                                _gameClient.GetUsers()[playerUsername].GameResult = result;
                                _uiManager.AppendLog($"Game result for {playerUsername}: {result}", UIManager.LogLevel.Info, playerUsername);

                                if (playerUsername != username)
                                {
                                    var cards = player["cs"]?.ToObject<int[]>();
                                    if (cards != null && cards.Length == 13)
                                    {
                                        var chi1 = cards.Take(5).ToArray();
                                        var chi2 = cards.Skip(5).Take(5).ToArray();
                                        var chi3 = cards.Skip(10).Take(3).ToArray();
                                        
                                        lock (_lock)
                                        {
                                            _opponentRealHands[playerUsername] = (chi1, chi2, chi3);
                                            _gameResults[playerUsername] = (int)moneyChange;
                                        }
                                        
                                        _uiManager.AppendLog($"Saved opponent hand for {playerUsername}: Chi 1: {CardUtilityMaubinh.ConvertCardsToString(chi1)}, Chi 2: {CardUtilityMaubinh.ConvertCardsToString(chi2)}, Chi 3: {CardUtilityMaubinh.ConvertCardsToString(chi3)}, Score: {moneyChange}", UIManager.LogLevel.Info, playerUsername);
                                    }
                                }
                            }
                        }
                        _gameClient.RefreshUserList?.Invoke();
                    }
                }

                // Delay 8 seconds before clearing
                _uiManager.AppendLog("Waiting 8 seconds before clearing Mau Binh data...", UIManager.LogLevel.Info);
                await Task.Delay(8000);

                lock (_lock)
                {
                    _userCards.Clear();
                    _suggestionHandler.ClearSuggestions();
                    
                    try
                    {
                        if (_gameClient.MauBinhSuggestionForm != null && !_gameClient.MauBinhSuggestionForm.IsDisposed)
                        {
                            _gameClient.MauBinhSuggestionForm.Invoke(new Action(() =>
                            {
                                _gameClient.MauBinhSuggestionForm.ClearCardDisplay();
                            }));
                            _uiManager.AppendLog("Cleared card display in MauBinhSuggestionForm", UIManager.LogLevel.Info);
                        }
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Error clearing card display: {ex.Message}", UIManager.LogLevel.Error);
                    }
                }

                GameEnded?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 602 for {username}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Xử lý cmd 603 - Cập nhật bài Mậu Binh
        /// </summary>
        private void HandleCmd603(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 603 (Mau Binh card update)", UIManager.LogLevel.Debug);

                if (!messageData.ContainsKey("cs"))
                {
                    _uiManager.AppendLog($"Cmd 603 for {username} lacks 'cs' field", UIManager.LogLevel.Warning);
                    return;
                }

                var cardIds = messageData["cs"]?.ToObject<int[]>();
                if (cardIds == null || cardIds.Length != 13)
                {
                    _uiManager.AppendLog($"Invalid card data in cmd 603 for {username}: {cardIds?.Length ?? 0} cards (expected 13)", UIManager.LogLevel.Error);
                    return;
                }

                string cardsString = CardUtilityMaubinh.ConvertCardsToString(cardIds);
                _uiManager.AppendLog($"Cmd 603 Mau Binh cards for {username}: {cardsString}", UIManager.LogLevel.Info);

                lock (_lock)
                {
                    _userCards[username] = cardIds;
                }

                CardsUpdated?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 603 for {username}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        public void ResetGameData()
        {
            lock (_lock)
            {
                _userCards.Clear();
                _opponentRealHands.Clear();
                _gameResults.Clear();
            }
            _uiManager.AppendLog("Reset Mau Binh game data", UIManager.LogLevel.Info);
        }

        public bool IsTeamMode()
        {
            return _userCards.Count >= 3;
        }

        public void UpdateUserCards(string username, int[] cards)
        {
            if (string.IsNullOrEmpty(username) || cards == null || cards.Length != 13)
            {
                _uiManager.AppendLog($"Cannot update Mau Binh cards for {username}: Invalid data", UIManager.LogLevel.Error);
                return;
            }

            lock (_lock)
            {
                _userCards[username] = cards;
                string cardsString = CardUtilityMaubinh.ConvertCardsToString(cards);
                _uiManager.AppendLog($"Updated Mau Binh cards for {username}: {cardsString}", UIManager.LogLevel.Info);
            }

            CardsUpdated?.Invoke();
        }

        // Getters for Mau Binh specific data
        public (int[] Chi1, int[] Chi2, int[] Chi3)? GetOpponentRealHand(string username)
        {
            lock (_lock)
            {
                return _opponentRealHands.ContainsKey(username) ? _opponentRealHands[username] : null;
            }
        }

        public int? GetGameResult(string username)
        {
            lock (_lock)
            {
                return _gameResults.ContainsKey(username) ? _gameResults[username] : null;
            }
        }

        public Dictionary<string, int[]> GetUserCards() => _userCards.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }
}
