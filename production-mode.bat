@echo off
echo ========================================
echo   AutoGameBai Production Mode Helper
echo ========================================

echo.
echo Current configuration:
echo - Debug Mode: DISABLED (Protection ENABLED)
echo - Build Configuration: Release
echo - Anti-Debug Protection: ON
echo - String Encryption: ON
echo - Debugger Attachment: BLOCKED

echo.
echo Building in Release mode...
dotnet build AutoGameBai.csproj --configuration Release --no-restore

if %ERRORLEVEL% equ 0 (
    echo.
    echo ✅ Production build completed successfully!
    echo.
    echo Protection features:
    echo - Anti-debug detection active
    echo - String encryption enabled
    echo - Code virtualization active
    echo - Tamper detection enabled
    echo.
    echo Output: bin\Release\net8.0-windows\AutoGameBai.exe
    echo.
    echo ⚠️  WARNING: This build cannot be debugged!
) else (
    echo.
    echo ❌ Production build failed!
    echo Check the error messages above.
)

echo.
pause
