using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using System;
using System.IO;
using System.Threading.Tasks;

namespace AutoGameBai.Scripts
{
    public class JavaScriptInjector
    {
        private readonly UIManager _uiManager;
        private static string? _profileButtonsScript;

        public JavaScriptInjector(UIManager uiManager)
        {
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
        }

        /// <summary>
        /// Load profile-buttons.js script từ file
        /// </summary>
        private static string GetProfileButtonsScript()
        {
            if (_profileButtonsScript == null)
            {
                try
                {
                    string scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Scripts", "profile-buttons.js");
                    if (File.Exists(scriptPath))
                    {
                        _profileButtonsScript = File.ReadAllText(scriptPath);
                    }
                    else
                    {
                        // Fallback inline script nếu file không tồn tại
                        _profileButtonsScript = GetInlineProfileButtonsScript();
                    }
                }
                catch (Exception)
                {
                    _profileButtonsScript = GetInlineProfileButtonsScript();
                }
            }
            return _profileButtonsScript;
        }

        /// <summary>
        /// Inline script backup nếu file không tồn tại
        /// </summary>
        private static string GetInlineProfileButtonsScript()
        {
            return @"
(function() {
    'use strict';

    if (window.profileButtonsLoaded) return;
    window.profileButtonsLoaded = true;

    let getKeyButton = null;
    let leaveRoomButton = null;
    let isGetKeyRunning = false;
    let getKeyInterval = null;

    // Tạo CSS styles
    function createStyles() {
        if (document.getElementById('profile-buttons-styles')) return;

        const style = document.createElement('style');
        style.id = 'profile-buttons-styles';
        style.textContent = `
            .profile-button {
                padding: 8px 16px;
                margin: 5px;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 80px;
                height: 32px;
                z-index: 99999;
            }

            .get-key-btn {
                background-color: #007bff;
                color: white;
            }

            .get-key-btn:hover {
                background-color: #0056b3;
            }

            .get-key-btn.stop {
                background-color: #dc3545;
                color: white;
            }

            .get-key-btn.stop:hover {
                background-color: #c82333;
            }

            .leave-room-btn {
                background-color: #6c757d;
                color: white;
            }

            .leave-room-btn:hover {
                background-color: #545b62;
            }

            .button-container {
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 99999;
                display: flex;
                gap: 5px;
            }
        `;
        document.head.appendChild(style);
    }

    // Tạo Get Key button
    function createGetKeyButton() {
        getKeyButton = document.createElement('button');
        getKeyButton.className = 'profile-button get-key-btn';
        getKeyButton.textContent = 'Get Key';
        getKeyButton.onclick = toggleGetKey;
        return getKeyButton;
    }

    // Tạo Leave Room button
    function createLeaveRoomButton() {
        leaveRoomButton = document.createElement('button');
        leaveRoomButton.className = 'profile-button leave-room-btn';
        leaveRoomButton.textContent = 'Thoát bàn';
        leaveRoomButton.onclick = leaveRoom;
        return leaveRoomButton;
    }

    // Toggle Get Key functionality
    function toggleGetKey() {
        if (!isGetKeyRunning) {
            startGetKey();
        } else {
            stopGetKey();
        }
    }

    // Bắt đầu Get Key
    function startGetKey() {
        isGetKeyRunning = true;
        getKeyButton.textContent = 'Stop';
        getKeyButton.classList.add('stop');
        console.log('🔑 Bắt đầu Get Key Room...');
    }

    // Dừng Get Key
    function stopGetKey() {
        isGetKeyRunning = false;
        getKeyButton.textContent = 'Get Key';
        getKeyButton.classList.remove('stop');
        console.log('🛑 Đã dừng Get Key Room');
    }

    // Thoát phòng
    function leaveRoom() {
        console.log('🚪 Thoát phòng');
    }

    // Khởi tạo buttons
    function initButtons() {
        // Xóa buttons cũ nếu có
        const existingContainer = document.querySelector('.button-container');
        if (existingContainer) {
            existingContainer.remove();
        }

        // Tạo styles
        createStyles();

        // Tạo container
        const container = document.createElement('div');
        container.className = 'button-container';

        // Tạo và thêm buttons
        container.appendChild(createGetKeyButton());
        container.appendChild(createLeaveRoomButton());

        // Thêm vào body
        document.body.appendChild(container);

        console.log('✅ Profile buttons đã được khởi tạo');
    }

    // Khởi tạo ngay lập tức
    initButtons();

    // Export functions
    window.profileButtons = {
        startGetKey: startGetKey,
        stopGetKey: stopGetKey,
        leaveRoom: leaveRoom,
        isGetKeyRunning: () => isGetKeyRunning,
        reinit: initButtons
    };

})();
";
        }

        /// <summary>
        /// Inject profile buttons vào trang web
        /// </summary>
        public async Task InjectProfileButtons(ChromeDriver driver, string username)
        {
            try
            {
                if (driver == null)
                {
                    _uiManager.AppendLog($"Driver null cho {username}, không thể inject JavaScript", UIManager.LogLevel.Warning, username);
                    return;
                }

                // Đợi trang load xong
                await Task.Delay(3000);

                // Kiểm tra nếu trang đã load xong
                bool isReady = (bool)driver.ExecuteScript("return document.readyState === 'complete'");
                if (!isReady)
                {
                    _uiManager.AppendLog($"Trang chưa load xong cho {username}, đợi thêm...", UIManager.LogLevel.Debug, username);
                    await Task.Delay(2000);
                }

                // Inject script
                string script = GetProfileButtonsScript();
                driver.ExecuteScript(script);

                // Kiểm tra xem buttons đã được tạo chưa
                await Task.Delay(1000);
                bool buttonsExist = (bool)driver.ExecuteScript("return document.querySelector('.button-container') !== null");

                if (buttonsExist)
                {
                    _uiManager.AppendLog($"✅ Đã inject profile buttons thành công cho {username}", UIManager.LogLevel.Info, username);
                }
                else
                {
                    _uiManager.AppendLog($"⚠️ Inject script thành công nhưng buttons chưa xuất hiện cho {username}", UIManager.LogLevel.Warning, username);

                    // Thử inject lại với script đơn giản hơn
                    string simpleScript = @"
                        if (!document.querySelector('.button-container')) {
                            const container = document.createElement('div');
                            container.className = 'button-container';
                            container.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 99999; display: flex; gap: 5px;';

                            const getKeyBtn = document.createElement('button');
                            getKeyBtn.textContent = 'Get Key';
                            getKeyBtn.style.cssText = 'padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; font-weight: bold;';

                            const leaveBtn = document.createElement('button');
                            leaveBtn.textContent = 'Thoát bàn';
                            leaveBtn.style.cssText = 'padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; font-weight: bold;';

                            container.appendChild(getKeyBtn);
                            container.appendChild(leaveBtn);
                            document.body.appendChild(container);

                            console.log('✅ Simple buttons injected');
                        }
                    ";
                    driver.ExecuteScript(simpleScript);
                    _uiManager.AppendLog($"🔄 Đã thử inject simple buttons cho {username}", UIManager.LogLevel.Info, username);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi inject profile buttons cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Inject JavaScript methods để gọi C# functions
        /// </summary>
        public void InjectCSharpMethods(ChromeDriver driver, string username)
        {
            try
            {
                string csharpMethodsScript = $@"
                    window.csharpMethods = {{
                        getKeyRoom: function(username) {{
                            console.log('🔑 Calling C# GetKeyRoom for: ' + username);
                            try {{
                                // Gọi C# method thông qua window.external
                                if (window.external && window.external.GetKeyRoom) {{
                                    window.external.GetKeyRoom(username);
                                    console.log('✅ GetKeyRoom called successfully');
                                }} else {{
                                    console.warn('C# GetKeyRoom method not available');
                                    alert('🔑 GetKeyRoom cho ' + username + ' - External method không khả dụng');
                                }}
                            }} catch (e) {{
                                console.error('Error calling GetKeyRoom:', e);
                                alert('❌ Lỗi khi gọi GetKeyRoom: ' + e.message);
                            }}
                        }},
                        leaveRoom: function(username) {{
                            console.log('🚪 Calling C# LeaveRoom for: ' + username);
                            try {{
                                // Gọi C# method thông qua window.external
                                if (window.external && window.external.LeaveRoom) {{
                                    window.external.LeaveRoom(username);
                                    console.log('✅ LeaveRoom called successfully');
                                }} else {{
                                    console.warn('C# LeaveRoom method not available');
                                    alert('🚪 LeaveRoom cho ' + username + ' - External method không khả dụng');
                                }}
                            }} catch (e) {{
                                console.error('Error calling LeaveRoom:', e);
                                alert('❌ Lỗi khi gọi LeaveRoom: ' + e.message);
                            }}
                        }},
                        getCurrentUsername: function() {{
                            // Detect username từ profile context
                            const username = '{username}';
                            console.log('🔍 Detected username: ' + username);
                            return username;
                        }},
                        isInRoom: function() {{
                            try {{
                                if (window.external && window.external.IsInRoom) {{
                                    return window.external.IsInRoom();
                                }}
                                return false;
                            }} catch (e) {{
                                console.error('Error checking IsInRoom:', e);
                                return false;
                            }}
                        }},
                        getRoomInfo: function() {{
                            try {{
                                if (window.external && window.external.GetRoomInfo) {{
                                    return window.external.GetRoomInfo();
                                }}
                                return 'Không có thông tin';
                            }} catch (e) {{
                                console.error('Error getting RoomInfo:', e);
                                return 'Lỗi';
                            }}
                        }}
                    }};
                    console.log('✅ C# methods injected for {username}');
                ";

                driver.ExecuteScript(csharpMethodsScript);
                _uiManager.AppendLog($"✅ Đã inject C# methods cho {username}", UIManager.LogLevel.Info, username);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi inject C# methods cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Test và debug buttons
        /// </summary>
        public void TestButtons(ChromeDriver driver, string username)
        {
            try
            {
                string testScript = @"
                    console.log('🧪 Testing buttons...');

                    // Kiểm tra buttons có tồn tại không
                    const container = document.querySelector('.button-container');
                    const getKeyBtn = document.querySelector('.get-key-btn');
                    const leaveBtn = document.querySelector('.leave-room-btn');

                    console.log('Container exists:', !!container);
                    console.log('Get Key button exists:', !!getKeyBtn);
                    console.log('Leave Room button exists:', !!leaveBtn);

                    if (leaveBtn) {
                        console.log('Leave button background:', getComputedStyle(leaveBtn).backgroundColor);
                        console.log('Leave button color:', getComputedStyle(leaveBtn).color);
                    }

                    // Kiểm tra C# methods
                    console.log('window.csharpMethods exists:', !!window.csharpMethods);
                    if (window.csharpMethods) {
                        console.log('getKeyRoom method:', typeof window.csharpMethods.getKeyRoom);
                        console.log('leaveRoom method:', typeof window.csharpMethods.leaveRoom);
                        console.log('getCurrentUsername method:', typeof window.csharpMethods.getCurrentUsername);
                    }

                    return {
                        container: !!container,
                        getKeyBtn: !!getKeyBtn,
                        leaveBtn: !!leaveBtn,
                        csharpMethods: !!window.csharpMethods
                    };
                ";

                var result = driver.ExecuteScript(testScript);
                _uiManager.AppendLog($"🧪 Test results cho {username}: {result}", UIManager.LogLevel.Info, username);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi test buttons cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Force inject simple buttons (fallback method)
        /// </summary>
        public void ForceInjectSimpleButtons(ChromeDriver driver, string username)
        {
            try
            {
                string forceScript = @"
                    // Xóa buttons cũ nếu có
                    const existing = document.querySelector('.button-container');
                    if (existing) existing.remove();

                    // Tạo container
                    const container = document.createElement('div');
                    container.className = 'button-container';
                    container.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 99999; display: flex; gap: 5px;';

                    // Tạo Get Key button
                    const getKeyBtn = document.createElement('button');
                    getKeyBtn.textContent = 'Get Key';
                    getKeyBtn.className = 'get-key-btn';
                    getKeyBtn.style.cssText = 'padding: 6px 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px; font-weight: bold; min-width: 70px; height: 28px; margin: 3px;';

                    let isGetKeyRunning = false;
                    getKeyBtn.onclick = function() {
                        if (!isGetKeyRunning) {
                            isGetKeyRunning = true;
                            getKeyBtn.textContent = 'Stop';
                            getKeyBtn.style.background = '#dc3545';
                            console.log('🔑 Get Key started for {username}');

                            // Test button functionality với alert
                            try {
                                console.log('🔑 Get Key button clicked for {username}');
                                alert('🔑 Get Key button hoạt động! Username: {username}');

                                // Log để debug
                                console.log('CSHARP_COMMAND:GetKeyRoom:{username}:100:10');
                            } catch (e) {
                                console.error('Error in GetKey button:', e);
                                alert('❌ Lỗi: ' + e.message);
                            }
                        } else {
                            isGetKeyRunning = false;
                            getKeyBtn.textContent = 'Get Key';
                            getKeyBtn.style.background = '#007bff';
                            console.log('🛑 Get Key stopped for {username}');
                        }
                    };

                    // Tạo Leave Room button với FORCE RED background
                    const leaveBtn = document.createElement('button');
                    leaveBtn.textContent = 'Thoát bàn';
                    leaveBtn.className = 'leave-room-btn';

                    // FORCE RED - Multiple methods
                    leaveBtn.style.cssText = 'background: #dc3545 !important; background-color: #dc3545 !important; color: white !important; padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 11px; font-weight: bold; min-width: 70px; height: 28px; margin: 3px; box-shadow: 0 2px 4px rgba(220,53,69,0.3);';

                    // Additional force methods
                    leaveBtn.style.backgroundColor = '#dc3545';
                    leaveBtn.style.background = '#dc3545';
                    leaveBtn.style.setProperty('background-color', '#dc3545', 'important');
                    leaveBtn.style.setProperty('background', '#dc3545', 'important');

                    // Force via attribute
                    leaveBtn.setAttribute('style', leaveBtn.getAttribute('style') + '; background: #dc3545 !important;');

                    leaveBtn.onclick = function() {
                        console.log('🚪 Leave room clicked for {username}');

                        // Thay đổi màu khi click để confirm
                        this.style.setProperty('background-color', '#28a745', 'important');
                        this.textContent = 'Đang thoát...';

                        // Test button functionality với alert
                        try {
                            console.log('🚪 Leave Room button clicked for {username}');
                            alert('🚪 Leave Room button hoạt động! Username: {username}');

                            // Log để debug
                            console.log('CSHARP_COMMAND:LeaveRoom:{username}');
                        } catch (e) {
                            console.error('Error in LeaveRoom button:', e);
                            alert('❌ Lỗi: ' + e.message);
                        }

                        // Reset button sau 3 giây
                        setTimeout(() => {
                            this.style.setProperty('background-color', '#dc3545', 'important');
                            this.textContent = 'Thoát bàn';
                        }, 3000);
                    };

                    // Thêm hover effects với force
                    getKeyBtn.onmouseover = function() {
                        if (!isGetKeyRunning) {
                            this.style.setProperty('background', '#0056b3', 'important');
                            this.style.setProperty('background-color', '#0056b3', 'important');
                        }
                    };
                    getKeyBtn.onmouseout = function() {
                        if (!isGetKeyRunning) {
                            this.style.setProperty('background', '#007bff', 'important');
                            this.style.setProperty('background-color', '#007bff', 'important');
                        }
                    };

                    leaveBtn.onmouseover = function() {
                        this.style.setProperty('background', '#c82333', 'important');
                        this.style.setProperty('background-color', '#c82333', 'important');
                    };
                    leaveBtn.onmouseout = function() {
                        this.style.setProperty('background', '#dc3545', 'important');
                        this.style.setProperty('background-color', '#dc3545', 'important');
                    };

                    // Thêm vào container
                    container.appendChild(getKeyBtn);
                    container.appendChild(leaveBtn);

                    // Thêm vào body
                    document.body.appendChild(container);

                    console.log('✅ Force injected simple profile buttons');
                    return true;
                ";

                bool success = (bool)driver.ExecuteScript(forceScript);
                _uiManager.AppendLog($"🚀 Force inject simple buttons cho {username}: {(success ? "thành công" : "thất bại")}", UIManager.LogLevel.Info, username);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi force inject cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Inject script khi trang reload (F5)
        /// </summary>
        public void SetupPageReloadHandler(ChromeDriver driver, string username)
        {
            try
            {
                // Tạo script để detect page reload và tự động inject lại
                string reloadHandlerScript = @"
(function() {
    // Lưu trạng thái vào sessionStorage
    if (!sessionStorage.getItem('profileButtonsSetup')) {
        sessionStorage.setItem('profileButtonsSetup', 'true');

        // Setup MutationObserver để detect khi DOM thay đổi
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Kiểm tra nếu buttons chưa có thì tạo lại
                    setTimeout(() => {
                        if (!document.querySelector('.button-container') && window.profileButtons) {
                            window.profileButtons.reinit();
                        }
                    }, 1000);
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Setup event listener cho beforeunload
        window.addEventListener('beforeunload', function() {
            sessionStorage.removeItem('profileButtonsSetup');
        });
    }
})();
";

                driver.ExecuteScript(reloadHandlerScript);
                _uiManager.AppendLog($"✅ Đã setup reload handler cho {username}", UIManager.LogLevel.Debug, username);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi setup reload handler cho {username}: {ex.Message}", UIManager.LogLevel.Warning, username);
            }
        }
    }
}
