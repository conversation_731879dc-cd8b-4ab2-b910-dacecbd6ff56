{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"AutoGameBai/1.0.0": {"dependencies": {"HIC.System.Windows.Forms.DataVisualization": "1.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.Configuration.Json": "9.0.4", "Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.NET.ILLink.Tasks": "8.0.12", "Newtonsoft.Json": "13.0.3", "OpenCvSharp4": "4.11.0.20250507", "OpenCvSharp4.runtime.win": "4.11.0.20250507", "OpenCvSharp4.Extensions": "4.11.0.20250507", "Selenium.WebDriver": "4.25.0", "Selenium.WebDriver.ChromeDriver": "129.0.6668.7000", "Serilog": "4.2.0", "Serilog.Sinks.File": "7.0.0", "SocketIOClient": "3.1.2", "System.Data.SQLite": "1.0.119", "System.Management": "9.0.4", "System.Net.Http.Json": "9.0.4", "System.Text.Json": "9.0.4", "WebDriverManager": "2.17.5", "WebSocketSharp-netstandard": "1.0.1", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "8.0.12", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "8.0.12"}, "runtime": {"AutoGameBai.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.12": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1224.60305"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "8.0.1224.60305"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/8.0.12": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60306"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60306"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.1.0.0", "fileVersion": "8.0.1224.60306"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60306"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60306"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60306"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60306"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60306"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60306"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60306"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60306"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60305"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22621.3233"}, "PenImc_cor3.dll": {"fileVersion": "8.0.1224.60305"}, "PresentationNative_cor3.dll": {"fileVersion": "8.0.24.36504"}, "vcruntime140_cor3.dll": {"fileVersion": "14.42.34430.0"}, "wpfgfx_cor3.dll": {"fileVersion": "8.0.1224.60305"}}}, "AngleSharp/1.1.2": {"runtime": {"lib/net8.0/AngleSharp.dll": {"assemblyVersion": "1.1.2.0", "fileVersion": "1.1.2.0"}}}, "Azure.Core/1.6.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.0.0"}, "runtime": {"lib/netstandard2.0/Azure.Core.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.600.20.52802"}}}, "Azure.Identity/1.3.0": {"dependencies": {"Azure.Core": "1.6.0", "Microsoft.Identity.Client": "4.22.0", "Microsoft.Identity.Client.Extensions.Msal": "2.16.5", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.300.20.56202"}}}, "EntityFramework/6.4.4": {"dependencies": {"System.CodeDom": "9.0.4", "System.Configuration.ConfigurationManager": "4.7.0", "System.Data.SqlClient": "4.8.1"}, "runtime": {"lib/netstandard2.1/EntityFramework.SqlServer.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.400.420.21404"}, "lib/netstandard2.1/EntityFramework.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.400.420.21404"}}}, "HIC.System.Windows.Forms.DataVisualization/1.0.1": {"dependencies": {"Microsoft.Data.SqlClient": "3.0.0"}, "runtime": {"lib/net5.0-windows7.0/System.Windows.Forms.DataVisualization.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.19.46214"}}}, "Microsoft.Data.SqlClient/3.0.0": {"dependencies": {"Azure.Identity": "1.3.0", "Microsoft.Data.SqlClient.SNI.runtime": "3.0.0", "Microsoft.Identity.Client": "4.22.0", "Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Runtime.Caching": "4.7.0"}, "runtime": {"runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}}, "Microsoft.Data.SqlClient.SNI.runtime/3.0.0": {"native": {"runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"fileVersion": "3.0.0.0"}}}, "Microsoft.Extensions.Configuration/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "System.Text.Json": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileSystemGlobbing": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Primitives/9.0.4": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Identity.Client/4.22.0": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.22.0.0", "fileVersion": "4.22.0.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.16.5": {"dependencies": {"Microsoft.Identity.Client": "4.22.0", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.16.5.0", "fileVersion": "2.16.5.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Logging/6.8.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.8.0", "System.IdentityModel.Tokens.Jwt": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Tokens/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.NET.ILLink.Tasks/8.0.12": {}, "Microsoft.Win32.SystemEvents/8.0.0": {}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "OpenCvSharp4/4.11.0.20250507": {"dependencies": {"System.Memory": "4.6.3"}, "runtime": {"lib/net6.0/OpenCvSharp.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "OpenCvSharp4.Extensions/4.11.0.20250507": {"dependencies": {"OpenCvSharp4": "4.11.0.20250507", "System.Drawing.Common": "8.0.11"}, "runtime": {"lib/net6.0/OpenCvSharp.Extensions.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "OpenCvSharp4.runtime.win/4.11.0.20250507": {"native": {"runtimes/win-x64/native/OpenCvSharpExtern.dll": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/opencv_videoio_ffmpeg4110_64.dll": {"fileVersion": "2024.12.0.0"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"native": {"runtimes/win-x64/native/sni.dll": {"fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "Selenium.WebDriver/4.25.0": {"runtime": {"lib/net8.0/WebDriver.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Selenium.WebDriver.ChromeDriver/129.0.6668.7000": {}, "Serilog/4.2.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/7.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpZipLib/1.4.2": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SocketIO.Core/3.1.2": {"runtime": {"lib/netstandard2.0/SocketIO.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SocketIO.Serializer.Core/3.1.2": {"dependencies": {"SocketIO.Core": "3.1.2"}, "runtime": {"lib/netstandard2.0/SocketIO.Serializer.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SocketIO.Serializer.SystemTextJson/3.1.2": {"dependencies": {"SocketIO.Core": "3.1.2", "SocketIO.Serializer.Core": "3.1.2"}, "runtime": {"lib/netstandard2.0/SocketIO.Serializer.SystemTextJson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SocketIOClient/3.1.2": {"dependencies": {"SocketIO.Serializer.Core": "3.1.2", "SocketIO.Serializer.SystemTextJson": "3.1.2"}, "runtime": {"lib/net6.0/SocketIOClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "native": {"runtimes/win-x64/native/SQLite.Interop.dll": {"fileVersion": "*********"}}}, "System.CodeDom/9.0.4": {"runtime": {"lib/net8.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Configuration.ConfigurationManager/4.7.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.7.0", "System.Security.Permissions": "4.7.0"}}, "System.Data.SqlClient/4.8.1": {"dependencies": {"runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "4.6.1.1", "fileVersion": "4.700.20.6702"}}}, "System.Data.SQLite/1.0.119": {"dependencies": {"System.Data.SQLite.Core": "1.0.119", "System.Data.SQLite.EF6": "1.0.119"}}, "System.Data.SQLite.Core/1.0.119": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.119"}}, "System.Data.SQLite.EF6/1.0.119": {"dependencies": {"EntityFramework": "6.4.4"}, "runtime": {"lib/netstandard2.1/System.Data.SQLite.EF6.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "System.Drawing.Common/8.0.11": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "System.IO.Pipelines/9.0.4": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Management/9.0.4": {"dependencies": {"System.CodeDom": "9.0.4"}, "runtime": {"runtimes/win/lib/net8.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Memory/4.6.3": {}, "System.Net.Http.Json/9.0.4": {"dependencies": {"System.Text.Json": "9.0.4"}, "runtime": {"lib/net8.0/System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "4.7.0"}, "runtime": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.ProtectedData/4.7.0": {}, "System.Security.Permissions/4.7.0": {"dependencies": {"System.Windows.Extensions": "4.7.0"}}, "System.Text.Encodings.Web/9.0.4": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Text.Json/9.0.4": {"dependencies": {"System.IO.Pipelines": "9.0.4", "System.Text.Encodings.Web": "9.0.4"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Windows.Extensions/4.7.0": {"dependencies": {"System.Drawing.Common": "8.0.11"}}, "WebDriverManager/2.17.5": {"dependencies": {"AngleSharp": "1.1.2", "Newtonsoft.Json": "13.0.3", "SharpZipLib": "1.4.2"}, "runtime": {"lib/netstandard2.1/WebDriverManager.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "WebSocketSharp-netstandard/1.0.1": {"runtime": {"lib/netstandard2.0/websocket-sharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"AutoGameBai/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.12": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/8.0.12": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "AngleSharp/1.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-aRFpAqixbuj1Vmqy2hsWPF0PJygo1SfjvmpBvVWZv6i+/u+C/L4wDdwFIzyCGUbjqr61NsZdPNPqDE8wlmG2qA==", "path": "anglesharp/1.1.2", "hashPath": "anglesharp.1.1.2.nupkg.sha512"}, "Azure.Core/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-kI4m2NsODPOrxo0OoKjk6B3ADbdovhDQIEmI4039upjjZKRaewVLx/Uz4DfRa/NtnIRZQPUALe1yvdHWAoRt4w==", "path": "azure.core/1.6.0", "hashPath": "azure.core.1.6.0.nupkg.sha512"}, "Azure.Identity/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-l1SYfZKOFBuUFG7C2SWHmJcrQQaiXgBdVCycx4vcZQkC6efDVt7mzZ5pfJAFEJDBUq7mjRQ0RPq9ZDGdSswqMg==", "path": "azure.identity/1.3.0", "hashPath": "azure.identity.1.3.0.nupkg.sha512"}, "EntityFramework/6.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-yj1+/4tci7Panu3jKDHYizxwVm0Jvm7b7m057b5h4u8NUHGCR8WIWirBTw+8EptRffwftIWPBeIRGNKD1ewEMQ==", "path": "entityframework/6.4.4", "hashPath": "entityframework.6.4.4.nupkg.sha512"}, "HIC.System.Windows.Forms.DataVisualization/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-2i<PERSON>r<PERSON>Jov37VIGLATCcI8tAaFemn8KJW/b4686foj+CslKTSGa2NcQgvVCYF+kR4+6d9cg09X1xrcoIgnVlznsA==", "path": "hic.system.windows.forms.datavisualization/1.0.1", "hashPath": "hic.system.windows.forms.datavisualization.1.0.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K63Y4hORbBcKLWH5wnKgzyn7TOfYzevIEwIedQHBIkmkEBA9SCqgvom+XTuE+fAFGvINGkhFItaZ2dvMGdT5iw==", "path": "microsoft.bcl.asyncinterfaces/1.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.1.0.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MUauWfCLsZQQMUR/wZhec5MH6+NTPmPp9i/OsjIMmIu2ICYUGOVm1x7RTqKxq19UWxXMSG03/O0FyXQJrpDs9A==", "path": "microsoft.data.sqlclient/3.0.0", "hashPath": "microsoft.data.sqlclient.3.0.0.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-n1sNyjJgu2pYWKgw3ZPikw3NiRvG4kt7Ya5MK8u77Rgj/1bTFqO/eDF4k5W9H5GXplMZCpKkNbp5kNBICgSB0w==", "path": "microsoft.data.sqlclient.sni.runtime/3.0.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-KIVBrMbItnCJDd1RF4KEaE8jZwDJcDUJW5zXpbwQ05HNYTK1GveHxHK0B3SjgDJuR48GRACXAO+BLhL8h34S7g==", "path": "microsoft.extensions.configuration/9.0.4", "hashPath": "microsoft.extensions.configuration.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0LN/DiIKvBrkqp7gkF3qhGIeZk6/B63PthAHjQsxymJfIBcz0kbf4/p/t4lMgggVxZ+flRi5xvTwlpPOoZk8fg==", "path": "microsoft.extensions.configuration.abstractions/9.0.4", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cdrjcl9RIcwt3ECbnpP0Gt1+pkjdW90mq5yFYy8D9qRj2NqFFcv3yDp141iEamsd9E218sGxK8WHaIOcrqgDJg==", "path": "microsoft.extensions.configuration.binder/9.0.4", "hashPath": "microsoft.extensions.configuration.binder.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UY864WQ3AS2Fkc8fYLombWnjrXwYt+BEHHps0hY4sxlgqaVW06AxbpgRZjfYf8PyRbplJqruzZDB/nSLT+7RLQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.4", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-vVXI70CgT/dmXV3MM+n/BR2rLXEoAyoK0hQT+8MrbCMuJBiLRxnTtSrksNiASWCwOtxo/Tyy7CO8AGthbsYxnw==", "path": "microsoft.extensions.configuration.json/9.0.4", "hashPath": "microsoft.extensions.configuration.json.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-f2MTUaS2EQ3lX4325ytPAISZqgBfXmY0WvgD80ji6Z20AoDNiCESxsqo6mFRwHJD/jfVKRw9FsW6+86gNre3ug==", "path": "microsoft.extensions.dependencyinjection/9.0.4", "hashPath": "microsoft.extensions.dependencyinjection.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UI0TQPVkS78bFdjkTodmkH0Fe8lXv9LnhGFKgKrsgUJ5a5FVdFRcgjIkBVLbGgdRhxWirxH/8IXUtEyYJx6GQg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.4", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-gQN2o/KnBfVk6Bd71E2YsvO5lsqrqHmaepDGk+FB/C4aiQY9B0XKKNKfl5/TqcNOs9OEithm4opiMHAErMFyEw==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.4", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-qkQ9V7KFZdTWNThT7ke7E/Jad38s46atSs3QUYZB8f3thBTrcrousdY4Y/tyCtcH5YjsPSiByjuN+L8W/ThMQg==", "path": "microsoft.extensions.fileproviders.physical/9.0.4", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-05Lh2ItSk4mzTdDWATW9nEcSybwprN8Tz42Fs5B+jwdXUpauktdAQUI1Am4sUQi2C63E5hvQp8gXvfwfg9mQGQ==", "path": "microsoft.extensions.filesystemglobbing/9.0.4", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "path": "microsoft.extensions.primitives/9.0.4", "hashPath": "microsoft.extensions.primitives.9.0.4.nupkg.sha512"}, "Microsoft.Identity.Client/4.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-GlamU9rs8cSVIx9WSGv5QKpt66KkE+ImxNa/wNZZUJ3knt3PM98T9sOY8B7NcEfhw7NoxU2/0TSOcmnRSJQgqw==", "path": "microsoft.identity.client/4.22.0", "hashPath": "microsoft.identity.client.4.22.0.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.16.5": {"type": "package", "serviceable": true, "sha512": "sha512-VlGUZEpF8KP/GCfFI59sdE0WA0o9quqwM1YQY0dSp6jpGy5EOBkureaybLfpwCuYUUjQbLkN2p7neUIcQCfbzA==", "path": "microsoft.identity.client.extensions.msal/2.16.5", "hashPath": "microsoft.identity.client.extensions.msal.2.16.5.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+7JIww64PkMt7NWFxoe4Y/joeF7TAtA/fQ0b2GFGcagzB59sKkTt/sMZWR6aSZht5YC7SdHi3W6yM1yylRGJCQ==", "path": "microsoft.identitymodel.jsonwebtokens/6.8.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfh/p4MaN4gkmhPxwbu8IjrmoDncGfHHPh1sTnc0AcM/Oc39/fzC9doKNWvUAjzFb8LqA6lgZyblTrIsX/wDXg==", "path": "microsoft.identitymodel.logging/6.8.0", "hashPath": "microsoft.identitymodel.logging.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-OJZx5nPdiH+MEkwCkbJrTAUiO/YzLe0VSswNlDxJsJD9bhOIdXHufh650pfm59YH1DNevp3/bXzukKrG57gA1w==", "path": "microsoft.identitymodel.protocols/6.8.0", "hashPath": "microsoft.identitymodel.protocols.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-X/PiV5l3nYYsodtrNMrNQIVlDmHpjQQ5w48E+o/D5H4es2+4niEyQf3l03chvZGWNzBRhfSstaXr25/Ye4AeYw==", "path": "microsoft.identitymodel.protocols.openidconnect/6.8.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-gTqzsGcmD13HgtNePPcuVHZ/NXWmyV+InJgalW/FhWpII1D7V1k0obIseGlWMeA4G+tZfeGMfXr0klnWbMR/mQ==", "path": "microsoft.identitymodel.tokens/6.8.0", "hashPath": "microsoft.identitymodel.tokens.6.8.0.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-FV4HnQ3JI15PHnJ5PGTbz+rYvrih42oLi/7UMIshNwCwUZhTq13UzrggtXk4ygrcMcN+4jsS6hhshx2p/Zd0ig==", "path": "microsoft.net.illink.tasks/8.0.12", "hashPath": "microsoft.net.illink.tasks.8.0.12.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "OpenCvSharp4/4.11.0.20250507": {"type": "package", "serviceable": true, "sha512": "sha512-j/R+G6xGC5IV2wGRU0/GF5qG/FrP+Uxp8dmNnXFlIdiw8Gfo4mtvKcBOBfS/bn4pP/7FNHLFX/xvMtgPJeDjAA==", "path": "opencvsharp4/4.11.0.20250507", "hashPath": "opencvsharp4.4.11.0.20250507.nupkg.sha512"}, "OpenCvSharp4.Extensions/4.11.0.20250507": {"type": "package", "serviceable": true, "sha512": "sha512-xVA5xpgvI6cWBx+Zf+kL7NIVc8dpT6ScgHq3nV5tlMBpoG2aClKaLSFHtlPAE99ASfqNieaDzeOduJTe4V1aGw==", "path": "opencvsharp4.extensions/4.11.0.20250507", "hashPath": "opencvsharp4.extensions.4.11.0.20250507.nupkg.sha512"}, "OpenCvSharp4.runtime.win/4.11.0.20250507": {"type": "package", "serviceable": true, "sha512": "sha512-3PxMXyzR+pkL9UOx2PiBjZm+/iyCIu5D6OlA1sq9MH7oWrfvVnCXJlUYhIJv67F7SLUwUuGwXDIQBfdsL/54lg==", "path": "opencvsharp4.runtime.win/4.11.0.20250507", "hashPath": "opencvsharp4.runtime.win.4.11.0.20250507.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Selenium.WebDriver/4.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-0g/9I5ennfL2ZaQoXsfEPj+GcqrKlcVr1yxh3C/Eed/acNZrC38OaXLorsuHdAHiGn+QySV3iz8/hFcOXvVcoA==", "path": "selenium.webdriver/4.25.0", "hashPath": "selenium.webdriver.4.25.0.nupkg.sha512"}, "Selenium.WebDriver.ChromeDriver/129.0.6668.7000": {"type": "package", "serviceable": true, "sha512": "sha512-/Gee6rlh9rQInxDh+/sstA8pCJEHf5za/NhlqwMOmpXK74i0bFTuKRB9+Jtuk8hptOJ351JIw89ztakrsMIGeQ==", "path": "selenium.webdriver.chromedriver/129.0.6668.7000", "hashPath": "selenium.webdriver.chromedriver.129.0.6668.7000.nupkg.sha512"}, "Serilog/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gmoWVOvKgbME8TYR+gwMf7osROiWAURterc6Rt2dQyX7wtjZYpqFiA/pY6ztjGQKKV62GGCyOcmtP1UKMHgSmA==", "path": "serilog/4.2.0", "hashPath": "serilog.4.2.0.nupkg.sha512"}, "Serilog.Sinks.File/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKL7mXv7qaiNBUC71ssvn/dU0k9t0o45+qm2XgKAlSt19xF+ijjxyA3R6HmCgfKEKwfcfkwWjayuQtRueZFkYw==", "path": "serilog.sinks.file/7.0.0", "hashPath": "serilog.sinks.file.7.0.0.nupkg.sha512"}, "SharpZipLib/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "path": "sharpziplib/1.4.2", "hashPath": "sharpziplib.1.4.2.nupkg.sha512"}, "SocketIO.Core/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-tb6vZ7DYhZooRGLHTPK7/Gifbu+kHbkQRbsee0LAnI28pWc5+Br+juh4pvid3kfIrGTl5dzAfy5huLELVU4svA==", "path": "socketio.core/3.1.2", "hashPath": "socketio.core.3.1.2.nupkg.sha512"}, "SocketIO.Serializer.Core/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-/cJtU3JVzjqP/Jh3BpMMR0aNzYua6s9e3+hjZ6OnYprFHrGYyg/n9OcST1dBjmOvvgyJ3GwJlyq0NjnPETAHsA==", "path": "socketio.serializer.core/3.1.2", "hashPath": "socketio.serializer.core.3.1.2.nupkg.sha512"}, "SocketIO.Serializer.SystemTextJson/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-AA8w3Y4JldC3FaL1D4WuyG4zLPdrzjf8AQx1LCKtzlMlTLOcbEW0UGt+BhKxkUnxYAD5O5fsqRrTNCGXu6VeWA==", "path": "socketio.serializer.systemtextjson/3.1.2", "hashPath": "socketio.serializer.systemtextjson.3.1.2.nupkg.sha512"}, "SocketIOClient/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-CMmnmhvG1Qf8k6BPh3RLqc/9MNvGUlCOyptjoP8a62bowPQkHTWvN25yy+gQqx5Ry/NgzkQHBJNpmRJhZNZrVQ==", "path": "socketioclient/3.1.2", "hashPath": "socketioclient.3.1.2.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512"}, "System.CodeDom/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-aJdv7Z87PoPW8ijvGIincocooYRbH3Umtlz7OrJKf6GDInbdu1bvk5X1fFlmbl7WP+9ntoIBd7NjKz+siVFdHA==", "path": "system.codedom/9.0.4", "hashPath": "system.codedom.9.0.4.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-/anOTeSZCNNI2zDilogWrZ8pNqCmYbzGNexUnNhjW8k0sHqEZ2nHJBp147jBV3hGYswu5lINpNg1vxR7bnqvVA==", "path": "system.configuration.configurationmanager/4.7.0", "hashPath": "system.configuration.configurationmanager.4.7.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-HKLykcv6eZLbLnSMnlQ6Os4+UAmFE+AgYm92CTvJYeTOBtOYusX3qu8OoGhFrnKZax91UcLcDo5vPrqvJUTSNQ==", "path": "system.data.sqlclient/4.8.1", "hashPath": "system.data.sqlclient.4.8.1.nupkg.sha512"}, "System.Data.SQLite/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-JSOJpnBf9goMnxGTJFGCmm6AffxgtpuXNXV5YvWO8UNC2zwd12qkUe5lAbnY+2ohIkIukgIjbvR1RA/sWILv3w==", "path": "system.data.sqlite/1.0.119", "hashPath": "system.data.sqlite.1.0.119.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "path": "system.data.sqlite.core/1.0.119", "hashPath": "system.data.sqlite.core.1.0.119.nupkg.sha512"}, "System.Data.SQLite.EF6/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-BwwgCSeA80gsxdXtU7IQEBrN9kQXWQrD11hNYOJZbXBBI1C4r7hA4QhBAalO1nzijXikthGRUADIEMI3nlucLA==", "path": "system.data.sqlite.ef6/1.0.119", "hashPath": "system.data.sqlite.ef6.1.0.119.nupkg.sha512"}, "System.Drawing.Common/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-AVyutHHKrX0Mt9C9T8W3Ccat3cVauUwvN+EqnVpSQi92nwBqoQ+2ZRvGP1S+rKK+6TGXmRflSYNShVjl2mMBlw==", "path": "system.drawing.common/8.0.11", "hashPath": "system.drawing.common.8.0.11.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-5tBCjAub2Bhd5qmcd0WhR5s354e4oLYa//kOWrkX+6/7ZbDDJjMTfwLSOiZ/MMpWdE4DWPLOfTLOq/juj9CKzA==", "path": "system.identitymodel.tokens.jwt/6.8.0", "hashPath": "system.identitymodel.tokens.jwt.6.8.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-luF2Xba+lTe2GOoNQdZLe8q7K6s7nSpWZl9jIwWNMszN4/Yv0lmxk9HISgMmwdyZ83i3UhAGXaSY9o6IJBUuuA==", "path": "system.io.pipelines/9.0.4", "hashPath": "system.io.pipelines.9.0.4.nupkg.sha512"}, "System.Management/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-MfY9+SrkSvchAoFiChTkhQYCAjBaQQ9u3LwiKmho7oTbujucnxCzORlPgLhId/ChLSptsQ/1UmtPGLZaKYVdsA==", "path": "system.management/9.0.4", "hashPath": "system.management.9.0.4.nupkg.sha512"}, "System.Memory/4.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-qdcDOgnFZY40+Q9876JUHnlHu7bosOHX8XISRoH94fwk6hgaeQGSgfZd8srWRZNt5bV9ZW2TljcegDNxsf+96A==", "path": "system.memory/4.6.3", "hashPath": "system.memory.4.6.3.nupkg.sha512"}, "System.Net.Http.Json/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-t34SoSx8l/O5kv7IwYXENjQOTpVKFkH3ydZejcZCsNsymthJrdCguvHFcbP3ayeBOXdm4Bq8bhmVGCgr0RXIQg==", "path": "system.net.http.json/9.0.4", "hashPath": "system.net.http.json.9.0.4.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Permissions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "path": "system.security.permissions/4.7.0", "hashPath": "system.security.permissions.4.7.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-V+5cCPpk1S2ngekUs9nDrQLHGiWFZMg8BthADQr+Fwi59a8DdHFu26S2oi9Bfgv+d67bqmkPqctJXMEXiimXUg==", "path": "system.text.encodings.web/9.0.4", "hashPath": "system.text.encodings.web.9.0.4.nupkg.sha512"}, "System.Text.Json/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-pYtmpcO6R3Ef1XilZEHgXP2xBPVORbYEzRP7dl0IAAbN8Dm+kfwio8aCKle97rAWXOExr292MuxWYurIuwN62g==", "path": "system.text.json/9.0.4", "hashPath": "system.text.json.9.0.4.nupkg.sha512"}, "System.Windows.Extensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "path": "system.windows.extensions/4.7.0", "hashPath": "system.windows.extensions.4.7.0.nupkg.sha512"}, "WebDriverManager/2.17.5": {"type": "package", "serviceable": true, "sha512": "sha512-gxCxFF+SxBdb1/oC0WNIPZkxT/WsfKcw4rHsB266f/fJbEmvsEIn/CodTreDIdgkB1nPlRK4qw7SE0wIcKyiBQ==", "path": "webdrivermanager/2.17.5", "hashPath": "webdrivermanager.2.17.5.nupkg.sha512"}, "WebSocketSharp-netstandard/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-knoinAv31vbdxXdypnzN2MPX5hyntVLWceq6OdWRXkr9zzqp/RWbxSuLFGbkW8De1061URgNAN43BUTlgMx1CA==", "path": "websocketsharp-netstandard/1.0.1", "hashPath": "websocketsharp-netstandard.1.0.1.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}