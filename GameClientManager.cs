﻿using AutoGameBai.Gamephom;
using AutoGameBai.Gamemaubinh;
using AutoGameBai.GameTienLen;
using AutoGameBai.Models;
using AutoGameBai.Room;
using AutoGameBai.WebSocket;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.DevTools;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace AutoGameBai
{
    public class GameClientManager
    {
        private readonly List<string> _gameLogs = new();
        private readonly UIManager _uiManager;
        private readonly ProfileManager _profileManager;
        private readonly UserManager _userManager;
        private readonly DriverManager _driverManager;
        private readonly RoomStateManager _roomStateManager;
        private readonly MauBinhCardManager _mauBinhCardManager;
        private readonly PhomCardManager _phomCardManager;
        private readonly WebSocketManager _webSocketManager;
        private readonly ImageChecker _imageChecker;
        private readonly Random _random = new();
        private readonly object _lock = new();
        private readonly Form _mainForm;
        private string _selectedGame = "HitClub";
        private string _selectedCardGame = "Mậu Binh"; // Game bài đã chọn từ GameSelectionForm
        private readonly List<int> _playedCards = new List<int>(); // Store played cards
        private readonly List<int> _drawnCards = new List<int>(); // Store drawn cards
        private int _lastPlayedCard = -1; // Store the last played card

        // Tracking user states for room conditions
        private readonly Dictionary<string, bool> _usersInGetEmptyTableMode = new Dictionary<string, bool>();
        private readonly Dictionary<string, string> _usersMainUserTarget = new Dictionary<string, string>();

        // COMMENTED OUT - Legacy form temporarily disabled
        // public MauBinhSuggestionForm MauBinhSuggestionForm { get; set; }
        public PhomSuggestionForm PhomSuggestionForm { get; set; }
        public TienLenSuggestionForm TienLenSuggestionForm { get; set; }

        public event Action CardsUpdated;

        public GameClientManager(UIManager uiManager, string gameName, bool isProfileSizeLocked = true, Form mainForm = null)
        {
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            GameName = gameName ?? throw new ArgumentNullException(nameof(gameName));
            _mainForm = mainForm ?? throw new ArgumentNullException(nameof(mainForm));
            _profileManager = new ProfileManager(this, mainForm);
            _userManager = new UserManager(_uiManager);
            _driverManager = new DriverManager(_uiManager, isProfileSizeLocked);
            _roomStateManager = new RoomStateManager(_uiManager, this);

            // Set GameClient reference in DriverManager
            _driverManager.SetGameClient(this);
            _imageChecker = new ImageChecker(_uiManager);
            _mauBinhCardManager = new MauBinhCardManager(_uiManager);
            _phomCardManager = new PhomCardManager(_uiManager, this);
            var suggestionHandler = new MauBinhSuggestionHandler(_uiManager);
            _webSocketManager = new WebSocketManager(this, _uiManager, suggestionHandler, _mauBinhCardManager);
            _userManager.LoadUsersFromFile();
            RefreshUserList = null;
            RefreshGameLogs = null;

            // Subscribe to events from all game handlers
            _webSocketManager.GetMauBinhHandler().CardsUpdated += () => CardsUpdated?.Invoke();
            _webSocketManager.GetPhomHandler().CardsUpdated += () => CardsUpdated?.Invoke();
            _webSocketManager.GetTienLenHandler().CardsUpdated += () => CardsUpdated?.Invoke();
            _uiManager.AppendLog("Khởi tạo GameClientManager thành công", UIManager.LogLevel.Info);
        }

        public string GameName { get; }
        public Action RefreshUserList { get; set; }
        public Action RefreshGameLogs { get; set; }
        public string SelectedGame => _selectedGame;

        public object GetLock() => _lock;
        public DriverManager GetDriverManager() => _driverManager;

        public void SetSelectedGame(string game)
        {
            if (game != "HitClub" && game != "SunWin")
            {
                throw new ArgumentException("Game phải là 'HitClub' hoặc 'SunWin'.");
            }
            _selectedGame = game;
            _uiManager.AppendLog($"Đã chọn game: {_selectedGame}", UIManager.LogLevel.Info);
            RefreshUserList?.Invoke();
        }

        public void SetSelectedCardGame(string cardGame)
        {
            if (cardGame != "Mậu Binh" && cardGame != "Phỏm" && cardGame != "Tiến Lên")
            {
                throw new ArgumentException("Card game phải là 'Mậu Binh', 'Phỏm' hoặc 'Tiến Lên'.");
            }
            _selectedCardGame = cardGame;
            _uiManager.AppendLog($"Đã chọn card game: {_selectedCardGame}", UIManager.LogLevel.Info);
        }

        public string GetSelectedGameName() => _selectedCardGame;

        public Dictionary<string, User> GetUsers() => _userManager.GetUsers(_selectedGame);
        public Dictionary<string, int> GetUserRooms() => _userManager.GetUserRooms();
        public List<string> GetGameLogs() => _gameLogs;
        public UIManager GetUIManager() => _uiManager;
        public Random GetRandom() => _random;
        public ProfileManager GetProfileManager() => _profileManager;
        public Dictionary<string, ChromeDriver> GetDrivers() => _driverManager.GetDrivers();
        public Dictionary<string, List<User>> GetRoomPlayers() => _roomStateManager.GetRoomPlayers();
        public Dictionary<string, int> GetRoomTimes() => _roomStateManager.GetRoomTimes();
        public Dictionary<string, int> GetUserSeats() => _roomStateManager.GetUserSeats();
        public Dictionary<string, int[]> GetUserCards() => _roomStateManager.GetUserCards();
        public MauBinhCardManager GetMauBinhCardManager() => _mauBinhCardManager;
        public PhomCardManager GetPhomCardManager() => _phomCardManager;
        public WebSocketManager GetWebSocketManager() => _webSocketManager;

        public List<int> GetPlayedCards()
        {
            return _playedCards;
        }

        public List<int> GetDrawnCards()
        {
            return _drawnCards;
        }

        public List<int> GetNockCards()
        {
            // Get nock cards from Phom handler (since nock cards are Phom-specific)
            return _webSocketManager.GetPhomHandler().GetKnownCards()
                .SelectMany(kvp => kvp.Value)
                .Distinct()
                .ToList();
        }

        public int GetLastPlayedCard()
        {
            return _lastPlayedCard;
        }

        public void UpdateLastPlayedCard(int cardId)
        {
            _lastPlayedCard = cardId;
            if (cardId != -1 && !_playedCards.Contains(cardId))
            {
                _playedCards.Add(cardId);
                // Sử dụng CardUtilityPhom cho phỏm hoặc CardUtilityMaubinh cho mậu binh
                string cardString = _selectedCardGame == "Mậu Binh"
                    ? CardUtilityMaubinh.ConvertCardsToString(new[] { cardId })
                    : CardUtilityPhom.ConvertCardsToString(new[] { cardId });
                _uiManager.AppendLog($"Updated last played card: {cardString}", UIManager.LogLevel.Debug);
            }
        }

        public void UpdateDrawnCard(int cardId)
        {
            if (cardId != -1 && !_drawnCards.Contains(cardId))
            {
                _drawnCards.Add(cardId);
                // Sử dụng CardUtilityPhom cho phỏm hoặc CardUtilityMaubinh cho mậu binh
                string cardString = _selectedCardGame == "Mậu Binh"
                    ? CardUtilityMaubinh.ConvertCardsToString(new[] { cardId })
                    : CardUtilityPhom.ConvertCardsToString(new[] { cardId });
                _uiManager.AppendLog($"Updated drawn card: {cardString}", UIManager.LogLevel.Debug);
            }
        }

        /// <summary>
        /// Reset played cards khi kết thúc ván
        /// </summary>
        public void ResetPlayedCards()
        {
            int playedCount = _playedCards.Count;
            int drawnCount = _drawnCards.Count;

            _playedCards.Clear();
            _drawnCards.Clear();
            _lastPlayedCard = -1;

            _uiManager.AppendLog($"✅ Đã reset played cards ({playedCount} lá), drawn cards ({drawnCount} lá) và last played card", UIManager.LogLevel.Info);
        }

        public PhomSuggestionForm GetPhomSuggestionForm()
        {
            return PhomSuggestionForm;
        }

        public TienLenSuggestionForm GetTienLenSuggestionForm()
        {
            return TienLenSuggestionForm;
        }

        public void ReloadUsersFromFile() => _userManager.ReloadUsersFromFile();
        public void UpdateTokenFile() => _userManager.UpdateTokenFile();

        public async Task AddUser(string username, string remoteDebuggingAddress)
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(remoteDebuggingAddress))
            {
                _uiManager.AppendLog($"Username hoặc remoteDebuggingAddress không hợp lệ cho {username}", UIManager.LogLevel.Error);
                return;
            }

            try
            {
                string groupName = _selectedGame == "HitClub" ? "Hit Club" : "SunWin";
                string gameUrl = _selectedGame == "HitClub" ? "https://web.hit.club/" : "https://play.sun.win/";

                var users = _userManager.GetUsers(_selectedGame);
                if (users.ContainsKey(username))
                {
                    _uiManager.AppendLog($"User {username} đã tồn tại, bỏ qua", UIManager.LogLevel.Warning, username);
                    return;
                }

                var user = new User
                {
                    Username = username
                };

                if (_driverManager.GetDrivers().ContainsKey(username))
                {
                    _driverManager.CleanupDriver(username);
                    _uiManager.AppendLog($"Đã xóa driver cũ cho {username} trước khi thêm mới", UIManager.LogLevel.Info, username);
                }

                var driver = _driverManager.InitializeDriver(username, remoteDebuggingAddress, gameUrl, user);

                string token = null;
                int maxRetries = 2;
                for (int retry = 0; retry < maxRetries; retry++)
                {
                    try
                    {
                        string tokenKey = _selectedGame == "HitClub" ? "token" : "user_token";
                        token = ((IJavaScriptExecutor)driver).ExecuteScript($"return localStorage.getItem('{tokenKey}');") as string;
                        if (!string.IsNullOrEmpty(token))
                        {
                            _uiManager.AppendLog($"Tìm thấy token ({tokenKey}) cho {username}: {token}", UIManager.LogLevel.Info, username);
                            break;
                        }

                        token = ((IJavaScriptExecutor)driver).ExecuteScript($"return sessionStorage.getItem('{tokenKey}');") as string;
                        if (!string.IsNullOrEmpty(token))
                        {
                            _uiManager.AppendLog($"Tìm thấy token ({tokenKey}) trong sessionStorage cho {username}: {token}", UIManager.LogLevel.Info, username);
                            break;
                        }

                        _uiManager.AppendLog($"Vui lòng đăng nhập thủ công tại {gameUrl} trong trình duyệt GPM-Login cho {username}, sau đó chờ token (lần thử {retry + 1}/{maxRetries}).", UIManager.LogLevel.Info, username);

                        for (int i = 0; i < 60; i++)
                        {
                            await Task.Delay(1000);
                            try
                            {
                                string currentUrl = driver.Url;
                                _uiManager.AppendLog($"URL hiện tại cho {username}: {currentUrl}", UIManager.LogLevel.Debug, username);
                                token = ((IJavaScriptExecutor)driver).ExecuteScript($"return localStorage.getItem('{tokenKey}');") as string;
                                if (!string.IsNullOrEmpty(token))
                                {
                                    _uiManager.AppendLog($"Tìm thấy token ({tokenKey}) cho {username}: {token}", UIManager.LogLevel.Info, username);
                                    break;
                                }

                                token = ((IJavaScriptExecutor)driver).ExecuteScript($"return sessionStorage.getItem('{tokenKey}');") as string;
                                if (!string.IsNullOrEmpty(token))
                                {
                                    _uiManager.AppendLog($"Tìm thấy token ({tokenKey}) trong sessionStorage cho {username}: {token}", UIManager.LogLevel.Info, username);
                                    break;
                                }
                            }
                            catch (Exception ex)
                            {
                                _uiManager.AppendLog($"Lỗi khi kiểm tra token cho {username}: {ex.Message}", UIManager.LogLevel.Warning, username);
                            }
                        }

                        if (!string.IsNullOrEmpty(token))
                        {
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Lỗi khi kiểm tra trạng thái đăng nhập cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                    }

                    if (retry < maxRetries - 1)
                    {
                        _uiManager.AppendLog($"Thử lại kiểm tra token cho {username}...", UIManager.LogLevel.Info, username);
                        await Task.Delay(5000);
                    }
                }

                if (string.IsNullOrEmpty(token))
                {
                    _uiManager.AppendLog($"Không tìm thấy token sau {maxRetries} lần thử cho {username}, vui lòng kiểm tra lại.", UIManager.LogLevel.Error, username);
                    _driverManager.CleanupDriver(username);
                    return;
                }

                user.Token = token;
                _userManager.GetUsers(_selectedGame).Add(username, user);

                await Task.Delay(3000);
                await _webSocketManager.InitializeWebSocketAsync(username, driver);

                lock (_lock)
                {
                    _userManager.ClearUserRoom(username);
                    _roomStateManager.GetRoomPlayers().Remove(username);
                    _roomStateManager.GetUserSeats().Remove(username);
                    _roomStateManager.GetUserCards().Remove(username);
                }

                _uiManager.AppendLog($"Đã thêm user {username} thành công", UIManager.LogLevel.Info, username);
                RefreshUserList?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi thêm user {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                _driverManager.CleanupDriver(username);
            }
        }

        public async Task SendWebSocketMessageAsync(string username, string message)
        {
            if (GetDrivers().TryGetValue(username, out var driver))
            {
                try
                {
                    ((IJavaScriptExecutor)driver).ExecuteScript($"websocket.send('{message}');");
                    _uiManager.AppendLog($"Đã gửi tin nhắn WebSocket cho {username}: {message}", UIManager.LogLevel.Info);
                }
                catch (Exception ex)
                {
                    _uiManager.AppendLog($"Lỗi khi gửi tin nhắn WebSocket cho {username}: {ex.Message}", UIManager.LogLevel.Error);
                }
            }
            else
            {
                _uiManager.AppendLog($"Không tìm thấy driver cho {username}", UIManager.LogLevel.Error);
            }
            await Task.CompletedTask;
        }

        public async Task OpenGPMProfileAsync(string username)
        {
            if (string.IsNullOrEmpty(username))
            {
                _uiManager.AppendLog("Username không hợp lệ!", UIManager.LogLevel.Error);
                return;
            }

            try
            {
                var user = GetUsers().GetValueOrDefault(username);
                if (user == null)
                {
                    _uiManager.AppendLog($"Không tìm thấy user {username} trong danh sách!", UIManager.LogLevel.Error);
                    return;
                }

                string groupName = _selectedGame == "HitClub" ? "Hit Club" : "SunWin";
                string gameUrl = _selectedGame == "HitClub" ? "https://web.hit.club/" : "https://play.sun.win/";

                if (_driverManager.GetDrivers().ContainsKey(username))
                {
                    _driverManager.CleanupDriver(username);
                    _uiManager.AppendLog($"Đã xóa driver cũ cho {username} trước khi mở mới", UIManager.LogLevel.Info, username);
                }

                string remoteDebuggingAddress = await _profileManager.OpenProfile(username, null, groupName);
                if (string.IsNullOrEmpty(remoteDebuggingAddress))
                {
                    _uiManager.AppendLog($"Không thể mở profile GPM-Login cho {username}: Không lấy được remote debugging address", UIManager.LogLevel.Error, username);
                    RefreshUserList?.Invoke();
                    return;
                }

                var driver = _driverManager.InitializeDriver(username, remoteDebuggingAddress, gameUrl, user);

                string token = null;
                int maxRetries = 2;
                for (int retry = 0; retry < maxRetries; retry++)
                {
                    try
                    {
                        string tokenKey = _selectedGame == "HitClub" ? "token" : "user_token";
                        token = ((IJavaScriptExecutor)driver).ExecuteScript($"return localStorage.getItem('{tokenKey}');") as string;
                        if (!string.IsNullOrEmpty(token))
                        {
                            _uiManager.AppendLog($"Tìm thấy token ({tokenKey}) cho {username}: {token}", UIManager.LogLevel.Info, username);
                            break;
                        }

                        token = ((IJavaScriptExecutor)driver).ExecuteScript($"return sessionStorage.getItem('{tokenKey}');") as string;
                        if (!string.IsNullOrEmpty(token))
                        {
                            _uiManager.AppendLog($"Tìm thấy token ({tokenKey}) trong sessionStorage cho {username}: {token}", UIManager.LogLevel.Info, username);
                            break;
                        }

                        _uiManager.AppendLog($"Vui lòng đăng nhập thủ công tại {gameUrl} trong trình duyệt GPM-Login cho {username}, sau đó chờ token (lần thử {retry + 1}/{maxRetries}).", UIManager.LogLevel.Info, username);

                        for (int i = 0; i < 60; i++)
                        {
                            await Task.Delay(1000);
                            try
                            {
                                string currentUrl = driver.Url;
                                _uiManager.AppendLog($"URL hiện tại cho {username}: {currentUrl}", UIManager.LogLevel.Debug, username);
                                token = ((IJavaScriptExecutor)driver).ExecuteScript($"return localStorage.getItem('{tokenKey}');") as string;
                                if (!string.IsNullOrEmpty(token))
                                {
                                    _uiManager.AppendLog($"Tìm thấy token ({tokenKey}) cho {username}: {token}", UIManager.LogLevel.Info, username);
                                    break;
                                }

                                token = ((IJavaScriptExecutor)driver).ExecuteScript($"return sessionStorage.getItem('{tokenKey}');") as string;
                                if (!string.IsNullOrEmpty(token))
                                {
                                    _uiManager.AppendLog($"Tìm thấy token ({tokenKey}) trong sessionStorage cho {username}: {token}", UIManager.LogLevel.Info, username);
                                    break;
                                }
                            }
                            catch (Exception ex)
                            {
                                _uiManager.AppendLog($"Lỗi khi kiểm tra token cho {username}: {ex.Message}", UIManager.LogLevel.Warning, username);
                            }
                        }

                        if (!string.IsNullOrEmpty(token))
                        {
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Lỗi khi kiểm tra trạng thái đăng nhập cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                    }

                    if (retry < maxRetries - 1)
                    {
                        _uiManager.AppendLog($"Thử lại kiểm tra token cho {username}...", UIManager.LogLevel.Info, username);
                        await Task.Delay(5000);
                    }
                }

                if (string.IsNullOrEmpty(token))
                {
                    _uiManager.AppendLog($"Không tìm thấy token sau {maxRetries} lần thử cho {username}, vui lòng kiểm tra lại.", UIManager.LogLevel.Error, username);
                    RefreshUserList?.Invoke();
                    return;
                }

                user.Token = token;
                await Task.Delay(3000);
                await _webSocketManager.InitializeWebSocketAsync(username, _driverManager.GetDrivers()[username]);

                lock (_lock)
                {
                    _userManager.ClearUserRoom(username);
                    _roomStateManager.GetRoomPlayers().Remove(username);
                    _roomStateManager.GetUserSeats().Remove(username);
                    _roomStateManager.GetUserCards().Remove(username);
                }

                RefreshUserList?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi mở profile GPM cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                RefreshUserList?.Invoke();
            }
        }

        public bool IsInLobby(string username, ChromeDriver driver, string imagePath = null)
        {
            string defaultImagePath = _selectedGame == "HitClub" ? "img/phongcho_hitclub.png" : "img/phongcho_sunwin.png";
            string checkImagePath = imagePath ?? defaultImagePath;
            // Giảm threshold từ 0.95 xuống 0.55 để phù hợp với kích thước profile mới 700x500
            bool isPresent = _imageChecker.IsImagePresent(driver, checkImagePath, 0.55);
            if (!isPresent)
            {
                _uiManager.AppendLog($"User {username} không ở giao diện cần kiểm tra! (Hình: {checkImagePath})", UIManager.LogLevel.Error, username);
            }
            return isPresent;
        }

        public async Task<(bool Success, int Seat, int PlayerCount)> JoinRoomAsync(string username, string roomId, CancellationToken cancellationToken, TextBox txtAttemptDelay = null)
        {
            var startTime = DateTime.Now;
            try
            {
                if (_userManager.GetUserRooms().ContainsKey(username))
                {
                    _uiManager.AppendLog($"User {username} đang ở phòng, thực hiện thoát trước.", UIManager.LogLevel.Info, username);
                    await LeaveRoomAsync(username, 200);
                }

                cancellationToken.ThrowIfCancellationRequested();

                int maxRetries = 3;
                int retry = 0;
                bool success = false;
                int seat = -1;
                int playerCount = 0;
                int attemptDelay = txtAttemptDelay != null && int.TryParse(txtAttemptDelay.Text, out int d) && d >= 0 ? d : 200;

                while (!success && retry < maxRetries)
                {
                    retry++;
                    _uiManager.AppendLog($"Thử vào phòng lần {retry}/{maxRetries} cho {username}", UIManager.LogLevel.Info, username);

                    try
                    {
                        if (!_driverManager.IsDriverActive(username))
                        {
                            _uiManager.AppendLog($"Profile cho {username} đã bị đóng.", UIManager.LogLevel.Error, username);
                            return (false, -1, 0);
                        }

                        var driver = _driverManager.GetDrivers()[username];
                        var result = await _roomStateManager.JoinRoom(username, roomId, driver, cancellationToken, attemptDelay);
                        success = result.Success;
                        seat = result.Seat;
                        playerCount = result.PlayerCount;

                        if (!success)
                        {
                            _uiManager.AppendLog($"Vào phòng thất bại cho {username}, thử lại", UIManager.LogLevel.Warning, username);
                            if (retry < maxRetries)
                            {
                                await Task.Delay(attemptDelay, cancellationToken);
                            }
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        _uiManager.AppendLog($"Đã hủy vào phòng cho {username}", UIManager.LogLevel.Info, username);
                        throw;
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Lỗi khi thử vào phòng lần {retry} cho {username}: {ex.Message}", UIManager.LogLevel.Warning, username);
                        if (retry < maxRetries)
                        {
                            await Task.Delay(attemptDelay, cancellationToken);
                        }
                    }
                }

                if (success)
                {
                    _uiManager.AppendLog($"Vào phòng thành công cho {username} (sit: {seat}, số người: {playerCount})", UIManager.LogLevel.Info, username);
                    _userManager.UpdateUserRoom(username, int.Parse(roomId.Replace("K", "000").Replace("M", "000000")));
                    return (true, seat, playerCount);
                }
                else
                {
                    _uiManager.AppendLog($"Không thể vào phòng sau {maxRetries} lần thử cho {username}", UIManager.LogLevel.Warning, username);
                    return (false, -1, 0);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi vào phòng cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                throw;
            }
            finally
            {
                _uiManager.AppendLog($"Hoàn thành JoinRoomAsync cho {username}, thời gian: {(DateTime.Now - startTime).TotalMilliseconds}ms", UIManager.LogLevel.Info, username);
                RefreshUserList?.Invoke();
            }
        }

        public async Task LeaveRoomAsync(string username, int delayJoinRoom)
        {
            var startTime = DateTime.Now;
            try
            {
                if (!_driverManager.IsDriverActive(username))
                {
                    _uiManager.AppendLog($"Driver cho {username} không hoạt động, coi như đã thoát phòng", UIManager.LogLevel.Warning, username);
                    lock (_lock)
                    {
                        _roomStateManager.GetRoomPlayers().Remove(username);
                        _roomStateManager.GetRoomTimes().Remove(username);
                        _roomStateManager.GetUserSeats().Remove(username);
                        _roomStateManager.GetUserCards().Remove(username);
                    }
                    _userManager.ClearUserRoom(username);
                    return;
                }

                lock (_lock)
                {
                    if (_roomStateManager.GetUserCards().ContainsKey(username))
                    {
                        _uiManager.AppendLog($"User {username} đã nhận bài (cmd 600), bỏ qua thoát phòng để tránh crash", UIManager.LogLevel.Warning, username);
                        return;
                    }
                }

                var driver = _driverManager.GetDrivers()[username];
                // Sử dụng phương thức JavaScript mới
                await _roomStateManager.LeaveRoomWithJavaScript(username, driver);
                _userManager.ClearUserRoom(username);

                // Xóa main user nếu user này là main user
                // Note: Sẽ được xử lý bởi RoomManager khi cần thiết
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi rời phòng cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                throw;
            }
            finally
            {
                _uiManager.AppendLog($"Hoàn thành LeaveRoomAsync cho {username}, thời gian: {(DateTime.Now - startTime).TotalMilliseconds}ms", UIManager.LogLevel.Info, username);
                RefreshUserList?.Invoke();
            }
        }



        /// <summary>
        /// Kiểm tra user có đang ở chế độ Get Empty Table không
        /// </summary>
        public bool IsUserInGetEmptyTableMode(string username)
        {
            lock (_lock)
            {
                return _usersInGetEmptyTableMode.GetValueOrDefault(username, false);
            }
        }

        /// <summary>
        /// Lấy main user mà user này cần tìm để join
        /// </summary>
        public string GetMainUserForJoin(string username)
        {
            lock (_lock)
            {
                return _usersMainUserTarget.GetValueOrDefault(username, null);
            }
        }



        /// <summary>
        /// Đặt user vào chế độ Get Empty Table
        /// </summary>
        public void SetUserGetEmptyTableMode(string username, bool isActive)
        {
            lock (_lock)
            {
                if (isActive)
                {
                    _usersInGetEmptyTableMode[username] = true;
                    _usersMainUserTarget.Remove(username);
                    _uiManager.AppendLog($"✅ Đặt {username} vào chế độ Get Empty Table", UIManager.LogLevel.Debug, username);
                }
                else
                {
                    _usersInGetEmptyTableMode.Remove(username);
                    _uiManager.AppendLog($"❌ Tắt chế độ Get Empty Table cho {username}", UIManager.LogLevel.Debug, username);
                }
            }
        }

        /// <summary>
        /// Đặt main user target cho user
        /// </summary>
        public void SetUserMainUserTarget(string username, string mainUser)
        {
            lock (_lock)
            {
                if (!string.IsNullOrEmpty(mainUser))
                {
                    _usersMainUserTarget[username] = mainUser;
                    _usersInGetEmptyTableMode[username] = false;
                    _uiManager.AppendLog($"✅ Đặt {username} tìm mainUser: {mainUser}", UIManager.LogLevel.Debug, username);
                }
                else
                {
                    _usersMainUserTarget.Remove(username);
                    _uiManager.AppendLog($"❌ Xóa mainUser target cho {username}", UIManager.LogLevel.Debug, username);
                }
            }
        }

        /// <summary>
        /// Clear tất cả tracking states cho user
        /// </summary>
        public void ClearUserTrackingStates(string username)
        {
            lock (_lock)
            {
                _usersInGetEmptyTableMode.Remove(username);
                _usersMainUserTarget.Remove(username);
                _uiManager.AppendLog($"🧹 Cleared tracking states cho {username}", UIManager.LogLevel.Debug, username);
            }
        }

        public async Task JoinRoomCheckMainUserAsync(string username, string roomId, string mainUser, int maxPlayers, CancellationToken cancellationToken)
        {
            var startTime = DateTime.Now;
            try
            {
                if (!_driverManager.IsDriverActive(username))
                {
                    _uiManager.AppendLog($"Profile cho {username} đã bị đóng.", UIManager.LogLevel.Error, username);
                    throw new Exception("Profile đã bị đóng");
                }

                var driver = _driverManager.GetDrivers()[username];
                var (success, seat, playerCount) = await _roomStateManager.JoinRoom(username, roomId, driver, cancellationToken);

                if (!success)
                {
                    _uiManager.AppendLog($"Vào phòng thất bại cho {username}", UIManager.LogLevel.Warning, username);
                    throw new Exception("Không thể vào phòng");
                }

                if (_roomStateManager.GetRoomPlayers().TryGetValue(username, out var players) &&
                    players.Any(p => p?.Username == mainUser) && playerCount <= maxPlayers)
                {
                    _uiManager.AppendLog($"User {username} đã vào phòng thành công với mainUser {mainUser}", UIManager.LogLevel.Info, username);
                    _userManager.UpdateUserRoom(username, int.Parse(roomId.Replace("K", "000").Replace("M", "000000")));
                }
                else
                {
                    _uiManager.AppendLog($"Không tìm thấy mainUser {mainUser} trong phòng hoặc số người vượt quá {maxPlayers}, thoát phòng", UIManager.LogLevel.Info, username);
                    await LeaveRoomAsync(username, 200);
                    throw new Exception("Không tìm thấy mainUser hoặc phòng đầy");
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi vào phòng với mainUser cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                throw;
            }
            finally
            {
                _uiManager.AppendLog($"Hoàn thành JoinRoomCheckMainUserAsync cho {username}, thời gian: {(DateTime.Now - startTime).TotalMilliseconds}ms", UIManager.LogLevel.Info, username);
                RefreshUserList?.Invoke();
            }
        }

        public async Task InitializeAsync()
        {
            Serilog.Log.Information("InitializeAsync started.");
            await Task.CompletedTask;
        }

        public void CloseDriver(string username)
        {
            if (_driverManager.GetDrivers().ContainsKey(username))
            {
                _driverManager.CleanupDriver(username);
                _userManager.ClearUserRoom(username);
                lock (_lock)
                {
                    _roomStateManager.GetRoomPlayers().Remove(username);
                    _roomStateManager.GetRoomTimes().Remove(username);
                    _roomStateManager.GetUserSeats().Remove(username);
                    _roomStateManager.GetUserCards().Remove(username);
                }
                _uiManager.AppendLog($"Đã đóng driver và xóa trạng thái cho {username}", UIManager.LogLevel.Info, username);
                RefreshUserList?.Invoke();
            }
        }

        public async Task JumpTableAsync(TextBox numberThuban, ComboBox comboBoxRooms, TextBox txtDelaySwitchUser, TextBox txtAttemptDelay, Action<string> updateJumpTableButtonText, Action refreshUserList)
        {
            var roomManager = new RoomManager(this, _uiManager, _mainForm);
            await roomManager.JumpTableAsync(numberThuban, comboBoxRooms, txtDelaySwitchUser, txtAttemptDelay, updateJumpTableButtonText, refreshUserList);
        }

        public void SaveGameData(UIManager uiManager, Dictionary<string, List<int[]>> suggestionsPerUser, int[] opponentCards)
        {
            try
            {
                string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }
                string filePath = Path.Combine(logDirectory, $"game_history_{DateTime.Now:yyyyMMdd_HHmmss}.json");

                var gameData = new
                {
                    Team = suggestionsPerUser.ToDictionary(kv => kv.Key, kv => kv.Value.Select(cards => new
                    {
                        Cards = CardUtilityMaubinh.ConvertCardsToString(cards),
                        Chi1 = CardUtilityMaubinh.ConvertCardsToString(cards.Take(5).ToArray()),
                        Chi2 = CardUtilityMaubinh.ConvertCardsToString(cards.Skip(5).Take(5).ToArray()),
                        Chi3 = CardUtilityMaubinh.ConvertCardsToString(cards.Skip(10).Take(3).ToArray())
                    }).ToList()),
                    Opponent = new
                    {
                        Cards = CardUtilityMaubinh.ConvertCardsToString(opponentCards),
                        Chi1 = opponentCards.Length == 13 ? CardUtilityMaubinh.ConvertCardsToString(opponentCards.Take(5).ToArray()) : null,
                        Chi2 = opponentCards.Length == 13 ? CardUtilityMaubinh.ConvertCardsToString(opponentCards.Skip(5).Take(5).ToArray()) : null,
                        Chi3 = opponentCards.Length == 13 ? CardUtilityMaubinh.ConvertCardsToString(opponentCards.Skip(10).Take(3).ToArray()) : null
                    },
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
                var json = JsonConvert.SerializeObject(gameData, Formatting.Indented);
                File.WriteAllText(filePath, json);
                uiManager.AppendLog($"Đã lưu dữ liệu ván bài vào {filePath}", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                uiManager.AppendLog($"Lỗi khi lưu dữ liệu ván bài: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        public void Dispose()
        {
            try
            {
                foreach (var username in _driverManager.GetDrivers().Keys.ToList())
                {
                    _driverManager.CleanupDriver(username);
                }

                // Dispose WebSocketManager
                _webSocketManager?.Dispose();

                _mauBinhCardManager.Dispose();
                // COMMENTED OUT - Legacy form temporarily disabled
                // if (MauBinhSuggestionForm != null && !MauBinhSuggestionForm.IsDisposed)
                // {
                //     MauBinhSuggestionForm.Invoke((Action)(() =>
                //     {
                //         MauBinhSuggestionForm.Close();
                //         MauBinhSuggestionForm.Dispose();
                //     }));
                // }
                if (PhomSuggestionForm != null && !PhomSuggestionForm.IsDisposed)
                {
                    PhomSuggestionForm.Invoke((Action)(() =>
                    {
                        PhomSuggestionForm.Close();
                        PhomSuggestionForm.Dispose();
                    }));
                }
                _uiManager.AppendLog("Đã dọn dẹp tất cả driver và tài nguyên", UIManager.LogLevel.Info);

                // Additional cleanup: terminate any lingering chromedriver.exe processes
                try
                {
                    foreach (var process in System.Diagnostics.Process.GetProcessesByName("chromedriver"))
                    {
                        process.Kill();
                        process.WaitForExit(1000); // Wait briefly for process to exit
                        _uiManager.AppendLog($"Đã dừng tiến trình chromedriver.exe (PID: {process.Id})", UIManager.LogLevel.Info);
                    }
                }
                catch (Exception ex)
                {
                    _uiManager.AppendLog($"Lỗi khi dừng các tiến trình chromedriver.exe: {ex.Message}", UIManager.LogLevel.Warning);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi dọn dẹp tài nguyên: {ex.Message}", UIManager.LogLevel.Error);
            }
        }
    }
}
