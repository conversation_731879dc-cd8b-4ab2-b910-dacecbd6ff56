﻿using AutoGameBai.Room;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AutoGameBai
{
    public class UserActionHandler
    {
        private readonly GameClientManager _gameClient;
        private readonly UIManager _uiManager;
        private readonly RoomManager _roomManager;
        private readonly Dictionary<string, CancellationTokenSource> _joinRoomCts;
        private readonly List<string> _selectedUsers;
        private readonly Action _refreshUserList;
        private readonly TextBox _txtAttemptDelay;
        private readonly Button _btnJumpTable;
        private readonly Action<string> _setGetEmptyTableButtonText;
        private readonly Form _mainForm;

        public UserActionHandler(GameClientManager gameClient, UIManager uiManager, RoomManager roomManager,
            Dictionary<string, CancellationTokenSource> joinRoomCts, List<string> selectedUsers,
            Action refreshUserList, TextBox txtAttemptDelay, Button btnJumpTable, Action<string> setGetEmptyTableButtonText,
            Form mainForm)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _roomManager = roomManager ?? throw new ArgumentNullException(nameof(roomManager));
            _joinRoomCts = joinRoomCts ?? throw new ArgumentNullException(nameof(joinRoomCts));
            _selectedUsers = selectedUsers ?? throw new ArgumentNullException(nameof(selectedUsers));
            _refreshUserList = refreshUserList ?? throw new ArgumentNullException(nameof(refreshUserList));
            _txtAttemptDelay = txtAttemptDelay ?? throw new ArgumentNullException(nameof(txtAttemptDelay));
            _btnJumpTable = btnJumpTable ?? throw new ArgumentNullException(nameof(btnJumpTable));
            _setGetEmptyTableButtonText = setGetEmptyTableButtonText ?? throw new ArgumentNullException(nameof(setGetEmptyTableButtonText));
            _mainForm = mainForm ?? throw new ArgumentNullException(nameof(mainForm));
        }

        public async Task HandleCellContentClick(DataGridViewCellEventArgs e, DataGridViewRow row, ComboBox comboBoxRooms, TextBox numberThuban, ComboBox? comboBoxGame = null)
        {
            if (e.ColumnIndex < 0 || e.RowIndex < 0)
            {
                _uiManager.AppendLog("Cell index không hợp lệ", UIManager.LogLevel.Error);
                return;
            }

            string? username = row.Tag?.ToString();
            if (string.IsNullOrEmpty(username))
            {
                _uiManager.AppendLog("Không tìm thấy username trong row.Tag", UIManager.LogLevel.Error);
                return;
            }

            var columnName = row.DataGridView.Columns[e.ColumnIndex].Name;

            try
            {
                switch (columnName)
                {
                    case "WebButton":
                        bool isProfileOpen = _gameClient.GetProfileManager().IsProfileOpen(username);
                        if (isProfileOpen)
                        {
                            _uiManager.AppendLog($"Đang đóng profile cho {username}...", UIManager.LogLevel.Info, username);
                            await _gameClient.GetProfileManager().CloseProfile(username);
                            _gameClient.CloseDriver(username);
                            _uiManager.AppendLog($"Đã đóng profile cho {username}", UIManager.LogLevel.Info, username);
                        }
                        else
                        {
                            _uiManager.AppendLog($"Đang mở profile cho {username}...", UIManager.LogLevel.Info, username);
                            await _gameClient.OpenGPMProfileAsync(username);
                            _uiManager.AppendLog($"Đã mở profile cho {username}", UIManager.LogLevel.Info, username);
                        }
                        break;

                    case "NickChinhButton":
                        var user = _gameClient.GetUsers().GetValueOrDefault(username);
                        if (user == null)
                        {
                            _uiManager.AppendLog($"Không tìm thấy user {username}", UIManager.LogLevel.Error, username);
                            return;
                        }

                        bool newMainUserState = !user.IsMainUser;

                        if (newMainUserState)
                        {
                            _roomManager.MainUserWithSeatZero = username;
                            _uiManager.AppendLog($"Đã đặt {username} làm MainUser với seat 0", UIManager.LogLevel.Info, username);

                            foreach (var u in _gameClient.GetUsers().Values)
                            {
                                u.IsMainUser = false;
                            }

                            user.IsMainUser = true;
                        }
                        else
                        {
                            if (_roomManager.MainUserWithSeatZero == username)
                            {
                                _roomManager.MainUserWithSeatZero = null;
                                _uiManager.AppendLog($"Đã xóa {username} khỏi MainUser với seat 0", UIManager.LogLevel.Info, username);
                            }
                            user.IsMainUser = false;
                        }

                        _gameClient.UpdateTokenFile();
                        _uiManager.AppendLog($"Đã cập nhật trạng thái Nick Chính cho {username}: {user.IsMainUser}", UIManager.LogLevel.Info, username);
                        _refreshUserList();
                        break;



                    case "ActionButton":
                        DialogResult result = MessageBox.Show(_mainForm, $"Bạn có chắc chắn muốn xóa user {username}?", "Xác nhận", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                        if (result == DialogResult.Yes)
                        {
                            if (_gameClient.GetUserRooms().ContainsKey(username))
                            {
                                await _gameClient.LeaveRoomAsync(username, 200);
                                _uiManager.AppendLog($"Đã rời phòng cho {username} trước khi xóa", UIManager.LogLevel.Info, username);
                            }
                            if (_gameClient.GetProfileManager().IsProfileOpen(username))
                            {
                                await _gameClient.GetProfileManager().CloseProfile(username);
                                _gameClient.CloseDriver(username);
                                _uiManager.AppendLog($"Đã đóng profile cho {username} trước khi xóa", UIManager.LogLevel.Info, username);
                            }
                            _gameClient.GetUsers().Remove(username);
                            _gameClient.UpdateTokenFile();
                            _uiManager.AppendLog($"Đã xóa user {username}", UIManager.LogLevel.Info, username);
                            _refreshUserList();
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi xử lý hành động cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
            finally
            {
                _refreshUserList();
            }
        }
    }
}
