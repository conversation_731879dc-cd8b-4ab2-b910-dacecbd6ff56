using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AutoGameBai.WebSocket
{
    /// <summary>
    /// Interface cho các game-specific WebSocket handlers
    /// </summary>
    public interface IGameWebSocketHandler
    {
        /// <summary>
        /// Danh sách các cmd mà handler n<PERSON><PERSON> x<PERSON> lý
        /// </summary>
        HashSet<int> SupportedCommands { get; }

        /// <summary>
        /// Xử lý message với cmd cụ thể
        /// </summary>
        /// <param name="messageData">Dữ liệu message</param>
        /// <param name="username">Username của người gửi</param>
        /// <param name="cmd">Command number</param>
        void HandleCommand(JObject messageData, string username, int cmd);

        /// <summary>
        /// Reset dữ liệu game khi kết thúc ván
        /// </summary>
        void ResetGameData();

        /// <summary>
        /// Kiểm tra xem có phải team mode không
        /// </summary>
        bool IsTeamMode();

        /// <summary>
        /// Cập nhật bài của user
        /// </summary>
        void UpdateUserCards(string username, int[] cards);

        /// <summary>
        /// Events
        /// </summary>
        event Action? CardsUpdated;
        event Action? PlayCardReceived;
        event Action? GameEnded;
        event Action? NewGameStarted;
    }
}
