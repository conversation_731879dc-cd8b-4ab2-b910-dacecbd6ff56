﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AutoGameBai
{
    public static class MessageHandler
    {
        private static readonly HashSet<string> _processedMessages = new();

        public static async Task HandleMessage(GameClientManager manager, string username, string message)
        {
            try
            {
                // Kiểm tra tin nhắn trùng lặp
                string messageKey = $"{username}:{message}";
                lock (_processedMessages)
                {
                    if (_processedMessages.Contains(messageKey))
                    {
                        return;
                    }
                    _processedMessages.Add(messageKey);
                    if (_processedMessages.Count > 1000)
                    {
                        _processedMessages.Clear();
                    }
                }

                // Kiểm tra nếu tin nhắn không phải JSON hoặc không phải mảng
                if (string.IsNullOrEmpty(message) || !message.StartsWith("["))
                {
                    manager.GetUIManager().AppendLog($"[WebSocket] {username}: Skipping non-JSON message: {message}", UIManager.LogLevel.Warning, username);
                    return;
                }

                // Phân tích tin nhắn
                var jsonArray = JArray.Parse(message);
                if (jsonArray.Count < 1)
                {
                    manager.GetUIManager().AppendLog($"[WebSocket] {username}: Skipping invalid message (empty array)", UIManager.LogLevel.Warning, username);
                    return;
                }

                int messageType = jsonArray[0].Value<int>();

                // Bỏ qua tin nhắn type 7 (MiniGame, ...)
                if (messageType == 7)
                {
                    return;
                }

                // Chỉ xử lý type 5 (event message)
                if (messageType != 5)
                {
                    manager.GetUIManager().AppendLog($"[WebSocket] {username}: Skipping control message (type {messageType}): {message}", UIManager.LogLevel.Info, username);
                    return;
                }

                if (jsonArray.Count < 2)
                {
                    manager.GetUIManager().AppendLog($"[WebSocket] {username}: Skipping invalid message (not enough elements)", UIManager.LogLevel.Warning, username);
                    return;
                }

                var data = jsonArray[1] as JObject;
                if (data == null || !data.ContainsKey("cmd"))
                {
                    manager.GetUIManager().AppendLog($"[WebSocket] {username}: Skipping message without cmd: {message}", UIManager.LogLevel.Warning, username);
                    return;
                }

                int cmd = data["cmd"]?.Value<int>() ?? -1;

                // Bỏ qua cmd 10
                if (cmd == 10)
                {
                    return;
                }

                // Xử lý cmd 7 với t=10, status=2
                if (cmd == 7 && data.ContainsKey("t") && data["t"]?.Value<int>() == 10 && data.ContainsKey("message") && data["message"]?.ToString() == "{\"status\":2}")
                {
                    manager.GetUIManager().AppendLog($"Received cmd 7 (t:10, status:2) for {username}, updating state", UIManager.LogLevel.Info, username);
                    return;
                }

                switch (cmd)
                {
                    case 100: // Thông tin người dùng
                        if (data.ContainsKey("dn") && data.ContainsKey("As"))
                        {
                            string? displayName = data["dn"]?.ToString();
                            string? uid = data["uid"]?.ToString();
                            long gold = data["As"]?["gold"]?.Value<long>() ?? 0;

                            if (!string.IsNullOrEmpty(username) && manager.GetUsers().ContainsKey(username))
                            {
                                var user = manager.GetUsers()[username];
                                user.DisplayName = displayName ?? user.DisplayName;
                                user.Uid = uid ?? user.Uid;
                                user.Gold = gold;

                                if (manager.SelectedGame == "SunWin")
                                {
                                    user.Chip = data["As"]?["chip"]?.Value<long>() ?? 0;
                                    user.Vip = data["As"]?["vip"]?.Value<int>() ?? 0;
                                    user.Exp = data["As"]?["exp"]?.Value<long>() ?? 0;
                                    manager.GetUIManager().AppendLog($"Cập nhật thông tin user {username}: Gold={user.Gold}, Chip={user.Chip}, Vip={user.Vip}, Exp={user.Exp}", UIManager.LogLevel.Info, username);
                                }
                                else
                                {
                                    manager.GetUIManager().AppendLog($"Cập nhật thông tin user {username}: Gold={user.Gold}", UIManager.LogLevel.Info, username);
                                }
                            }
                        }
                        break;

                    case 104: // Thông tin hệ thống
                        if (data.ContainsKey("na") && data.ContainsKey("ur"))
                        {
                            int na = data["na"]?.Value<int>() ?? 0;
                            int ur = data["ur"]?.Value<int>() ?? 0;
                            manager.GetUIManager().AppendLog($"System info for {username}: na={na}, ur={ur}", UIManager.LogLevel.Info, username);
                        }
                        break;

                    case 202: // Vào phòng
                        manager.GetUIManager().AppendLog($"[WebSocket] {username}: Received cmd 202 (Join Room)", UIManager.LogLevel.Info, username);
                        // Xử lý trong WebSocketHandler.cs, không cần xử lý lại ở đây
                        break;

                    case 600: // Nhận bài
                        manager.GetUIManager().AppendLog($"[WebSocket] {username}: Received cmd 600 (Cards)", UIManager.LogLevel.Info, username);
                        // Xử lý trong WebSocketHandler.cs
                        break;

                    case 602: // Kết quả ván
                        manager.GetUIManager().AppendLog($"[WebSocket] {username}: Received cmd 602 (Game Result)", UIManager.LogLevel.Info, username);
                        // Xử lý trong WebSocketHandler.cs
                        break;

                    default:
                        manager.GetUIManager().AppendLog($"[WebSocket] {username}: Unhandled command cmd={cmd}", UIManager.LogLevel.Warning, username);
                        break;
                }
            }
            catch (Exception ex)
            {
                manager.GetUIManager().AppendLog($"Lỗi xử lý tin nhắn WebSocket cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }

            await Task.CompletedTask;
        }
    }
}