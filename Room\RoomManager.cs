﻿using AutoGameBai.UI;
using OpenQA.Selenium;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AutoGameBai.Room
{
    public class RoomManager
    {
        private readonly GameClientManager _gameClient;
        private readonly UIManager _uiManager;
        private readonly Form _mainForm;
        private CancellationTokenSource? _getEmptyTableCts;
        private CancellationTokenSource? _jumpTableCts;
        private bool _jumpTableCompleted;
        private readonly ConcurrentDictionary<string, CancellationTokenSource> _joinRoomCts;
        private readonly MainUserManager _mainUserManager;

        public RoomManager(GameClientManager gameClient, UIManager uiManager, Form mainForm)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _mainForm = mainForm ?? throw new ArgumentNullException(nameof(mainForm));
            _getEmptyTableCts = null;
            _jumpTableCts = null;
            _jumpTableCompleted = false;
            _joinRoomCts = new ConcurrentDictionary<string, CancellationTokenSource>();
            _mainUserManager = new MainUserManager(gameClient, uiManager);
        }

        public bool IsJumpTableCompleted => _jumpTableCompleted;
        public string? MainUserWithSeatZero
        {
            get => _mainUserManager.MainUserWithSeatZero;
            set => _mainUserManager.MainUserWithSeatZero = value;
        }

        public bool IsGetEmptyTableRunning => _getEmptyTableCts != null;
        public bool IsJumpTableRunning => _jumpTableCts != null;
        public bool IsJoinRoomRunning => _joinRoomCts.Any();

        private void CleanupRoomState(string username)
        {
            lock (_gameClient.GetLock())
            {
                _gameClient.GetUserRooms().Remove(username);
                _gameClient.GetRoomPlayers().Remove(username);
                _gameClient.GetRoomTimes().Remove(username);
                _gameClient.GetUserSeats().Remove(username);
                _gameClient.GetUserCards().Remove(username);
            }
            _uiManager.AppendLog($"Đã xóa trạng thái phòng cho {username}", UIManager.LogLevel.Info, username);
        }

        public void CancelGetEmptyTable()
        {
            if (_getEmptyTableCts != null)
            {
                _getEmptyTableCts.Cancel();
                _getEmptyTableCts.Dispose();
                _getEmptyTableCts = null;
                foreach (var username in _gameClient.GetDrivers().Keys)
                {
                    CleanupRoomState(username);
                }
                _uiManager.AppendLog("Đã hủy lấy bàn trống", UIManager.LogLevel.Info);
            }
        }

        public void CancelJumpTable()
        {
            if (_jumpTableCts != null)
            {
                _jumpTableCts.Cancel();
                _jumpTableCts.Dispose();
                _jumpTableCts = null;
                foreach (var username in _gameClient.GetDrivers().Keys)
                {
                    CleanupRoomState(username);
                }
                _mainUserManager.ClearSuccessfulUsers();
                _uiManager.AppendLog("Đã hủy nhảy bàn", UIManager.LogLevel.Info);
            }
        }

        public void CancelJoinRoom()
        {
            foreach (var cts in _joinRoomCts.Values)
            {
                try
                {
                    cts.Cancel();
                    cts.Dispose();
                }
                catch (ObjectDisposedException) { }
            }
            _joinRoomCts.Clear();
            _uiManager.AppendLog("Đã hủy tất cả tác vụ vào phòng cụ thể", UIManager.LogLevel.Info);
        }

        /// <summary>
        /// Get Key Room cho user cụ thể - tương tự GetEmptyTable nhưng chỉ cho 1 user
        /// </summary>
        public async Task<bool> GetKeyRoomAsync(string username, string roomId, int maxAttempts, int attemptDelay, CancellationToken cancellationToken)
        {
            try
            {
                if (!_gameClient.GetDrivers().TryGetValue(username, out var driver) || !_gameClient.GetDriverManager().IsDriverActive(username))
                {
                    _uiManager.AppendLog($"Profile cho {username} chưa được mở hoặc không hoạt động!", UIManager.LogLevel.Error, username);
                    return false;
                }

                if (!_gameClient.IsInLobby(username, driver))
                {
                    _uiManager.AppendLog($"User {username} không ở giao diện vào phòng!", UIManager.LogLevel.Error, username);
                    return false;
                }

                _uiManager.AppendLog($"🔑 Bắt đầu Get Key Room cho {username}, roomId: {roomId}, maxAttempts: {maxAttempts}", UIManager.LogLevel.Info, username);

                // Set tracking state cho Get Key Room
                _gameClient.SetUserGetKeyRoomMode(username, true);

                bool foundEmptyTable = false;
                for (int attempt = 1; attempt <= maxAttempts; attempt++)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    _uiManager.AppendLog($"🔍 Thử lần {attempt}/{maxAttempts} Get Key Room cho {username}", UIManager.LogLevel.Info, username);

                    var (success, seat, playerCount) = await _gameClient.JoinRoomAsync(username, roomId, cancellationToken);
                    if (success && seat == 0 && playerCount == 1)
                    {
                        _uiManager.AppendLog($"✅ Tìm thấy bàn trống hợp lệ cho {username} (sit: {seat}, số người: {playerCount})", UIManager.LogLevel.Info, username);
                        foundEmptyTable = true;
                        break;
                    }
                    else if (success)
                    {
                        _uiManager.AppendLog($"❌ Bàn không hợp lệ cho {username} (sit: {seat}, số người: {playerCount}), thoát và thử lại", UIManager.LogLevel.Info, username);
                        await _gameClient.LeaveRoomAsync(username, 200);
                    }

                    if (attempt < maxAttempts)
                    {
                        await Task.Delay(attemptDelay, cancellationToken);
                    }
                }

                if (foundEmptyTable)
                {
                    _uiManager.AppendLog($"🎯 Get Key Room thành công cho {username}!", UIManager.LogLevel.Info, username);
                    return true;
                }
                else
                {
                    _uiManager.AppendLog($"❌ Không tìm thấy bàn trống sau {maxAttempts} lần thử cho {username}", UIManager.LogLevel.Warning, username);
                    return false;
                }
            }
            catch (OperationCanceledException)
            {
                _uiManager.AppendLog($"🛑 Đã hủy Get Key Room cho {username}", UIManager.LogLevel.Info, username);
                return false;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi Get Key Room cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                return false;
            }
            finally
            {
                // Clear tracking state khi hoàn thành
                _gameClient.SetUserGetKeyRoomMode(username, false);
            }
        }

        public async Task GetEmptyTableAsync(TextBox numberThuban, ComboBox comboBoxRooms, TextBox txtDelaySwitchUser, TextBox txtAttemptDelay, Action<string> updateGetEmptyTableButtonText, Action? refreshUserList)
        {
            if (_getEmptyTableCts != null)
            {
                CancelGetEmptyTable();
                updateGetEmptyTableButtonText("Lấy Bàn Trống");
                return;
            }

            try
            {
                var availableUsers = _gameClient.GetDrivers().Keys.OrderBy(u => u).ToList();
                var usersCopy = new List<string>(availableUsers);

                foreach (var username in usersCopy)
                {
                    if (_gameClient.GetDrivers().ContainsKey(username))
                    {
                        _gameClient.GetUserRooms().Remove(username);
                        _uiManager.AppendLog($"Làm mới trạng thái phòng cho {username}", UIManager.LogLevel.Debug, username);
                    }
                }

                if (usersCopy.Count == 0)
                {
                    _uiManager.AppendLog("Không có user nào đang mở web để lấy bàn trống!", UIManager.LogLevel.Error);
                    MessageBoxHelper.ShowMessageBox(_mainForm, "Không có user nào đang mở web để lấy bàn trống!", "Lỗi", MessageBoxIcon.Error);
                    return;
                }

                foreach (var username in usersCopy)
                {
                    if (!_gameClient.GetDrivers().TryGetValue(username, out var driver) || !_gameClient.IsInLobby(username, driver))
                    {
                        MessageBoxHelper.ShowMessageBox(_mainForm, $"User {username} không ở giao diện vào phòng. Vui lòng kiểm tra!", "Lỗi", MessageBoxIcon.Error);
                        return;
                    }
                }

                int maxAttempts = int.TryParse(numberThuban.Text, out int n) && n > 0 ? n : 10;
                int delayJoinRoom = 0; // Giá trị cố định là 0
                int delaySwitchUser = int.TryParse(txtDelaySwitchUser.Text, out int d2) && d2 >= 0 ? d2 : 1000;
                int attemptDelay = int.TryParse(txtAttemptDelay.Text, out int d3) && d3 >= 0 ? d3 : 200;
                string roomId = comboBoxRooms.SelectedItem as string ?? "100";

                _uiManager.AppendLog($"Bắt đầu lấy bàn trống với maxAttempts: {maxAttempts}, roomId: {roomId}, delayJoinRoom: {delayJoinRoom}, delaySwitchUser: {delaySwitchUser}, attemptDelay: {attemptDelay}", UIManager.LogLevel.Info);
                _getEmptyTableCts = new CancellationTokenSource();
                updateGetEmptyTableButtonText("Dừng Lấy Bàn");

                // Set tracking state cho tất cả users trong Get Empty Table mode
                foreach (var username in usersCopy)
                {
                    _gameClient.SetUserGetEmptyTableMode(username, true);
                }

                bool seatZeroFound = false;
                int index = 0;

                while (!seatZeroFound && index < usersCopy.Count && _getEmptyTableCts != null && !_getEmptyTableCts.Token.IsCancellationRequested)
                {
                    string username = usersCopy[index];
                    if (!_gameClient.GetDrivers().TryGetValue(username, out var driver) || !_gameClient.GetDriverManager().IsDriverActive(username))
                    {
                        _uiManager.AppendLog($"Profile cho {username} đã bị đóng hoặc không hoạt động, bỏ qua", UIManager.LogLevel.Warning, username);
                        index++;
                        continue;
                    }

                    _uiManager.AppendLog($"Đang thử lấy bàn trống với {username}", UIManager.LogLevel.Info, username);
                    var startTime = DateTime.Now;

                    for (int attempt = 1; attempt <= maxAttempts && _getEmptyTableCts != null && !_getEmptyTableCts.Token.IsCancellationRequested; attempt++)
                    {
                        if (_gameClient.GetUserCards().ContainsKey(username))
                        {
                            _uiManager.AppendLog($"User {username} đã nhận bài (cmd 600), dừng lấy bàn trống", UIManager.LogLevel.Warning, username);
                            CancelGetEmptyTable();
                            updateGetEmptyTableButtonText("Lấy Bàn Trống");
                            return;
                        }

                        _uiManager.AppendLog($"Thử lần {attempt}/{maxAttempts} cho {username}", UIManager.LogLevel.Info, username);

                        try
                        {
                            // Kiểm tra driver trước khi thực hiện
                            if (!_gameClient.GetDriverManager().IsDriverActive(username))
                            {
                                _uiManager.AppendLog($"Driver cho {username} không hoạt động, bỏ qua", UIManager.LogLevel.Warning, username);
                                break;
                            }

                            (bool success, int seat, int playerCount) = await _gameClient.JoinRoomAsync(username, roomId, _getEmptyTableCts.Token, txtAttemptDelay);
                            if (success)
                            {
                                // Kiểm tra điều kiện bàn trống (sit = 0 và playerCount <= 2)
                                if (seat == 0 && playerCount <= 2)
                                {
                                    _mainUserManager.SetMainUser(username);
                                    _uiManager.AppendLog($"✅ Tìm thấy bàn trống hợp lệ cho {username} (sit: {seat}, số người: {playerCount})", UIManager.LogLevel.Info, username);
                                    _jumpTableCompleted = false;
                                    MessageBoxHelper.ShowMessageBox(_mainForm, $"Tìm thấy bàn trống! User: {username}", "Thành công", MessageBoxIcon.Information);
                                    seatZeroFound = true;
                                    updateGetEmptyTableButtonText("Lấy Bàn Trống");
                                    await Task.Delay(delayJoinRoom, _getEmptyTableCts.Token);
                                    break;
                                }
                                else
                                {
                                    // BaseWebSocketHandler sẽ tự động thoát phòng, chỉ cần chờ attemptDelay và thử lại
                                    _uiManager.AppendLog($"⏳ Bàn không phù hợp cho {username} (sit: {seat}, số người: {playerCount}), BaseWebSocketHandler sẽ tự động thoát phòng", UIManager.LogLevel.Info, username);
                                    if (attempt < maxAttempts)
                                    {
                                        await Task.Delay(attemptDelay, _getEmptyTableCts.Token);
                                    }
                                }
                            }
                            else
                            {
                                // Vào phòng thất bại, thử lại sau attemptDelay
                                _uiManager.AppendLog($"❌ Vào phòng thất bại cho {username}, thử lại sau {attemptDelay}ms", UIManager.LogLevel.Warning, username);
                                if (attempt < maxAttempts)
                                {
                                    await Task.Delay(attemptDelay, _getEmptyTableCts.Token);
                                }
                            }
                        }
                        catch (OperationCanceledException)
                        {
                            _uiManager.AppendLog($"Đã hủy lấy bàn trống cho {username}", UIManager.LogLevel.Info, username);
                            break;
                        }
                        catch (WebDriverException wex)
                        {
                            _uiManager.AppendLog($"Profile cho {username} đã bị đóng: {wex.Message}", UIManager.LogLevel.Warning, username);
                            break;
                        }
                        catch (Exception ex)
                        {
                            _uiManager.AppendLog($"Lỗi khi thử lần {attempt} cho {username}: {ex.Message}", UIManager.LogLevel.Warning, username);
                            if (attempt < maxAttempts)
                            {
                                try
                                {
                                    await Task.Delay(attemptDelay, _getEmptyTableCts.Token);
                                }
                                catch (OperationCanceledException)
                                {
                                    _uiManager.AppendLog($"Hủy chờ giữa các lần thử cho {username}", UIManager.LogLevel.Info, username);
                                    break;
                                }
                            }
                            else
                            {
                                if (_gameClient.GetUserRooms().ContainsKey(username) || _gameClient.GetRoomPlayers().ContainsKey(username))
                                {
                                    _uiManager.AppendLog($"Đạt maxAttempts, thoát phòng cho {username} trước khi dừng", UIManager.LogLevel.Info, username);
                                    await _gameClient.LeaveRoomAsync(username, delayJoinRoom);
                                }
                            }
                        }
                    }

                    var endTime = DateTime.Now;
                    _uiManager.AppendLog($"Hoàn thành xử lý cho {username}, thời gian: {(endTime - startTime).TotalMilliseconds}ms", UIManager.LogLevel.Info, username);

                    index++;
                    if (index < usersCopy.Count && _getEmptyTableCts != null && !_getEmptyTableCts.Token.IsCancellationRequested)
                    {
                        try
                        {
                            await Task.Delay(delaySwitchUser, _getEmptyTableCts.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            _uiManager.AppendLog($"Hủy chờ khi chuyển user", UIManager.LogLevel.Info);
                            break;
                        }
                    }
                    else if (!seatZeroFound)
                    {
                        _uiManager.AppendLog("Đã thử hết user mà không tìm thấy bàn trống", UIManager.LogLevel.Warning);
                        MessageBoxHelper.ShowMessageBox(_mainForm, "Đã thử hết user mà không tìm thấy bàn trống", "Thông báo", MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi lấy bàn trống: {ex.Message}", UIManager.LogLevel.Error);
                MessageBoxHelper.ShowMessageBox(_mainForm, $"Lỗi khi lấy bàn trống: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
            finally
            {
                if (_getEmptyTableCts != null)
                {
                    _getEmptyTableCts.Cancel();
                    _getEmptyTableCts.Dispose();
                    _getEmptyTableCts = null;
                }

                // Clear tracking states cho tất cả users
                var allUsers = _gameClient.GetDrivers().Keys.ToList();
                foreach (var username in allUsers)
                {
                    _gameClient.SetUserGetEmptyTableMode(username, false);
                }

                updateGetEmptyTableButtonText("Lấy Bàn Trống");
                refreshUserList?.Invoke();
            }
        }

        public async Task JumpTableAsync(TextBox numberThuban, ComboBox comboBoxRooms, TextBox txtDelaySwitchUser, TextBox txtAttemptDelay, Action<string> updateJumpTableButtonText, Action? refreshUserList)
        {
            if (_jumpTableCts != null)
            {
                CancelJumpTable();
                updateJumpTableButtonText("Nhảy Bàn");
                return;
            }

            try
            {
                if (string.IsNullOrEmpty(_mainUserManager.MainUserWithSeatZero))
                {
                    var mainUser = _gameClient.GetUsers().Values.FirstOrDefault(u => u.IsMainUser);
                    if (mainUser != null)
                    {
                        _mainUserManager.MainUserWithSeatZero = mainUser.Username;
                        _uiManager.AppendLog($"Đã tìm thấy và đặt {mainUser.Username} làm MainUser với seat 0", UIManager.LogLevel.Info);
                    }
                    else
                    {
                        MessageBoxHelper.ShowMessageBox(_mainForm, "Không tìm thấy user chính (Main User)! Vui lòng chọn một user làm Nick Chính trước.", "Lỗi", MessageBoxIcon.Error);
                        return;
                    }
                }

                if (!_gameClient.GetDrivers().TryGetValue(_mainUserManager.MainUserWithSeatZero, out var mainDriver) || !_gameClient.GetDriverManager().IsDriverActive(_mainUserManager.MainUserWithSeatZero))
                {
                    MessageBoxHelper.ShowMessageBox(_mainForm, $"Profile của user chính {_mainUserManager.MainUserWithSeatZero} chưa được mở hoặc không hoạt động! Vui lòng mở profile trước.", "Lỗi", MessageBoxIcon.Error);
                    return;
                }

                var openWebUsers = _gameClient.GetDrivers().Keys
                    .Where(u => u != _mainUserManager.MainUserWithSeatZero && !_gameClient.GetUserRooms().ContainsKey(u))
                    .OrderBy(u => u)
                    .ToList();

                foreach (var username in openWebUsers)
                {
                    if (!_gameClient.GetDrivers().TryGetValue(username, out var driver) || !_gameClient.IsInLobby(username, driver))
                    {
                        MessageBoxHelper.ShowMessageBox(_mainForm, $"User {username} không ở giao diện vào phòng. Vui lòng kiểm tra!", "Lỗi", MessageBoxIcon.Error);
                        return;
                    }
                }

                if (openWebUsers.Count < 1)
                {
                    _uiManager.AppendLog("Cần ít nhất 1 user đang mở web để nhảy bàn!", UIManager.LogLevel.Error);
                    MessageBoxHelper.ShowMessageBox(_mainForm, "Cần ít nhất 1 user đang mở web để nhảy bàn!", "Lỗi", MessageBoxIcon.Error);
                    return;
                }

                int maxAttempts = int.TryParse(numberThuban.Text, out int n) && n > 0 ? n : 10;
                string roomId = comboBoxRooms.SelectedItem as string ?? "100";
                int delayJoinRoom = 0; // Giá trị cố định là 0
                int delaySwitchUser = int.TryParse(txtDelaySwitchUser.Text, out int d1) && d1 >= 0 ? d1 : 1000;
                int attemptDelay = int.TryParse(txtAttemptDelay.Text, out int d) && d >= 0 ? d : 200;

                _uiManager.AppendLog($"Bắt đầu nhảy bàn với Main User: {_mainUserManager.MainUserWithSeatZero}, roomId: {roomId}, maxAttempts: {maxAttempts}, delayJoinRoom: {delayJoinRoom}, delaySwitchUser: {delaySwitchUser}, attemptDelay: {attemptDelay}", UIManager.LogLevel.Info);
                _jumpTableCts = new CancellationTokenSource();
                updateJumpTableButtonText("Dừng Nhảy Bàn");

                // Set tracking state cho tất cả users tìm main user
                foreach (var username in openWebUsers)
                {
                    _gameClient.SetUserMainUserTarget(username, _mainUserManager.MainUserWithSeatZero);
                }

                var tasks = new List<Task>();

                foreach (var user in openWebUsers)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        if (_gameClient.GetUserCards().ContainsKey(user))
                        {
                            _uiManager.AppendLog($"User {user} đã nhận bài (cmd 600), dừng nhảy bàn", UIManager.LogLevel.Warning, user);
                            return;
                        }

                        if (!_gameClient.GetDrivers().TryGetValue(user, out var driver) || !_gameClient.GetDriverManager().IsDriverActive(user))
                        {
                            _uiManager.AppendLog($"Profile cho {user} đã bị đóng hoặc không hoạt động, bỏ qua", UIManager.LogLevel.Warning, user);
                            return;
                        }

                        bool userFoundMainUser = false;
                        for (int attempt = 1; attempt <= maxAttempts && !userFoundMainUser && !_jumpTableCts.Token.IsCancellationRequested; attempt++)
                        {
                            if (_mainUserManager.GetSuccessfulUserCount() >= 2)
                                break;

                            _uiManager.AppendLog($"Thử lần {attempt}/{maxAttempts} nhảy bàn cho {user}", UIManager.LogLevel.Info, user);

                            try
                            {
                                await _gameClient.JoinRoomCheckMainUserAsync(user, roomId, _mainUserManager.MainUserWithSeatZero, 3, _jumpTableCts.Token);
                                if (_gameClient.GetRoomPlayers().TryGetValue(user, out var players) &&
                                    players.Any(p => p?.Username == _mainUserManager.MainUserWithSeatZero))
                                {
                                    userFoundMainUser = true;
                                    _mainUserManager.HandleUserFoundMainUser(user);
                                    _uiManager.AppendLog($"✅ User {user} đã vào bàn thành công với {_mainUserManager.MainUserWithSeatZero}, foundCount = {_mainUserManager.GetSuccessfulUserCount()}", UIManager.LogLevel.Info, user);
                                }
                                else
                                {
                                    // BaseWebSocketHandler sẽ tự động thoát phòng, chỉ cần chờ attemptDelay và thử lại
                                    _uiManager.AppendLog($"⏳ Không tìm thấy mainUser {_mainUserManager.MainUserWithSeatZero} trong phòng của {user}, BaseWebSocketHandler sẽ tự động thoát phòng", UIManager.LogLevel.Info, user);
                                    if (attempt < maxAttempts)
                                    {
                                        await Task.Delay(attemptDelay, _jumpTableCts.Token);
                                    }
                                }
                            }
                            catch (OperationCanceledException)
                            {
                                _uiManager.AppendLog($"Đã hủy nhảy bàn cho {user}", UIManager.LogLevel.Info, user);
                                break;
                            }
                            catch (WebDriverException wex)
                            {
                                _uiManager.AppendLog($"Profile đã bị đóng cho {user}: {wex.Message}", UIManager.LogLevel.Warning, user);
                                break;
                            }
                            catch (Exception ex)
                            {
                                _uiManager.AppendLog($"Lỗi khi nhảy bàn lần {attempt} cho {user}: {ex.Message}", UIManager.LogLevel.Warning, user);
                                if (attempt < maxAttempts)
                                {
                                    await Task.Delay(attemptDelay, _jumpTableCts.Token);
                                }
                            }
                        }

                        if (!userFoundMainUser)
                        {
                            _uiManager.AppendLog($"Không thể nhảy bàn cho {user} sau {maxAttempts} lần thử", UIManager.LogLevel.Warning, user);
                        }
                    }, _jumpTableCts.Token));
                }

                await Task.WhenAll(tasks);

                if (_mainUserManager.GetSuccessfulUserCount() >= 2)
                {
                    _jumpTableCompleted = true;
                    _uiManager.AppendLog("Nhảy bàn thành công với 2 user tìm thấy MainUser", UIManager.LogLevel.Info);
                    AutoGameBai.UI.MessageBoxHelper.ShowMessageBox(_mainForm, "Nhảy bàn thành công với 2 user tìm thấy MainUser", "Thành công", MessageBoxIcon.Information);
                }
                else
                {
                    _uiManager.AppendLog($"Nhảy bàn chưa hoàn thành, chỉ có {_mainUserManager.GetSuccessfulUserCount()} user tìm thấy mainUser {_mainUserManager.MainUserWithSeatZero}", UIManager.LogLevel.Warning);
                    AutoGameBai.UI.MessageBoxHelper.ShowMessageBox(_mainForm, $"Nhảy bàn chưa hoàn thành, chỉ có {_mainUserManager.GetSuccessfulUserCount()} user tìm thấy mainUser", "Thông báo", MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi nhảy bàn: {ex.Message}", UIManager.LogLevel.Error);
                MessageBoxHelper.ShowMessageBox(_mainForm, $"Lỗi khi nhảy bàn: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
            finally
            {
                if (_jumpTableCts != null)
                {
                    _jumpTableCts.Cancel();
                    _jumpTableCts.Dispose();
                    _jumpTableCts = null;
                }

                // Clear tracking states cho tất cả users
                var allUsers = _gameClient.GetDrivers().Keys.ToList();
                foreach (var username in allUsers)
                {
                    _gameClient.SetUserMainUserTarget(username, null);
                }

                updateJumpTableButtonText("Nhảy Bàn");
                refreshUserList?.Invoke();
            }
        }
    }
}
