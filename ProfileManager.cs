﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AutoGameBai
{
    public class ProfileManager : IDisposable
    {
        private readonly Dictionary<string, string> _profileIds = new();
        private readonly Dictionary<string, bool> _profileStatusCache = new();
        private readonly GameClientManager _gameClient;
        private string? _apiUrl;
        private readonly HttpClient _httpClient;
        private readonly object _lock = new object();
        private readonly Dictionary<string, int> _groupNameToIdMap = new();
        private readonly Form? _mainForm;
        private bool _groupMappingsLoaded = false;

        public ProfileManager(GameClientManager gameClient, Form? mainForm = null)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _mainForm = mainForm;
            _httpClient = new HttpClient { Timeout = TimeSpan.FromSeconds(30) };
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        public void Dispose()
        {
            _httpClient.Dispose();
        }

        public void SetApiUrl(string apiUrl)
        {
            if (string.IsNullOrEmpty(apiUrl)) throw new ArgumentException("API URL không được để trống.", nameof(apiUrl));
            _apiUrl = apiUrl;
            _profileStatusCache.Clear();
            _groupMappingsLoaded = false;
        }

        public string? GetApiUrl() => _apiUrl;

        public bool IsGPMLoginRunning()
        {
            if (string.IsNullOrEmpty(_apiUrl))
            {
                _gameClient.GetUIManager().AppendLog("API URL chưa được thiết lập, coi như GPM-Login không chạy.", UIManager.LogLevel.Warning);
                return false;
            }

            try
            {
                var response = _httpClient.GetAsync(_apiUrl + "/api/v3/profiles").Result;
                bool isRunning = response.IsSuccessStatusCode;
                _gameClient.GetUIManager().AppendLog($"Kiểm tra GPM-Login tại {_apiUrl}: {(isRunning ? "Đang chạy" : "Không chạy")}", UIManager.LogLevel.Info);
                return isRunning;
            }
            catch (Exception ex)
            {
                _gameClient.GetUIManager().AppendLog($"Lỗi khi kiểm tra GPM-Login tại {_apiUrl}: {ex.Message}", UIManager.LogLevel.Error);
                return false;
            }
        }

        private bool EnsureApiUrl(bool showMessageBox = true)
        {
            if (string.IsNullOrEmpty(_apiUrl))
            {
                if (showMessageBox)
                {
                    LogAndShowError("API URL chưa được thiết lập.", string.Empty);
                }
                else
                {
                    _gameClient.GetUIManager().AppendLog("API URL chưa được thiết lập.", UIManager.LogLevel.Error);
                }
                return false;
            }
            return true;
        }

        private void LogAndShowError(string message, string username, Exception? ex = null)
        {
            _gameClient.GetUIManager().AppendLog(message, UIManager.LogLevel.Error, username);
            if (_mainForm is { Visible: true })
            {
                if (_mainForm.InvokeRequired)
                {
                    _mainForm.Invoke(new Action(() =>
                    {
                        MessageBox.Show(_mainForm, message, "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }));
                }
                else
                {
                    MessageBox.Show(_mainForm, message, "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            if (ex != null)
            {
                Log.Error(ex, message);
            }
        }

        private bool IsJson(string? content)
        {
            if (string.IsNullOrEmpty(content)) return false;
            try
            {
                JsonConvert.DeserializeObject(content);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<(bool Success, List<Dictionary<string, object>> Data)> ParseJsonResponseAsync(HttpResponseMessage response, string username)
        {
            var content = await response.Content.ReadAsStringAsync();
            if (!IsJson(content))
            {
                LogAndShowError($"Phản hồi API không phải JSON: {content}", username);
                return (false, new List<Dictionary<string, object>>());
            }

            var result = JsonConvert.DeserializeObject<Dictionary<string, object>>(content);
            if (result == null ||
                !result.TryGetValue("success", out var success) || success is not bool isSuccess || !isSuccess ||
                !result.TryGetValue("data", out var data) || data is not JArray jArray)
            {
                LogAndShowError($"Phản hồi API không hợp lệ: {content}", username);
                return (false, new List<Dictionary<string, object>>());
            }

            return (true, jArray.ToObject<List<Dictionary<string, object>>>() ?? new List<Dictionary<string, object>>());
        }

        private async Task<HttpResponseMessage> RetryHttpRequestAsync(Func<Task<HttpResponseMessage>> requestFunc, string username, int maxRetries = 3, int delayMs = 1000)
        {
            for (int retry = 0; retry < maxRetries; retry++)
            {
                var response = await requestFunc();
                if (response.IsSuccessStatusCode)
                    return response;
                var errorContent = await response.Content.ReadAsStringAsync();
                _gameClient.GetUIManager().AppendLog($"Lỗi lần thử {retry + 1}/{maxRetries} cho {username}: {response.StatusCode} - {errorContent}", UIManager.LogLevel.Error, username);
                if (retry < maxRetries - 1)
                    await Task.Delay(delayMs);
            }
            throw new HttpRequestException($"Không thể thực hiện yêu cầu sau {maxRetries} lần thử");
        }

        public async Task LoadGroupMappingsAsync()
        {
            if (_groupMappingsLoaded || !EnsureApiUrl(false)) return;

            try
            {
                var response = await _httpClient.GetAsync(_apiUrl + "/api/v3/groups");
                if (!response.IsSuccessStatusCode)
                {
                    _gameClient.GetUIManager().AppendLog($"Lỗi lấy danh sách nhóm: {response.StatusCode}", UIManager.LogLevel.Error);
                    return;
                }

                var content = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<Dictionary<string, object>>(content);
                if (result == null ||
                    !result.TryGetValue("success", out var success) || success is not bool isSuccess || !isSuccess ||
                    !result.TryGetValue("data", out var data) || data is not JArray jArray)
                {
                    _gameClient.GetUIManager().AppendLog($"Phản hồi API không hợp lệ: {content}", UIManager.LogLevel.Error);
                    return;
                }

                foreach (var group in jArray.ToObject<List<Dictionary<string, object>>>())
                {
                    if (group.TryGetValue("id", out var id) && group.TryGetValue("name", out var name) &&
                        int.TryParse(id?.ToString(), out int groupId) && name?.ToString() is string groupName)
                    {
                        _groupNameToIdMap[groupName] = groupId;
                    }
                }
                _gameClient.GetUIManager().AppendLog($"Đã tải ánh xạ nhóm: {JsonConvert.SerializeObject(_groupNameToIdMap)}", UIManager.LogLevel.Info);
                _groupMappingsLoaded = true;
            }
            catch (Exception ex)
            {
                _gameClient.GetUIManager().AppendLog($"Lỗi khi tải danh sách nhóm: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        public async Task<string?> OpenProfile(string username, string? proxy, string groupName, string browserVersion = "129.0.6533.73")
        {
            if (!EnsureApiUrl() || string.IsNullOrEmpty(username)) return null;

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                _gameClient.GetUIManager().AppendLog($"Bắt đầu mở profile cho {username}...", UIManager.LogLevel.Info, username);
                bool profileExists = await CheckProfileExists(username);
                string? remoteDebuggingAddress = profileExists
                    ? await OpenExistingProfile(username)
                    : await CreateProfile(username, groupName, browserVersion);

                if (string.IsNullOrEmpty(remoteDebuggingAddress))
                {
                    LogAndShowError($"Không lấy được remote_debugging_address cho {username}", username);
                }
                else
                {
                    lock (_lock)
                    {
                        _profileStatusCache[username] = true;
                    }
                    _gameClient.GetUIManager().AppendLog($"Mở profile cho {username} thành công. Thời gian: {stopwatch.ElapsedMilliseconds}ms", UIManager.LogLevel.Info, username);
                }

                return remoteDebuggingAddress;
            }
            catch (Exception ex)
            {
                LogAndShowError($"Lỗi khi mở profile cho {username}: {ex.Message}", username, ex);
                return null;
            }
            finally
            {
                stopwatch.Stop();
                _gameClient.RefreshUserList?.Invoke();
            }
        }

        public async Task<string?> CreateProfile(string username, string groupName, string browserVersion)
        {
            if (!EnsureApiUrl()) return null;

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                if (await CheckProfileExists(username))
                {
                    _gameClient.GetUIManager().AppendLog($"Profile {username} đã tồn tại, mở lại...", UIManager.LogLevel.Info, username);
                    return await OpenExistingProfile(username);
                }

                string apiGroupName = groupName == "Hit Club" ? "HitClub" : "SunWin";
                var profileData = new
                {
                    profile_name = username,
                    group_name = apiGroupName,
                    browser_core = "chromium",
                    browser_name = "Chrome",
                    browser_version = browserVersion,
                    is_random_browser_version = false,
                    startup_urls = apiGroupName == "HitClub" ? "https://web.hit.club/" : "https://play.sun.win/",
                    is_masked_font = true,
                    is_noise_canvas = false,
                    is_noise_webgl = false,
                    is_noise_client_rect = false,
                    is_noise_audio_context = true,
                    is_random_screen = false,
                    is_masked_webgl_data = true,
                    is_masked_media_device = true,
                    is_random_os = false,
                    os = "Windows 11",
                    webrtc_mode = 2,
                    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36"
                };

                var content = new StringContent(JsonConvert.SerializeObject(profileData), Encoding.UTF8, "application/json");
                var response = await RetryHttpRequestAsync(() => SendHttpRequestAsync(HttpMethod.Post, _apiUrl + "/api/v3/profiles/create", content, username), username);

                var result = JsonConvert.DeserializeObject<Dictionary<string, object>>(await response.Content.ReadAsStringAsync());
                if (result == null || !result.TryGetValue("data", out var data) || data is not JObject dataJObject ||
                    dataJObject["id"]?.ToString() is not string profileId)
                {
                    LogAndShowError($"Không tìm thấy ID trong dữ liệu API cho {username}", username);
                    return null;
                }

                lock (_lock)
                {
                    _profileIds[username] = profileId;
                    _profileStatusCache[username] = false;
                }
                _gameClient.GetUIManager().AppendLog($"Đã tạo profile mới cho {username} với ID: {profileId}. Thời gian: {stopwatch.ElapsedMilliseconds}ms", UIManager.LogLevel.Info, username);
                return await OpenExistingProfile(username);
            }
            catch (Exception ex)
            {
                LogAndShowError($"Lỗi khi tạo profile cho {username}: {ex.Message}", username, ex);
                return null;
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        public async Task<string?> OpenExistingProfile(string username)
        {
            if (!EnsureApiUrl()) return null;

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                string profileId;
                lock (_lock)
                {
                    if (!_profileIds.TryGetValue(username, out profileId))
                    {
                        LogAndShowError($"Không tìm thấy profile_id cho {username}", username);
                        return null;
                    }
                }

                var response = await RetryHttpRequestAsync(() => SendHttpRequestAsync(HttpMethod.Get, _apiUrl + $"/api/v3/profiles/start/{profileId}", null, username), username);
                var result = JsonConvert.DeserializeObject<Dictionary<string, object>>(await response.Content.ReadAsStringAsync());
                if (result == null)
                {
                    LogAndShowError($"Phản hồi API không hợp lệ cho {username}", username);
                    return null;
                }

                if (result.TryGetValue("success", out var success) && success is bool isSuccess && !isSuccess &&
                    result.TryGetValue("message", out var message) && message?.ToString() == "ALREADY_OPEN")
                {
                    var runningResponse = await _httpClient.GetAsync(_apiUrl + "/api/v3/profiles/running");
                    if (!runningResponse.IsSuccessStatusCode)
                    {
                        LogAndShowError($"Không thể lấy danh sách profile đang chạy cho {username}", username);
                        return null;
                    }

                    var (successRunning, profiles) = await ParseJsonResponseAsync(runningResponse, username);
                    if (!successRunning) return null;

                    var profile = profiles.FirstOrDefault(p => p.TryGetValue("profile_id", out var id) && id?.ToString() == profileId);
                    if (profile?.TryGetValue("remote_debugging_address", out var address) == true && address?.ToString() is string remoteAddress)
                    {
                        lock (_lock)
                        {
                            _profileStatusCache[username] = true;
                        }
                        _gameClient.GetUIManager().AppendLog($"Lấy được remote debugging address cho {username}: {remoteAddress}", UIManager.LogLevel.Info, username);
                        return remoteAddress;
                    }
                    return null;
                }

                if (result.TryGetValue("data", out var data) && data is JObject dataJObject &&
                    dataJObject["remote_debugging_address"]?.ToString() is string remoteDebuggingAddress &&
                    !string.IsNullOrEmpty(remoteDebuggingAddress))
                {
                    lock (_lock)
                    {
                        _profileStatusCache[username] = true;
                    }
                    _gameClient.GetUIManager().AppendLog($"Đã mở profile cho {username} với remote debugging: {remoteDebuggingAddress}. Thời gian: {stopwatch.ElapsedMilliseconds}ms", UIManager.LogLevel.Info, username);
                    return remoteDebuggingAddress;
                }

                LogAndShowError($"Không tìm thấy remote_debugging_address cho {username}", username);
                return null;
            }
            catch (Exception ex)
            {
                LogAndShowError($"Lỗi khi mở profile hiện có cho {username}: {ex.Message}", username, ex);
                return null;
            }
            finally
            {
                stopwatch.Stop();
                _gameClient.RefreshUserList?.Invoke();
            }
        }

        public async Task<bool> CheckProfileExists(string username)
        {
            if (!EnsureApiUrl() || string.IsNullOrEmpty(username)) return false;

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                var response = await SendHttpRequestAsync(HttpMethod.Get, _apiUrl + "/api/v3/profiles", null, username);
                var (success, profiles) = await ParseJsonResponseAsync(response, username);
                if (!success) return false;

                string normalizedUsername = username.Trim().ToLower();
                var profile = profiles.FirstOrDefault(p =>
                    p.TryGetValue("name", out var name) && name?.ToString()?.Trim().ToLower() == normalizedUsername);

                if (profile != null && profile.TryGetValue("id", out var profileId) && profileId?.ToString() is string id)
                {
                    lock (_lock)
                    {
                        _profileIds[username] = id;
                        _profileStatusCache[username] = false;
                    }
                    _gameClient.GetUIManager().AppendLog($"Tìm thấy profile cho {username} với ID: {id}. Thời gian: {stopwatch.ElapsedMilliseconds}ms", UIManager.LogLevel.Info, username);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _gameClient.GetUIManager().AppendLog($"Lỗi khi kiểm tra profile tồn tại cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                return false;
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        public async Task CloseProfile(string username)
        {
            if (!EnsureApiUrl() || string.IsNullOrEmpty(username)) return;

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                string profileId;
                lock (_lock)
                {
                    if (!_profileIds.TryGetValue(username, out profileId))
                    {
                        _gameClient.GetUIManager().AppendLog($"Không tìm thấy profile_id cho {username}", UIManager.LogLevel.Warning, username);
                        return;
                    }
                }

                var response = await SendHttpRequestAsync(HttpMethod.Get, _apiUrl + $"/api/v3/profiles/close/{profileId}", null, username);
                lock (_lock)
                {
                    _profileIds.Remove(username);
                    _profileStatusCache[username] = false;
                }
                _gameClient.GetUIManager().AppendLog($"Đã đóng profile cho {username}. Thời gian: {stopwatch.ElapsedMilliseconds}ms", UIManager.LogLevel.Info, username);
            }
            catch (Exception ex)
            {
                LogAndShowError($"Lỗi khi đóng profile cho {username}: {ex.Message}", username, ex);
            }
            finally
            {
                stopwatch.Stop();
                _gameClient.RefreshUserList?.Invoke();
            }
        }

        public bool IsProfileOpen(string? username)
        {
            if (string.IsNullOrEmpty(username)) return false;

            lock (_lock)
            {
                if (_profileStatusCache.TryGetValue(username, out bool isOpen))
                {
                    return isOpen;
                }
            }

            bool result = IsProfileOpenInternal(username);
            lock (_lock)
            {
                _profileStatusCache[username] = result;
            }
            _gameClient.GetUIManager().AppendLog($"Cập nhật trạng thái profile cho {username}: {(result ? "Mở" : "Đóng")}", UIManager.LogLevel.Debug, username);
            return result;
        }

        private bool IsProfileOpenInternal(string username)
        {
            if (!EnsureApiUrl() || !_profileIds.ContainsKey(username)) return false;

            try
            {
                var response = _httpClient.GetAsync(_apiUrl + "/api/v3/profiles/running").Result;
                if (!response.IsSuccessStatusCode) return false;

                var content = response.Content.ReadAsStringAsync().Result;
                if (!IsJson(content)) return false;

                var result = JsonConvert.DeserializeObject<Dictionary<string, object>>(content);
                if (result == null ||
                    !result.TryGetValue("success", out var success) || success is not bool isSuccess || !isSuccess ||
                    !result.TryGetValue("data", out var data) || data is not JArray profiles)
                {
                    return false;
                }

                return profiles.Any(p => p["profile_id"]?.ToString() == _profileIds[username]);
            }
            catch (Exception ex)
            {
                LogAndShowError($"Lỗi khi kiểm tra trạng thái profile cho {username}: {ex.Message}", username, ex);
                return false;
            }
        }

        private async Task<HttpResponseMessage> SendHttpRequestAsync(HttpMethod method, string requestUri, StringContent? content, string username)
        {
            try
            {
                using var request = new HttpRequestMessage(method, requestUri);
                if (content != null)
                    request.Content = content;

                var response = await _httpClient.SendAsync(request);
                _gameClient.GetUIManager().AppendLog($"Gửi {method} yêu cầu đến {requestUri}: {(response.IsSuccessStatusCode ? "Thành công" : $"Thất bại - {response.StatusCode}")}", UIManager.LogLevel.Debug, username);
                if (!response.IsSuccessStatusCode)
                    throw new HttpRequestException($"Yêu cầu thất bại: {response.StatusCode}");
                return response;
            }
            catch (Exception ex)
            {
                LogAndShowError($"Lỗi khi gửi yêu cầu đến {requestUri}: {ex.Message}", username, ex);
                throw;
            }
        }
    }
}