@echo off
echo ========================================
echo   AutoGameBai Test Build
echo ========================================

echo.
echo Step 1: Building Release version...
dotnet build AutoGameBai.csproj --configuration Release --no-restore

if %ERRORLEVEL% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Testing the executable...
echo Checking if AutoGameBai.exe can start...
cd "bin\Release\net8.0-windows"

echo Testing exe file...
if exist "AutoGameBai.exe" (
    echo ✅ AutoGameBai.exe found
    echo File size:
    dir AutoGameBai.exe | find "AutoGameBai.exe"
    
    echo.
    echo Testing dependencies...
    if exist "AutoGameBai.dll" echo ✅ AutoGameBai.dll found
    if exist "AutoGameBai.runtimeconfig.json" echo ✅ Runtime config found
    if exist "AutoGameBai.deps.json" echo ✅ Dependencies config found
    
    echo.
    echo Attempting to start exe (will close automatically)...
    timeout /t 2 /nobreak > nul
    start /wait /min AutoGameBai.exe --version 2>nul
    
    if %ERRORLEVEL% equ 0 (
        echo ✅ Executable started successfully!
    ) else (
        echo ⚠️  Executable test returned code: %ERRORLEVEL%
        echo This may be normal if --version flag is not supported
    )
) else (
    echo ❌ AutoGameBai.exe not found!
)

cd ..\..\..

echo.
echo ✅ Test completed!
echo.
echo Release build location: bin\Release\net8.0-windows\AutoGameBai.exe
echo.
pause
