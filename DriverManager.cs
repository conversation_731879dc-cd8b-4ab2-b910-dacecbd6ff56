﻿using AutoGameBai.Models;
using AutoGameBai.Scripts;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.DevTools;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;

namespace AutoGameBai
{
    public class DriverManager
    {
        private readonly Dictionary<string, ChromeDriver> _drivers = new();
        private readonly Dictionary<string, IDevToolsSession> _devToolsSessions = new();
        private readonly UIManager _uiManager;
        private readonly JavaScriptInjector _jsInjector;
        private bool _isProfileSizeLocked;
        private readonly object _lock = new object();
        private GameClientManager? _gameClient; // Reference to GameClientManager

        public DriverManager(UIManager uiManager, bool isProfileSizeLocked)
        {
            _uiManager = uiManager;
            _jsInjector = new JavaScriptInjector(uiManager);
            _isProfileSizeLocked = isProfileSizeLocked;
        }

        /// <summary>
        /// Set GameClientManager reference (called after GameClientManager is created)
        /// </summary>
        public void SetGameClient(GameClientManager gameClient)
        {
            _gameClient = gameClient;
        }

        public Dictionary<string, ChromeDriver> GetDrivers() => _drivers;

        public bool IsProfileSizeLocked
        {
            get => _isProfileSizeLocked;
            set
            {
                _isProfileSizeLocked = value;
                UpdateDriverSizes();
            }
        }

        public bool IsDriverActive(string username)
        {
            ChromeDriver driver;
            lock (_lock)
            {
                if (!_drivers.ContainsKey(username)) return false;
                driver = _drivers[username]; // Lấy driver trong lock để tránh race condition
            }

            try
            {
                // Kiểm tra driver có còn hoạt động không
                driver.ExecuteScript("return true");
                return true;
            }
            catch (WebDriverException)
            {
                _uiManager.AppendLog($"ChromeDriver cho {username} đã bị đóng.", UIManager.LogLevel.Warning, username);
                lock (_lock)
                {
                    _drivers.Remove(username);
                    _devToolsSessions.Remove(username);
                }
                return false;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi kiểm tra driver cho {username}: {ex.Message}", UIManager.LogLevel.Warning, username);
                return false;
            }
        }

        public ChromeDriver InitializeDriver(string username, string remoteDebuggingAddress, string gameUrl, User user)
        {
            try
            {
                lock (_lock)
                {
                    // Chỉ cleanup driver hiện tại của username này, KHÔNG kill tất cả chromedriver processes
                    if (_drivers.ContainsKey(username))
                    {
                        try
                        {
                            _drivers[username].Quit();
                            _uiManager.AppendLog($"Đã đóng ChromeDriver cũ cho {username}", UIManager.LogLevel.Info, username);
                        }
                        catch (Exception ex)
                        {
                            _uiManager.AppendLog($"Lỗi khi đóng ChromeDriver cũ cho {username}: {ex.Message}", UIManager.LogLevel.Warning, username);
                        }
                        finally
                        {
                            _drivers.Remove(username);
                        }
                    }
                    if (_devToolsSessions.ContainsKey(username))
                    {
                        try
                        {
                            _devToolsSessions[username].Dispose();
                            _uiManager.AppendLog($"Đã đóng DevToolsSession cũ cho {username}", UIManager.LogLevel.Info, username);
                        }
                        catch (Exception ex)
                        {
                            _uiManager.AppendLog($"Lỗi khi đóng DevToolsSession cũ cho {username}: {ex.Message}", UIManager.LogLevel.Warning, username);
                        }
                        finally
                        {
                            _devToolsSessions.Remove(username);
                        }
                    }
                }

                // Rest of the InitializeDriver method remains unchanged
                string chromeDriverPath = AppDomain.CurrentDomain.BaseDirectory;
                string chromeDriverExePath = Path.Combine(chromeDriverPath, "chromedriver.exe");

                if (!File.Exists(chromeDriverExePath))
                {
                    string errorMsg = $"Không tìm thấy chromedriver.exe tại {chromeDriverExePath}. Vui lòng đảm bảo file tồn tại và đúng phiên bản 129.0.6533.73.";
                    _uiManager.AppendLog(errorMsg, UIManager.LogLevel.Error, username);
                    throw new FileNotFoundException(errorMsg);
                }

                var service = ChromeDriverService.CreateDefaultService(chromeDriverPath);
                service.EnableVerboseLogging = true;
                service.HideCommandPromptWindow = true;

                var chromeOptions = new ChromeOptions();
                chromeOptions.DebuggerAddress = remoteDebuggingAddress;

                string localAppData = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                string goLoginProfilePath = Path.Combine(localAppData, "GoLogin", "profiles", username);
                chromeOptions.AddArgument($"--user-data-dir={goLoginProfilePath}");
                chromeOptions.AddArgument($"--profile-directory={username}");

                var driver = new ChromeDriver(service, chromeOptions);

                lock (_lock)
                {
                    _drivers[username] = driver;
                }

                if (_isProfileSizeLocked)
                {
                    driver.Manage().Window.Size = new Size(700, 500); // Kích thước profile 700x500
                }
                else
                {
                    driver.Manage().Window.Maximize();
                }

                var port = remoteDebuggingAddress?.Split(':').LastOrDefault() ?? "N/A";
                user.Port = port;

                driver.Navigate().GoToUrl(gameUrl);
                _uiManager.AppendLog($"Đã khởi tạo ChromeDriver tại {chromeDriverExePath} cho {username} và mở URL {gameUrl}", UIManager.LogLevel.Info, username);

                // Setup WebView external handler cho JavaScript calls
                SetupWebViewExternalHandler(driver, username);

                // Đợi một chút để page load xong, sau đó set lại kích thước để đảm bảo
                Task.Delay(2000).ContinueWith(_ =>
                {
                    try
                    {
                        if (_isProfileSizeLocked && _drivers.ContainsKey(username))
                        {
                            driver.Manage().Window.Size = new Size(700, 500);
                            _uiManager.AppendLog($"Đã set lại kích thước profile {username} về 700x500 sau khi load", UIManager.LogLevel.Info, username);
                        }
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Lỗi khi set lại kích thước profile {username}: {ex.Message}", UIManager.LogLevel.Warning, username);
                    }
                });

                try
                {
                    string chromeVersion = (string)driver.ExecuteScript("return navigator.userAgent");
                    _uiManager.AppendLog($"Phiên bản Chrome: {chromeVersion}", UIManager.LogLevel.Debug, username);
                }
                catch (Exception ex)
                {
                    _uiManager.AppendLog($"Lỗi khi kiểm tra phiên bản Chrome cho {username}: {ex.Message}", UIManager.LogLevel.Warning, username);
                }

                // Inject profile buttons sau khi trang load
                Task.Run(async () =>
                {
                    try
                    {
                        // Inject C# methods trước
                        _jsInjector.InjectCSharpMethods(driver, username);

                        // Thử inject bình thường trước
                        await _jsInjector.InjectProfileButtons(driver, username);

                        // Đợi 2 giây rồi kiểm tra xem buttons đã xuất hiện chưa
                        await Task.Delay(2000);
                        bool buttonsExist = (bool)driver.ExecuteScript("return document.querySelector('.button-container') !== null");

                        if (!buttonsExist)
                        {
                            _uiManager.AppendLog($"Buttons chưa xuất hiện, thử force inject cho {username}", UIManager.LogLevel.Info, username);
                            _jsInjector.ForceInjectSimpleButtons(driver, username);

                            // Đợi thêm 2 giây rồi test
                            await Task.Delay(2000);
                            _jsInjector.TestButtons(driver, username);
                        }
                        else
                        {
                            _uiManager.AppendLog($"Buttons đã xuất hiện, test chúng cho {username}", UIManager.LogLevel.Info, username);
                            _jsInjector.TestButtons(driver, username);
                        }

                        _jsInjector.SetupPageReloadHandler(driver, username);
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Lỗi khi inject JavaScript cho {username}: {ex.Message}", UIManager.LogLevel.Warning, username);

                        // Fallback: thử force inject
                        try
                        {
                            _jsInjector.ForceInjectSimpleButtons(driver, username);
                        }
                        catch (Exception ex2)
                        {
                            _uiManager.AppendLog($"Lỗi khi force inject cho {username}: {ex2.Message}", UIManager.LogLevel.Error, username);
                        }
                    }
                });

                return driver;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi khởi tạo driver cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                throw;
            }
        }

        /// <summary>
        /// Inject lại JavaScript cho user cụ thể (dùng khi F5 hoặc reload page)
        /// </summary>
        public async Task ReinjectJavaScript(string username)
        {
            try
            {
                ChromeDriver? driver;
                lock (_lock)
                {
                    if (!_drivers.TryGetValue(username, out driver))
                    {
                        _uiManager.AppendLog($"Không tìm thấy driver cho {username}", UIManager.LogLevel.Warning, username);
                        return;
                    }
                }

                await _jsInjector.InjectProfileButtons(driver, username);
                _jsInjector.SetupPageReloadHandler(driver, username);
                _uiManager.AppendLog($"✅ Đã reinject JavaScript cho {username}", UIManager.LogLevel.Info, username);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi reinject JavaScript cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Force inject simple buttons cho user cụ thể (method public)
        /// </summary>
        public void ForceInjectButtons(string username)
        {
            try
            {
                ChromeDriver? driver;
                lock (_lock)
                {
                    if (!_drivers.TryGetValue(username, out driver))
                    {
                        _uiManager.AppendLog($"Không tìm thấy driver cho {username}", UIManager.LogLevel.Warning, username);
                        return;
                    }
                }

                _jsInjector.ForceInjectSimpleButtons(driver, username);
                _uiManager.AppendLog($"🚀 Đã force inject buttons cho {username}", UIManager.LogLevel.Info, username);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi force inject buttons cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Setup WebView external handler để JavaScript có thể gọi C# methods
        /// </summary>
        private void SetupWebViewExternalHandler(ChromeDriver driver, string username)
        {
            try
            {
                // Inject external object vào window để JavaScript có thể gọi
                string setupScript = $@"
                    window.external = {{

                        LeaveRoom: function(username) {{
                            console.log('🚪 External LeaveRoom called for: ' + username);
                            // Sử dụng console.log với pattern đặc biệt để C# có thể detect
                            console.log('CSHARP_COMMAND:LeaveRoom:' + username);
                            alert('🚪 LeaveRoom được gọi cho ' + username);
                        }},
                        GetUsername: function() {{
                            return '{username}';
                        }},
                        IsInRoom: function() {{
                            return false; // Placeholder
                        }},
                        GetRoomInfo: function() {{
                            return 'Không có thông tin';
                        }}
                    }};

                    console.log('✅ External methods setup complete for {username}');
                ";

                driver.ExecuteScript(setupScript);
                _uiManager.AppendLog($"✅ Đã setup WebView external handler cho {username}", UIManager.LogLevel.Info, username);

                // Setup console log listener để detect CSHARP_COMMAND
                SetupConsoleLogListener(driver, username);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi setup WebView external handler cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Setup console log listener để detect CSHARP_COMMAND từ JavaScript
        /// </summary>
        private async void SetupConsoleLogListener(ChromeDriver driver, string username)
        {
            try
            {
                var devTools = driver.GetDevToolsSession();
                var runtime = devTools.GetVersionSpecificDomains<OpenQA.Selenium.DevTools.V129.DevToolsSessionDomains>().Runtime;

                await runtime.Enable();

                runtime.ConsoleAPICalled += async (sender, e) =>
                {
                    try
                    {
                        if (e.Type == "log" && e.Args?.Length > 0)
                        {
                            var message = e.Args[0]?.Value?.ToString();
                            if (!string.IsNullOrEmpty(message) && message.StartsWith("CSHARP_COMMAND:"))
                            {
                                var parts = message.Split(':');
                                if (parts.Length >= 3)
                                {
                                    var command = parts[1];
                                    var targetUsername = parts[2];

                                    _uiManager.AppendLog($"🎯 Detected CSHARP_COMMAND: {command} for {targetUsername}", UIManager.LogLevel.Info, username);

                                    // Xử lý command
                                    await HandleCSharpCommand(command, targetUsername);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"❌ Lỗi khi xử lý console log cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                    }
                };

                _uiManager.AppendLog($"✅ Đã setup console log listener cho {username}", UIManager.LogLevel.Info, username);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi setup console log listener cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Xử lý CSHARP_COMMAND từ JavaScript
        /// </summary>
        private async Task HandleCSharpCommand(string command, string username)
        {
            try
            {
                switch (command)
                {


                    case "LeaveRoom":
                        _uiManager.AppendLog($"🚪 Executing LeaveRoom for {username}", UIManager.LogLevel.Info, username);

                        if (_gameClient == null)
                        {
                            _uiManager.AppendLog($"❌ GameClient chưa được khởi tạo cho {username}", UIManager.LogLevel.Error, username);
                            return;
                        }

                        await Task.Run(async () =>
                        {
                            try
                            {
                                await _gameClient.LeaveRoomAsync(username, 200);
                                _uiManager.AppendLog($"✅ LeaveRoom thành công cho {username}", UIManager.LogLevel.Info, username);
                            }
                            catch (Exception ex)
                            {
                                _uiManager.AppendLog($"❌ Lỗi trong LeaveRoom cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                            }
                        });
                        break;

                    default:
                        _uiManager.AppendLog($"⚠️ Unknown CSHARP_COMMAND: {command} for {username}", UIManager.LogLevel.Warning, username);
                        break;
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi xử lý CSHARP_COMMAND {command} cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        public void CleanupDriver(string username)
        {
            lock (_lock)
            {
                if (_drivers.ContainsKey(username))
                {
                    try
                    {
                        _drivers[username].Quit();
                        _uiManager.AppendLog($"Đã đóng ChromeDriver cho {username}", UIManager.LogLevel.Info, username);
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Lỗi khi đóng ChromeDriver cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                    }
                    finally
                    {
                        _drivers.Remove(username);
                    }
                }
                if (_devToolsSessions.ContainsKey(username))
                {
                    try
                    {
                        _devToolsSessions[username].Dispose();
                        _uiManager.AppendLog($"Đã đóng DevToolsSession cho {username}", UIManager.LogLevel.Info, username);
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Lỗi khi đóng DevToolsSession cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                    }
                    finally
                    {
                        _devToolsSessions.Remove(username);
                    }
                }
            }
        }

        private void UpdateDriverSizes()
        {
            lock (_lock)
            {
                foreach (var driver in _drivers.Values)
                {
                    try
                    {
                        if (_isProfileSizeLocked)
                        {
                            driver.Manage().Window.Size = new Size(700, 500); // Kích thước profile 700x500
                        }
                        else
                        {
                            driver.Manage().Window.Maximize();
                        }
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Lỗi khi cập nhật kích thước driver: {ex.Message}", UIManager.LogLevel.Warning);
                    }
                }
            }
        }

        /// <summary>
        /// Force update kích thước tất cả profile về 700x500
        /// </summary>
        public void ForceUpdateAllProfileSizes()
        {
            lock (_lock)
            {
                int successCount = 0;
                int failCount = 0;

                foreach (var kvp in _drivers)
                {
                    try
                    {
                        kvp.Value.Manage().Window.Size = new Size(700, 500);
                        successCount++;
                        _uiManager.AppendLog($"Đã cập nhật kích thước profile {kvp.Key} về 700x500", UIManager.LogLevel.Info, kvp.Key);
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        _uiManager.AppendLog($"Lỗi khi cập nhật kích thước profile {kvp.Key}: {ex.Message}", UIManager.LogLevel.Warning, kvp.Key);
                    }
                }

                _uiManager.AppendLog($"Force update hoàn tất: {successCount} thành công, {failCount} thất bại", UIManager.LogLevel.Info);
            }
        }
    }
}