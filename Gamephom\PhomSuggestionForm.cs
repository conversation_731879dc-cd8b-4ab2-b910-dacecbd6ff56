﻿using AutoGameBai.Gamephom;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace AutoGameBai.Gamephom
{
    public partial class PhomSuggestionForm : Form
    {
        private readonly GameClientManager _gameClient;
        private readonly UIManager _uiManager;
        private readonly PhomCardManager _phomCardManager;
        private readonly PhomGameLogic _gameLogic;
        private readonly PhomAdvancedGameLogic _advancedGameLogic;
        private readonly PhomOptimizedGameLogic _optimizedGameLogic;
        private readonly PhomSmartGameLogic _smartGameLogic;
        private readonly Panel[] userPanels;
        private readonly Label[] userLabels;
        private readonly Label[] analysisLabels;
        private readonly Label[] warningLabels; // Warning labels cho "Lá Bài Cạ!"
        private readonly PictureBox[] suggestedCardLabels;
        private readonly Button[] drawCardButtons;
        private readonly Button[] playCardButtons;
        private readonly Button[] eatCardButtons;
        private readonly Button[] haphomButtons;
        private readonly PictureBox[][] cardImages;
        private bool _isGameActive;
        private readonly Dictionary<string, bool> _hasDrawnCard;
        private readonly Dictionary<string, bool> _hasEatenCard;

        private readonly Size _initialFormSize = new Size(420, 860);
        private readonly Size _initialCardSize = new Size(40, 60);
        private readonly Size _initialPanelSize = new Size(400, 210);
        private readonly Size _initialButtonSize = new Size(60, 25);
        private readonly Size _initialOpponentPanelSize = new Size(400, 180);
        private readonly Size _initialSuggestedCardSize = new Size(60, 80);

        public PhomSuggestionForm(GameClientManager gameClient, PhomCardManager phomCardManager)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _phomCardManager = phomCardManager ?? throw new ArgumentNullException(nameof(phomCardManager));
            _uiManager = _gameClient.GetUIManager();
            _gameLogic = new PhomGameLogic(_gameClient, _phomCardManager, _uiManager);
            _advancedGameLogic = new PhomAdvancedGameLogic(_gameClient, _phomCardManager, _uiManager);
            _optimizedGameLogic = new PhomOptimizedGameLogic(_gameClient, _phomCardManager, _uiManager);
            _smartGameLogic = new PhomSmartGameLogic(_gameClient, _phomCardManager, _uiManager);
            _uiManager?.AppendLog("Bắt đầu khởi tạo PhomSuggestionForm", UIManager.LogLevel.Debug);

            InitializeComponent();
            _uiManager?.AppendLog("Đã gọi InitializeComponent", UIManager.LogLevel.Debug);

            userPanels = new[] { userPanel1, userPanel2, userPanel3 };
            userLabels = new[] { userLabel1, userLabel2, userLabel3 };
            analysisLabels = new[] { analysisLabel1, analysisLabel2, analysisLabel3 };
            suggestedCardLabels = new[] { suggestedCardLabel1, suggestedCardLabel2, suggestedCardLabel3 };
            drawCardButtons = new[] { drawCardButton1, drawCardButton2, drawCardButton3 };
            playCardButtons = new[] { playCardButton1, playCardButton2, playCardButton3 };
            eatCardButtons = new[] { eatCardButton1, eatCardButton2, eatCardButton3 };
            haphomButtons = new[] { haphomButton1, haphomButton2, haphomButton3 };
            cardImages = new[] { cardImages1, cardImages2, cardImages3 };

            // Khởi tạo warning labels
            warningLabels = new Label[3];
            for (int i = 0; i < 3; i++)
            {
                warningLabels[i] = new Label
                {
                    Text = "",
                    BackColor = Color.Yellow,
                    ForeColor = Color.Red,
                    Font = new Font("Arial", 9, FontStyle.Bold),
                    TextAlign = ContentAlignment.MiddleCenter,
                    Size = new Size(130, 25),
                    Location = new Point(270, 85), // Dưới 4 buttons
                    Visible = false,
                    BorderStyle = BorderStyle.FixedSingle
                };
                userPanels[i].Controls.Add(warningLabels[i]);
            }

            _isGameActive = false;
            _hasDrawnCard = new Dictionary<string, bool>();
            _hasEatenCard = new Dictionary<string, bool>();

            ClearSuggestions();
            this.Resize += new EventHandler(PhomSuggestionForm_Resize);

            _gameClient.GetWebSocketManager().GetPhomHandler().CardsUpdated += () =>
            {
                lock (_gameClient.GetWebSocketManager().GetLock())
                {
                    _uiManager.AppendLog("Nhận được sự kiện CardsUpdated", UIManager.LogLevel.Debug);
                    var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
                    if (userCardsDict == null || userCardsDict.Count == 0)
                    {
                        _uiManager.AppendLog("userCardsDict rỗng hoặc null", UIManager.LogLevel.Error);
                        return;
                    }

                    // Chỉ xử lý CardsUpdated cho đầu ván (khi tất cả users có 9 hoặc 10 lá bài)
                    bool isGameStart = userCardsDict.Values.All(cards => cards.Length == 9 || cards.Length == 10);

                    if (isGameStart)
                    {
                        _uiManager.AppendLog("🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users", UIManager.LogLevel.Info);

                        if (InvokeRequired)
                        {
                            Invoke(new Action(() => ProcessGameStart(userCardsDict)));
                        }
                        else
                        {
                            ProcessGameStart(userCardsDict);
                        }
                    }
                    else
                    {
                        _uiManager.AppendLog("⏭️ Giữa ván - Bỏ qua CardsUpdated (sẽ xử lý qua UpdateSpecificUser)", UIManager.LogLevel.Debug);
                    }
                }
            };

            _gameClient.GetWebSocketManager().GetPhomHandler().PlayCardReceived += () =>
            {
                _uiManager.AppendLog("Nhận được tin nhắn đánh bài", UIManager.LogLevel.Info);
                if (InvokeRequired)
                {
                    Invoke(new Action(() => ProcessPlayCardReceived()));
                }
                else
                {
                    ProcessPlayCardReceived();
                }
            };

            _gameClient.GetWebSocketManager().GetPhomHandler().GameEnded += () =>
            {
                _uiManager.AppendLog("Nhận được thông báo kết thúc ván", UIManager.LogLevel.Info);
                if (InvokeRequired)
                {
                    Invoke(new Action(() => ResetGameState()));
                }
                else
                {
                    ResetGameState();
                }
            };

            this.Load += (sender, e) => UpdateOpponentPlayedCards();
            _uiManager?.AppendLog("Kết thúc khởi tạo PhomSuggestionForm", UIManager.LogLevel.Debug);
        }

        private void PhomSuggestionForm_Resize(object sender, EventArgs e)
        {
            float widthRatio = (float)this.ClientSize.Width / _initialFormSize.Width;
            float heightRatio = (float)this.ClientSize.Height / _initialFormSize.Height;

            // Layout dọc - 3 panel xếp theo chiều dọc
            for (int i = 0; i < userPanels.Length; i++)
            {
                userPanels[i].Size = new Size((int)(_initialPanelSize.Width * widthRatio), (int)(_initialPanelSize.Height * heightRatio));
                userPanels[i].Location = new Point((int)(10 * widthRatio), (int)((10 + i * 220) * heightRatio));

                // Buttons layout mới - di chuyển lên cao hơn
                eatCardButtons[i].Size = new Size((int)(_initialButtonSize.Width * widthRatio), (int)(_initialButtonSize.Height * heightRatio));
                eatCardButtons[i].Location = new Point((int)(270 * widthRatio), (int)(30 * heightRatio));

                haphomButtons[i].Size = new Size((int)(_initialButtonSize.Width * widthRatio), (int)(_initialButtonSize.Height * heightRatio));
                haphomButtons[i].Location = new Point((int)(335 * widthRatio), (int)(30 * heightRatio));

                drawCardButtons[i].Size = new Size((int)(_initialButtonSize.Width * widthRatio), (int)(_initialButtonSize.Height * heightRatio));
                drawCardButtons[i].Location = new Point((int)(270 * widthRatio), (int)(55 * heightRatio));

                playCardButtons[i].Size = new Size((int)(_initialButtonSize.Width * widthRatio), (int)(_initialButtonSize.Height * heightRatio));
                playCardButtons[i].Location = new Point((int)(335 * widthRatio), (int)(55 * heightRatio));

                // Suggested card - Vị trí mới trong panel có khung
                suggestedCardLabels[i].Size = new Size((int)(_initialSuggestedCardSize.Width * widthRatio), (int)(_initialSuggestedCardSize.Height * heightRatio));

                // Warning labels - Dưới 4 buttons
                warningLabels[i].Size = new Size((int)(130 * widthRatio), (int)(25 * heightRatio));
                warningLabels[i].Location = new Point((int)(270 * widthRatio), (int)(85 * heightRatio));

                // Card images - 3 hàng 4 cột
                for (int j = 0; j < cardImages[i].Length; j++)
                {
                    int row = j / 4; // 3 hàng
                    int col = j % 4; // 4 cột
                    int x = (int)((5 + col * 45) * widthRatio);
                    int y = (int)((5 + row * 45) * heightRatio);
                    cardImages[i][j].Location = new Point(x, y);
                    cardImages[i][j].Size = new Size((int)(_initialCardSize.Width * widthRatio), (int)(_initialCardSize.Height * heightRatio));
                }
            }

            // Panel các lá bài đã đánh ở dưới cùng - 3 hàng
            if (opponentPlayedCardsPanel != null)
            {
                opponentPlayedCardsPanel.Location = new Point((int)(10 * widthRatio), (int)(670 * heightRatio));
                opponentPlayedCardsPanel.Size = new Size((int)(_initialOpponentPanelSize.Width * widthRatio), (int)(_initialOpponentPanelSize.Height * heightRatio));
                opponentPlayedCardsLabel.Location = new Point((int)(5 * widthRatio), (int)(5 * heightRatio));
                for (int i = 0; i < 30; i++) // 3 hàng x 10 cột = 30 slots
                {
                    int row = i / 10; // 3 hàng
                    int col = i % 10; // 10 cột mỗi hàng
                    int x = (int)((5 + col * 38) * widthRatio);
                    int y = (int)((25 + row * 55) * heightRatio);
                    opponentPlayedCardImages[i].Location = new Point(x, y);
                    opponentPlayedCardImages[i].Size = new Size((int)(35 * widthRatio), (int)(45 * heightRatio));
                }
            }
        }

        public void ClearSuggestions()
        {
            for (int i = 0; i < userPanels.Length; i++)
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        // Reset suggested card
                        try
                        {
                            suggestedCardLabels[i].Image = Image.FromFile("card/card.png");
                        }
                        catch (Exception ex)
                        {
                            _uiManager.AppendLog($"Không tìm thấy hình ảnh mặc định tại card/card.png: {ex.Message}", UIManager.LogLevel.Error);
                        }
                        foreach (var image in cardImages[i])
                        {
                            try
                            {
                                image.Image = Image.FromFile("card/card.png");
                            }
                            catch (Exception ex)
                            {
                                _uiManager.AppendLog($"Không tìm thấy hình ảnh mặc định tại card/card.png: {ex.Message}", UIManager.LogLevel.Error);
                            }
                            image.Visible = true;
                            if (!userPanels[i].Controls.Contains(image))
                            {
                                userPanels[i].Controls.Add(image);
                            }
                        }
                        drawCardButtons[i].Enabled = false;
                        playCardButtons[i].Enabled = false;
                        eatCardButtons[i].Enabled = false;
                        haphomButtons[i].Enabled = false;
                        analysisLabels[i].Text = "Phân tích: Chưa có dữ liệu";
                        warningLabels[i].Text = "";
                        warningLabels[i].Visible = false;
                        userPanels[i].Invalidate();
                        userPanels[i].Refresh();
                    }));
                }
                else
                {
                    // Reset suggested card
                    try
                    {
                        suggestedCardLabels[i].Image = Image.FromFile("card/card.png");
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Không tìm thấy hình ảnh mặc định tại card/card.png: {ex.Message}", UIManager.LogLevel.Error);
                    }
                    foreach (var image in cardImages[i])
                    {
                        try
                        {
                            image.Image = Image.FromFile("card/card.png");
                        }
                        catch (Exception ex)
                        {
                            _uiManager.AppendLog($"Không tìm thấy hình ảnh mặc định tại card/card.png: {ex.Message}", UIManager.LogLevel.Error);
                        }
                        image.Visible = true;
                        if (!userPanels[i].Controls.Contains(image))
                        {
                            userPanels[i].Controls.Add(image);
                        }
                    }
                    drawCardButtons[i].Enabled = false;
                    playCardButtons[i].Enabled = false;
                    eatCardButtons[i].Enabled = false;
                    haphomButtons[i].Enabled = false;
                    analysisLabels[i].Text = "Phân tích: Chưa có dữ liệu";
                    warningLabels[i].Text = "";
                    warningLabels[i].Visible = false;
                    userPanels[i].Invalidate();
                    userPanels[i].Refresh();
                }
            }
            _isGameActive = false;
            _hasDrawnCard.Clear();
            _hasEatenCard.Clear();
            this.Invalidate();
            this.Refresh();
        }

        public void UpdateCardsDisplay(string username, int[] cards, int panelIndex)
        {
            if (panelIndex < 0 || panelIndex >= userPanels.Length)
            {
                _uiManager.AppendLog($"PanelIndex {panelIndex} không hợp lệ cho user {username}", UIManager.LogLevel.Error);
                return;
            }

            if (cards == null || cards.Length == 0)
            {
                _uiManager.AppendLog($"Danh sách bài rỗng cho {username} tại panel {panelIndex}", UIManager.LogLevel.Error);
                return;
            }

            _uiManager.AppendLog($"Cập nhật bài cho {username} tại panel {panelIndex}, số lá bài: {cards.Length}", UIManager.LogLevel.Debug);

            if (InvokeRequired)
            {
                Invoke(new Action(() =>
                {
                    // Hiển thị username
                    userLabels[panelIndex].Text = username;
                    userLabels[panelIndex].Font = new Font("Arial", 10, FontStyle.Bold);
                    userLabels[panelIndex].ForeColor = Color.Black;

                    foreach (var image in cardImages[panelIndex])
                    {
                        try
                        {
                            image.Image = Image.FromFile("card/card.png");
                        }
                        catch (Exception ex)
                        {
                            _uiManager.AppendLog($"Không tìm thấy hình ảnh mặc định tại card/card.png: {ex.Message}", UIManager.LogLevel.Error);
                        }
                        image.Visible = true;
                        if (!userPanels[panelIndex].Controls.Contains(image))
                        {
                            userPanels[panelIndex].Controls.Add(image);
                        }
                    }

                    var (phomIndex, remainingIndex) = _gameLogic.DisplayCards(cards, username, panelIndex, cardImages[panelIndex]);

                    userPanels[panelIndex].Invalidate();
                    userPanels[panelIndex].Refresh();
                    this.Invalidate();
                    this.Refresh();

                    _uiManager.AppendLog($"Hiển thị bài cho panel {panelIndex}: Số phỏm: {phomIndex}, Số bài rác: {remainingIndex}", UIManager.LogLevel.Debug);
                }));
            }
            else
            {
                // Hiển thị username
                userLabels[panelIndex].Text = username;
                userLabels[panelIndex].Font = new Font("Arial", 10, FontStyle.Bold);
                userLabels[panelIndex].ForeColor = Color.Black;

                foreach (var image in cardImages[panelIndex])
                {
                    try
                    {
                        image.Image = Image.FromFile("card/card.png");
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Không tìm thấy hình ảnh mặc định tại card/card.png: {ex.Message}", UIManager.LogLevel.Error);
                    }
                    image.Visible = true;
                    if (!userPanels[panelIndex].Controls.Contains(image))
                    {
                        userPanels[panelIndex].Controls.Add(image);
                    }
                }

                var (phomIndex, remainingIndex) = _gameLogic.DisplayCards(cards, username, panelIndex, cardImages[panelIndex]);

                userPanels[panelIndex].Invalidate();
                userPanels[panelIndex].Refresh();
                this.Invalidate();
                this.Refresh();

                _uiManager.AppendLog($"Hiển thị bài cho panel {panelIndex}: Số phỏm: {phomIndex}, Số bài rác: {remainingIndex}", UIManager.LogLevel.Debug);
            }
        }

        public void UpdateTurnStatus(int panelIndex, string username)
        {
            bool isTurn = _gameClient.GetWebSocketManager().GetPhomHandler().IsUserTurn(username);
            bool hasDrawnCard = _hasDrawnCard.ContainsKey(username) && _hasDrawnCard[username];
            bool hasEatenCard = _hasEatenCard.ContainsKey(username) && _hasEatenCard[username];
            bool hasDrawnOrEaten = hasDrawnCard || hasEatenCard;

            // CHỈ hiển thị gợi ý sau khi user đã rút bài hoặc ăn bài
            bool shouldShowSuggestion = hasDrawnOrEaten;

            _uiManager.AppendLog($"🎯 UpdateTurnStatus cho {username}: isTurn={isTurn}, hasDrawnCard={hasDrawnCard}, hasEatenCard={hasEatenCard}, shouldShowSuggestion={shouldShowSuggestion}", UIManager.LogLevel.Debug);

            if (InvokeRequired)
            {
                Invoke(new Action(() =>
                {
                    // Chỉ enable buttons cho user đang đến lượt
                    drawCardButtons[panelIndex].Enabled = isTurn;
                    playCardButtons[panelIndex].Enabled = isTurn;
                    eatCardButtons[panelIndex].Enabled = isTurn;
                    haphomButtons[panelIndex].Enabled = isTurn;

                    if (shouldShowSuggestion)
                    {
                        // Sử dụng thuật toán thông minh mới
                        var (suggestedCard, analysis) = _smartGameLogic.SuggestSmartCard(username, _gameClient.GetWebSocketManager().GetPhomHandler().GetFirstPlayerUid() == username);
                        _uiManager.AppendLog($"🧠 Gợi ý thông minh cho {username}: {analysis}", UIManager.LogLevel.Info);

                        // Parse analysis để tách warning text
                        string displayAnalysis = analysis;
                        string warningText = "";

                        if (analysis.Contains("|"))
                        {
                            var parts = analysis.Split('|');
                            displayAnalysis = parts[0];
                            warningText = parts.Length > 1 ? parts[1] : "";
                        }

                        // Hiển thị phân tích chi tiết chỉ cho user này
                        analysisLabels[panelIndex].Text = displayAnalysis;

                        // Hiển thị warning label nếu có
                        if (!string.IsNullOrEmpty(warningText))
                        {
                            warningLabels[panelIndex].Text = warningText;
                            warningLabels[panelIndex].Visible = true;
                        }
                        else
                        {
                            warningLabels[panelIndex].Text = "";
                            warningLabels[panelIndex].Visible = false;
                        }
                        // Display suggested card
                        if (suggestedCard != -1)
                        {
                            try
                            {
                                string imagePath = CardUtilityPhom.GetCardImagePath(new CardUtilityPhom.CardInfo(suggestedCard));
                                if (File.Exists(imagePath))
                                {
                                    suggestedCardLabels[panelIndex].Image = Image.FromFile(imagePath);
                                    suggestedCardLabels[panelIndex].Visible = true;
                                    suggestedCardLabels[panelIndex].Invalidate();
                                    _uiManager.AppendLog($"✅ Hiển thị lá bài gợi ý cho {username}: {CardUtilityPhom.ConvertCardsToString(new[] { suggestedCard })} tại {imagePath}", UIManager.LogLevel.Info);
                                }
                                else
                                {
                                    _uiManager.AppendLog($"❌ Hình ảnh không tồn tại cho lá bài gợi ý {suggestedCard} tại {imagePath}", UIManager.LogLevel.Error);
                                    suggestedCardLabels[panelIndex].Image = Image.FromFile("card/card.png");
                                    suggestedCardLabels[panelIndex].Visible = true;
                                }
                            }
                            catch (Exception ex)
                            {
                                _uiManager.AppendLog($"❌ Lỗi khi hiển thị lá bài gợi ý {suggestedCard}: {ex.Message}", UIManager.LogLevel.Error);
                                try
                                {
                                    suggestedCardLabels[panelIndex].Image = Image.FromFile("card/card.png");
                                    suggestedCardLabels[panelIndex].Visible = true;
                                }
                                catch
                                {
                                    _uiManager.AppendLog($"❌ Không tìm thấy hình ảnh mặc định tại card/card.png", UIManager.LogLevel.Error);
                                }
                            }
                        }
                        else
                        {
                            try
                            {
                                suggestedCardLabels[panelIndex].Image = Image.FromFile("card/card.png");
                                suggestedCardLabels[panelIndex].Visible = true;
                                _uiManager.AppendLog($"⚠️ Không có lá bài gợi ý cho {username}, hiển thị card mặc định", UIManager.LogLevel.Warning);
                            }
                            catch (Exception ex)
                            {
                                _uiManager.AppendLog($"❌ Không tìm thấy hình ảnh mặc định tại card/card.png: {ex.Message}", UIManager.LogLevel.Error);
                            }
                        }
                    }
                    else
                    {
                        // Clear analysis và suggested card cho user không đến lượt - KHÔNG làm mới panel lá bài
                        analysisLabels[panelIndex].Text = $"{username}: Chờ lượt chơi";
                        warningLabels[panelIndex].Text = "";
                        warningLabels[panelIndex].Visible = false;
                        try
                        {
                            suggestedCardLabels[panelIndex].Image = Image.FromFile("card/card.png");
                        }
                        catch (Exception ex)
                        {
                            _uiManager.AppendLog($"Không tìm thấy hình ảnh mặc định: {ex.Message}", UIManager.LogLevel.Error);
                        }
                        // Chỉ refresh analysis label và suggested card, KHÔNG refresh toàn bộ panel
                        analysisLabels[panelIndex].Invalidate();
                        suggestedCardLabels[panelIndex].Invalidate();
                        warningLabels[panelIndex].Invalidate();
                    }
                }));
            }
            else
            {
                drawCardButtons[panelIndex].Enabled = isTurn;
                playCardButtons[panelIndex].Enabled = isTurn;
                eatCardButtons[panelIndex].Enabled = isTurn;
                haphomButtons[panelIndex].Enabled = isTurn;

                if (shouldShowSuggestion)
                {
                    // Sử dụng thuật toán thông minh mới
                    var (suggestedCard, analysis) = _smartGameLogic.SuggestSmartCard(username, _gameClient.GetWebSocketManager().GetPhomHandler().GetFirstPlayerUid() == username);
                    _uiManager.AppendLog($"🧠 Gợi ý thông minh cho {username}: {analysis}", UIManager.LogLevel.Info);

                    // Parse analysis để tách warning text
                    string displayAnalysis = analysis;
                    string warningText = "";

                    if (analysis.Contains("|"))
                    {
                        var parts = analysis.Split('|');
                        displayAnalysis = parts[0];
                        warningText = parts.Length > 1 ? parts[1] : "";
                    }

                    // Hiển thị phân tích chi tiết
                    analysisLabels[panelIndex].Text = displayAnalysis;

                    // Hiển thị warning label nếu có
                    if (!string.IsNullOrEmpty(warningText))
                    {
                        warningLabels[panelIndex].Text = warningText;
                        warningLabels[panelIndex].Visible = true;
                    }
                    else
                    {
                        warningLabels[panelIndex].Text = "";
                        warningLabels[panelIndex].Visible = false;
                    }
                    if (suggestedCard != -1)
                    {
                        try
                        {
                            string imagePath = CardUtilityPhom.GetCardImagePath(new CardUtilityPhom.CardInfo(suggestedCard));
                            if (File.Exists(imagePath))
                            {
                                suggestedCardLabels[panelIndex].Image = Image.FromFile(imagePath);
                                suggestedCardLabels[panelIndex].Visible = true;
                                suggestedCardLabels[panelIndex].Invalidate();
                                _uiManager.AppendLog($"✅ Hiển thị lá bài gợi ý cho {username}: {CardUtilityPhom.ConvertCardsToString(new[] { suggestedCard })} tại {imagePath}", UIManager.LogLevel.Info);
                            }
                            else
                            {
                                _uiManager.AppendLog($"❌ Hình ảnh không tồn tại cho lá bài gợi ý {suggestedCard} tại {imagePath}", UIManager.LogLevel.Error);
                                suggestedCardLabels[panelIndex].Image = Image.FromFile("card/card.png");
                                suggestedCardLabels[panelIndex].Visible = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            _uiManager.AppendLog($"❌ Lỗi khi hiển thị lá bài gợi ý {suggestedCard}: {ex.Message}", UIManager.LogLevel.Error);
                            try
                            {
                                suggestedCardLabels[panelIndex].Image = Image.FromFile("card/card.png");
                                suggestedCardLabels[panelIndex].Visible = true;
                            }
                            catch
                            {
                                _uiManager.AppendLog($"❌ Không tìm thấy hình ảnh mặc định tại card/card.png", UIManager.LogLevel.Error);
                            }
                        }
                    }
                    else
                    {
                        try
                        {
                            suggestedCardLabels[panelIndex].Image = Image.FromFile("card/card.png");
                            suggestedCardLabels[panelIndex].Visible = true;
                            _uiManager.AppendLog($"⚠️ Không có lá bài gợi ý cho {username}, hiển thị card mặc định", UIManager.LogLevel.Warning);
                        }
                        catch (Exception ex)
                        {
                            _uiManager.AppendLog($"❌ Không tìm thấy hình ảnh mặc định tại card/card.png: {ex.Message}", UIManager.LogLevel.Error);
                        }
                    }
                }
                else
                {
                    // Clear analysis và suggested card cho user không đến lượt - KHÔNG làm mới panel lá bài
                    analysisLabels[panelIndex].Text = $"{username}: Chờ lượt chơi";
                    warningLabels[panelIndex].Text = "";
                    warningLabels[panelIndex].Visible = false;
                    try
                    {
                        suggestedCardLabels[panelIndex].Image = Image.FromFile("card/card.png");
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Không tìm thấy hình ảnh mặc định: {ex.Message}", UIManager.LogLevel.Error);
                    }
                    // Chỉ refresh analysis label và suggested card, KHÔNG refresh toàn bộ panel
                    analysisLabels[panelIndex].Invalidate();
                    suggestedCardLabels[panelIndex].Invalidate();
                    warningLabels[panelIndex].Invalidate();
                }
            }
        }

        private void drawCardButton_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            if (button == null) return;

            if (!int.TryParse(button.Tag?.ToString(), out int index))
            {
                ShowMessageBox("Tag của button không hợp lệ!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string username = userLabels[index].Text;
            if (string.IsNullOrEmpty(username) || username == "Chưa có dữ liệu bài")
            {
                ShowMessageBox("Không có user để rút bài!", "Cảnh báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            _uiManager.AppendLog($"Rút bài cho {username} (chưa triển khai thực thi)", UIManager.LogLevel.Info);
            ShowMessageBox($"Rút bài cho {username} (chưa triển khai thực thi)!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);

            _hasDrawnCard[username] = true;
            UpdateTurnStatus(index, username);
        }

        private void eatCardButton_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            if (button == null) return;

            if (!int.TryParse(button.Tag?.ToString(), out int index))
            {
                ShowMessageBox("Tag của button không hợp lệ!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string username = userLabels[index].Text;
            if (string.IsNullOrEmpty(username) || username == "Chưa có dữ liệu bài")
            {
                ShowMessageBox("Không có user để ăn bài!", "Cảnh báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            _gameLogic.EatCard(sender, userLabels, ShowMessageBox);

            _hasEatenCard[username] = true;
            UpdateTurnStatus(index, username);
        }

        private void playCardButton_Click(object sender, EventArgs e)
        {
            _gameLogic.PlayCard(sender, userLabels, ShowMessageBox);
        }

        private void haphomButton_Click(object sender, EventArgs e)
        {
            _gameLogic.Haphom(sender, userLabels, ShowMessageBox);
        }

        private void ShowMessageBox(string message, string caption, MessageBoxButtons buttons, MessageBoxIcon icon)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() =>
                {
                    MessageBox.Show(this, message, caption, buttons, icon);
                }));
            }
            else
            {
                MessageBox.Show(this, message, caption, buttons, icon);
            }
        }

        public void UpdateOpponentPlayedCards()
        {
            var playedCards = _gameClient.GetPlayedCards();
            if (playedCards == null || !playedCards.Any())
            {
                _uiManager.AppendLog("Không có lá bài nào đã được đánh", UIManager.LogLevel.Debug);
                return;
            }

            if (InvokeRequired)
            {
                Invoke(new Action(() => UpdateOpponentPlayedCardsDisplay(playedCards)));
            }
            else
            {
                UpdateOpponentPlayedCardsDisplay(playedCards);
            }
        }

        public void ClearOpponentPlayedCards()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() =>
                {
                    ClearOpponentPlayedCardsInternal();
                }));
            }
            else
            {
                ClearOpponentPlayedCardsInternal();
            }
        }

        private void ClearOpponentPlayedCardsInternal()
        {
            if (opponentPlayedCardImages != null)
            {
                foreach (var image in opponentPlayedCardImages)
                {
                    if (image != null)
                    {
                        // Dispose image để giải phóng memory
                        if (image.Image != null)
                        {
                            image.Image.Dispose();
                            image.Image = null;
                        }
                        image.Visible = false;
                    }
                }
            }
            if (opponentPlayedCardsLabel != null)
                opponentPlayedCardsLabel.Text = "Các lá bài đã đánh";
            if (opponentPlayedCardsPanel != null)
            {
                opponentPlayedCardsPanel.Invalidate();
                opponentPlayedCardsPanel.Refresh();
            }

            // Force garbage collection để clear memory
            GC.Collect();
            GC.WaitForPendingFinalizers();

            _uiManager.AppendLog("✅ Đã xóa HOÀN TOÀN tất cả các lá bài đã đánh và giải phóng memory", UIManager.LogLevel.Debug);
        }

        private void UpdateOpponentPlayedCardsDisplay(List<int> playedCards)
        {
            if (opponentPlayedCardImages != null)
            {
                foreach (var image in opponentPlayedCardImages)
                {
                    if (image != null)
                    {
                        image.Image = null;
                        image.Visible = false;
                    }
                }
            }

            // Sắp xếp lá bài theo thứ tự tăng dần
            var sortedPlayedCards = playedCards.OrderBy(cardId =>
            {
                try
                {
                    var cardInfo = new CardUtilityPhom.CardInfo(cardId);
                    // Sắp xếp theo: Rank trước, Suit sau
                    return cardInfo.Rank * 10 + cardInfo.Suit;
                }
                catch
                {
                    return cardId; // Fallback nếu không parse được
                }
            }).ToList();

            _uiManager.AppendLog($"Hiển thị {sortedPlayedCards.Count} lá bài đã đánh (đã sắp xếp)", UIManager.LogLevel.Info);

            for (int i = 0; i < Math.Min(sortedPlayedCards.Count, opponentPlayedCardImages.Length); i++)
            {
                try
                {
                    string imagePath = CardUtilityPhom.GetCardImagePath(new CardUtilityPhom.CardInfo(sortedPlayedCards[i]));
                    if (File.Exists(imagePath))
                    {
                        opponentPlayedCardImages[i].Image = Image.FromFile(imagePath);
                        opponentPlayedCardImages[i].Visible = true;
                        opponentPlayedCardImages[i].Invalidate();
                        _uiManager.AppendLog($"Hiển thị lá bài đã đánh {sortedPlayedCards[i]} tại vị trí {i}", UIManager.LogLevel.Debug);
                    }
                    else
                    {
                        _uiManager.AppendLog($"Không tìm thấy hình ảnh tại {imagePath} cho cardId {sortedPlayedCards[i]}", UIManager.LogLevel.Error);
                    }
                }
                catch (Exception ex)
                {
                    _uiManager.AppendLog($"Lỗi khi hiển thị lá bài đã đánh {sortedPlayedCards[i]}: {ex.Message}", UIManager.LogLevel.Error);
                }
            }

            opponentPlayedCardsLabel.Text = $"Các lá bài đã đánh ({sortedPlayedCards.Count} lá) - Đã sắp xếp";
            opponentPlayedCardsPanel.Invalidate();
            opponentPlayedCardsPanel.Refresh();
            this.Invalidate();
            this.Refresh();
        }

        private bool DetermineTeamMode(Dictionary<string, int[]> userCardsDict)
        {
            if (userCardsDict.Count >= 3)
            {
                return true;
            }

            var playerOrder = _gameClient.GetWebSocketManager().GetPhomHandler().GetPlayerOrder();
            if (playerOrder.Count >= 3)
            {
                return true;
            }

            return false;
        }

        private int GetPanelIndexForUser(string username, Dictionary<string, int[]> userCardsDict)
        {
            if (DetermineTeamMode(userCardsDict))
            {
                var teamCards = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
                if (teamCards.ContainsKey(username))
                {
                    return teamCards.Keys.ToList().IndexOf(username);
                }
            }

            return userCardsDict.Keys.ToList().IndexOf(username);
        }

        private void ProcessPlayCardReceived()
        {
            var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();

            _uiManager.AppendLog($"🔄 ProcessPlayCardReceived: Kiểm tra {userCardsDict.Count} users", UIManager.LogLevel.Debug);

            // CHỈ UPDATE CHO USER CÓ THAY ĐỔI THỰC SỰ
            foreach (var kvp in userCardsDict)
            {
                string user = kvp.Key;
                int panelIndex = GetPanelIndexForUser(user, userCardsDict);

                if (panelIndex >= 0 && panelIndex < userPanels.Length)
                {
                    bool shouldUpdate = false;
                    string updateReason = "";

                    // 1. Kiểm tra user đang đến lượt
                    bool isTurn = _gameClient.GetWebSocketManager().GetPhomHandler().IsUserTurn(user);
                    if (isTurn)
                    {
                        shouldUpdate = true;
                        updateReason = "đang đến lượt";
                    }

                    // 2. Kiểm tra user đã rút/ăn bài
                    bool hasDrawnOrEaten = (_hasDrawnCard.ContainsKey(user) && _hasDrawnCard[user]) ||
                                         (_hasEatenCard.ContainsKey(user) && _hasEatenCard[user]);
                    if (hasDrawnOrEaten)
                    {
                        shouldUpdate = true;
                        updateReason += (string.IsNullOrEmpty(updateReason) ? "" : " và ") + "đã rút/ăn bài";
                    }

                    // 3. Kiểm tra thay đổi số lượng bài (có thể user vừa đánh bài)
                    if (userLabels[panelIndex].Text == user)
                    {
                        // So sánh với số bài hiện tại đang hiển thị
                        var currentDisplayedCards = cardImages[panelIndex].Count(img => img.Visible);
                        if (currentDisplayedCards != kvp.Value.Length)
                        {
                            shouldUpdate = true;
                            updateReason += (string.IsNullOrEmpty(updateReason) ? "" : " và ") + $"thay đổi số bài ({currentDisplayedCards}→{kvp.Value.Length})";
                        }
                    }

                    if (shouldUpdate)
                    {
                        _uiManager.AppendLog($"🔄 UPDATE {user}: {updateReason}", UIManager.LogLevel.Info);
                        UpdateCardsDisplay(user, kvp.Value, panelIndex);
                        UpdateTurnStatus(panelIndex, user);
                    }
                    else
                    {
                        _uiManager.AppendLog($"⏸️ SKIP {user}: Không có thay đổi cần update", UIManager.LogLevel.Debug);
                    }
                }
            }

            // Update played cards (luôn cần thiết)
            UpdateOpponentPlayedCards();
        }

        /// <summary>
        /// Xử lý đầu ván - hiển thị tất cả users
        /// </summary>
        private void ProcessGameStart(Dictionary<string, int[]> userCardsDict)
        {
            ClearSuggestions();
            _isGameActive = true;

            foreach (var kvp in userCardsDict)
            {
                string user = kvp.Key;
                _hasDrawnCard[user] = false;
                _hasEatenCard[user] = false;
            }

            bool isTeamMode = DetermineTeamMode(userCardsDict);
            _uiManager.AppendLog($"Chế độ chơi: {(isTeamMode ? "Team" : "Solo")}", UIManager.LogLevel.Info);

            if (isTeamMode)
            {
                _uiManager.AppendLog("Xử lý chế độ Team", UIManager.LogLevel.Info);
                _gameLogic.ProcessTeamMode(userCardsDict, UpdateCardsDisplay);
            }
            else
            {
                _uiManager.AppendLog($"Xử lý chế độ Solo với {userCardsDict.Count} người chơi", UIManager.LogLevel.Info);
                _gameLogic.ProcessSoloMode(userCardsDict, UpdateCardsDisplay);
            }

            foreach (var kvp in userCardsDict)
            {
                string user = kvp.Key;
                int[] cards = kvp.Value;
                int panelIndex = GetPanelIndexForUser(user, userCardsDict);

                if (panelIndex >= 0 && panelIndex < userPanels.Length)
                {
                    if (cards.Length == 10)
                    {
                        _uiManager.AppendLog($"{user} đi trước (10 lá bài)", UIManager.LogLevel.Info);
                        _hasDrawnCard[user] = true;
                        UpdateTurnStatus(panelIndex, user);
                    }
                    else if (cards.Length == 9)
                    {
                        _uiManager.AppendLog($"{user} không đi trước (9 lá bài)", UIManager.LogLevel.Info);
                    }
                }
            }

            this.Invalidate();
            this.Refresh();
        }

        /// <summary>
        /// Update chỉ cho user cụ thể sau khi rút bài hoặc ăn bài - CẢI THIỆN LOGIC
        /// </summary>
        public void UpdateSpecificUser(string username, int[] cards)
        {
            try
            {
                if (string.IsNullOrEmpty(username) || cards == null)
                {
                    _uiManager.AppendLog($"❌ UpdateSpecificUser: Invalid parameters - username: {username}, cards: {cards?.Length ?? 0}", UIManager.LogLevel.Error);
                    return;
                }

                var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
                if (userCardsDict == null || !userCardsDict.ContainsKey(username))
                {
                    _uiManager.AppendLog($"❌ UpdateSpecificUser: User {username} not found in userCardsDict", UIManager.LogLevel.Error);
                    return;
                }

                // 🎯 KIỂM TRA: CHỈ UPDATE KHI ĐẾN LƯỢT USER TƯƠNG ỨNG
                bool isUserTurn = _gameClient.GetWebSocketManager().GetPhomHandler().IsUserTurn(username);
                bool hasUserDrawnOrEaten = (_hasDrawnCard.ContainsKey(username) && _hasDrawnCard[username]) ||
                                         (_hasEatenCard.ContainsKey(username) && _hasEatenCard[username]);

                // Chỉ update nếu:
                // 1. Đến lượt user này, HOẶC
                // 2. User này vừa rút/ăn bài
                if (!isUserTurn && !hasUserDrawnOrEaten)
                {
                    _uiManager.AppendLog($"⏸️ SKIP UPDATE: {username} - Không đến lượt và chưa rút/ăn bài", UIManager.LogLevel.Debug);
                    return;
                }

                int panelIndex = GetPanelIndexForUser(username, userCardsDict);

                if (panelIndex >= 0 && panelIndex < userPanels.Length)
                {
                    _uiManager.AppendLog($"🔄 UPDATE PANEL: {username} tại panel {panelIndex} với {cards.Length} lá bài (Turn: {isUserTurn}, DrawnOrEaten: {hasUserDrawnOrEaten})", UIManager.LogLevel.Info);

                    // Khởi tạo tracking nếu chưa có
                    if (!_hasDrawnCard.ContainsKey(username))
                        _hasDrawnCard[username] = false;
                    if (!_hasEatenCard.ContainsKey(username))
                        _hasEatenCard[username] = false;

                    if (InvokeRequired)
                    {
                        Invoke(new Action(() =>
                        {
                            try
                            {
                                UpdateCardsDisplay(username, cards, panelIndex);
                                UpdateTurnStatus(panelIndex, username);
                            }
                            catch (Exception ex)
                            {
                                _uiManager.AppendLog($"❌ Error in UpdateSpecificUser Invoke: {ex.Message}", UIManager.LogLevel.Error);
                            }
                        }));
                    }
                    else
                    {
                        UpdateCardsDisplay(username, cards, panelIndex);
                        UpdateTurnStatus(panelIndex, username);
                    }
                }
                else
                {
                    _uiManager.AppendLog($"❌ UpdateSpecificUser: Invalid panelIndex {panelIndex} for user {username}", UIManager.LogLevel.Error);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Exception in UpdateSpecificUser: {ex.Message}\nStackTrace: {ex.StackTrace}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Đánh dấu user đã rút bài và trigger gợi ý
        /// </summary>
        public void MarkUserDrawnCard(string username)
        {
            _hasDrawnCard[username] = true;
            _uiManager.AppendLog($"✅ Đánh dấu {username} đã rút bài", UIManager.LogLevel.Info);

            // Trigger gợi ý ngay sau khi rút bài
            TriggerSuggestionForUser(username);
        }

        /// <summary>
        /// Đánh dấu user đã ăn bài và trigger gợi ý
        /// </summary>
        public void MarkUserEatenCard(string username)
        {
            _hasEatenCard[username] = true;
            _uiManager.AppendLog($"✅ Đánh dấu {username} đã ăn bài", UIManager.LogLevel.Info);

            // Trigger gợi ý ngay sau khi ăn bài
            TriggerSuggestionForUser(username);
        }

        /// <summary>
        /// Trigger gợi ý cho user cụ thể
        /// </summary>
        private void TriggerSuggestionForUser(string username)
        {
            var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
            if (!userCardsDict.ContainsKey(username)) return;

            int panelIndex = GetPanelIndexForUser(username, userCardsDict);
            if (panelIndex >= 0 && panelIndex < userPanels.Length)
            {
                _uiManager.AppendLog($"🎯 Trigger gợi ý cho {username} sau khi rút/ăn bài", UIManager.LogLevel.Info);

                if (InvokeRequired)
                {
                    Invoke(new Action(() => UpdateTurnStatus(panelIndex, username)));
                }
                else
                {
                    UpdateTurnStatus(panelIndex, username);
                }
            }
        }

        /// <summary>
        /// Reset toàn bộ trạng thái game - public method
        /// </summary>
        public void ResetGameState()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(ResetGameState));
                    return;
                }

                ClearSuggestions();
                ClearOpponentPlayedCards();
                _isGameActive = false;
                _hasDrawnCard.Clear();
                _hasEatenCard.Clear();

                // Reset PhomSmartGameLogic global variables
                PhomSmartGameLogic.ResetGameState();

                // Clear tất cả card panels với memory cleanup
                for (int i = 0; i < userPanels.Length; i++)
                {
                    userLabels[i].Text = "";
                    userLabels[i].BackColor = Color.Transparent;

                    for (int j = 0; j < cardImages[i].Length; j++)
                    {
                        // Dispose image để giải phóng memory
                        if (cardImages[i][j].Image != null)
                        {
                            cardImages[i][j].Image.Dispose();
                            cardImages[i][j].Image = null;
                        }
                        cardImages[i][j].Visible = false;
                    }

                    // Clear suggested card images
                    if (suggestedCardLabels[i].Image != null)
                    {
                        suggestedCardLabels[i].Image.Dispose();
                        suggestedCardLabels[i].Image = null;
                    }
                }

                this.Invalidate();
                this.Refresh();

                _uiManager.AppendLog("✅ Đã reset hoàn toàn trạng thái PhomSuggestionForm", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi ResetGameState: {ex.Message}", UIManager.LogLevel.Error);
            }
        }
    }
}