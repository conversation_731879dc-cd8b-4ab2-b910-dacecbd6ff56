﻿using System;
using System.Windows.Forms;

namespace AutoGameBai.UI
{
    public static class MessageBoxHelper
    {
        public static void ShowMessageBox(Form parent, string message, string title, MessageBoxIcon icon)
        {
            try
            {
                using (var form = new Form())
                {
                    form.Text = title;
                    form.FormBorderStyle = FormBorderStyle.FixedDialog;
                    form.MaximizeBox = false;
                    form.MinimizeBox = false;
                    form.StartPosition = FormStartPosition.Manual;
                    form.Size = new Size(400, 180);
                    form.ShowInTaskbar = false;

                    // Căn giữa form so với parent nếu parent không null
                    if (parent != null)
                    {
                        // Đảm bảo form đã có kích thước hợp lệ
                        if (form.Width <= 0 || form.Height <= 0)
                        {
                            form.Size = new Size(400, 150); // Đặt lại kích thước mặc định nếu không hợp lệ
                        }

                        // Tính toán vị trí để căn giữa
                        int left = parent.Left + (parent.Width - form.Width) / 2;
                        int top = parent.Top + (parent.Height - form.Height) / 2;

                        // Đảm bảo form không nằm ngoài màn hình
                        left = Math.Max(0, left);
                        top = Math.Max(0, top);

                        form.Location = new Point(left, top);
                    }
                    else
                    {
                        // Nếu parent là null, đặt StartPosition là CenterScreen
                        form.StartPosition = FormStartPosition.CenterScreen;
                    }

                    var label = new Label
                    {
                        Text = message,
                        AutoSize = true,
                        Location = new Point(20, 20),
                        MaximumSize = new Size(form.ClientSize.Width - 40, 0)
                    };
                    form.Controls.Add(label);

                    var button = new Button
                    {
                        Text = "OK",
                        DialogResult = DialogResult.OK,
                        Location = new Point(form.ClientSize.Width - 100, form.ClientSize.Height - 50),
                        Size = new Size(75, 25)
                    };
                    form.Controls.Add(button);
                    form.AcceptButton = button;

                    // Điều chỉnh kích thước form nếu cần
                    int requiredHeight = label.Bottom + 70;
                    if (form.Height < requiredHeight)
                    {
                        form.Height = requiredHeight;
                    }

                    // Cập nhật lại vị trí button sau khi thay đổi kích thước form
                    button.Location = new Point(form.ClientSize.Width - 100, form.ClientSize.Height - 50);

                    // Hiển thị icon
                    if (icon != MessageBoxIcon.None)
                    {
                        var pictureBox = new PictureBox
                        {
                            Size = new Size(32, 32),
                            Location = new Point(20, 20),
                            Image = icon switch
                            {
                                MessageBoxIcon.Information => SystemIcons.Information.ToBitmap(),
                                MessageBoxIcon.Warning => SystemIcons.Warning.ToBitmap(),
                                MessageBoxIcon.Error => SystemIcons.Error.ToBitmap(),
                                MessageBoxIcon.Question => SystemIcons.Question.ToBitmap(),
                                _ => null
                            }
                        };
                        form.Controls.Add(pictureBox);
                        label.Location = new Point(60, 20);
                    }

                    form.ShowDialog(parent);
                }
            }
            catch (Exception ex)
            {
                // Ghi log lỗi nếu có
                Serilog.Log.Error(ex, "Lỗi khi hiển thị MessageBox: {Message}", message);
                // Fallback: Sử dụng MessageBox mặc định của hệ thống
                MessageBox.Show(message, title, MessageBoxButtons.OK, icon);
            }
        }

        public static DialogResult ShowMessageBoxWithResult(Form parent, string message, string title, MessageBoxIcon icon)
        {
            try
            {
                using (var form = new Form())
                {
                    form.Text = title;
                    form.FormBorderStyle = FormBorderStyle.FixedDialog;
                    form.MaximizeBox = false;
                    form.MinimizeBox = false;
                    form.StartPosition = FormStartPosition.Manual;
                    form.Size = new Size(400, 180);
                    form.ShowInTaskbar = false;

                    // Căn giữa form so với parent nếu parent không null
                    if (parent != null)
                    {
                        // Đảm bảo form đã có kích thước hợp lệ
                        if (form.Width <= 0 || form.Height <= 0)
                        {
                            form.Size = new Size(400, 150); // Đặt lại kích thước mặc định nếu không hợp lệ
                        }

                        // Tính toán vị trí để căn giữa
                        int left = parent.Left + (parent.Width - form.Width) / 2;
                        int top = parent.Top + (parent.Height - form.Height) / 2;

                        // Đảm bảo form không nằm ngoài màn hình
                        left = Math.Max(0, left);
                        top = Math.Max(0, top);

                        form.Location = new Point(left, top);
                    }
                    else
                    {
                        // Nếu parent là null, đặt StartPosition là CenterScreen
                        form.StartPosition = FormStartPosition.CenterScreen;
                    }

                    var label = new Label
                    {
                        Text = message,
                        AutoSize = true,
                        Location = new Point(20, 20),
                        MaximumSize = new Size(form.ClientSize.Width - 40, 0)
                    };
                    form.Controls.Add(label);

                    var btnYes = new Button
                    {
                        Text = "Yes",
                        DialogResult = DialogResult.Yes,
                        Location = new Point(form.ClientSize.Width - 180, form.ClientSize.Height - 50),
                        Size = new Size(75, 25)
                    };
                    var btnNo = new Button
                    {
                        Text = "No",
                        DialogResult = DialogResult.No,
                        Location = new Point(form.ClientSize.Width - 100, form.ClientSize.Height - 50),
                        Size = new Size(75, 25)
                    };
                    form.Controls.Add(btnYes);
                    form.Controls.Add(btnNo);
                    form.AcceptButton = btnYes;
                    form.CancelButton = btnNo;

                    // Điều chỉnh kích thước form nếu cần
                    int requiredHeight = label.Bottom + 70;
                    if (form.Height < requiredHeight)
                    {
                        form.Height = requiredHeight;
                    }

                    // Cập nhật lại vị trí các button sau khi thay đổi kích thước form
                    btnYes.Location = new Point(form.ClientSize.Width - 180, form.ClientSize.Height - 50);
                    btnNo.Location = new Point(form.ClientSize.Width - 100, form.ClientSize.Height - 50);

                    // Hiển thị icon
                    if (icon != MessageBoxIcon.None)
                    {
                        var pictureBox = new PictureBox
                        {
                            Size = new Size(32, 32),
                            Location = new Point(20, 20),
                            Image = icon switch
                            {
                                MessageBoxIcon.Information => SystemIcons.Information.ToBitmap(),
                                MessageBoxIcon.Warning => SystemIcons.Warning.ToBitmap(),
                                MessageBoxIcon.Error => SystemIcons.Error.ToBitmap(),
                                MessageBoxIcon.Question => SystemIcons.Question.ToBitmap(),
                                _ => null
                            }
                        };
                        form.Controls.Add(pictureBox);
                        label.Location = new Point(60, 20);
                    }

                    return form.ShowDialog(parent);
                }
            }
            catch (Exception ex)
            {
                // Ghi log lỗi nếu có
                Serilog.Log.Error(ex, "Lỗi khi hiển thị MessageBoxWithResult: {Message}", message);
                // Fallback: Sử dụng MessageBox mặc định của hệ thống
                return MessageBox.Show(message, title, MessageBoxButtons.YesNo, icon);
            }
        }
    }
}