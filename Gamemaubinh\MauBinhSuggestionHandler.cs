using System;
using System.Collections.Generic;
using System.Linq;

namespace AutoGameBai.Gamemaubinh
{
    /// <summary>
    /// Temporary stub for MauBinhSuggestionHandler to maintain compatibility
    /// This class delegates to the new MauBinhEngine
    /// </summary>
    public class MauBinhSuggestionHandler
    {
        private readonly UIManager _uiManager;
        private readonly MauBinhEngine _engine;

        public MauBinhSuggestionHandler(UIManager uiManager)
        {
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _engine = new MauBinhEngine(uiManager);
        }

        /// <summary>
        /// Generate suggestions using new engine
        /// </summary>
        public List<(int[] Chi1, int[] Chi2, int[] Chi3)> GenerateSuggestions(int[] cards)
        {
            try
            {
                var suggestions = _engine.GenerateSuggestions(cards);
                return suggestions.Select(s => (s.Chi1, s.Chi2, s.Chi3)).ToList();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi MauBinhSuggestionHandler: {ex.Message}", UIManager.LogLevel.Error);
                return new List<(int[] Chi1, int[] Chi2, int[] Chi3)>();
            }
        }

        /// <summary>
        /// Generate team suggestions
        /// </summary>
        public List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)> GenerateTeamSuggestions(Dictionary<string, int[]> userCards)
        {
            try
            {
                // Use TeamStrategyEngine for team suggestions
                var teamEngine = new TeamStrategyEngine(_uiManager);
                var users = userCards.Select((kvp, index) => new TeamStrategyEngine.UserInfo
                {
                    Username = kvp.Key,
                    Cards = kvp.Value,
                    Index = index
                }).ToList();

                var result = teamEngine.CalculateTeamStrategy(users);

                // Convert to legacy format - use best suggestions for each user
                var teamSuggestions = new List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)>();
                foreach (var user in users)
                {
                    // Use MauBinhEngine to get best suggestion for each user
                    var suggestions = _engine.GenerateSuggestions(user.Cards);
                    if (suggestions.Any())
                    {
                        var best = suggestions.First();
                        teamSuggestions.Add((user.Username, best.Chi1, best.Chi2, best.Chi3));
                    }
                }

                return teamSuggestions;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GenerateTeamSuggestions: {ex.Message}", UIManager.LogLevel.Error);
                return new List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)>();
            }
        }

        /// <summary>
        /// Generate team suggestions with chicken cards (overload)
        /// </summary>
        public List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)> GenerateTeamSuggestions(Dictionary<string, int[]> userCards, int[] chickenCards)
        {
            // For now, ignore chicken cards and use regular team suggestions
            return GenerateTeamSuggestions(userCards);
        }

        /// <summary>
        /// Analyze cards and recommend strategy
        /// </summary>
        public string AnalyzeCards(int[] cards)
        {
            try
            {
                var suggestions = _engine.GenerateSuggestions(cards);
                if (suggestions.Any())
                {
                    var best = suggestions.First();
                    return $"Gợi ý tốt nhất: {best.Description}";
                }
                return "Không tìm thấy gợi ý phù hợp";
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi AnalyzeCards: {ex.Message}", UIManager.LogLevel.Error);
                return "Lỗi phân tích bài";
            }
        }

        /// <summary>
        /// Check if cards have special combinations
        /// </summary>
        public bool HasSpecialCombination(int[] cards)
        {
            try
            {
                var cardInfos = cards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();

                // Check for special combinations
                if (CardUtilityMaubinh.HasDragonStraightFlush(cardInfos)) return true;
                if (CardUtilityMaubinh.HasDragonStraight(cardInfos)) return true;
                if (CardUtilityMaubinh.HasSameSuit(cardInfos)) return true;
                if (CardUtilityMaubinh.HasSixPairs(cardInfos)) return true;
                if (CardUtilityMaubinh.HasThreeFlushes(cardInfos)) return true;

                return false;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi HasSpecialCombination: {ex.Message}", UIManager.LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// Get suggestion description
        /// </summary>
        public string GetSuggestionDescription(int[] chi1, int[] chi2, int[] chi3)
        {
            try
            {
                var chi1Type = CardUtilityMaubinh.EvaluateHand(chi1);
                var chi2Type = CardUtilityMaubinh.EvaluateHand(chi2);
                var chi3Type = CardUtilityMaubinh.EvaluateHand(chi3);

                return $"Chi1: {chi1Type}, Chi2: {chi2Type}, Chi3: {chi3Type}";
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GetSuggestionDescription: {ex.Message}", UIManager.LogLevel.Error);
                return "Không thể mô tả";
            }
        }

        /// <summary>
        /// Validate suggestion follows Mau Binh rules
        /// </summary>
        public bool ValidateSuggestion(int[] chi1, int[] chi2, int[] chi3)
        {
            try
            {
                // Check lengths
                if (chi1.Length != 5 || chi2.Length != 5 || chi3.Length != 3)
                    return false;

                // Check no duplicate cards
                var allCards = chi1.Concat(chi2).Concat(chi3).ToList();
                if (allCards.Count != allCards.Distinct().Count())
                    return false;

                // Check Chi1 > Chi2 > Chi3 rule
                var chi1Strength = CardUtilityMaubinh.GetChiStrength(chi1.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());
                var chi2Strength = CardUtilityMaubinh.GetChiStrength(chi2.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());
                var chi3Strength = CardUtilityMaubinh.GetChiStrength(chi3.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());

                return chi1Strength >= chi2Strength && chi2Strength >= chi3Strength;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi ValidateSuggestion: {ex.Message}", UIManager.LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// Add suggestion (stub for compatibility)
        /// </summary>
        public void AddSuggestion(int[] chi1, int[] chi2, int[] chi3, string description = "")
        {
            // Stub implementation - just log
            _uiManager.AppendLog($"📝 AddSuggestion: {description}", UIManager.LogLevel.Info);
        }

        /// <summary>
        /// Add suggestion with username (overload)
        /// </summary>
        public void AddSuggestion(string username, (int[] Chi1, int[] Chi2, int[] Chi3) suggestion)
        {
            // Stub implementation - just log
            _uiManager.AppendLog($"📝 AddSuggestion for {username}", UIManager.LogLevel.Info);
        }

        /// <summary>
        /// Clear suggestions (stub for compatibility)
        /// </summary>
        public void ClearSuggestions()
        {
            // Stub implementation - just log
            _uiManager.AppendLog("🧹 ClearSuggestions", UIManager.LogLevel.Info);
        }


    }
}
