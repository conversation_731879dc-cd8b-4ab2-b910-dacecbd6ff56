using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using AutoGameBai.Forms;
using AutoGameBai.Gamemaubinh;
using AutoGameBai.Room;

using OpenQA.Selenium;
using WinFormsKeys = System.Windows.Forms.Keys;

namespace AutoGameBai.UI
{
    public class EventHandlers
    {
        private readonly Form _form;
        private readonly GameClientManager _gameClient;
        private readonly UIManager _uiManager;
        private readonly RoomManager _roomManager;
        private readonly UserActionHandler _userActionHandler;
        private readonly MauBinhSuggestionHandler _suggestionHandler;
        private readonly List<string> _selectedUsers;
        private readonly Dictionary<string, CancellationTokenSource> _joinRoomCts;
        private bool isAllSelected = false;

        public EventHandlers(
            Form form,
            GameClientManager gameClient,
            UIManager uiManager,
            RoomManager roomManager,
            UserActionHandler userActionHandler,
            MauBinhSuggestionHandler suggestionHandler,
            List<string> selectedUsers,
            Dictionary<string, CancellationTokenSource> joinRoomCts)
        {
            _form = form ?? throw new ArgumentNullException(nameof(form));
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _roomManager = roomManager ?? throw new ArgumentNullException(nameof(roomManager));
            _userActionHandler = userActionHandler ?? throw new ArgumentNullException(nameof(userActionHandler));
            _suggestionHandler = suggestionHandler ?? throw new ArgumentNullException(nameof(suggestionHandler));
            _selectedUsers = selectedUsers ?? throw new ArgumentNullException(nameof(selectedUsers));
            _joinRoomCts = joinRoomCts ?? throw new ArgumentNullException(nameof(joinRoomCts));
        }

        public void BtnViewLog_Click(object sender, EventArgs e)
        {
            try
            {
                var driverManager = _gameClient?.GetDriverManager();
                if (driverManager == null)
                {
                    _uiManager?.AppendLog("DriverManager không khả dụng.", UIManager.LogLevel.Warning);
                    return;
                }

                var drivers = _gameClient?.GetDrivers();
                if (drivers == null || !drivers.Any())
                {
                    _uiManager?.AppendLog("Không có profile Chrome nào đang mở.", UIManager.LogLevel.Warning);
                    return;
                }

                // Sử dụng method mới để force update kích thước
                driverManager.ForceUpdateAllProfileSizes();

                // Bỏ MessageBox theo yêu cầu
            }
            catch (Exception ex)
            {
                _uiManager?.AppendLog($"Lỗi khi đặt lại kích thước Chrome: {ex.Message}", UIManager.LogLevel.Error);
                // Bỏ MessageBox theo yêu cầu
            }
        }

        public async void BtnSaveConfig_Click(object sender, EventArgs e)
        {
            try
            {
                var startTime = DateTime.Now;
                _uiManager?.AppendLog("Bắt đầu lưu cấu hình", UIManager.LogLevel.Info);

                var txtApiUrl = _form.Controls.Find("txtApiUrl", true).First() as TextBox;
                var numberThuban = _form.Controls.Find("numberThuban", true).First() as TextBox;
                var txtDelaySwitchUser = _form.Controls.Find("txtDelaySwitchUser", true).First() as TextBox;
                var txtAttemptDelay = _form.Controls.Find("txtAttemptDelay", true).First() as TextBox;
                var txtVerChrome = _form.Controls.Find("VerChrome", true).First() as TextBox;

                string apiUrl = txtApiUrl!.Text.Trim();
                string numberThubanValue = numberThuban!.Text.Trim();
                string delaySwitchUserValue = txtDelaySwitchUser!.Text.Trim();
                string attemptDelayValue = txtAttemptDelay!.Text.Trim();
                string verChrome = txtVerChrome!.Text.Trim();

                if (string.IsNullOrEmpty(apiUrl))
                {
                    MessageBoxHelper.ShowMessageBox(_form, "API URL không được để trống!", "Lỗi", MessageBoxIcon.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(verChrome))
                {
                    _uiManager?.AppendLog("VerChrome không được để trống, sử dụng giá trị mặc định: 129.0.6533.73", UIManager.LogLevel.Warning);
                    verChrome = "129.0.6533.73";
                    txtVerChrome.Text = verChrome;
                    MessageBoxHelper.ShowMessageBox(_form, "VerChrome không được để trống, đã sử dụng giá trị mặc định: 129.0.6533.73", "Cảnh báo", MessageBoxIcon.Warning);
                }

                string configContent = $"ApiUrl={apiUrl}\r\n" +
                                      $"NumberThuban={numberThubanValue}\r\n" +
                                      $"DelaySwitchUser={delaySwitchUserValue}\r\n" +
                                      $"AttemptDelay={attemptDelayValue}\r\n" +
                                      $"VerChrome={verChrome}";

                string configFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.txt");
                await File.WriteAllTextAsync(configFile, configContent);

                _gameClient.GetProfileManager().SetApiUrl(apiUrl);

                var endTime = DateTime.Now;
                _uiManager?.AppendLog($"Đã lưu cấu hình vào {configFile}", UIManager.LogLevel.Info);
                _uiManager?.AppendLog($"Lưu cấu hình hoàn tất trong {(endTime - startTime).TotalSeconds:F2} giây", UIManager.LogLevel.Info);

                MessageBoxHelper.ShowMessageBox(_form, "Đã lưu cấu hình thành công!", "Thông báo", MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _uiManager?.AppendLog($"Lỗi khi lưu cấu hình: {ex.Message}", UIManager.LogLevel.Error);
                MessageBoxHelper.ShowMessageBox(_form, $"Lỗi khi lưu cấu hình: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }

        public void BtnAddUser_Click(object sender, EventArgs e)
        {
            try
            {
                var addUserForm = new AddUserForm(_gameClient);
                addUserForm.StartPosition = FormStartPosition.CenterParent;
                if (addUserForm.ShowDialog(_form) == DialogResult.OK)
                {
                    LoadUsersToDataGridView();
                    _uiManager?.AppendLog("Thêm user thành công từ AddUserForm", UIManager.LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                _uiManager?.AppendLog($"Lỗi khi mở AddUserForm: {ex.Message}", UIManager.LogLevel.Error);
                MessageBoxHelper.ShowMessageBox(_form, $"Lỗi khi mở form thêm user: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }

        public void BtnSelectAll_Click(object sender, EventArgs e)
        {
            isAllSelected = !isAllSelected;
            var tabControl = _form.Controls.OfType<TabControl>().First();
            var activeGridView = tabControl.SelectedTab.Name == "tabUsers"
                ? (_form.Controls.Find("dataGridViewUsers", true).First() as DataGridView)
                : (_form.Controls.Find("dataGridViewSunWinUsers", true).First() as DataGridView);

            foreach (DataGridViewRow row in activeGridView!.Rows)
            {
                row.Selected = isAllSelected;
                string? username = row.Tag?.ToString();
                if (username != null)
                {
                    if (isAllSelected && !_selectedUsers.Contains(username))
                    {
                        _selectedUsers.Add(username);
                        row.Cells["ID"].Style.BackColor = Color.FromArgb(0, 120, 215);
                    }
                    else if (!isAllSelected && _selectedUsers.Contains(username))
                    {
                        _selectedUsers.Remove(username);
                        row.Cells["ID"].Style.BackColor = Color.White;
                    }
                }
            }
        }

        public async void BtnOpenWebAll_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedUsers.Count == 0)
                {
                    MessageBoxHelper.ShowMessageBox(_form, "Vui lòng chọn ít nhất một user!", "Thông báo", MessageBoxIcon.Information);
                    return;
                }

                var selectedUsersCopy = new List<string>(_selectedUsers);
                foreach (var username in selectedUsersCopy)
                {
                    if (!_gameClient.GetProfileManager().IsProfileOpen(username))
                    {
                        _uiManager.AppendLog($"Đang mở profile cho {username}...", UIManager.LogLevel.Info, username);
                        await _gameClient.OpenGPMProfileAsync(username);
                        _uiManager.AppendLog($"Đã mở profile cho {username}", UIManager.LogLevel.Info, username);
                        await Task.Delay(1000);
                    }
                }
                LoadUsersToDataGridView();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi mở web cho tất cả: {ex.Message}", UIManager.LogLevel.Error);
                MessageBoxHelper.ShowMessageBox(_form, $"Lỗi khi mở web cho tất cả: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }

        public async void BtnGetEmptyTable_Click(object sender, EventArgs e)
        {
            try
            {
                var numberThuban = _form.Controls.Find("numberThuban", true).First() as TextBox;
                var comboBoxRooms = _form.Controls.Find("comboBoxRooms", true).First() as ComboBox;
                var txtAttemptDelay = _form.Controls.Find("txtAttemptDelay", true).First() as TextBox;
                var btnGetEmptyTable = sender as Button;

                // Tạo dummy TextBox cho txtDelaySwitchUser với giá trị mặc định
                var dummyDelaySwitchUser = new TextBox { Text = "1000" };

                await _roomManager.GetEmptyTableAsync(
                    numberThuban,
                    comboBoxRooms,
                    dummyDelaySwitchUser,
                    txtAttemptDelay,
                    text => btnGetEmptyTable!.Text = text,
                    LoadUsersToDataGridView);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi lấy bàn trống: {ex.Message}", UIManager.LogLevel.Error);
                MessageBoxHelper.ShowMessageBox(_form, $"Lỗi khi lấy bàn trống: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }

        public async void BtnJumpTable_Click(object sender, EventArgs e)
        {
            try
            {
                var numberThuban = _form.Controls.Find("numberThuban", true).First() as TextBox;
                var comboBoxRooms = _form.Controls.Find("comboBoxRooms", true).First() as ComboBox;
                var txtAttemptDelay = _form.Controls.Find("txtAttemptDelay", true).First() as TextBox;

                // Tạo dummy TextBox cho txtDelaySwitchUser với giá trị mặc định
                var dummyDelaySwitchUser = new TextBox { Text = "1000" };
                var btnJumpTable = sender as Button;

                if (_roomManager.IsJumpTableRunning)
                {
                    _roomManager.CancelJumpTable();
                    btnJumpTable!.Text = "Nhảy Bàn";
                    return;
                }

                if (_selectedUsers.Count == 0)
                {
                    MessageBoxHelper.ShowMessageBox(_form, "Vui lòng chọn ít nhất một user để nhảy bàn!", "Thông báo", MessageBoxIcon.Information);
                    return;
                }

                if (string.IsNullOrEmpty(_roomManager.MainUserWithSeatZero))
                {
                    MessageBoxHelper.ShowMessageBox(_form, "Chưa có user chính ngồi ở vị trí 0. Vui lòng lấy bàn trống trước!", "Thông báo", MessageBoxIcon.Information);
                    return;
                }

                await _roomManager.JumpTableAsync(
                    numberThuban,
                    comboBoxRooms,
                    dummyDelaySwitchUser,
                    txtAttemptDelay,
                    text => btnJumpTable!.Text = text,
                    LoadUsersToDataGridView);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi nhảy bàn: {ex.Message}", UIManager.LogLevel.Error);
                MessageBoxHelper.ShowMessageBox(_form, $"Lỗi khi nhảy bàn: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }

        public async void BtnShowSuggestions_Click(object sender, EventArgs e)
        {
            try
            {
                // Lấy game đã chọn từ GameClientManager
                string selectedGame = _gameClient.GetSelectedGameName() ?? "Mậu Binh";
                _uiManager.AppendLog($"BtnShowSuggestions_Click: Hiển thị form gợi ý cho {selectedGame}", UIManager.LogLevel.Debug);

                var loadingForm = new Form
                {
                    Text = "Đang tải...",
                    Size = new Size(300, 100),
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    StartPosition = FormStartPosition.CenterScreen,
                    MaximizeBox = false,
                    MinimizeBox = false,
                    ControlBox = false
                };

                var loadingLabel = new Label
                {
                    Text = $"Đang tải form gợi ý {selectedGame}...",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 10, FontStyle.Bold)
                };

                loadingForm.Controls.Add(loadingLabel);
                loadingForm.Show(_form);

                try
                {
                    if (selectedGame == "Mậu Binh")
                    {
                        // Đóng PhomSuggestionForm nếu đang mở
                        if (_gameClient.PhomSuggestionForm != null && !_gameClient.PhomSuggestionForm.IsDisposed)
                        {
                            _gameClient.PhomSuggestionForm.Close();
                            _gameClient.PhomSuggestionForm = null;
                        }

                        if (_gameClient.MauBinhSuggestionForm == null || _gameClient.MauBinhSuggestionForm.IsDisposed)
                        {
                            await Task.Run(() =>
                            {
                                _form.Invoke((MethodInvoker)delegate
                                {
                                    _gameClient.MauBinhSuggestionForm = new Gamemaubinh.MauBinhSuggestionForm(_gameClient, _uiManager, _suggestionHandler);
                                    _gameClient.MauBinhSuggestionForm.FormClosed += (s, args) => _gameClient.MauBinhSuggestionForm = null;
                                });
                            });
                        }

                        var suggestionForm = _gameClient.MauBinhSuggestionForm;
                        if (!suggestionForm.Visible)
                        {
                            suggestionForm.Show();
                        }
                        else
                        {
                            suggestionForm.BringToFront();
                        }
                    }
                    else if (selectedGame == "Phỏm")
                    {
                        // Đóng các form khác nếu đang mở
                        if (_gameClient.MauBinhSuggestionForm != null && !_gameClient.MauBinhSuggestionForm.IsDisposed)
                        {
                            _gameClient.MauBinhSuggestionForm.Close();
                            _gameClient.MauBinhSuggestionForm = null;
                        }
                        if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                        {
                            _gameClient.TienLenSuggestionForm.Close();
                            _gameClient.TienLenSuggestionForm = null;
                        }

                        if (_gameClient.PhomSuggestionForm == null || _gameClient.PhomSuggestionForm.IsDisposed)
                        {
                            var phomCardManager = new Gamephom.PhomCardManager(_uiManager, _gameClient);
                            await Task.Run(() =>
                            {
                                _form.Invoke((MethodInvoker)delegate
                                {
                                    _gameClient.PhomSuggestionForm = new Gamephom.PhomSuggestionForm(_gameClient, phomCardManager);
                                    _gameClient.PhomSuggestionForm.FormClosed += (s, args) => _gameClient.PhomSuggestionForm = null;
                                });
                            });
                        }

                        var suggestionForm = _gameClient.PhomSuggestionForm;
                        if (!suggestionForm.Visible)
                        {
                            suggestionForm.Show();
                        }
                        else
                        {
                            suggestionForm.BringToFront();
                        }
                    }
                    else if (selectedGame == "Tiến Lên")
                    {
                        // Đóng các form khác nếu đang mở
                        if (_gameClient.MauBinhSuggestionForm != null && !_gameClient.MauBinhSuggestionForm.IsDisposed)
                        {
                            _gameClient.MauBinhSuggestionForm.Close();
                            _gameClient.MauBinhSuggestionForm = null;
                        }
                        if (_gameClient.PhomSuggestionForm != null && !_gameClient.PhomSuggestionForm.IsDisposed)
                        {
                            _gameClient.PhomSuggestionForm.Close();
                            _gameClient.PhomSuggestionForm = null;
                        }

                        if (_gameClient.TienLenSuggestionForm == null || _gameClient.TienLenSuggestionForm.IsDisposed)
                        {
                            await Task.Run(() =>
                            {
                                _form.Invoke((MethodInvoker)delegate
                                {
                                    _gameClient.TienLenSuggestionForm = new GameTienLen.TienLenSuggestionForm(_uiManager, _gameClient);
                                    _gameClient.TienLenSuggestionForm.FormClosed += (s, args) => _gameClient.TienLenSuggestionForm = null;
                                });
                            });
                        }

                        var suggestionForm = _gameClient.TienLenSuggestionForm;
                        if (!suggestionForm.Visible)
                        {
                            suggestionForm.Show();
                        }
                        else
                        {
                            suggestionForm.BringToFront();
                        }
                    }
                    else
                    {
                        MessageBoxHelper.ShowMessageBox(_form, $"Chức năng gợi ý cho {selectedGame} chưa được hỗ trợ!", "Thông báo", MessageBoxIcon.Information);
                    }
                }
                finally
                {
                    loadingForm.Close();
                    loadingForm.Dispose();
                }
            }
            catch (Exception ex)
            {
                _uiManager?.AppendLog($"Lỗi khi hiển thị form gợi ý: {ex.Message}", UIManager.LogLevel.Error);
                MessageBoxHelper.ShowMessageBox(_form, $"Lỗi khi hiển thị form gợi ý: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }



        public void RdoHitClub_CheckedChanged(object sender, EventArgs e)
        {
            var rdoHitClub = sender as RadioButton;
            var tabControl = _form.Controls.OfType<TabControl>().First();
            var tabUsers = tabControl.TabPages["tabUsers"];
            if (rdoHitClub!.Checked)
            {
                _gameClient.SetSelectedGame("HitClub");
                tabControl.SelectedTab = tabUsers;
                _uiManager.AppendLog("Đã chọn game: HitClub", UIManager.LogLevel.Info);
            }
        }

        public void RdoSunWin_CheckedChanged(object sender, EventArgs e)
        {
            var rdoSunWin = sender as RadioButton;
            var tabControl = _form.Controls.OfType<TabControl>().First();
            var tabSunWinUsers = tabControl.TabPages["tabSunWinUsers"];
            if (rdoSunWin!.Checked)
            {
                _gameClient.SetSelectedGame("SunWin");
                tabControl.SelectedTab = tabSunWinUsers;
                _uiManager.AppendLog("Đã chọn game: SunWin", UIManager.LogLevel.Info);
            }
        }

        public void TabControl_Selecting(object sender, TabControlCancelEventArgs e)
        {
            var rdoHitClub = _form.Controls.Find("rdoHitClub", true).First() as RadioButton;
            var rdoSunWin = _form.Controls.Find("rdoSunWin", true).First() as RadioButton;
            if (e.TabPage.Name == "tabUsers" && !rdoHitClub!.Checked)
            {
                e.Cancel = true;
                MessageBoxHelper.ShowMessageBox(_form, "Vui lòng chọn Hit Club để xem profile Hit Club!", "Lỗi", MessageBoxIcon.Error);
            }
            else if (e.TabPage.Name == "tabSunWinUsers" && !rdoSunWin!.Checked)
            {
                e.Cancel = true;
                MessageBoxHelper.ShowMessageBox(_form, "Vui lòng chọn Sun Win để xem profile Sun Win!", "Lỗi", MessageBoxIcon.Error);
            }
        }

        public void Form_Click(object sender, EventArgs e)
        {
            _selectedUsers.Clear();
            isAllSelected = false;

            var dataGridViewUsers = _form.Controls.Find("dataGridViewUsers", true).FirstOrDefault() as DataGridView;
            if (dataGridViewUsers != null)
            {
                foreach (DataGridViewRow row in dataGridViewUsers.Rows)
                {
                    row.Selected = false;
                    row.Cells["ID"].Style.BackColor = Color.White;
                }
                dataGridViewUsers.ClearSelection();
            }

            var dataGridViewSunWinUsers = _form.Controls.Find("dataGridViewSunWinUsers", true).FirstOrDefault() as DataGridView;
            if (dataGridViewSunWinUsers != null)
            {
                foreach (DataGridViewRow row in dataGridViewSunWinUsers.Rows)
                {
                    row.Selected = false;
                    row.Cells["ID"].Style.BackColor = Color.White;
                }
                dataGridViewSunWinUsers.ClearSelection();
            }
        }

        public void Form1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Control && e.KeyCode == WinFormsKeys.A)
            {
                var tabControl = _form.Controls.OfType<TabControl>().First();
                DataGridView? activeGridView = null;

                if (tabControl.SelectedTab.Name == "tabUsers")
                {
                    var foundControl = _form.Controls.Find("dataGridViewUsers", true).FirstOrDefault();
                    activeGridView = foundControl as DataGridView;
                }
                else if (tabControl.SelectedTab.Name == "tabSunWinUsers")
                {
                    var foundControl = _form.Controls.Find("dataGridViewSunWinUsers", true).FirstOrDefault();
                    activeGridView = foundControl as DataGridView;
                }

                if (activeGridView != null)
                {
                    foreach (DataGridViewRow row in activeGridView.Rows)
                    {
                        row.Selected = true;
                        string? username = row.Tag?.ToString();
                        if (username != null && !_selectedUsers.Contains(username))
                        {
                            _selectedUsers.Add(username);
                            row.Cells["ID"].Style.BackColor = Color.FromArgb(0, 120, 215);
                        }
                    }
                }
                e.Handled = true;
            }

        }

        public void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                _uiManager?.AppendLog("Bắt đầu đóng Form1", UIManager.LogLevel.Debug);

                foreach (var cts in _joinRoomCts.Values)
                {
                    try { cts.Cancel(); } catch { }
                }

                try
                {
                    if (_gameClient?.MauBinhSuggestionForm != null && !_gameClient.MauBinhSuggestionForm.IsDisposed)
                    {
                        _gameClient.MauBinhSuggestionForm.Close();
                        _gameClient.MauBinhSuggestionForm.Dispose();
                        _gameClient.MauBinhSuggestionForm = null;
                    }
                }
                catch { }

                try
                {
                    if (_gameClient?.PhomSuggestionForm != null && !_gameClient.PhomSuggestionForm.IsDisposed)
                    {
                        _gameClient.PhomSuggestionForm.Close();
                        _gameClient.PhomSuggestionForm.Dispose();
                        _gameClient.PhomSuggestionForm = null;
                    }
                }
                catch { }

                try
                {
                    if (_gameClient?.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                    {
                        _gameClient.TienLenSuggestionForm.Close();
                        _gameClient.TienLenSuggestionForm.Dispose();
                        _gameClient.TienLenSuggestionForm = null;
                    }
                }
                catch { }

                try
                {
                    IEnumerable<IWebDriver> drivers = _gameClient?.GetDrivers()?.Values ?? Enumerable.Empty<IWebDriver>();
                    Parallel.ForEach(drivers, driver =>
                    {
                        try
                        {
                            driver.Quit();
                            _uiManager?.AppendLog($"Đã đóng driver cho {driver}", UIManager.LogLevel.Info);
                        }
                        catch { }
                    });
                    _gameClient?.GetDrivers()?.Clear();

                    // Gentle termination to reduce antivirus false positives
                    var chromedriverProcesses = System.Diagnostics.Process.GetProcessesByName("chromedriver");
                    foreach (var process in chromedriverProcesses)
                    {
                        try
                        {
                            // Try graceful close first
                            if (!process.HasExited)
                            {
                                process.CloseMainWindow();
                                if (process.WaitForExit(2000)) // Wait 2 seconds
                                {
                                    _uiManager?.AppendLog($"Gracefully closed chromedriver.exe (PID: {process.Id})", UIManager.LogLevel.Info);
                                    continue;
                                }
                            }

                            // Only use Kill() as last resort
                            if (!process.HasExited)
                            {
                                process.Kill();
                                process.WaitForExit(1000);
                                _uiManager?.AppendLog($"Force terminated chromedriver.exe (PID: {process.Id})", UIManager.LogLevel.Info);
                            }
                        }
                        catch { }
                    }
                }
                catch { }

                try { _gameClient?.GetMauBinhCardManager()?.Dispose(); } catch { }



                _uiManager?.AppendLog("Đã đóng Form1 thành công", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager?.AppendLog($"Lỗi khi đóng Form1: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        public async void DataGridViewUsers_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0) return;
            var dataGridViewUsers = _form.Controls.Find("dataGridViewUsers", true).First() as DataGridView;
            var row = dataGridViewUsers!.Rows[e.RowIndex];
            try
            {
                if (!_gameClient.GetProfileManager().IsGPMLoginRunning())
                {
                    MessageBoxHelper.ShowMessageBox(_form, "GPM-Login chưa chạy. Vui lòng mở GPM-Login trước!", "Lỗi", MessageBoxIcon.Error);
                    return;
                }
                await _userActionHandler.HandleCellContentClick(e, row, _form.Controls.Find("comboBoxRooms", true).First() as ComboBox, _form.Controls.Find("numberThuban", true).First() as TextBox);
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowMessageBox(_form, $"Lỗi khi xử lý hành động trên cột Web: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }

        public async void DataGridViewSunWinUsers_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0) return;
            var dataGridViewSunWinUsers = _form.Controls.Find("dataGridViewSunWinUsers", true).First() as DataGridView;
            var row = dataGridViewSunWinUsers!.Rows[e.RowIndex];
            try
            {
                if (!_gameClient.GetProfileManager().IsGPMLoginRunning())
                {
                    MessageBoxHelper.ShowMessageBox(_form, "GPM-Login chưa chạy. Vui lòng mở GPM-Login trước!", "Lỗi", MessageBoxIcon.Error);
                    return;
                }
                await _userActionHandler.HandleCellContentClick(e, row, _form.Controls.Find("comboBoxRooms", true).First() as ComboBox, _form.Controls.Find("numberThuban", true).First() as TextBox);
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowMessageBox(_form, $"Lỗi khi xử lý hành động trên cột Web: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }

        public void DataGridViewUsers_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            // Placeholder for future implementation
        }

        public void DataGridViewSunWinUsers_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            // Placeholder for future implementation
        }

        public void DataGridViewUsers_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0) return;
            var dataGridViewUsers = _form.Controls.Find("dataGridViewUsers", true).FirstOrDefault() as DataGridView;
            if (dataGridViewUsers == null) return;

            var row = dataGridViewUsers.Rows[e.RowIndex];
            string? username = row.Tag?.ToString();
            if (string.IsNullOrEmpty(username)) return;

            bool isCtrlPressed = (Control.ModifierKeys & WinFormsKeys.Control) == WinFormsKeys.Control;

            if (!isCtrlPressed)
            {
                _selectedUsers.Clear();
                foreach (DataGridViewRow r in dataGridViewUsers.Rows)
                {
                    r.Cells["ID"].Style.BackColor = Color.White;
                }

                // Chỉ clear SunWin nếu control tồn tại
                var dataGridViewSunWinUsers = _form.Controls.Find("dataGridViewSunWinUsers", true).FirstOrDefault() as DataGridView;
                if (dataGridViewSunWinUsers != null)
                {
                    foreach (DataGridViewRow r in dataGridViewSunWinUsers.Rows)
                    {
                        r.Cells["ID"].Style.BackColor = Color.White;
                    }
                }
            }

            if (_selectedUsers.Contains(username))
            {
                _selectedUsers.Remove(username);
                row.Cells["ID"].Style.BackColor = Color.White;
            }
            else
            {
                _selectedUsers.Add(username);
                row.Cells["ID"].Style.BackColor = Color.FromArgb(0, 120, 215);
            }

            dataGridViewUsers.ClearSelection();

            // Chỉ clear SunWin nếu control tồn tại
            var dataGridViewSunWinUsersForClear = _form.Controls.Find("dataGridViewSunWinUsers", true).FirstOrDefault() as DataGridView;
            if (dataGridViewSunWinUsersForClear != null)
            {
                dataGridViewSunWinUsersForClear.ClearSelection();
            }

            foreach (DataGridViewRow r in dataGridViewUsers.Rows)
            {
                string? rowUsername = r.Tag?.ToString();
                if (rowUsername != null && _selectedUsers.Contains(rowUsername))
                {
                    r.Cells["ID"].Style.BackColor = Color.FromArgb(0, 120, 215);
                }
            }

            if (dataGridViewSunWinUsersForClear != null)
            {
                foreach (DataGridViewRow r in dataGridViewSunWinUsersForClear.Rows)
                {
                    string? rowUsername = r.Tag?.ToString();
                    if (rowUsername != null && _selectedUsers.Contains(rowUsername))
                    {
                        r.Cells["ID"].Style.BackColor = Color.FromArgb(0, 120, 215);
                    }
                }
            }
        }

        public void DataGridViewSunWinUsers_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0) return;
            var dataGridViewSunWinUsers = _form.Controls.Find("dataGridViewSunWinUsers", true).FirstOrDefault() as DataGridView;
            if (dataGridViewSunWinUsers == null) return;

            var row = dataGridViewSunWinUsers.Rows[e.RowIndex];
            string? username = row.Tag?.ToString();
            if (string.IsNullOrEmpty(username)) return;

            bool isCtrlPressed = (Control.ModifierKeys & WinFormsKeys.Control) == WinFormsKeys.Control;

            if (!isCtrlPressed)
            {
                _selectedUsers.Clear();

                // Chỉ clear HitClub nếu control tồn tại
                var dataGridViewUsers = _form.Controls.Find("dataGridViewUsers", true).FirstOrDefault() as DataGridView;
                if (dataGridViewUsers != null)
                {
                    foreach (DataGridViewRow r in dataGridViewUsers.Rows)
                    {
                        r.Cells["ID"].Style.BackColor = Color.White;
                    }
                }

                foreach (DataGridViewRow r in dataGridViewSunWinUsers.Rows)
                {
                    r.Cells["ID"].Style.BackColor = Color.White;
                }
            }

            if (_selectedUsers.Contains(username))
            {
                _selectedUsers.Remove(username);
                row.Cells["ID"].Style.BackColor = Color.White;
            }
            else
            {
                _selectedUsers.Add(username);
                row.Cells["ID"].Style.BackColor = Color.FromArgb(0, 120, 215);
            }

            // Chỉ clear HitClub nếu control tồn tại
            var dataGridViewUsersForClear = _form.Controls.Find("dataGridViewUsers", true).FirstOrDefault() as DataGridView;
            if (dataGridViewUsersForClear != null)
            {
                dataGridViewUsersForClear.ClearSelection();
            }

            dataGridViewSunWinUsers.ClearSelection();

            if (dataGridViewUsersForClear != null)
            {
                foreach (DataGridViewRow r in dataGridViewUsersForClear.Rows)
                {
                    string? rowUsername = r.Tag?.ToString();
                    if (rowUsername != null && _selectedUsers.Contains(rowUsername))
                    {
                        r.Cells["ID"].Style.BackColor = Color.FromArgb(0, 120, 215);
                    }
                }
            }

            foreach (DataGridViewRow r in dataGridViewSunWinUsers.Rows)
            {
                string? rowUsername = r.Tag?.ToString();
                if (rowUsername != null && _selectedUsers.Contains(rowUsername))
                {
                    r.Cells["ID"].Style.BackColor = Color.FromArgb(0, 120, 215);
                }
            }
        }

        public void openwsk_Click(object sender, EventArgs e)
        {
            try
            {
                _uiManager?.AppendLog("openwsk_Click: Mở WebSocket", UIManager.LogLevel.Debug);
                MessageBoxHelper.ShowMessageBox(_form, "Chức năng mở WebSocket chưa được triển khai!", "Thông báo", MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _uiManager?.AppendLog($"Lỗi khi mở WebSocket: {ex.Message}", UIManager.LogLevel.Error);
                MessageBoxHelper.ShowMessageBox(_form, $"Lỗi khi mở WebSocket: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }

        private void LoadUsersToDataGridView()
        {
            var form1 = _form as Form1;
            form1?.LoadUsersToDataGridView();
        }


    }
}
