using System;
using System.Collections.Generic;
using System.Linq;

namespace AutoGameBai.Gamemaubinh
{
    /// <summary>
    /// Thuật toán Mau Binh tối ưu theo logic mới
    /// 1. <PERSON><PERSON><PERSON> tra trường hợp đặc biệt (tới trắng)
    /// 2. <PERSON><PERSON> tích tổ hợp mạnh Chi 1 & Chi 3
    /// 3. <PERSON><PERSON>o gợi ý hợp lệ (Chi1 > Chi2 > Chi3)
    /// 4. Hiển thị tối đa 6 gợi ý
    /// </summary>
    /// <summary>
    /// Helper class để thay thế tuple types
    /// </summary>
    public class ChiCombination
    {
        public List<CardUtilityMaubinh.CardInfo> Cards { get; set; }
        public string Type { get; set; }
        public double Score { get; set; }
        public int Priority { get; set; }

        public ChiCombination(List<CardUtilityMaubinh.CardInfo> cards, string type, double score, int priority = 0)
        {
            Cards = cards;
            Type = type;
            Score = score;
            Priority = priority;
        }
    }

    public class OptimizedMauBinhEngine
    {
        private readonly UIManager _uiManager;

        public class OptimizedSuggestion
        {
            public int[] Chi1 { get; set; } = new int[5];
            public int[] Chi2 { get; set; } = new int[5];
            public int[] Chi3 { get; set; } = new int[3];
            public double TotalScore { get; set; }
            public string Description { get; set; } = "";
            public string Chi1Type { get; set; } = "";
            public string Chi2Type { get; set; } = "";
            public string Chi3Type { get; set; } = "";
            public bool IsSpecialCase { get; set; }

            // Thêm properties bị thiếu
            public double Score { get; set; }
            public string Strategy { get; set; } = "";
        }

        public OptimizedMauBinhEngine(UIManager uiManager)
        {
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
        }

        /// <summary>
        /// Thuật toán mới theo yêu cầu: Kiểm tra đặc biệt tới trắng + Chi1/Chi2/Chi3 ưu tiên
        /// </summary>
        public List<OptimizedSuggestion> GenerateOptimizedSuggestions(int[] cards)
        {
            if (cards == null || cards.Length != 13)
            {
                _uiManager.AppendLog("❌ Số lượng bài không hợp lệ", UIManager.LogLevel.Error);
                return new List<OptimizedSuggestion>();
            }

            _uiManager.AppendLog("🚀 Bắt đầu thuật toán Mậu Binh theo yêu cầu mới", UIManager.LogLevel.Info);

            try
            {
                var cardInfos = cards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();
                var allSuggestions = new List<OptimizedSuggestion>();

                // BƯỚC 1: KIỂM TRA ĐẶC BIỆT TỚI TRẮNG
                _uiManager.AppendLog("🏆 Kiểm tra đặc biệt tới trắng", UIManager.LogLevel.Info);
                var specialHandType = CardUtilityMaubinh.CheckMauBinhSpecialHands(cardInfos);
                if (specialHandType > 0)
                {
                    var specialSuggestion = CreateSpecialHandSuggestion(cardInfos, specialHandType);
                    if (specialSuggestion != null)
                    {
                        allSuggestions.Add(specialSuggestion);
                        _uiManager.AppendLog($"✅ Phát hiện bài đặc biệt: {GetSpecialHandName(specialHandType)}", UIManager.LogLevel.Info);
                        return allSuggestions; // Trả về ngay nếu có bài đặc biệt
                    }
                }

                // BƯỚC 2: GÁN CHI 1 TRƯỚC (6 cases) - lấy tối đa 3 gợi ý
                _uiManager.AppendLog("🥇 Gán Chi 1 trước: Thùng phá sảnh, Tứ quý, Cù lũ, Thùng, Sảnh, Xám", UIManager.LogLevel.Info);
                var chi1FirstSuggestions = GenerateStrategy_Chi1First_NewLogic(cardInfos);
                var validChi1First = chi1FirstSuggestions.Where(IsValidSuggestion).Take(3).ToList();
                allSuggestions.AddRange(validChi1First);
                _uiManager.AppendLog($"✅ Chi1 ưu tiên: {validChi1First.Count}/3 gợi ý hợp lệ", UIManager.LogLevel.Info);

                // BƯỚC 3: GÁN CHI 2 TRƯỚC (8 cases) - lấy tối đa 3 gợi ý
                _uiManager.AppendLog("🥈 Gán Chi 2 trước: Thùng phá sảnh, Tứ quý, Cù lũ, Thùng, Sảnh, Xám, Thú, Đôi", UIManager.LogLevel.Info);
                var chi2FirstSuggestions = GenerateStrategy_Chi2First_NewLogic(cardInfos);
                var validChi2First = chi2FirstSuggestions.Where(IsValidSuggestion).Take(3).ToList();
                allSuggestions.AddRange(validChi2First);
                _uiManager.AppendLog($"✅ Chi2 ưu tiên: {validChi2First.Count}/3 gợi ý hợp lệ", UIManager.LogLevel.Info);

                // BƯỚC 4: GÁN CHI 3 TRƯỚC (2 cases) - lấy tối đa 2 gợi ý
                _uiManager.AppendLog("🥉 Gán Chi 3 trước: Xám, Đôi", UIManager.LogLevel.Info);
                var chi3FirstSuggestions = GenerateStrategy_Chi3First_NewLogic(cardInfos);
                var validChi3First = chi3FirstSuggestions.Where(IsValidSuggestion).Take(2).ToList();
                allSuggestions.AddRange(validChi3First);
                _uiManager.AppendLog($"✅ Chi3 ưu tiên: {validChi3First.Count}/2 gợi ý hợp lệ", UIManager.LogLevel.Info);

                // BƯỚC 5: Loại bỏ trùng lặp và sắp xếp theo điểm cao nhất
                var uniqueSuggestions = RemoveDuplicateSuggestions(allSuggestions);
                var finalSuggestions = uniqueSuggestions
                    .OrderByDescending(s => s.TotalScore)
                    .Take(8) // Tối đa 8 gợi ý (3+3+2)
                    .ToList();

                _uiManager.AppendLog($"🎯 Hoàn thành: {finalSuggestions.Count} gợi ý theo logic mới (Chi1:{validChi1First.Count}, Chi2:{validChi2First.Count}, Chi3:{validChi3First.Count})", UIManager.LogLevel.Info);

                // BƯỚC 6: FALLBACK nếu không có gợi ý nào
                if (finalSuggestions.Count == 0)
                {
                    _uiManager.AppendLog("⚠️ Logic mới không tạo được gợi ý, fallback sang thuật toán cũ", UIManager.LogLevel.Warning);
                    return GenerateOptimizedSuggestionsOld(cards);
                }

                return finalSuggestions;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi thuật toán mới: {ex.Message}", UIManager.LogLevel.Error);
                _uiManager.AppendLog("🔄 Fallback sang gợi ý đơn giản (thuật toán cũ bị disable)", UIManager.LogLevel.Warning);

                // QUICK FIX: Tạo gợi ý đơn giản thay vì dùng thuật toán cũ có lỗi
                return CreateSimpleFallbackSuggestions(cards);
            }
        }

        /// <summary>
        /// Thuật toán cũ (backup)
        /// </summary>
        private List<OptimizedSuggestion> GenerateOptimizedSuggestionsOld(int[] cards)
        {
            var cardInfos = cards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();
            var suggestions = new List<OptimizedSuggestion>();

            try
            {
                // 1. KIỂM TRA TRƯỜNG HỢP ĐẶC BIỆT (Tới trắng) - Sử dụng logic game gốc
                var specialHandType = CardUtilityMaubinh.CheckMauBinhSpecialHands(cardInfos);
                if (specialHandType > 0)
                {
                    var specialSuggestion = CreateSpecialHandSuggestion(cardInfos, specialHandType);
                    if (specialSuggestion != null)
                    {
                        suggestions.Add(specialSuggestion);
                        _uiManager.AppendLog($"🏆 Phát hiện bài đặc biệt: {GetSpecialHandName(specialHandType)}", UIManager.LogLevel.Info);
                        return suggestions; // Trả về ngay nếu có bài đặc biệt
                    }
                }

                // 2. TẠO GỢI Ý ĐA DẠNG THEO NHIỀU CHIẾN LƯỢC
                suggestions.AddRange(GenerateDiverseSuggestions(cardInfos));

                // 3. LOẠI BỎ TRÙNG LẶP VÀ LẤY 6 GỢI Ý ĐA DẠNG NHẤT
                suggestions = RemoveDuplicatesAndSelectDiverse(suggestions);

                _uiManager.AppendLog($"✅ Thuật toán cũ tạo thành công {suggestions.Count} gợi ý tối ưu", UIManager.LogLevel.Info);

                return suggestions;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi thuật toán cũ: {ex.Message}", UIManager.LogLevel.Error);
                return new List<OptimizedSuggestion>();
            }
        }

        /// <summary>
        /// 1. KIỂM TRA TRƯỜNG HỢP ĐẶC BIỆT (Tới trắng)
        /// </summary>
        private OptimizedSuggestion? CheckSpecialCases(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            // Rồng thẳng thùng (A-2-3-4-5-6-7-8-9-10-J-Q-K cùng chất)
            if (HasDragonStraightFlush(cardInfos))
            {
                return CreateSpecialCaseSuggestion(cardInfos, "🐉 Rồng Thẳng Thùng", 10000);
            }

            // Rồng thẳng (A-2-3-4-5-6-7-8-9-10-J-Q-K khác chất)
            if (HasDragonStraight(cardInfos))
            {
                return CreateSpecialCaseSuggestion(cardInfos, "🐲 Rồng Thẳng", 5000);
            }

            // 13 lá cùng chất
            if (HasSameSuit(cardInfos))
            {
                return CreateSpecialCaseSuggestion(cardInfos, "🌊 13 Lá Cùng Chất", 3000);
            }

            // 6 đôi + 1 lẻ
            if (HasSixPairs(cardInfos))
            {
                return CreateSpecialCaseSuggestion(cardInfos, "🎰 6 Đôi", 2000);
            }

            // 3 thùng
            if (HasThreeFlushes(cardInfos))
            {
                return CreateSpecialCaseSuggestion(cardInfos, "🌊 3 Thùng", 1500);
            }

            // 3 sảnh
            if (HasThreeStraights(cardInfos))
            {
                return CreateSpecialCaseSuggestion(cardInfos, "📈 3 Sảnh", 1500);
            }

            return null; // Không có trường hợp đặc biệt
        }

        /// <summary>
        /// 2. PHÂN TÍCH TỔ HỢP MẠNH CHO CHI 1 (5 lá)
        /// </summary>
        private List<ChiCombination> AnalyzeChi1Combinations(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var combinations = new List<ChiCombination>();

            try
            {
                // Tối ưu: Ưu tiên các combinations có khả năng tạo bài mạnh
                var priorityCombinations = GetPriorityChi1Combinations(cardInfos);

                foreach (var combo in priorityCombinations.Take(150)) // Giới hạn để tránh quá chậm
                {
                    // Sử dụng logic game gốc để tính điểm chính xác
                    var gameScore = CardUtilityMaubinh.GetGameOriginalScore(combo);
                    var handType = CardUtilityMaubinh.EvaluateHand(combo.Select(c => c.Id).ToArray());
                    var strength = CardUtilityMaubinh.GetChiStrength(combo);

                    string typeName = GetHandTypeNameFromGameScore(gameScore, handType);
                    double finalScore = Math.Max(gameScore, strength); // Lấy điểm cao hơn

                    combinations.Add(new ChiCombination(combo, typeName, finalScore));
                }

                _uiManager.AppendLog($"📊 Chi1: Phân tích {combinations.Count} combinations với logic game gốc", UIManager.LogLevel.Debug);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi phân tích Chi1: {ex.Message}", UIManager.LogLevel.Error);

                // Fallback: sử dụng logic cũ
                var allChi1Combos = GetCombinations(cardInfos, 5);
                foreach (var combo in allChi1Combos.Take(100))
                {
                    var handType = CardUtilityMaubinh.EvaluateHand(combo.Select(c => c.Id).ToArray());
                    var strength = CardUtilityMaubinh.GetChiStrength(combo);
                    string typeName = "";
                    double score = 0;

                    switch (handType)
                    {
                        case CardUtilityMaubinh.HandType.StraightFlush:
                            typeName = "🔥 Thùng Phá Sảnh";
                            score = 8000 + strength;
                            break;
                        case CardUtilityMaubinh.HandType.FourOfAKind:
                            typeName = "💎 Tứ Quý";
                            score = 7000 + strength;
                            break;
                        case CardUtilityMaubinh.HandType.FullHouse:
                            typeName = "🏠 Cù Lũ";
                            score = 6000 + strength;
                            break;
                        case CardUtilityMaubinh.HandType.Flush:
                            typeName = "🌊 Thùng";
                            score = 5000 + strength;
                            break;
                        case CardUtilityMaubinh.HandType.Straight:
                            typeName = "📈 Sảnh";
                            score = 4000 + strength;
                            break;
                        case CardUtilityMaubinh.HandType.ThreeOfAKind:
                            typeName = "🎯 Xám";
                            score = 3000 + strength;
                            break;
                        case CardUtilityMaubinh.HandType.TwoPair:
                            typeName = "👥 Thú";
                            score = 2000 + strength;
                            break;
                        case CardUtilityMaubinh.HandType.OnePair:
                            typeName = "💫 Đôi";
                            score = 1000 + strength;
                            break;
                        default:
                            typeName = "🃏 Mậu Thầu";
                            score = strength;
                            break;
                    }

                    combinations.Add(new ChiCombination(combo, typeName, score));
                }
            }

            // Sắp xếp theo điểm số giảm dần
            return combinations.OrderByDescending(c => c.Score).ToList();
        }

        /// <summary>
        /// 2. PHÂN TÍCH TỔ HỢP MẠNH CHO CHI 2 (5 lá) - ƯU TIÊN TRÁNH MẬU THẦU
        /// </summary>
        private List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)> AnalyzeChi2Combinations(List<CardUtilityMaubinh.CardInfo> remainingCards)
        {
            var combinations = new List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)>();

            // Tạo tất cả combinations 5 lá
            var allChi2Combos = GetCombinations(remainingCards, 5);

            foreach (var combo in allChi2Combos)
            {
                var handType = CardUtilityMaubinh.EvaluateHand(combo.Select(c => c.Id).ToArray());
                var strength = CardUtilityMaubinh.GetChiStrength(combo);
                string typeName = "";
                double score = 0;

                switch (handType)
                {
                    case CardUtilityMaubinh.HandType.StraightFlush:
                        typeName = "🔥 Thùng Phá Sảnh";
                        score = 8000 + strength;
                        break;
                    case CardUtilityMaubinh.HandType.FourOfAKind:
                        typeName = "💎 Tứ Quý";
                        score = 7000 + strength;
                        break;
                    case CardUtilityMaubinh.HandType.FullHouse:
                        typeName = "🏠 Cù Lũ";
                        score = 6000 + strength;
                        break;
                    case CardUtilityMaubinh.HandType.Flush:
                        typeName = "🌊 Thùng";
                        score = 5000 + strength;
                        break;
                    case CardUtilityMaubinh.HandType.Straight:
                        typeName = "📈 Sảnh";
                        score = 4000 + strength;
                        break;
                    case CardUtilityMaubinh.HandType.ThreeOfAKind:
                        typeName = "🎯 Xám";
                        score = 3000 + strength;
                        break;
                    case CardUtilityMaubinh.HandType.TwoPair:
                        typeName = "👥 Thú";
                        score = 2000 + strength;
                        break;
                    case CardUtilityMaubinh.HandType.OnePair:
                        typeName = "💫 Đôi";
                        score = 1000 + strength;
                        break;
                    default:
                        typeName = "🃏 Mậu Thầu";
                        // GIẢM ĐIỂM MẬU THẦU CHI 2 để ưu tiên tránh
                        score = strength * 0.1; // Giảm mạnh điểm mậu thầu
                        break;
                }

                combinations.Add((combo, typeName, score));
            }

            // Sắp xếp theo điểm số giảm dần - mậu thầu sẽ có điểm thấp nhất
            var sortedCombinations = combinations.OrderByDescending(c => c.Score).ToList();

            // Lọc ưu tiên: Lấy tối đa 30% mậu thầu
            var nonHighCard = sortedCombinations.Where(c => !c.Type.Contains("🃏")).ToList();
            var highCard = sortedCombinations.Where(c => c.Type.Contains("🃏")).ToList();

            var result = new List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)>();
            result.AddRange(nonHighCard); // Thêm tất cả non-mậu thầu trước
            result.AddRange(highCard.Take(Math.Max(1, highCard.Count / 3))); // Chỉ lấy 30% mậu thầu

            _uiManager.AppendLog($"📊 Chi2: {nonHighCard.Count} non-mậu thầu, {highCard.Count} mậu thầu → Lấy {result.Count} combinations", UIManager.LogLevel.Debug);

            return result;
        }

        /// <summary>
        /// 3. PHÂN TÍCH TỔ HỢP MẠNH CHO CHI 3 (3 lá) - ƯU TIÊN 3 LÁ LỚN NHẤT CHO MẬU THẦU
        /// </summary>
        private List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)> AnalyzeChi3Combinations(List<CardUtilityMaubinh.CardInfo> remainingCards)
        {
            var combinations = new List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)>();

            // Tạo tất cả combinations 3 lá
            var allChi3Combos = GetCombinations(remainingCards, 3);

            foreach (var combo in allChi3Combos)
            {
                var handType = CardUtilityMaubinh.EvaluateHand(combo.Select(c => c.Id).ToArray());
                var strength = CardUtilityMaubinh.GetChiStrength(combo);
                string typeName = "";
                double score = 0;

                switch (handType)
                {
                    case CardUtilityMaubinh.HandType.ThreeOfAKind:
                        typeName = "🔥 Xám";
                        score = 3000 + strength;
                        break;
                    case CardUtilityMaubinh.HandType.OnePair:
                        typeName = "💎 Đôi";
                        score = 1000 + strength;
                        break;
                    default:
                        typeName = "🃏 Mậu Thầu";
                        // Đối với mậu thầu, sắp xếp lại để lấy 3 lá lớn nhất
                        var sortedCombo = combo.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();
                        strength = CardUtilityMaubinh.GetChiStrength(sortedCombo);
                        score = strength;
                        combinations.Add((sortedCombo, typeName, score)); // Thêm combo đã sắp xếp
                        continue; // Skip thêm combo gốc
                }

                combinations.Add((combo, typeName, score));
            }

            // Sắp xếp theo điểm số giảm dần, ưu tiên Xám > Đôi > Mậu thầu (3 lá lớn nhất)
            return combinations.OrderByDescending(c => c.Score).ToList();
        }

        /// <summary>
        /// Tạo combinations của n lá từ danh sách cards
        /// </summary>
        private IEnumerable<List<CardUtilityMaubinh.CardInfo>> GetCombinations(List<CardUtilityMaubinh.CardInfo> cards, int count)
        {
            if (count == 0) yield return new List<CardUtilityMaubinh.CardInfo>();
            else if (cards.Count >= count)
            {
                var first = cards[0];
                var rest = cards.Skip(1).ToList();

                foreach (var combination in GetCombinations(rest, count - 1))
                {
                    yield return new List<CardUtilityMaubinh.CardInfo> { first }.Concat(combination).ToList();
                }

                foreach (var combination in GetCombinations(rest, count))
                {
                    yield return combination;
                }
            }
        }

        /// <summary>
        /// Kiểm tra tính hợp lệ: Chi1 > Chi2 > Chi3 (sử dụng GetChiStrength chính xác)
        /// </summary>
        private bool IsValidMauBinh(List<CardUtilityMaubinh.CardInfo> chi1,
                                   List<CardUtilityMaubinh.CardInfo> chi2,
                                   List<CardUtilityMaubinh.CardInfo> chi3)
        {
            if (chi1.Count != 5 || chi2.Count != 5 || chi3.Count != 3) return false;

            var chi1Strength = CardUtilityMaubinh.GetChiStrength(chi1);
            var chi2Strength = CardUtilityMaubinh.GetChiStrength(chi2);
            var chi3Strength = CardUtilityMaubinh.GetChiStrength(chi3);

            bool isValid = chi1Strength >= chi2Strength && chi2Strength >= chi3Strength;

            if (!isValid)
            {
                _uiManager.AppendLog($"🔍 Validation failed: Chi1({chi1Strength:F0}) ≥ Chi2({chi2Strength:F0}) ≥ Chi3({chi3Strength:F0})", UIManager.LogLevel.Debug);
            }

            return isValid;
        }

        /// <summary>
        /// Tạo suggestion từ các tổ hợp
        /// </summary>
        private OptimizedSuggestion CreateSuggestion(
            (List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score) chi1Combo,
            List<CardUtilityMaubinh.CardInfo> chi2Cards,
            (List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score) chi3Combo)
        {
            var chi2Type = GetHandTypeName(chi2Cards);
            var chi2Score = CardUtilityMaubinh.GetChiStrength(chi2Cards);

            // Tính tổng điểm với trọng số: Chi3 × 3.0 + Chi1 × 2.0 + Chi2 × 1.0
            var totalScore = chi3Combo.Score * 3.0 + chi1Combo.Score * 2.0 + chi2Score * 1.0;

            return new OptimizedSuggestion
            {
                Chi1 = chi1Combo.Cards.Select(c => c.Id).ToArray(),
                Chi2 = chi2Cards.Select(c => c.Id).ToArray(),
                Chi3 = chi3Combo.Cards.Select(c => c.Id).ToArray(),
                TotalScore = totalScore,
                Description = $"{chi1Combo.Type} + {chi3Combo.Type}",
                Chi1Type = chi1Combo.Type,
                Chi2Type = chi2Type,
                Chi3Type = chi3Combo.Type,
                IsSpecialCase = false
            };
        }

        /// <summary>
        /// Lấy tên loại bài
        /// </summary>
        private string GetHandTypeName(List<CardUtilityMaubinh.CardInfo> cards)
        {
            var handType = CardUtilityMaubinh.EvaluateHand(cards.Select(c => c.Id).ToArray());

            return handType switch
            {
                CardUtilityMaubinh.HandType.StraightFlush => "🔥 Thùng Phá Sảnh",
                CardUtilityMaubinh.HandType.FourOfAKind => "💎 Tứ Quý",
                CardUtilityMaubinh.HandType.FullHouse => "🏠 Cù Lũ",
                CardUtilityMaubinh.HandType.Flush => "🌊 Thùng",
                CardUtilityMaubinh.HandType.Straight => "📈 Sảnh",
                CardUtilityMaubinh.HandType.ThreeOfAKind => "🎯 Xám",
                CardUtilityMaubinh.HandType.TwoPair => "👥 Thú",
                CardUtilityMaubinh.HandType.OnePair => "💫 Đôi",
                _ => "🃏 Mậu Thầu"
            };
        }

        /// <summary>
        /// Tạo suggestion cho trường hợp đặc biệt
        /// </summary>
        private OptimizedSuggestion CreateSpecialCaseSuggestion(List<CardUtilityMaubinh.CardInfo> cardInfos, string description, double score)
        {
            // Sắp xếp bài theo rank giảm dần
            var sortedCards = cardInfos.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();

            return new OptimizedSuggestion
            {
                Chi1 = sortedCards.Take(5).Select(c => c.Id).ToArray(),
                Chi2 = sortedCards.Skip(5).Take(5).Select(c => c.Id).ToArray(),
                Chi3 = sortedCards.Skip(10).Take(3).Select(c => c.Id).ToArray(),
                TotalScore = score,
                Description = description,
                Chi1Type = description,
                Chi2Type = description,
                Chi3Type = description,
                IsSpecialCase = true
            };
        }

        /// <summary>
        /// Tạo gợi ý đa dạng theo nhiều chiến lược
        /// </summary>
        private List<OptimizedSuggestion> GenerateDiverseSuggestions(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();

            // Kiểm tra trường hợp đặc biệt: Có tứ quý
            var fourOfAKindSuggestions = GenerateSpecialFourOfAKindSuggestions(cardInfos);
            if (fourOfAKindSuggestions.Any())
            {
                suggestions.AddRange(fourOfAKindSuggestions);
                _uiManager.AppendLog($"🎰 Tìm thấy {fourOfAKindSuggestions.Count} gợi ý tứ quý đặc biệt", UIManager.LogLevel.Info);
            }

            // Chiến lược 1: Ưu tiên Chi 1 mạnh nhất
            suggestions.AddRange(GenerateStrategy_StrongestChi1(cardInfos));

            // Chiến lược 2: Ưu tiên Chi 3 mạnh nhất
            suggestions.AddRange(GenerateStrategy_StrongestChi3(cardInfos));

            // Chiến lược 3: Cân bằng tổng điểm
            suggestions.AddRange(GenerateStrategy_Balanced(cardInfos));

            // Chiến lược 4: Tránh mậu thầu
            suggestions.AddRange(GenerateStrategy_AvoidHighCard(cardInfos));

            // Chiến lược 5: Tối ưu theo loại bài
            suggestions.AddRange(GenerateStrategy_ByHandType(cardInfos));

            return suggestions;
        }

        /// <summary>
        /// Tạo gợi ý đặc biệt cho trường hợp có tứ quý - TRÁNH CHI2 VÀ CHI3 ĐỀU MẬU THẦU
        /// </summary>
        private List<OptimizedSuggestion> GenerateSpecialFourOfAKindSuggestions(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();

            try
            {
                // Tìm tứ quý
                var rankGroups = cardInfos.GroupBy(c => c.Rank).Where(g => g.Count() >= 4).ToList();
                if (!rankGroups.Any()) return suggestions;

                var fourOfAKindRank = rankGroups.First().Key;
                var fourOfAKindCards = cardInfos.Where(c => c.Rank == fourOfAKindRank).Take(4).ToList();
                var remainingCards = cardInfos.Except(fourOfAKindCards).ToList();

                _uiManager.AppendLog($"🎰 Tìm thấy tứ quý {fourOfAKindRank}, còn lại {remainingCards.Count} lá", UIManager.LogLevel.Info);

                // CHIẾN LƯỢC 1: Tứ quý ở Chi1 + tìm đôi cho Chi3 (ưu tiên cao nhất)
                suggestions.AddRange(CreateFourOfAKindChi1WithPairChi3(fourOfAKindCards, remainingCards, fourOfAKindRank));

                // CHIẾN LƯỢC 2: Xám ở Chi2 + đôi ở Chi3 (ưu tiên thứ 2)
                suggestions.AddRange(CreateThreeOfAKindChi2WithPairChi3(fourOfAKindCards, remainingCards, fourOfAKindRank));

                // CHIẾN LƯỢC 3: Tìm thùng/sảnh cho Chi1 + xám ở Chi2 (nếu có thể)
                suggestions.AddRange(CreateFlushStraightChi1WithThreeOfAKindChi2(fourOfAKindCards, remainingCards, fourOfAKindRank));

                // CHIẾN LƯỢC 4: Đôi ở Chi2 + đôi ở Chi3 (nếu có đủ đôi)
                suggestions.AddRange(CreateDoublePairStrategy(fourOfAKindCards, remainingCards, fourOfAKindRank));
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo gợi ý tứ quý: {ex.Message}", UIManager.LogLevel.Error);
            }

            return suggestions;
        }

        /// <summary>
        /// Chiến lược 1: Tứ quý ở Chi1 + đôi ở Chi3
        /// </summary>
        private List<OptimizedSuggestion> CreateFourOfAKindChi1WithPairChi3(
            List<CardUtilityMaubinh.CardInfo> fourOfAKindCards,
            List<CardUtilityMaubinh.CardInfo> remainingCards,
            int fourOfAKindRank)
        {
            var suggestions = new List<OptimizedSuggestion>();

            // Tìm đôi trong 9 lá còn lại
            var pairGroups = remainingCards.GroupBy(c => c.Rank).Where(g => g.Count() >= 2).ToList();

            foreach (var pairGroup in pairGroups.OrderByDescending(g => CardUtilityMaubinh.GetCardValue(g.Key)))
            {
                var pairRank = pairGroup.Key;
                var pairCards = pairGroup.Take(2).ToList();
                var remainingAfterPair = remainingCards.Except(pairCards).ToList();

                // Lấy lá cao nhất để ghép với tứ quý
                var highestCard = remainingAfterPair.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).First();
                var chi1Cards = fourOfAKindCards.Concat(new[] { highestCard }).ToList();
                var remainingForChi23 = remainingAfterPair.Except(new[] { highestCard }).ToList();

                // Chi3: Đôi + 1 lá cao nhất
                var highestForChi3 = remainingForChi23.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).First();
                var chi3Cards = pairCards.Concat(new[] { highestForChi3 }).ToList();
                var chi2Cards = remainingForChi23.Except(new[] { highestForChi3 }).ToList();

                if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Cards, chi2Cards, chi3Cards))
                {
                    var chi2Type = GetHandTypeName(chi2Cards);

                    // Chỉ chấp nhận nếu Chi2 KHÔNG phải mậu thầu hoặc Chi3 có đôi
                    if (!chi2Type.Contains("Mậu Thầu") || chi3Cards.GroupBy(c => c.Rank).Any(g => g.Count() >= 2))
                    {
                        var suggestion = new OptimizedSuggestion
                        {
                            Chi1 = chi1Cards.Select(c => c.Id).ToArray(),
                            Chi2 = chi2Cards.Select(c => c.Id).ToArray(),
                            Chi3 = chi3Cards.Select(c => c.Id).ToArray(),
                            TotalScore = 30000 + CardUtilityMaubinh.GetCardValue(pairRank) * 100, // Điểm cao nhất
                            Description = $"🎰 Tứ Quý {CardUtilityMaubinh.GetRankName(fourOfAKindRank)} + {chi2Type} + Đôi {CardUtilityMaubinh.GetRankName(pairRank)}",
                            Chi1Type = "💎 Tứ Quý",
                            Chi2Type = chi2Type,
                            Chi3Type = "💎 Đôi",
                            IsSpecialCase = false
                        };
                        suggestions.Add(suggestion);
                        _uiManager.AppendLog($"✅ Chiến lược 1: {suggestion.Description}", UIManager.LogLevel.Info);
                        break; // Chỉ lấy đôi mạnh nhất
                    }
                }
            }

            return suggestions;
        }

        /// <summary>
        /// Chiến lược 2: Xám ở Chi2 + đôi ở Chi3
        /// </summary>
        private List<OptimizedSuggestion> CreateThreeOfAKindChi2WithPairChi3(
            List<CardUtilityMaubinh.CardInfo> fourOfAKindCards,
            List<CardUtilityMaubinh.CardInfo> remainingCards,
            int fourOfAKindRank)
        {
            var suggestions = new List<OptimizedSuggestion>();

            // Sử dụng 3 con từ tứ quý cho Chi2
            var threeOfAKindCards = fourOfAKindCards.Take(3).ToList();
            var availableCards = remainingCards.Concat(fourOfAKindCards.Skip(3)).ToList();

            // Tìm đôi cho Chi3
            var pairGroups = availableCards.GroupBy(c => c.Rank).Where(g => g.Count() >= 2).ToList();

            foreach (var pairGroup in pairGroups.OrderByDescending(g => CardUtilityMaubinh.GetCardValue(g.Key)))
            {
                var pairRank = pairGroup.Key;
                var pairCards = pairGroup.Take(2).ToList();
                var remainingAfterPair = availableCards.Except(pairCards).ToList();

                // Chi3: Đôi + 1 lá cao nhất
                var highestForChi3 = remainingAfterPair.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).First();
                var chi3Cards = pairCards.Concat(new[] { highestForChi3 }).ToList();

                // Chi2: Xám + 2 lá còn lại
                var remainingForChi2 = remainingAfterPair.Except(new[] { highestForChi3 }).Take(2).ToList();
                var chi2Cards = threeOfAKindCards.Concat(remainingForChi2).ToList();

                // Chi1: 5 lá còn lại
                var chi1Cards = remainingAfterPair.Except(new[] { highestForChi3 }).Skip(2).ToList();

                if (chi1Cards.Count == 5 && chi2Cards.Count == 5 && IsValidMauBinh(chi1Cards, chi2Cards, chi3Cards))
                {
                    var chi1Type = GetHandTypeName(chi1Cards);

                    var suggestion = new OptimizedSuggestion
                    {
                        Chi1 = chi1Cards.Select(c => c.Id).ToArray(),
                        Chi2 = chi2Cards.Select(c => c.Id).ToArray(),
                        Chi3 = chi3Cards.Select(c => c.Id).ToArray(),
                        TotalScore = 25000 + CardUtilityMaubinh.GetCardValue(pairRank) * 100,
                        Description = $"🎯 {chi1Type} + Xám {CardUtilityMaubinh.GetRankName(fourOfAKindRank)} + Đôi {CardUtilityMaubinh.GetRankName(pairRank)}",
                        Chi1Type = chi1Type,
                        Chi2Type = "🎯 Xám",
                        Chi3Type = "💎 Đôi",
                        IsSpecialCase = false
                    };
                    suggestions.Add(suggestion);
                    _uiManager.AppendLog($"✅ Chiến lược 2: {suggestion.Description}", UIManager.LogLevel.Info);
                    break; // Chỉ lấy đôi mạnh nhất
                }
            }

            return suggestions;
        }

        /// <summary>
        /// Chiến lược 3: Tìm thùng/sảnh cho Chi1 + xám ở Chi2
        /// </summary>
        private List<OptimizedSuggestion> CreateFlushStraightChi1WithThreeOfAKindChi2(
            List<CardUtilityMaubinh.CardInfo> fourOfAKindCards,
            List<CardUtilityMaubinh.CardInfo> remainingCards,
            int fourOfAKindRank)
        {
            var suggestions = new List<OptimizedSuggestion>();

            // Sử dụng 3 con từ tứ quý cho Chi2
            var threeOfAKindCards = fourOfAKindCards.Take(3).ToList();
            var availableCards = remainingCards.Concat(fourOfAKindCards.Skip(3)).ToList();

            // Thử tìm thùng hoặc sảnh cho Chi1
            var chi1Combinations = AnalyzeChi1Combinations(availableCards);
            var flushStraightCombos = chi1Combinations.Where(c =>
                c.Type.Contains("Thùng") || c.Type.Contains("Sảnh")).ToList();

            foreach (var chi1Combo in flushStraightCombos.Take(2))
            {
                var remainingAfterChi1 = availableCards.Except(chi1Combo.Cards).ToList();

                // Chi2: Xám + 2 lá còn lại
                var remainingForChi2 = remainingAfterChi1.Take(2).ToList();
                var chi2Cards = threeOfAKindCards.Concat(remainingForChi2).ToList();

                // Chi3: 3 lá còn lại
                var chi3Cards = remainingAfterChi1.Skip(2).ToList();

                if (chi3Cards.Count == 3 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Cards))
                {
                    var chi3Type = GetHandTypeName(chi3Cards);

                    var suggestion = new OptimizedSuggestion
                    {
                        Chi1 = chi1Combo.Cards.Select(c => c.Id).ToArray(),
                        Chi2 = chi2Cards.Select(c => c.Id).ToArray(),
                        Chi3 = chi3Cards.Select(c => c.Id).ToArray(),
                        TotalScore = 22000 + chi1Combo.Score,
                        Description = $"🌊 {chi1Combo.Type} + Xám {CardUtilityMaubinh.GetRankName(fourOfAKindRank)} + {chi3Type}",
                        Chi1Type = chi1Combo.Type,
                        Chi2Type = "🎯 Xám",
                        Chi3Type = chi3Type,
                        IsSpecialCase = false
                    };
                    suggestions.Add(suggestion);
                    _uiManager.AppendLog($"✅ Chiến lược 3: {suggestion.Description}", UIManager.LogLevel.Info);
                }
            }

            return suggestions;
        }

        /// <summary>
        /// Chiến lược 4: Đôi ở Chi2 + đôi ở Chi3 (nếu có đủ đôi)
        /// </summary>
        private List<OptimizedSuggestion> CreateDoublePairStrategy(
            List<CardUtilityMaubinh.CardInfo> fourOfAKindCards,
            List<CardUtilityMaubinh.CardInfo> remainingCards,
            int fourOfAKindRank)
        {
            var suggestions = new List<OptimizedSuggestion>();

            // Tìm các đôi trong 9 lá còn lại
            var pairGroups = remainingCards.GroupBy(c => c.Rank).Where(g => g.Count() >= 2).ToList();

            if (pairGroups.Count >= 2)
            {
                var sortedPairs = pairGroups.OrderByDescending(g => CardUtilityMaubinh.GetCardValue(g.Key)).ToList();
                var firstPair = sortedPairs[0].Take(2).ToList();
                var secondPair = sortedPairs[1].Take(2).ToList();

                var usedCards = firstPair.Concat(secondPair).ToList();
                var remainingAfterPairs = remainingCards.Except(usedCards).ToList();

                // Chi1: Sử dụng 1 con từ tứ quý + 4 lá cao nhất còn lại
                var highestCards = remainingAfterPairs.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).Take(4).ToList();
                var chi1Cards = fourOfAKindCards.Take(1).Concat(highestCards).ToList();

                // Chi2: Đôi thứ nhất + 3 con từ tứ quý
                var chi2Cards = firstPair.Concat(fourOfAKindCards.Skip(1).Take(3)).ToList();

                // Chi3: Đôi thứ hai + 1 lá còn lại
                var lastCard = remainingAfterPairs.Skip(4).FirstOrDefault();
                if (lastCard != null)
                {
                    var chi3Cards = secondPair.Concat(new[] { lastCard }).ToList();

                    if (IsValidMauBinh(chi1Cards, chi2Cards, chi3Cards))
                    {
                        var chi1Type = GetHandTypeName(chi1Cards);

                        var suggestion = new OptimizedSuggestion
                        {
                            Chi1 = chi1Cards.Select(c => c.Id).ToArray(),
                            Chi2 = chi2Cards.Select(c => c.Id).ToArray(),
                            Chi3 = chi3Cards.Select(c => c.Id).ToArray(),
                            TotalScore = 20000 + CardUtilityMaubinh.GetCardValue(sortedPairs[0].Key) * 100 + CardUtilityMaubinh.GetCardValue(sortedPairs[1].Key) * 10,
                            Description = $"👥 {chi1Type} + Đôi {CardUtilityMaubinh.GetRankName(sortedPairs[0].Key)} + Đôi {CardUtilityMaubinh.GetRankName(sortedPairs[1].Key)}",
                            Chi1Type = chi1Type,
                            Chi2Type = "💫 Đôi",
                            Chi3Type = "💫 Đôi",
                            IsSpecialCase = false
                        };
                        suggestions.Add(suggestion);
                        _uiManager.AppendLog($"✅ Chiến lược 4: {suggestion.Description}", UIManager.LogLevel.Info);
                    }
                }
            }

            return suggestions;
        }

        /// <summary>
        /// Chiến lược 1: Ưu tiên Chi 1 mạnh nhất - TẠO ĐA DẠNG
        /// </summary>
        private List<OptimizedSuggestion> GenerateStrategy_StrongestChi1(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();
            var chi1Combinations = AnalyzeChi1Combinations(cardInfos);

            _uiManager.AppendLog($"🎯 Tìm được {chi1Combinations.Count} Chi1 combinations", UIManager.LogLevel.Info);

            // Lấy các tổ hợp Chi 1 khác nhau theo loại bài
            var diverseChi1 = GetDiverseHandTypes(chi1Combinations, 5); // Tăng từ 3 lên 5

            foreach (var chi1Combo in diverseChi1)
            {
                var remainingCards = cardInfos.Except(chi1Combo.Cards).ToList();
                var chi3Combinations = AnalyzeChi3Combinations(remainingCards);

                // Tạo đa dạng Chi3: Lấy cả mạnh và yếu
                var diverseChi3 = GetDiverseChi3Types(chi3Combinations);

                foreach (var chi3Combo in diverseChi3)
                {
                    var chi2Cards = remainingCards.Except(chi3Combo.Cards).ToList();

                    if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Combo.Cards))
                    {
                        var suggestion = CreateSuggestion(chi1Combo, chi2Cards, chi3Combo);
                        suggestion.Description = $"💪 {chi1Combo.Type} + {GetHandTypeName(chi2Cards)} + {chi3Combo.Type}";
                        suggestions.Add(suggestion);

                        // Chỉ lấy 1 gợi ý cho mỗi combination Chi1-Chi3
                        break;
                    }
                }
            }

            _uiManager.AppendLog($"💪 Strategy_StrongestChi1: Tạo được {suggestions.Count} gợi ý", UIManager.LogLevel.Info);
            return suggestions;
        }

        /// <summary>
        /// Lấy các Chi3 đa dạng: Ưu tiên đôi > mậu thầu cao > mậu thầu thấp
        /// </summary>
        private List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)> GetDiverseChi3Types(
            List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)> chi3Combinations)
        {
            var diverse = new List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)>();

            // 1. Lấy đôi mạnh nhất (nếu có)
            var pairs = chi3Combinations.Where(c => c.Type.Contains("Đôi")).OrderByDescending(c => c.Score).Take(1);
            diverse.AddRange(pairs);

            // 2. Lấy xám mạnh nhất (nếu có)
            var threeOfAKind = chi3Combinations.Where(c => c.Type.Contains("Xám")).OrderByDescending(c => c.Score).Take(1);
            diverse.AddRange(threeOfAKind);

            // 3. Lấy mậu thầu cao nhất
            var highCards = chi3Combinations.Where(c => c.Type.Contains("Mậu Thầu")).OrderByDescending(c => c.Score).Take(1);
            diverse.AddRange(highCards);

            // 4. Lấy mậu thầu thấp nhất (để tạo đa dạng)
            var lowCards = chi3Combinations.Where(c => c.Type.Contains("Mậu Thầu")).OrderBy(c => c.Score).Take(1);
            diverse.AddRange(lowCards);

            return diverse.Distinct().ToList();
        }

        /// <summary>
        /// Chiến lược 2: Ưu tiên Chi 3 mạnh nhất
        /// </summary>
        private List<OptimizedSuggestion> GenerateStrategy_StrongestChi3(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();

            // Tìm tất cả Chi 3 có thể và nhóm theo loại
            var allChi3Combos = GetCombinations(cardInfos, 3).ToList();
            var chi3ByType = new Dictionary<string, List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)>>();

            foreach (var combo in allChi3Combos.Take(100)) // Giới hạn để tránh quá chậm
            {
                var handType = CardUtilityMaubinh.EvaluateHand(combo.Select(c => c.Id).ToArray());
                var strength = CardUtilityMaubinh.GetChiStrength(combo);
                string typeName = GetHandTypeName(combo);
                double score = GetScoreByHandType(handType, strength);

                if (!chi3ByType.ContainsKey(typeName))
                    chi3ByType[typeName] = new List<(List<CardUtilityMaubinh.CardInfo>, string, double)>();

                chi3ByType[typeName].Add((combo, typeName, score));
            }

            // Lấy Chi 3 YẾU NHẤT từ mỗi loại để đảm bảo luật Chi1 > Chi2 > Chi3
            foreach (var typeGroup in chi3ByType)
            {
                var weakestChi3 = typeGroup.Value.OrderBy(c => c.Score).First(); // Lấy yếu nhất thay vì mạnh nhất
                var remainingCards = cardInfos.Except(weakestChi3.Cards).ToList();

                var chi1Combinations = AnalyzeChi1Combinations(remainingCards);
                // Sắp xếp Chi1 theo độ mạnh giảm dần để lấy mạnh nhất trước
                var sortedChi1 = chi1Combinations.OrderByDescending(c => c.Score).ToList();

                foreach (var chi1Combo in sortedChi1.Take(3))
                {
                    var chi2Cards = remainingCards.Except(chi1Combo.Cards).ToList();

                    if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, weakestChi3.Cards))
                    {
                        var suggestion = CreateSuggestion(chi1Combo, chi2Cards, weakestChi3);
                        suggestion.Description = $"🎯 {chi1Combo.Type} + {weakestChi3.Type}";
                        suggestions.Add(suggestion);
                        break;
                    }
                }
            }

            return suggestions;
        }

        /// <summary>
        /// Chiến lược 3: Cân bằng tổng điểm
        /// </summary>
        private List<OptimizedSuggestion> GenerateStrategy_Balanced(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();

            // Tạo combinations cân bằng
            var chi1Combinations = AnalyzeChi1Combinations(cardInfos);

            foreach (var chi1Combo in chi1Combinations.Take(5))
            {
                var remainingCards = cardInfos.Except(chi1Combo.Cards).ToList();
                var chi3Combinations = AnalyzeChi3Combinations(remainingCards);

                foreach (var chi3Combo in chi3Combinations.Take(3))
                {
                    var chi2Cards = remainingCards.Except(chi3Combo.Cards).ToList();

                    if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Combo.Cards))
                    {
                        var chi1Strength = chi1Combo.Score;
                        var chi2Strength = CardUtilityMaubinh.GetChiStrength(chi2Cards);
                        var chi3Strength = chi3Combo.Score;

                        // Tính độ cân bằng (variance thấp = cân bằng)
                        var strengths = new[] { chi1Strength, chi2Strength, chi3Strength };
                        var average = strengths.Average();
                        var variance = strengths.Select(s => Math.Pow(s - average, 2)).Average();

                        // Ưu tiên combinations có variance thấp
                        if (variance < 1000000) // Threshold cho cân bằng
                        {
                            var suggestion = CreateSuggestion(chi1Combo, chi2Cards, chi3Combo);
                            suggestion.Description = $"⚖️ {chi1Combo.Type} + {chi3Combo.Type}";
                            suggestion.TotalScore -= variance * 0.1; // Penalty cho variance cao
                            suggestions.Add(suggestion);
                        }
                    }
                }
            }

            return suggestions.OrderByDescending(s => s.TotalScore).Take(2).ToList();
        }

        /// <summary>
        /// Lấy điểm số theo loại bài
        /// </summary>
        private double GetScoreByHandType(CardUtilityMaubinh.HandType handType, double strength)
        {
            return handType switch
            {
                CardUtilityMaubinh.HandType.StraightFlush => 8000 + strength,
                CardUtilityMaubinh.HandType.FourOfAKind => 7000 + strength,
                CardUtilityMaubinh.HandType.FullHouse => 6000 + strength,
                CardUtilityMaubinh.HandType.Flush => 5000 + strength,
                CardUtilityMaubinh.HandType.Straight => 4000 + strength,
                CardUtilityMaubinh.HandType.ThreeOfAKind => 3000 + strength,
                CardUtilityMaubinh.HandType.TwoPair => 2000 + strength,
                CardUtilityMaubinh.HandType.OnePair => 1000 + strength,
                _ => strength
            };
        }

        /// <summary>
        /// Chiến lược 4: Tránh mậu thầu - MẠNH HÓA LOGIC
        /// </summary>
        private List<OptimizedSuggestion> GenerateStrategy_AvoidHighCard(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();
            var chi1Combinations = AnalyzeChi1Combinations(cardInfos);

            _uiManager.AppendLog("🚫 Chiến lược tránh mậu thầu - ưu tiên đôi/ba cho Chi2+Chi3", UIManager.LogLevel.Info);

            // Ưu tiên Chi 1 có đôi/ba/thùng/sảnh (không phải mậu thầu)
            var strongChi1 = chi1Combinations.Where(c =>
                !c.Type.Contains("🃏") &&
                !c.Type.Contains("Mậu Thầu") &&
                !c.Type.Contains("High Card")).Take(5);

            foreach (var chi1Combo in strongChi1)
            {
                var remainingCards = cardInfos.Except(chi1Combo.Cards).ToList();
                var chi3Combinations = AnalyzeChi3Combinations(remainingCards);

                // Ưu tiên Chi 3 không phải mậu thầu
                var nonHighCardChi3 = chi3Combinations.Where(c => !c.Type.Contains("🃏")).Take(2);
                if (!nonHighCardChi3.Any())
                    nonHighCardChi3 = chi3Combinations.Take(1); // Fallback nếu không có

                foreach (var chi3Combo in nonHighCardChi3)
                {
                    var chi2Cards = remainingCards.Except(chi3Combo.Cards).ToList();

                    if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Combo.Cards))
                    {
                        var chi2Type = GetHandTypeName(chi2Cards);

                        // KIỂM TRA MẠNH: Chi2 không được là mậu thầu
                        if (!chi2Type.Contains("🃏") && !chi2Type.Contains("Mậu Thầu") && !chi2Type.Contains("High Card"))
                        {
                            var suggestion = CreateSuggestion(chi1Combo, chi2Cards, chi3Combo);
                            suggestion.Description = $"🛡️ {chi1Combo.Type} + {chi3Combo.Type}";
                            suggestions.Add(suggestion);
                            _uiManager.AppendLog($"✅ Tránh mậu thầu: {suggestion.Description}", UIManager.LogLevel.Info);
                            break;
                        }
                        else
                        {
                            _uiManager.AppendLog($"❌ Loại bỏ: Chi2 là mậu thầu ({chi2Type})", UIManager.LogLevel.Debug);
                        }
                    }
                }
            }

            return suggestions;
        }

        /// <summary>
        /// Chiến lược 5: Tối ưu theo loại bài
        /// </summary>
        private List<OptimizedSuggestion> GenerateStrategy_ByHandType(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();

            // Tìm các loại bài đặc biệt
            var rankGroups = cardInfos.GroupBy(c => c.Rank).ToList();
            var suitGroups = cardInfos.GroupBy(c => c.Suit).ToList();

            // Nếu có nhiều đôi, tạo gợi ý tối ưu đôi
            var pairs = rankGroups.Where(g => g.Count() >= 2).ToList();
            if (pairs.Count >= 2)
            {
                var pairSuggestion = CreatePairOptimizedSuggestion(cardInfos, pairs);
                if (pairSuggestion != null)
                    suggestions.Add(pairSuggestion);
            }

            // Nếu có thùng, tạo gợi ý tối ưu thùng
            var flushSuit = suitGroups.FirstOrDefault(g => g.Count() >= 5);
            if (flushSuit != null)
            {
                var flushSuggestion = CreateFlushOptimizedSuggestion(cardInfos, flushSuit.ToList());
                if (flushSuggestion != null)
                    suggestions.Add(flushSuggestion);
            }

            return suggestions;
        }

        /// <summary>
        /// Tạo gợi ý tối ưu cho nhiều đôi
        /// </summary>
        private OptimizedSuggestion? CreatePairOptimizedSuggestion(List<CardUtilityMaubinh.CardInfo> cardInfos, List<IGrouping<int, CardUtilityMaubinh.CardInfo>> pairs)
        {
            // Sắp xếp đôi theo giá trị giảm dần
            var sortedPairs = pairs.OrderByDescending(p => CardUtilityMaubinh.GetCardValue(p.Key)).ToList();

            // Đặt đôi lớn nhất ở Chi 3
            var chi3Pair = sortedPairs[0].Take(2).ToList();
            var bestSingle = cardInfos.Except(chi3Pair).OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).First();
            var chi3Cards = chi3Pair.Concat(new[] { bestSingle }).ToList();

            var remainingCards = cardInfos.Except(chi3Cards).ToList();
            var chi1Combinations = AnalyzeChi1Combinations(remainingCards);

            foreach (var chi1Combo in chi1Combinations.Take(3))
            {
                var chi2Cards = remainingCards.Except(chi1Combo.Cards).ToList();

                if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Cards))
                {
                    var suggestion = CreateSuggestion(chi1Combo, chi2Cards, (chi3Cards, "💎 Đôi", 1000));
                    suggestion.Description = $"👥 {chi1Combo.Type} + Đôi {CardUtilityMaubinh.GetRankName(sortedPairs[0].Key)}";
                    return suggestion;
                }
            }

            return null;
        }

        /// <summary>
        /// Tạo gợi ý tối ưu cho thùng
        /// </summary>
        private OptimizedSuggestion? CreateFlushOptimizedSuggestion(List<CardUtilityMaubinh.CardInfo> cardInfos, List<CardUtilityMaubinh.CardInfo> flushCards)
        {
            // Tạo thùng cho Chi 1
            var chi1Flush = flushCards.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).Take(5).ToList();
            var remainingCards = cardInfos.Except(chi1Flush).ToList();
            var chi3Combinations = AnalyzeChi3Combinations(remainingCards);

            foreach (var chi3Combo in chi3Combinations.Take(3))
            {
                var chi2Cards = remainingCards.Except(chi3Combo.Cards).ToList();

                if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Flush, chi2Cards, chi3Combo.Cards))
                {
                    var suggestion = CreateSuggestion((chi1Flush, "🌊 Thùng", 5000), chi2Cards, chi3Combo);
                    suggestion.Description = $"🌊 Thùng + {chi3Combo.Type}";
                    return suggestion;
                }
            }

            return null;
        }

        /// <summary>
        /// Lấy các loại bài đa dạng
        /// </summary>
        private List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)> GetDiverseHandTypes(
            List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)> combinations, int count)
        {
            var diverse = new List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)>();
            var seenTypes = new HashSet<string>();

            foreach (var combo in combinations)
            {
                if (!seenTypes.Contains(combo.Type))
                {
                    diverse.Add(combo);
                    seenTypes.Add(combo.Type);

                    if (diverse.Count >= count)
                        break;
                }
            }

            return diverse;
        }

        /// <summary>
        /// Loại bỏ trùng lặp và chọn gợi ý đa dạng - TRÁNH CHI2 VÀ CHI3 ĐỀU MẬU THẦU
        /// </summary>
        private List<OptimizedSuggestion> RemoveDuplicatesAndSelectDiverse(List<OptimizedSuggestion> suggestions)
        {
            var unique = new List<OptimizedSuggestion>();
            var seenCombinations = new HashSet<string>();

            _uiManager.AppendLog($"🔍 Đang lọc từ {suggestions.Count} gợi ý ban đầu", UIManager.LogLevel.Info);

            // BƯỚC 1: Lọc ra các gợi ý có Chi2 và Chi3 đều mậu thầu
            var goodSuggestions = suggestions.Where(s =>
                !(s.Chi2Type.Contains("Mậu Thầu") && s.Chi3Type.Contains("Mậu Thầu")))
                .OrderByDescending(s => s.TotalScore)
                .ToList();

            var badSuggestions = suggestions.Where(s =>
                s.Chi2Type.Contains("Mậu Thầu") && s.Chi3Type.Contains("Mậu Thầu"))
                .OrderByDescending(s => s.TotalScore)
                .ToList();

            _uiManager.AppendLog($"✅ Gợi ý tốt (không có Chi2+Chi3 mậu thầu): {goodSuggestions.Count}", UIManager.LogLevel.Info);
            _uiManager.AppendLog($"⚠️ Gợi ý kém (Chi2+Chi3 đều mậu thầu): {badSuggestions.Count}", UIManager.LogLevel.Warning);

            // BƯỚC 2: Ưu tiên lấy gợi ý tốt trước
            foreach (var suggestion in goodSuggestions)
            {
                // Tạo key duy nhất cho combination
                var comboKey = string.Join(",", suggestion.Chi1.OrderBy(x => x)) + "|" +
                              string.Join(",", suggestion.Chi2.OrderBy(x => x)) + "|" +
                              string.Join(",", suggestion.Chi3.OrderBy(x => x));

                if (!seenCombinations.Contains(comboKey))
                {
                    seenCombinations.Add(comboKey);
                    unique.Add(suggestion);

                    if (unique.Count >= 6) // Giới hạn 6 gợi ý
                        break;
                }
            }

            // BƯỚC 3: Nếu không đủ 6 gợi ý, tạo gợi ý khẩn cấp tránh Chi2+Chi3 mậu thầu
            if (unique.Count < 6)
            {
                var needed = 6 - unique.Count;
                _uiManager.AppendLog($"🚨 Cần thêm {needed} gợi ý, tạo gợi ý khẩn cấp tránh Chi2+Chi3 mậu thầu", UIManager.LogLevel.Warning);

                var allCards = suggestions.First().Chi1.Concat(suggestions.First().Chi2).Concat(suggestions.First().Chi3).ToArray();
                var emergencySuggestions = GenerateEmergencyNonHighCardSuggestions(allCards);

                foreach (var suggestion in emergencySuggestions.Take(needed))
                {
                    var comboKey = string.Join(",", suggestion.Chi1.OrderBy(x => x)) + "|" +
                                  string.Join(",", suggestion.Chi2.OrderBy(x => x)) + "|" +
                                  string.Join(",", suggestion.Chi3.OrderBy(x => x));

                    if (!seenCombinations.Contains(comboKey))
                    {
                        seenCombinations.Add(comboKey);
                        suggestion.Description = "🚨 " + suggestion.Description + " (Khẩn cấp)";
                        unique.Add(suggestion);

                        if (unique.Count >= 6)
                            break;
                    }
                }

                // Nếu vẫn không đủ, mới lấy từ backup (nhưng ưu tiên thấp)
                if (unique.Count < 6)
                {
                    var stillNeeded = 6 - unique.Count;
                    _uiManager.AppendLog($"⚠️ Vẫn thiếu {stillNeeded} gợi ý, lấy từ backup cuối cùng", UIManager.LogLevel.Warning);

                    foreach (var suggestion in badSuggestions.Take(stillNeeded))
                    {
                        var comboKey = string.Join(",", suggestion.Chi1.OrderBy(x => x)) + "|" +
                                      string.Join(",", suggestion.Chi2.OrderBy(x => x)) + "|" +
                                      string.Join(",", suggestion.Chi3.OrderBy(x => x));

                        if (!seenCombinations.Contains(comboKey))
                        {
                            seenCombinations.Add(comboKey);
                            suggestion.TotalScore *= 0.3; // Giảm 70% điểm
                            suggestion.Description = "⚠️ " + suggestion.Description + " (Backup cuối)";
                            unique.Add(suggestion);

                            if (unique.Count >= 6)
                                break;
                        }
                    }
                }
            }

            // BƯỚC 4: Nếu chỉ có 1 gợi ý duy nhất, tạo thêm các biến thể
            if (unique.Count == 1)
            {
                _uiManager.AppendLog("🔄 Chỉ có 1 gợi ý tối ưu, tạo thêm biến thể", UIManager.LogLevel.Info);
                var variations = GenerateVariations(unique[0], suggestions.First().Chi1.Concat(suggestions.First().Chi2).Concat(suggestions.First().Chi3).ToArray());

                // Lọc biến thể để tránh Chi2+Chi3 đều mậu thầu
                var goodVariations = variations.Where(v =>
                    !(v.Chi2Type.Contains("Mậu Thầu") && v.Chi3Type.Contains("Mậu Thầu"))).ToList();

                unique.AddRange(goodVariations);
            }

            _uiManager.AppendLog($"✅ Đã lọc thành {unique.Count} gợi ý đa dạng (ưu tiên tránh Chi2+Chi3 mậu thầu)", UIManager.LogLevel.Info);
            return unique;
        }

        /// <summary>
        /// Tạo gợi ý khẩn cấp tránh Chi2+Chi3 đều mậu thầu
        /// </summary>
        private List<OptimizedSuggestion> GenerateEmergencyNonHighCardSuggestions(int[] allCards)
        {
            var emergencySuggestions = new List<OptimizedSuggestion>();
            var cardInfos = allCards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();

            try
            {
                _uiManager.AppendLog("🚨 Tạo gợi ý khẩn cấp - ưu tiên tránh Chi2+Chi3 mậu thầu", UIManager.LogLevel.Info);

                // Chiến lược 1: Ưu tiên tạo đôi cho Chi3
                var pairEmergency = CreateEmergencyPairForChi3(cardInfos);
                if (pairEmergency != null) emergencySuggestions.Add(pairEmergency);

                // Chiến lược 2: Ưu tiên tạo ba cho Chi3
                var threeOfKindEmergency = CreateEmergencyThreeOfKindForChi3(cardInfos);
                if (threeOfKindEmergency != null) emergencySuggestions.Add(threeOfKindEmergency);

                // Chiến lược 3: Phân bố đều để tránh Chi2+Chi3 mậu thầu
                var balancedEmergency = CreateEmergencyBalancedDistribution(cardInfos);
                if (balancedEmergency != null) emergencySuggestions.Add(balancedEmergency);

                // Chiến lược 4: Ưu tiên Chi2 có đôi
                var chi2PairEmergency = CreateEmergencyChi2Pair(cardInfos);
                if (chi2PairEmergency != null) emergencySuggestions.Add(chi2PairEmergency);

                // Chiến lược 5: Sử dụng bài thấp cho Chi1 để dành bài tốt cho Chi2+Chi3
                var lowChi1Emergency = CreateEmergencyLowChi1(cardInfos);
                if (lowChi1Emergency != null) emergencySuggestions.Add(lowChi1Emergency);

                _uiManager.AppendLog($"🚨 Đã tạo {emergencySuggestions.Count} gợi ý khẩn cấp", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo gợi ý khẩn cấp: {ex.Message}", UIManager.LogLevel.Error);
            }

            return emergencySuggestions;
        }

        /// <summary>
        /// Tạo gợi ý khẩn cấp với đôi cho Chi3
        /// </summary>
        private OptimizedSuggestion? CreateEmergencyPairForChi3(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            try
            {
                // Tìm đôi tốt nhất cho Chi3
                var rankGroups = cardInfos.GroupBy(c => c.Rank).Where(g => g.Count() >= 2).ToList();

                foreach (var pairGroup in rankGroups.OrderByDescending(g => CardUtilityMaubinh.GetCardValue(g.Key)))
                {
                    var pairCards = pairGroup.Take(2).ToList();
                    var remainingForChi3 = cardInfos.Except(pairCards).OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).Take(1).ToList();
                    var chi3Cards = pairCards.Concat(remainingForChi3).ToList();

                    if (chi3Cards.Count == 3)
                    {
                        var remainingCards = cardInfos.Except(chi3Cards).ToList();

                        // Tìm Chi1 tốt nhất từ 10 lá còn lại
                        var chi1Combinations = AnalyzeChi1Combinations(remainingCards);
                        foreach (var chi1Combo in chi1Combinations.Take(3))
                        {
                            var chi2Cards = remainingCards.Except(chi1Combo.Cards).ToList();

                            if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Cards))
                            {
                                var chi2Type = GetHandTypeName(chi2Cards);

                                // Kiểm tra Chi2 không phải mậu thầu
                                if (!chi2Type.Contains("Mậu Thầu") && !chi2Type.Contains("🃏"))
                                {
                                    var suggestion = CreateSuggestion(chi1Combo, chi2Cards, (chi3Cards, "💎 Đôi", 1000 + CardUtilityMaubinh.GetChiStrength(chi3Cards)));
                                    suggestion.Description = $"💎 {chi1Combo.Type} + Đôi {CardUtilityMaubinh.GetRankName(pairGroup.Key)}";
                                    return suggestion;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo emergency pair cho Chi3: {ex.Message}", UIManager.LogLevel.Error);
            }
            return null;
        }

        /// <summary>
        /// Tạo gợi ý khẩn cấp với ba cho Chi3
        /// </summary>
        private OptimizedSuggestion? CreateEmergencyThreeOfKindForChi3(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            try
            {
                // Tìm ba cho Chi3
                var rankGroups = cardInfos.GroupBy(c => c.Rank).Where(g => g.Count() >= 3).ToList();

                foreach (var threeGroup in rankGroups.OrderByDescending(g => CardUtilityMaubinh.GetCardValue(g.Key)))
                {
                    var threeCards = threeGroup.Take(3).ToList();
                    var remainingCards = cardInfos.Except(threeCards).ToList();

                    var chi1Combinations = AnalyzeChi1Combinations(remainingCards);
                    foreach (var chi1Combo in chi1Combinations.Take(3))
                    {
                        var chi2Cards = remainingCards.Except(chi1Combo.Cards).ToList();

                        if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, threeCards))
                        {
                            var chi2Type = GetHandTypeName(chi2Cards);

                            // Kiểm tra Chi2 không phải mậu thầu
                            if (!chi2Type.Contains("Mậu Thầu") && !chi2Type.Contains("🃏"))
                            {
                                var suggestion = CreateSuggestion(chi1Combo, chi2Cards, (threeCards, "🎯 Ba", 2000 + CardUtilityMaubinh.GetChiStrength(threeCards)));
                                suggestion.Description = $"🎯 {chi1Combo.Type} + Ba {CardUtilityMaubinh.GetRankName(threeGroup.Key)}";
                                return suggestion;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo emergency three of kind cho Chi3: {ex.Message}", UIManager.LogLevel.Error);
            }
            return null;
        }

        /// <summary>
        /// Tạo gợi ý khẩn cấp với phân bố cân bằng
        /// </summary>
        private OptimizedSuggestion? CreateEmergencyBalancedDistribution(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            try
            {
                // Phân bố để tránh Chi2+Chi3 mậu thầu
                var sortedCards = cardInfos.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();

                // Lấy 5 lá cao nhất cho Chi1
                var chi1Cards = sortedCards.Take(5).ToList();
                var remainingCards = sortedCards.Skip(5).ToList();

                // Tìm đôi trong 8 lá còn lại cho Chi3
                var rankGroups = remainingCards.GroupBy(c => c.Rank).Where(g => g.Count() >= 2).ToList();

                if (rankGroups.Any())
                {
                    var bestPair = rankGroups.OrderByDescending(g => CardUtilityMaubinh.GetCardValue(g.Key)).First();
                    var pairCards = bestPair.Take(2).ToList();
                    var remainingForChi3 = remainingCards.Except(pairCards).OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).Take(1).ToList();
                    var chi3Cards = pairCards.Concat(remainingForChi3).ToList();
                    var chi2Cards = remainingCards.Except(chi3Cards).ToList();

                    if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Cards, chi2Cards, chi3Cards))
                    {
                        var chi1Type = GetHandTypeName(chi1Cards);
                        var chi2Type = GetHandTypeName(chi2Cards);

                        var suggestion = CreateSuggestion(
                            (chi1Cards, chi1Type, CardUtilityMaubinh.GetChiStrength(chi1Cards)),
                            chi2Cards,
                            (chi3Cards, "💎 Đôi", 1000 + CardUtilityMaubinh.GetChiStrength(chi3Cards))
                        );
                        suggestion.Description = $"⚖️ {chi1Type} + Đôi {CardUtilityMaubinh.GetRankName(bestPair.Key)}";
                        return suggestion;
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo emergency balanced distribution: {ex.Message}", UIManager.LogLevel.Error);
            }
            return null;
        }

        /// <summary>
        /// Tạo gợi ý khẩn cấp với đôi cho Chi2
        /// </summary>
        private OptimizedSuggestion? CreateEmergencyChi2Pair(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            try
            {
                // Tìm đôi cho Chi2
                var rankGroups = cardInfos.GroupBy(c => c.Rank).Where(g => g.Count() >= 2).ToList();

                foreach (var pairGroup in rankGroups.OrderByDescending(g => CardUtilityMaubinh.GetCardValue(g.Key)))
                {
                    var pairCards = pairGroup.Take(2).ToList();
                    var remainingCards = cardInfos.Except(pairCards).ToList();

                    // Tìm 3 lá khác để tạo Chi2 có đôi
                    var otherCards = remainingCards.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).Take(3).ToList();
                    var chi2Cards = pairCards.Concat(otherCards).ToList();

                    if (chi2Cards.Count == 5)
                    {
                        var remainingForChi1And3 = remainingCards.Except(otherCards).ToList();

                        // Chi1 lấy 5 lá tốt nhất
                        var chi1Cards = remainingForChi1And3.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).Take(5).ToList();
                        var chi3Cards = remainingForChi1And3.Except(chi1Cards).ToList();

                        if (chi3Cards.Count == 3 && IsValidMauBinh(chi1Cards, chi2Cards, chi3Cards))
                        {
                            var chi1Type = GetHandTypeName(chi1Cards);
                            var chi3Type = GetHandTypeName(chi3Cards);

                            var suggestion = CreateSuggestion(
                                (chi1Cards, chi1Type, CardUtilityMaubinh.GetChiStrength(chi1Cards)),
                                chi2Cards,
                                (chi3Cards, chi3Type, CardUtilityMaubinh.GetChiStrength(chi3Cards))
                            );
                            suggestion.Description = $"💎 {chi1Type} + Đôi Chi2 {CardUtilityMaubinh.GetRankName(pairGroup.Key)}";
                            return suggestion;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo emergency Chi2 pair: {ex.Message}", UIManager.LogLevel.Error);
            }
            return null;
        }

        /// <summary>
        /// Tạo gợi ý khẩn cấp với Chi1 thấp
        /// </summary>
        private OptimizedSuggestion? CreateEmergencyLowChi1(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            try
            {
                // Lấy 5 lá thấp nhất cho Chi1
                var sortedCards = cardInfos.OrderBy(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();
                var chi1Cards = sortedCards.Take(5).ToList();
                var remainingCards = cardInfos.Except(chi1Cards).ToList();

                // Tìm đôi trong 8 lá còn lại cho Chi3
                var rankGroups = remainingCards.GroupBy(c => c.Rank).Where(g => g.Count() >= 2).ToList();

                if (rankGroups.Any())
                {
                    var bestPair = rankGroups.OrderByDescending(g => CardUtilityMaubinh.GetCardValue(g.Key)).First();
                    var pairCards = bestPair.Take(2).ToList();
                    var remainingForChi3 = remainingCards.Except(pairCards).OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).Take(1).ToList();
                    var chi3Cards = pairCards.Concat(remainingForChi3).ToList();
                    var chi2Cards = remainingCards.Except(chi3Cards).ToList();

                    if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Cards, chi2Cards, chi3Cards))
                    {
                        var chi1Type = GetHandTypeName(chi1Cards);
                        var chi2Type = GetHandTypeName(chi2Cards);

                        var suggestion = CreateSuggestion(
                            (chi1Cards, chi1Type, CardUtilityMaubinh.GetChiStrength(chi1Cards)),
                            chi2Cards,
                            (chi3Cards, "💎 Đôi", 1000 + CardUtilityMaubinh.GetChiStrength(chi3Cards))
                        );
                        suggestion.Description = $"🛡️ {chi1Type} + Đôi {CardUtilityMaubinh.GetRankName(bestPair.Key)}";
                        return suggestion;
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo emergency low Chi1: {ex.Message}", UIManager.LogLevel.Error);
            }
            return null;
        }

        /// <summary>
        /// Tạo các biến thể từ gợi ý tốt nhất
        /// </summary>
        private List<OptimizedSuggestion> GenerateVariations(OptimizedSuggestion bestSuggestion, int[] allCards)
        {
            var variations = new List<OptimizedSuggestion>();
            var cardInfos = allCards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();

            try
            {
                // Biến thể 1: Ưu tiên Chi 3 mậu thầu cao nhất
                var highCardVariation = CreateHighCardVariation(cardInfos);
                if (highCardVariation != null) variations.Add(highCardVariation);

                // Biến thể 2: Ưu tiên cân bằng điểm số
                var balancedVariation = CreateBalancedVariation(cardInfos);
                if (balancedVariation != null) variations.Add(balancedVariation);

                // Biến thể 3: Ưu tiên đôi cho Chi 3 nếu có
                var pairVariation = CreatePairVariation(cardInfos);
                if (pairVariation != null) variations.Add(pairVariation);

                // Biến thể 4: Ưu tiên Chi 1 thấp hơn nhưng Chi 3 cao hơn
                var conservativeVariation = CreateConservativeVariation(cardInfos);
                if (conservativeVariation != null) variations.Add(conservativeVariation);

                // Biến thể 5: Sắp xếp khác cho cùng loại bài
                var alternativeVariation = CreateAlternativeVariation(cardInfos);
                if (alternativeVariation != null) variations.Add(alternativeVariation);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo biến thể: {ex.Message}", UIManager.LogLevel.Error);
            }

            return variations.Take(5).ToList(); // Tối đa 5 biến thể
        }

        /// <summary>
        /// Biến thể 1: Ưu tiên Chi 3 mậu thầu cao nhất
        /// </summary>
        private OptimizedSuggestion? CreateHighCardVariation(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            try
            {
                // Sắp xếp theo giá trị giảm dần để lấy 3 lá cao nhất cho Chi 3
                var sortedCards = cardInfos.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();
                var chi3Cards = sortedCards.Take(3).ToList();
                var remainingCards = cardInfos.Except(chi3Cards).ToList();

                var chi1Combinations = AnalyzeChi1Combinations(remainingCards);
                foreach (var chi1Combo in chi1Combinations.Take(3))
                {
                    var chi2Cards = remainingCards.Except(chi1Combo.Cards).ToList();

                    if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Cards))
                    {
                        var suggestion = CreateSuggestion(chi1Combo, chi2Cards, (chi3Cards, "🃏 Mậu Thầu Cao", CardUtilityMaubinh.GetChiStrength(chi3Cards)));
                        suggestion.Description = $"🎯 {chi1Combo.Type} + Mậu Thầu Cao";
                        return suggestion;
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo biến thể mậu thầu cao: {ex.Message}", UIManager.LogLevel.Error);
            }
            return null;
        }

        /// <summary>
        /// Biến thể 2: Ưu tiên cân bằng điểm số
        /// </summary>
        private OptimizedSuggestion? CreateBalancedVariation(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            try
            {
                var chi1Combinations = AnalyzeChi1Combinations(cardInfos);

                // Tìm combination có điểm số cân bằng nhất
                foreach (var chi1Combo in chi1Combinations.Take(5))
                {
                    var remainingCards = cardInfos.Except(chi1Combo.Cards).ToList();
                    var chi3Combinations = AnalyzeChi3Combinations(remainingCards);

                    foreach (var chi3Combo in chi3Combinations.Take(3))
                    {
                        var chi2Cards = remainingCards.Except(chi3Combo.Cards).ToList();

                        if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Combo.Cards))
                        {
                            var chi1Strength = chi1Combo.Score;
                            var chi2Strength = CardUtilityMaubinh.GetChiStrength(chi2Cards);
                            var chi3Strength = chi3Combo.Score;

                            // Tính variance
                            var strengths = new[] { chi1Strength, chi2Strength, chi3Strength };
                            var average = strengths.Average();
                            var variance = strengths.Select(s => Math.Pow(s - average, 2)).Average();

                            if (variance < 500000) // Threshold cân bằng
                            {
                                var suggestion = CreateSuggestion(chi1Combo, chi2Cards, chi3Combo);
                                suggestion.Description = $"⚖️ {chi1Combo.Type} + {chi3Combo.Type}";
                                return suggestion;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo biến thể cân bằng: {ex.Message}", UIManager.LogLevel.Error);
            }
            return null;
        }

        /// <summary>
        /// Biến thể 3: Ưu tiên đôi cho Chi 3
        /// </summary>
        private OptimizedSuggestion? CreatePairVariation(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            try
            {
                // Tìm đôi cho Chi 3
                var rankGroups = cardInfos.GroupBy(c => c.Rank).Where(g => g.Count() >= 2).ToList();

                foreach (var pairGroup in rankGroups.OrderByDescending(g => CardUtilityMaubinh.GetCardValue(g.Key)))
                {
                    var pairCards = pairGroup.Take(2).ToList();
                    var remainingForChi3 = cardInfos.Except(pairCards).OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).Take(1).ToList();
                    var chi3Cards = pairCards.Concat(remainingForChi3).ToList();

                    if (chi3Cards.Count == 3)
                    {
                        var remainingCards = cardInfos.Except(chi3Cards).ToList();
                        var chi1Combinations = AnalyzeChi1Combinations(remainingCards);

                        foreach (var chi1Combo in chi1Combinations.Take(3))
                        {
                            var chi2Cards = remainingCards.Except(chi1Combo.Cards).ToList();

                            if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Cards))
                            {
                                var suggestion = CreateSuggestion(chi1Combo, chi2Cards, (chi3Cards, "💎 Đôi", 1000 + CardUtilityMaubinh.GetChiStrength(chi3Cards)));
                                suggestion.Description = $"💎 {chi1Combo.Type} + Đôi {CardUtilityMaubinh.GetRankName(pairGroup.Key)}";
                                return suggestion;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo biến thể đôi: {ex.Message}", UIManager.LogLevel.Error);
            }
            return null;
        }

        /// <summary>
        /// Biến thể 4: Conservative - Chi 1 thấp hơn nhưng Chi 3 cao hơn
        /// </summary>
        private OptimizedSuggestion? CreateConservativeVariation(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            try
            {
                var chi1Combinations = AnalyzeChi1Combinations(cardInfos);

                // Lấy Chi 1 không phải mạnh nhất
                foreach (var chi1Combo in chi1Combinations.Skip(1).Take(3))
                {
                    var remainingCards = cardInfos.Except(chi1Combo.Cards).ToList();
                    var chi3Combinations = AnalyzeChi3Combinations(remainingCards);

                    // Ưu tiên Chi 3 mạnh nhất
                    foreach (var chi3Combo in chi3Combinations.Take(2))
                    {
                        var chi2Cards = remainingCards.Except(chi3Combo.Cards).ToList();

                        if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Combo.Cards))
                        {
                            var suggestion = CreateSuggestion(chi1Combo, chi2Cards, chi3Combo);
                            suggestion.Description = $"🛡️ {chi1Combo.Type} + {chi3Combo.Type}";
                            return suggestion;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo biến thể conservative: {ex.Message}", UIManager.LogLevel.Error);
            }
            return null;
        }

        /// <summary>
        /// Biến thể 5: Sắp xếp khác cho cùng loại bài
        /// </summary>
        private OptimizedSuggestion? CreateAlternativeVariation(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            try
            {
                // Sắp xếp theo suit trước, rank sau để tạo variation khác
                var sortedCards = cardInfos.OrderBy(c => c.Suit).ThenByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();

                var chi1Cards = sortedCards.Take(5).ToList();
                var chi2Cards = sortedCards.Skip(5).Take(5).ToList();
                var chi3Cards = sortedCards.Skip(10).Take(3).ToList();

                if (IsValidMauBinh(chi1Cards, chi2Cards, chi3Cards))
                {
                    var chi1Type = GetHandTypeName(chi1Cards);
                    var chi2Type = GetHandTypeName(chi2Cards);
                    var chi3Type = GetHandTypeName(chi3Cards);

                    var suggestion = CreateSuggestion(
                        (chi1Cards, chi1Type, CardUtilityMaubinh.GetChiStrength(chi1Cards)),
                        chi2Cards,
                        (chi3Cards, chi3Type, CardUtilityMaubinh.GetChiStrength(chi3Cards))
                    );
                    suggestion.Description = $"🔄 {chi1Type} + {chi3Type}";
                    return suggestion;
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo biến thể alternative: {ex.Message}", UIManager.LogLevel.Error);
            }
            return null;
        }

        /// <summary>
        /// Chuyển đổi sang format cũ để tương thích
        /// </summary>
        public List<(int[] Chi1, int[] Chi2, int[] Chi3)> ConvertToLegacyFormat(List<OptimizedSuggestion> suggestions)
        {
            return suggestions.Select(s => (s.Chi1, s.Chi2, s.Chi3)).ToList();
        }

        #region Special Cases Detection

        /// <summary>
        /// Kiểm tra rồng thẳng thùng (A-2-3-4-5-6-7-8-9-10-J-Q-K cùng chất)
        /// </summary>
        private bool HasDragonStraightFlush(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suitGroups = cardInfos.GroupBy(c => c.Suit).ToList();

            foreach (var suitGroup in suitGroups)
            {
                if (suitGroup.Count() == 13)
                {
                    var ranks = suitGroup.Select(c => c.Rank).OrderBy(r => r).ToList();
                    var expectedRanks = new[] { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13 }; // A-2-3-4-5-6-7-8-9-10-J-Q-K

                    return ranks.SequenceEqual(expectedRanks);
                }
            }

            return false;
        }

        /// <summary>
        /// Kiểm tra rồng thẳng (A-2-3-4-5-6-7-8-9-10-J-Q-K khác chất)
        /// </summary>
        private bool HasDragonStraight(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var ranks = cardInfos.Select(c => c.Rank).OrderBy(r => r).ToList();
            var expectedRanks = new[] { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13 }; // A-2-3-4-5-6-7-8-9-10-J-Q-K

            return ranks.SequenceEqual(expectedRanks);
        }

        /// <summary>
        /// Kiểm tra 13 lá cùng chất
        /// </summary>
        private bool HasSameSuit(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suitGroups = cardInfos.GroupBy(c => c.Suit).ToList();
            return suitGroups.Any(g => g.Count() == 13);
        }

        /// <summary>
        /// Kiểm tra 6 đôi + 1 lẻ
        /// </summary>
        private bool HasSixPairs(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var rankGroups = cardInfos.GroupBy(c => c.Rank).ToList();
            var pairCount = rankGroups.Count(g => g.Count() == 2);
            var singleCount = rankGroups.Count(g => g.Count() == 1);

            return pairCount == 6 && singleCount == 1;
        }

        /// <summary>
        /// Kiểm tra 3 thùng (Chi1, Chi2, Chi3 đều thùng)
        /// </summary>
        private bool HasThreeFlushes(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            // Thử tất cả combinations có thể để tạo 3 thùng
            var allChi1Combos = GetCombinations(cardInfos, 5);

            foreach (var chi1 in allChi1Combos.Take(50)) // Giới hạn để tránh quá chậm
            {
                if (IsFlush(chi1))
                {
                    var remaining = cardInfos.Except(chi1).ToList();
                    var allChi2Combos = GetCombinations(remaining, 5);

                    foreach (var chi2 in allChi2Combos.Take(20))
                    {
                        if (IsFlush(chi2))
                        {
                            var chi3 = remaining.Except(chi2).ToList();
                            if (chi3.Count == 3 && IsFlush(chi3))
                            {
                                return true;
                            }
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Kiểm tra 3 sảnh (Chi1, Chi2, Chi3 đều sảnh)
        /// </summary>
        private bool HasThreeStraights(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            // Thử tất cả combinations có thể để tạo 3 sảnh
            var allChi1Combos = GetCombinations(cardInfos, 5);

            foreach (var chi1 in allChi1Combos.Take(50)) // Giới hạn để tránh quá chậm
            {
                if (IsStraight(chi1))
                {
                    var remaining = cardInfos.Except(chi1).ToList();
                    var allChi2Combos = GetCombinations(remaining, 5);

                    foreach (var chi2 in allChi2Combos.Take(20))
                    {
                        if (IsStraight(chi2))
                        {
                            var chi3 = remaining.Except(chi2).ToList();
                            if (chi3.Count == 3 && IsStraight(chi3))
                            {
                                return true;
                            }
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Kiểm tra thùng
        /// </summary>
        private bool IsFlush(List<CardUtilityMaubinh.CardInfo> cards)
        {
            if (cards.Count < 3) return false;
            var firstSuit = cards[0].Suit;
            return cards.All(c => c.Suit == firstSuit);
        }

        /// <summary>
        /// Kiểm tra sảnh
        /// </summary>
        private bool IsStraight(List<CardUtilityMaubinh.CardInfo> cards)
        {
            if (cards.Count < 3) return false;

            var ranks = cards.Select(c => c.Rank).OrderBy(r => r).ToList();

            // Kiểm tra sảnh thường
            for (int i = 1; i < ranks.Count; i++)
            {
                if (ranks[i] != ranks[i - 1] + 1)
                    return false;
            }

            return true;
        }

        #endregion

        #region NEW ALGORITHM METHODS

        /// <summary>
        /// Chiến lược 1: Ưu tiên Chi 1 mạnh nhất trước
        /// </summary>
        private List<OptimizedSuggestion> GenerateStrategy_Chi1First_Enhanced(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();

            try
            {
                var chi1Combinations = AnalyzeChi1Combinations(cardInfos);

                // Lấy top 5 Chi1 mạnh nhất (không phải mậu thầu)
                var strongChi1 = chi1Combinations
                    .Where(c => !c.Type.Contains("🃏") && !c.Type.Contains("Mậu Thầu"))
                    .Take(5)
                    .ToList();

                foreach (var chi1Combo in strongChi1)
                {
                    var remainingCards = cardInfos.Except(chi1Combo.Cards).ToList();
                    var chi3Combinations = AnalyzeChi3Combinations(remainingCards);

                    // Ưu tiên Chi3 có đôi/ba
                    var strongChi3 = chi3Combinations
                        .Where(c => c.Type.Contains("Đôi") || c.Type.Contains("Xám"))
                        .Take(2)
                        .ToList();

                    if (!strongChi3.Any())
                    {
                        // Fallback: lấy Chi3 mậu thầu với 3 lá LỚN NHẤT
                        strongChi3 = GetBestHighCardChi3(remainingCards).Take(1).ToList();
                    }

                    foreach (var chi3Combo in strongChi3)
                    {
                        var chi2Cards = remainingCards.Except(chi3Combo.Cards).ToList();

                        if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Combo.Cards))
                        {
                            var chi2Type = GetHandTypeName(chi2Cards);

                            // Tránh Chi2+Chi3 đều mậu thầu
                            bool chi2IsHighCard = chi2Type.Contains("🃏") || chi2Type.Contains("Mậu Thầu");
                            bool chi3IsHighCard = chi3Combo.Type.Contains("🃏") || chi3Combo.Type.Contains("Mậu Thầu");

                            if (!(chi2IsHighCard && chi3IsHighCard))
                            {
                                var suggestion = CreateSuggestion(chi1Combo, chi2Cards, chi3Combo);
                                suggestion.Description = $"🥇 {chi1Combo.Type} + {chi3Combo.Type}";
                                suggestions.Add(suggestion);
                                _uiManager.AppendLog($"✅ Chi1 First: {suggestion.Description}", UIManager.LogLevel.Debug);
                            }
                        }
                    }

                    if (suggestions.Count >= 3) break; // Tối đa 3 gợi ý
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GenerateStrategy_Chi1First_Enhanced: {ex.Message}", UIManager.LogLevel.Error);
            }

            return suggestions;
        }

        /// <summary>
        /// Chiến lược 2: Ưu tiên Chi 2 mạnh nhất trước
        /// </summary>
        private List<OptimizedSuggestion> GenerateStrategy_Chi2First_Enhanced(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();

            try
            {
                // Sử dụng method AnalyzeChi2Combinations mới để tránh mậu thầu
                var chi2Combinations = AnalyzeChi2Combinations(cardInfos);

                // Lấy top 5 Chi2 mạnh nhất (không phải mậu thầu)
                var strongChi2 = chi2Combinations
                    .Where(c => !c.Type.Contains("🃏") && !c.Type.Contains("Mậu Thầu"))
                    .OrderByDescending(c => c.Score)
                    .Take(5)
                    .ToList();

                foreach (var chi2Combo in strongChi2)
                {
                    var remainingCards = cardInfos.Except(chi2Combo.Cards).ToList();
                    var chi1Combinations = AnalyzeChi1Combinations(remainingCards);
                    var chi3Combinations = AnalyzeChi3Combinations(remainingCards);

                    // Tìm Chi1 và Chi3 phù hợp
                    foreach (var chi1Combo in chi1Combinations.Take(3))
                    {
                        var remainingForChi3 = remainingCards.Except(chi1Combo.Cards).ToList();
                        if (remainingForChi3.Count == 3)
                        {
                            var chi3Cards = remainingForChi3;

                            // Nếu Chi3 là mậu thầu, đảm bảo lấy 3 lá lớn nhất
                            var chi3Type = GetHandTypeName(chi3Cards);
                            if (chi3Type.Contains("🃏") || chi3Type.Contains("Mậu Thầu"))
                            {
                                // Sắp xếp lại Chi3 để lấy 3 lá lớn nhất
                                chi3Cards = chi3Cards.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();
                                _uiManager.AppendLog($"🃏 Chi3 mậu thầu được sắp xếp: {string.Join(",", chi3Cards.Select(c => GetCardName(c.Id)))}", UIManager.LogLevel.Debug);
                            }

                            if (IsValidMauBinh(chi1Combo.Cards, chi2Combo.Cards, chi3Cards))
                            {
                                // Tránh Chi2+Chi3 đều mậu thầu
                                bool chi2IsHighCard = chi2Combo.Type.Contains("🃏") || chi2Combo.Type.Contains("Mậu Thầu");
                                bool chi3IsHighCard = chi3Type.Contains("🃏") || chi3Type.Contains("Mậu Thầu");

                                if (!(chi2IsHighCard && chi3IsHighCard))
                                {
                                    var suggestion = new OptimizedSuggestion
                                    {
                                        Chi1 = chi1Combo.Cards.Select(c => c.Id).ToArray(),
                                        Chi2 = chi2Combo.Cards.Select(c => c.Id).ToArray(),
                                        Chi3 = chi3Cards.Select(c => c.Id).ToArray(),
                                        TotalScore = chi1Combo.Score * 2.0 + chi2Combo.Score * 2.5 + CardUtilityMaubinh.GetChiStrength(chi3Cards) * 3.0,
                                        Description = $"🥈 {chi1Combo.Type} + {chi2Combo.Type}",
                                        Chi1Type = chi1Combo.Type,
                                        Chi2Type = chi2Combo.Type,
                                        Chi3Type = chi3Type,
                                        IsSpecialCase = false
                                    };
                                    suggestions.Add(suggestion);
                                    _uiManager.AppendLog($"✅ Chi2 First: {suggestion.Description}", UIManager.LogLevel.Debug);
                                }
                            }
                        }
                    }

                    if (suggestions.Count >= 3) break; // Tối đa 3 gợi ý
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GenerateStrategy_Chi2First_Enhanced: {ex.Message}", UIManager.LogLevel.Error);
            }

            return suggestions;
        }

        /// <summary>
        /// Chiến lược 3: Ưu tiên Chi 3 mạnh nhất trước
        /// </summary>
        private List<OptimizedSuggestion> GenerateStrategy_Chi3First_Enhanced(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();

            try
            {
                var chi3Combinations = AnalyzeChi3Combinations(cardInfos);

                // Lấy top 5 Chi3 mạnh nhất (ưu tiên đôi/ba)
                var strongChi3 = chi3Combinations
                    .Where(c => c.Type.Contains("Đôi") || c.Type.Contains("Xám"))
                    .Take(5)
                    .ToList();

                if (!strongChi3.Any())
                {
                    // Fallback: lấy Chi3 mậu thầu với 3 lá LỚN NHẤT
                    strongChi3 = GetBestHighCardChi3(cardInfos).Take(3).ToList();
                }

                foreach (var chi3Combo in strongChi3)
                {
                    var remainingCards = cardInfos.Except(chi3Combo.Cards).ToList();
                    var chi1Combinations = AnalyzeChi1Combinations(remainingCards);

                    foreach (var chi1Combo in chi1Combinations.Take(3))
                    {
                        var chi2Cards = remainingCards.Except(chi1Combo.Cards).ToList();

                        if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Combo.Cards, chi2Cards, chi3Combo.Cards))
                        {
                            var chi2Type = GetHandTypeName(chi2Cards);

                            // Tránh Chi2+Chi3 đều mậu thầu
                            bool chi2IsHighCard = chi2Type.Contains("🃏") || chi2Type.Contains("Mậu Thầu");
                            bool chi3IsHighCard = chi3Combo.Type.Contains("🃏") || chi3Combo.Type.Contains("Mậu Thầu");

                            if (!(chi2IsHighCard && chi3IsHighCard))
                            {
                                var suggestion = CreateSuggestion(chi1Combo, chi2Cards, chi3Combo);
                                suggestion.Description = $"🥉 {chi1Combo.Type} + {chi3Combo.Type}";
                                suggestions.Add(suggestion);
                                _uiManager.AppendLog($"✅ Chi3 First: {suggestion.Description}", UIManager.LogLevel.Debug);
                            }
                        }
                    }

                    if (suggestions.Count >= 3) break; // Tối đa 3 gợi ý
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GenerateStrategy_Chi3First_Enhanced: {ex.Message}", UIManager.LogLevel.Error);
            }

            return suggestions;
        }

        /// <summary>
        /// Kiểm tra gợi ý có hợp lệ không (tránh Chi2+Chi3 đều mậu thầu)
        /// </summary>
        private bool IsValidSuggestion(OptimizedSuggestion suggestion)
        {
            bool chi2IsHighCard = suggestion.Chi2Type.Contains("🃏") || suggestion.Chi2Type.Contains("Mậu Thầu");
            bool chi3IsHighCard = suggestion.Chi3Type.Contains("🃏") || suggestion.Chi3Type.Contains("Mậu Thầu");

            // Không cho phép Chi2+Chi3 đều mậu thầu
            return !(chi2IsHighCard && chi3IsHighCard);
        }

        /// <summary>
        /// Loại bỏ gợi ý trùng lặp
        /// </summary>
        private List<OptimizedSuggestion> RemoveDuplicateSuggestions(List<OptimizedSuggestion> suggestions)
        {
            var unique = new List<OptimizedSuggestion>();
            var seenCombinations = new HashSet<string>();

            foreach (var suggestion in suggestions.OrderByDescending(s => s.TotalScore))
            {
                var comboKey = string.Join(",", suggestion.Chi1.OrderBy(x => x)) + "|" +
                              string.Join(",", suggestion.Chi2.OrderBy(x => x)) + "|" +
                              string.Join(",", suggestion.Chi3.OrderBy(x => x));

                if (!seenCombinations.Contains(comboKey))
                {
                    seenCombinations.Add(comboKey);
                    unique.Add(suggestion);
                }
            }

            return unique;
        }

        /// <summary>
        /// Lấy điểm số theo loại bài
        /// </summary>
        private double GetHandTypeScore(CardUtilityMaubinh.HandType handType)
        {
            return handType switch
            {
                CardUtilityMaubinh.HandType.StraightFlush => 8000,
                CardUtilityMaubinh.HandType.FourOfAKind => 7000,
                CardUtilityMaubinh.HandType.FullHouse => 6000,
                CardUtilityMaubinh.HandType.Flush => 5000,
                CardUtilityMaubinh.HandType.Straight => 4000,
                CardUtilityMaubinh.HandType.ThreeOfAKind => 3000,
                CardUtilityMaubinh.HandType.TwoPair => 2000,
                CardUtilityMaubinh.HandType.OnePair => 1000,
                _ => 0
            };
        }

        /// <summary>
        /// Lấy Chi3 mậu thầu tốt nhất (3 lá lớn nhất)
        /// </summary>
        private List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)> GetBestHighCardChi3(List<CardUtilityMaubinh.CardInfo> availableCards)
        {
            var results = new List<(List<CardUtilityMaubinh.CardInfo> Cards, string Type, double Score)>();

            try
            {
                // Sắp xếp tất cả lá theo giá trị giảm dần
                var sortedCards = availableCards.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();

                // Tạo các combination 3 lá mậu thầu, ưu tiên 3 lá lớn nhất
                var highCardCombinations = new List<List<CardUtilityMaubinh.CardInfo>>();

                // Combination 1: 3 lá lớn nhất
                if (sortedCards.Count >= 3)
                {
                    var topThree = sortedCards.Take(3).ToList();
                    if (IsHighCard(topThree))
                    {
                        highCardCombinations.Add(topThree);
                    }
                }

                // Combination 2: Thay lá thứ 3 bằng lá thứ 4 (nếu có)
                if (sortedCards.Count >= 4)
                {
                    var altThree = new List<CardUtilityMaubinh.CardInfo> { sortedCards[0], sortedCards[1], sortedCards[3] };
                    if (IsHighCard(altThree))
                    {
                        highCardCombinations.Add(altThree);
                    }
                }

                // Combination 3: Thay lá thứ 2 bằng lá thứ 4 (nếu có)
                if (sortedCards.Count >= 4)
                {
                    var altThree2 = new List<CardUtilityMaubinh.CardInfo> { sortedCards[0], sortedCards[2], sortedCards[3] };
                    if (IsHighCard(altThree2))
                    {
                        highCardCombinations.Add(altThree2);
                    }
                }

                // Tính điểm cho từng combination
                foreach (var combo in highCardCombinations)
                {
                    var strength = CardUtilityMaubinh.GetChiStrength(combo);
                    var score = strength; // Mậu thầu chỉ tính điểm strength

                    results.Add((combo, "🃏 Mậu Thầu", score));
                    _uiManager.AppendLog($"🃏 Chi3 mậu thầu: {string.Join(",", combo.Select(c => GetCardName(c.Id)))} - Điểm: {score:F0}", UIManager.LogLevel.Debug);
                }

                // Sắp xếp theo điểm giảm dần (3 lá lớn nhất sẽ có điểm cao nhất)
                results = results.OrderByDescending(r => r.Score).ToList();

                _uiManager.AppendLog($"✅ Đã tạo {results.Count} Chi3 mậu thầu tối ưu", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GetBestHighCardChi3: {ex.Message}", UIManager.LogLevel.Error);
            }

            return results;
        }

        /// <summary>
        /// Kiểm tra 3 lá có phải mậu thầu không (không có đôi, không có ba)
        /// </summary>
        private bool IsHighCard(List<CardUtilityMaubinh.CardInfo> cards)
        {
            if (cards.Count != 3) return false;

            var handType = CardUtilityMaubinh.EvaluateHand(cards.Select(c => c.Id).ToArray());
            return handType == CardUtilityMaubinh.HandType.HighCard;
        }

        /// <summary>
        /// Lấy tên bài từ ID
        /// </summary>
        private string GetCardName(int cardId)
        {
            try
            {
                var cardInfo = new CardUtilityMaubinh.CardInfo(cardId);
                string rank = cardInfo.Rank switch
                {
                    1 => "A",
                    11 => "J",
                    12 => "Q",
                    13 => "K",
                    _ => cardInfo.Rank.ToString()
                };
                string suit = cardInfo.Suit switch
                {
                    0 => "♠",
                    1 => "♣",
                    2 => "♦",
                    3 => "♥",
                    _ => ""
                };
                return $"{rank}{suit}";
            }
            catch
            {
                return $"Card{cardId}";
            }
        }

        #endregion

        #region Special Hands Methods (Game Original Logic)



        /// <summary>
        /// Tạo gợi ý thay thế cho bài đặc biệt
        /// </summary>
        private OptimizedSuggestion CreateAlternativeSpecialHandSuggestion(List<CardUtilityMaubinh.CardInfo> cardInfos, int specialHandType)
        {
            // Sử dụng thuật toán tối ưu thông thường nhưng với điểm cao
            var normalSuggestions = GenerateStrategy_StrongestChi1(cardInfos);
            var bestSuggestion = normalSuggestions.FirstOrDefault();

            if (bestSuggestion != null)
            {
                bestSuggestion.Description = $"🏆 {GetSpecialHandName(specialHandType)} (Alt)";
                bestSuggestion.Score = 10000 + specialHandType;
                bestSuggestion.Strategy = "SpecialHandAlt";
                bestSuggestion.TotalScore = 10000 + specialHandType;
                bestSuggestion.IsSpecialCase = true;
            }

            return bestSuggestion;
        }

        /// <summary>
        /// Lấy tên bài đặc biệt - dựa trên game gốc
        /// </summary>
        private string GetSpecialHandName(int specialHandType)
        {
            return specialHandType switch
            {
                15 => "🐉 Sảnh Rồng Đồng Hoa",
                14 => "🐲 Sảnh Rồng",
                13 => "🌊 Đồng Hoa",
                16 => "🎰 5 Đôi 1 Xám",
                12 => "🎰 6 Đôi",
                11 => "🌊 3 Thùng",
                10 => "📈 3 Sảnh",
                _ => "🏆 Bài Đặc Biệt"
            };
        }

        /// <summary>
        /// Tạo suggestion cho bài đặc biệt tới trắng
        /// </summary>
        private OptimizedSuggestion? CreateSpecialHandSuggestion(List<CardUtilityMaubinh.CardInfo> cardInfos, int specialHandType)
        {
            try
            {
                var specialName = GetSpecialHandName(specialHandType);

                // Sắp xếp bài theo rank giảm dần để tối ưu
                var sortedCards = cardInfos.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();

                // Tính điểm đặc biệt dựa trên loại bài
                double specialScore = specialHandType switch
                {
                    15 => 100000, // Sảnh Rồng Đồng Hoa
                    14 => 50000,  // Sảnh Rồng
                    13 => 30000,  // Đồng Hoa
                    16 => 25000,  // 5 Đôi 1 Xám
                    12 => 20000,  // 6 Đôi
                    11 => 15000,  // 3 Thùng
                    10 => 15000,  // 3 Sảnh
                    _ => 10000    // Bài đặc biệt khác
                };

                return new OptimizedSuggestion
                {
                    Chi1 = sortedCards.Take(5).Select(c => c.Id).ToArray(),
                    Chi2 = sortedCards.Skip(5).Take(5).Select(c => c.Id).ToArray(),
                    Chi3 = sortedCards.Skip(10).Take(3).Select(c => c.Id).ToArray(),
                    TotalScore = specialScore,
                    Description = specialName,
                    Chi1Type = specialName,
                    Chi2Type = specialName,
                    Chi3Type = specialName,
                    IsSpecialCase = true
                };
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo suggestion đặc biệt: {ex.Message}", UIManager.LogLevel.Error);
                return null;
            }
        }

        /// <summary>
        /// Lấy tên loại bài dựa trên điểm game gốc
        /// </summary>
        private string GetHandTypeNameFromGameScore(int gameScore, CardUtilityMaubinh.HandType handType)
        {
            if (gameScore > 544) return "🔥 Thùng Phá Sảnh";
            if (gameScore > 476) return "💎 Tứ Quý";
            if (gameScore > 408) return "🏠 Cù Lũ";
            if (gameScore > 340) return "🌊 Thùng";
            if (gameScore > 272) return "📈 Sảnh";
            if (gameScore > 204) return "🎯 Xám";
            if (gameScore > 136) return "👥 Thú";
            if (gameScore > 68) return "💫 Đôi";
            return "🃏 Mậu Thầu";
        }

        /// <summary>
        /// Lấy các combinations ưu tiên cho Chi 1 - tối ưu hiệu suất
        /// </summary>
        private IEnumerable<List<CardUtilityMaubinh.CardInfo>> GetPriorityChi1Combinations(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            // Ưu tiên 1: Tìm các combinations có thể tạo bài mạnh
            var priorityCards = new List<CardUtilityMaubinh.CardInfo>();

            // Thêm các lá có thể tạo tứ quý/xám - tránh anonymous types
            var rankGroups = new Dictionary<int, List<CardUtilityMaubinh.CardInfo>>();
            foreach (var card in cardInfos)
            {
                if (!rankGroups.ContainsKey(card.Rank))
                    rankGroups[card.Rank] = new List<CardUtilityMaubinh.CardInfo>();
                rankGroups[card.Rank].Add(card);
            }

            foreach (var group in rankGroups.Values.Where(g => g.Count >= 2))
            {
                priorityCards.AddRange(group);
            }

            // Thêm các lá có thể tạo thùng - tránh anonymous types
            var suitGroups = new Dictionary<int, List<CardUtilityMaubinh.CardInfo>>();
            foreach (var card in cardInfos)
            {
                if (!suitGroups.ContainsKey(card.Suit))
                    suitGroups[card.Suit] = new List<CardUtilityMaubinh.CardInfo>();
                suitGroups[card.Suit].Add(card);
            }

            foreach (var group in suitGroups.Values.Where(g => g.Count >= 5))
            {
                priorityCards.AddRange(group.Take(5));
            }

            // Thêm các lá cao
            var highCards = cardInfos.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).Take(7);
            priorityCards.AddRange(highCards);

            // Loại bỏ trùng lặp
            priorityCards = priorityCards.Distinct().ToList();

            // Tạo combinations từ priority cards trước
            if (priorityCards.Count >= 5)
            {
                foreach (var combo in GetCombinations(priorityCards, 5).Take(100))
                {
                    yield return combo;
                }
            }

            // Sau đó tạo combinations từ tất cả cards
            foreach (var combo in GetCombinations(cardInfos, 5).Take(100))
            {
                yield return combo;
            }
        }

        /// <summary>
        /// QUICK FIX: Tạo gợi ý đơn giản khi thuật toán chính fail
        /// </summary>
        private List<OptimizedSuggestion> CreateSimpleFallbackSuggestions(int[] cards)
        {
            var suggestions = new List<OptimizedSuggestion>();

            try
            {
                var cardInfos = cards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();

                // Tạo 3 gợi ý đơn giản với các strategy khác nhau

                // Strategy 1: Sắp xếp theo rank giảm dần
                var sortedByRank = cardInfos.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();
                suggestions.Add(CreateSimpleSuggestion(sortedByRank, "🎯 Sắp xếp theo rank cao", 1000));

                // Strategy 2: Ưu tiên đôi/xám
                var pairFirst = ArrangePairsFirst(cardInfos);
                suggestions.Add(CreateSimpleSuggestion(pairFirst, "💎 Ưu tiên đôi/xám", 1100));

                // Strategy 3: Cân bằng
                var balanced = ArrangeBalanced(cardInfos);
                suggestions.Add(CreateSimpleSuggestion(balanced, "⚖️ Cân bằng", 1200));

                _uiManager.AppendLog($"✅ Tạo được {suggestions.Count} gợi ý fallback đơn giản", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo fallback: {ex.Message}", UIManager.LogLevel.Error);

                // Last resort: Tạo gợi ý cơ bản nhất
                var cardInfos = cards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();
                suggestions.Add(CreateBasicSuggestion(cardInfos));
            }

            return suggestions;
        }

        private OptimizedSuggestion CreateSimpleSuggestion(List<CardUtilityMaubinh.CardInfo> cards, string description, double score)
        {
            return new OptimizedSuggestion
            {
                Chi1 = cards.Take(5).Select(c => c.Id).ToArray(),
                Chi2 = cards.Skip(5).Take(5).Select(c => c.Id).ToArray(),
                Chi3 = cards.Skip(10).Take(3).Select(c => c.Id).ToArray(),
                TotalScore = score,
                Description = description,
                Chi1Type = GetHandTypeName(cards.Take(5).ToList()),
                Chi2Type = GetHandTypeName(cards.Skip(5).Take(5).ToList()),
                Chi3Type = GetHandTypeName(cards.Skip(10).Take(3).ToList()),
                IsSpecialCase = false
            };
        }

        private List<CardUtilityMaubinh.CardInfo> ArrangePairsFirst(List<CardUtilityMaubinh.CardInfo> cards)
        {
            var pairs = cards.GroupBy(c => c.Rank).Where(g => g.Count() >= 2).SelectMany(g => g.Take(2)).ToList();
            var singles = cards.Except(pairs).OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();
            return pairs.Concat(singles).ToList();
        }

        private List<CardUtilityMaubinh.CardInfo> ArrangeBalanced(List<CardUtilityMaubinh.CardInfo> cards)
        {
            return cards.OrderBy(c => c.Suit).ThenByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).ToList();
        }

        private OptimizedSuggestion CreateBasicSuggestion(List<CardUtilityMaubinh.CardInfo> cards)
        {
            return new OptimizedSuggestion
            {
                Chi1 = cards.Take(5).Select(c => c.Id).ToArray(),
                Chi2 = cards.Skip(5).Take(5).Select(c => c.Id).ToArray(),
                Chi3 = cards.Skip(10).Take(3).Select(c => c.Id).ToArray(),
                TotalScore = 500,
                Description = "🔧 Gợi ý cơ bản",
                Chi1Type = "🃏 Mậu Thầu",
                Chi2Type = "🃏 Mậu Thầu",
                Chi3Type = "🃏 Mậu Thầu",
                IsSpecialCase = false
            };
        }

        #region NEW LOGIC METHODS - Theo yêu cầu mới

        /// <summary>
        /// GÁN CHI 1 TRƯỚC - 6 cases: Thùng phá sảnh, Tứ quý, Cù lũ, Thùng, Sảnh, Xám
        /// Kiểm tra Chi1 > Chi2 > Chi3, lấy tối đa 3 gợi ý hợp lệ với điểm cao nhất
        /// </summary>
        private List<OptimizedSuggestion> GenerateStrategy_Chi1First_NewLogic(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();

            try
            {
                // Tìm tất cả combinations Chi1 có thể
                var chi1Combinations = GetCombinations(cardInfos, 5);
                var validChi1Cases = new List<ChiCombination>();

                foreach (var chi1Cards in chi1Combinations.Take(200)) // Giới hạn để tránh quá chậm
                {
                    var handType = CardUtilityMaubinh.EvaluateHand(chi1Cards.Select(c => c.Id).ToArray());
                    var gameScore = CardUtilityMaubinh.GetGameOriginalScore(chi1Cards);
                    var strength = CardUtilityMaubinh.GetChiStrength(chi1Cards);

                    // Chỉ lấy 6 cases theo yêu cầu
                    switch (handType)
                    {
                        case CardUtilityMaubinh.HandType.StraightFlush:
                            validChi1Cases.Add(new ChiCombination(chi1Cards, "🔥 Thùng Phá Sảnh", gameScore + strength, 1));
                            break;
                        case CardUtilityMaubinh.HandType.FourOfAKind:
                            validChi1Cases.Add(new ChiCombination(chi1Cards, "💎 Tứ Quý", gameScore + strength, 2));
                            break;
                        case CardUtilityMaubinh.HandType.FullHouse:
                            validChi1Cases.Add(new ChiCombination(chi1Cards, "🏠 Cù Lũ", gameScore + strength, 3));
                            break;
                        case CardUtilityMaubinh.HandType.Flush:
                            validChi1Cases.Add(new ChiCombination(chi1Cards, "🌊 Thùng", gameScore + strength, 4));
                            break;
                        case CardUtilityMaubinh.HandType.Straight:
                            validChi1Cases.Add(new ChiCombination(chi1Cards, "📈 Sảnh", gameScore + strength, 5));
                            break;
                        case CardUtilityMaubinh.HandType.ThreeOfAKind:
                            validChi1Cases.Add(new ChiCombination(chi1Cards, "🎯 Xám", gameScore + strength, 6));
                            break;
                    }
                }

                // Sắp xếp theo priority và điểm số
                var sortedChi1 = validChi1Cases
                    .OrderBy(c => c.Priority)
                    .ThenByDescending(c => c.Score)
                    .Take(10) // Lấy top 10 Chi1 tốt nhất
                    .ToList();

                _uiManager.AppendLog($"🎯 Chi1 First: Tìm được {sortedChi1.Count} Chi1 cases hợp lệ", UIManager.LogLevel.Debug);

                // Với mỗi Chi1, tìm Chi2 và Chi3 tốt nhất
                foreach (var chi1Case in sortedChi1)
                {
                    var remainingCards = cardInfos.Except(chi1Case.Cards).ToList();

                    // Tìm tất cả combinations Chi2 và Chi3
                    var chi2Combinations = GetCombinations(remainingCards, 5);

                    foreach (var chi2Cards in chi2Combinations.Take(50))
                    {
                        var chi3Cards = remainingCards.Except(chi2Cards).ToList();

                        if (chi3Cards.Count == 3 && IsValidMauBinh(chi1Case.Cards, chi2Cards, chi3Cards))
                        {
                            var suggestion = CreateSuggestionFromComponents(chi1Case.Cards, chi2Cards, chi3Cards);
                            suggestion.Description = $"Chi1: {chi1Case.Type}";
                            suggestions.Add(suggestion);

                            if (suggestions.Count >= 3) break; // Tối đa 3 gợi ý
                        }
                    }

                    if (suggestions.Count >= 3) break;
                }

                _uiManager.AppendLog($"✅ Chi1 First: Tạo được {suggestions.Count} gợi ý hợp lệ", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GenerateStrategy_Chi1First_NewLogic: {ex.Message}", UIManager.LogLevel.Error);
            }

            return suggestions.OrderByDescending(s => s.TotalScore).Take(3).ToList();
        }

        /// <summary>
        /// GÁN CHI 2 TRƯỚC - 8 cases: Thùng phá sảnh, Tứ quý, Cù lũ, Thùng, Sảnh, Xám, Thú, Đôi
        /// Kiểm tra Chi1 > Chi2 > Chi3, lấy tối đa 3 gợi ý hợp lệ với điểm cao nhất
        /// </summary>
        private List<OptimizedSuggestion> GenerateStrategy_Chi2First_NewLogic(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();

            try
            {
                // Tìm tất cả combinations Chi2 có thể
                var chi2Combinations = GetCombinations(cardInfos, 5);
                var validChi2Cases = new List<ChiCombination>();

                foreach (var chi2Cards in chi2Combinations.Take(200))
                {
                    var handType = CardUtilityMaubinh.EvaluateHand(chi2Cards.Select(c => c.Id).ToArray());
                    var gameScore = CardUtilityMaubinh.GetGameOriginalScore(chi2Cards);
                    var strength = CardUtilityMaubinh.GetChiStrength(chi2Cards);

                    // Lấy 8 cases theo yêu cầu
                    switch (handType)
                    {
                        case CardUtilityMaubinh.HandType.StraightFlush:
                            validChi2Cases.Add(new ChiCombination(chi2Cards, "🔥 Thùng Phá Sảnh", gameScore + strength, 1));
                            break;
                        case CardUtilityMaubinh.HandType.FourOfAKind:
                            validChi2Cases.Add(new ChiCombination(chi2Cards, "💎 Tứ Quý", gameScore + strength, 2));
                            break;
                        case CardUtilityMaubinh.HandType.FullHouse:
                            validChi2Cases.Add(new ChiCombination(chi2Cards, "🏠 Cù Lũ", gameScore + strength, 3));
                            break;
                        case CardUtilityMaubinh.HandType.Flush:
                            validChi2Cases.Add(new ChiCombination(chi2Cards, "🌊 Thùng", gameScore + strength, 4));
                            break;
                        case CardUtilityMaubinh.HandType.Straight:
                            validChi2Cases.Add(new ChiCombination(chi2Cards, "📈 Sảnh", gameScore + strength, 5));
                            break;
                        case CardUtilityMaubinh.HandType.ThreeOfAKind:
                            validChi2Cases.Add(new ChiCombination(chi2Cards, "🎯 Xám", gameScore + strength, 6));
                            break;
                        case CardUtilityMaubinh.HandType.TwoPair:
                            validChi2Cases.Add(new ChiCombination(chi2Cards, "👥 Thú", gameScore + strength, 7));
                            break;
                        case CardUtilityMaubinh.HandType.OnePair:
                            validChi2Cases.Add(new ChiCombination(chi2Cards, "💫 Đôi", gameScore + strength, 8));
                            break;
                    }
                }

                // Sắp xếp theo priority và điểm số
                var sortedChi2 = validChi2Cases
                    .OrderBy(c => c.Priority)
                    .ThenByDescending(c => c.Score)
                    .Take(10)
                    .ToList();

                _uiManager.AppendLog($"🎯 Chi2 First: Tìm được {sortedChi2.Count} Chi2 cases hợp lệ", UIManager.LogLevel.Debug);

                // Với mỗi Chi2, tìm Chi1 và Chi3 tốt nhất
                foreach (var chi2Case in sortedChi2)
                {
                    var remainingCards = cardInfos.Except(chi2Case.Cards).ToList();

                    // Tìm tất cả combinations Chi1 và Chi3
                    var chi1Combinations = GetCombinations(remainingCards, 5);

                    foreach (var chi1Cards in chi1Combinations.Take(50))
                    {
                        var chi3Cards = remainingCards.Except(chi1Cards).ToList();

                        if (chi3Cards.Count == 3 && IsValidMauBinh(chi1Cards, chi2Case.Cards, chi3Cards))
                        {
                            var suggestion = CreateSuggestionFromComponents(chi1Cards, chi2Case.Cards, chi3Cards);
                            suggestion.Description = $"Chi2: {chi2Case.Type}";
                            suggestions.Add(suggestion);

                            if (suggestions.Count >= 3) break;
                        }
                    }

                    if (suggestions.Count >= 3) break;
                }

                _uiManager.AppendLog($"✅ Chi2 First: Tạo được {suggestions.Count} gợi ý hợp lệ", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GenerateStrategy_Chi2First_NewLogic: {ex.Message}", UIManager.LogLevel.Error);
            }

            return suggestions.OrderByDescending(s => s.TotalScore).Take(3).ToList();
        }

        /// <summary>
        /// GÁN CHI 3 TRƯỚC - 2 cases: Xám, Đôi
        /// Kiểm tra Chi1 > Chi2 > Chi3, lấy tối đa 2 gợi ý hợp lệ với điểm cao nhất
        /// </summary>
        private List<OptimizedSuggestion> GenerateStrategy_Chi3First_NewLogic(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            var suggestions = new List<OptimizedSuggestion>();

            try
            {
                // Tìm tất cả combinations Chi3 có thể
                var chi3Combinations = GetCombinations(cardInfos, 3);
                var validChi3Cases = new List<ChiCombination>();

                foreach (var chi3Cards in chi3Combinations.Take(100))
                {
                    var handType = CardUtilityMaubinh.EvaluateHand(chi3Cards.Select(c => c.Id).ToArray());
                    var gameScore = CardUtilityMaubinh.GetGameOriginalScore(chi3Cards);
                    var strength = CardUtilityMaubinh.GetChiStrength(chi3Cards);

                    // Chỉ lấy 2 cases theo yêu cầu
                    switch (handType)
                    {
                        case CardUtilityMaubinh.HandType.ThreeOfAKind:
                            validChi3Cases.Add(new ChiCombination(chi3Cards, "🔥 Xám", gameScore + strength, 1));
                            break;
                        case CardUtilityMaubinh.HandType.OnePair:
                            validChi3Cases.Add(new ChiCombination(chi3Cards, "💎 Đôi", gameScore + strength, 2));
                            break;
                    }
                }

                // Sắp xếp theo priority và điểm số
                var sortedChi3 = validChi3Cases
                    .OrderBy(c => c.Priority)
                    .ThenByDescending(c => c.Score)
                    .Take(5)
                    .ToList();

                _uiManager.AppendLog($"🎯 Chi3 First: Tìm được {sortedChi3.Count} Chi3 cases hợp lệ", UIManager.LogLevel.Debug);

                // Với mỗi Chi3, tìm Chi1 và Chi2 tốt nhất
                foreach (var chi3Case in sortedChi3)
                {
                    var remainingCards = cardInfos.Except(chi3Case.Cards).ToList();

                    // Tìm tất cả combinations Chi1 và Chi2
                    var chi1Combinations = GetCombinations(remainingCards, 5);

                    foreach (var chi1Cards in chi1Combinations.Take(50))
                    {
                        var chi2Cards = remainingCards.Except(chi1Cards).ToList();

                        if (chi2Cards.Count == 5 && IsValidMauBinh(chi1Cards, chi2Cards, chi3Case.Cards))
                        {
                            var suggestion = CreateSuggestionFromComponents(chi1Cards, chi2Cards, chi3Case.Cards);
                            suggestion.Description = $"Chi3: {chi3Case.Type}";
                            suggestions.Add(suggestion);

                            if (suggestions.Count >= 2) break;
                        }
                    }

                    if (suggestions.Count >= 2) break;
                }

                _uiManager.AppendLog($"✅ Chi3 First: Tạo được {suggestions.Count} gợi ý hợp lệ", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GenerateStrategy_Chi3First_NewLogic: {ex.Message}", UIManager.LogLevel.Error);
            }

            return suggestions.OrderByDescending(s => s.TotalScore).Take(2).ToList();
        }

        /// <summary>
        /// Tạo suggestion từ các components riêng biệt
        /// FIXED: Trả về đúng format theo định nghĩa Chi1/Chi2/Chi3
        /// </summary>
        private OptimizedSuggestion CreateSuggestionFromComponents(
            List<CardUtilityMaubinh.CardInfo> chi1Cards,
            List<CardUtilityMaubinh.CardInfo> chi2Cards,
            List<CardUtilityMaubinh.CardInfo> chi3Cards)
        {
            var chi1Score = CardUtilityMaubinh.GetChiStrength(chi1Cards);
            var chi2Score = CardUtilityMaubinh.GetChiStrength(chi2Cards);
            var chi3Score = CardUtilityMaubinh.GetChiStrength(chi3Cards);

            // Tính tổng điểm với trọng số: Chi1 × 3.0 + Chi2 × 2.0 + Chi3 × 1.0
            var totalScore = chi1Score * 3.0 + chi2Score * 2.0 + chi3Score * 1.0;

            // FIXED: Trả về đúng format theo định nghĩa của bạn
            // Chi1: 5 lá đầu, Chi2: 5 lá giữa, Chi3: 3 lá cuối
            return new OptimizedSuggestion
            {
                Chi1 = chi1Cards.Select(c => c.Id).ToArray(),  // Chi1: 5 lá
                Chi2 = chi2Cards.Select(c => c.Id).ToArray(),  // Chi2: 5 lá
                Chi3 = chi3Cards.Select(c => c.Id).ToArray(),  // Chi3: 3 lá
                TotalScore = totalScore,
                Chi1Type = GetHandTypeName(chi1Cards),
                Chi2Type = GetHandTypeName(chi2Cards),
                Chi3Type = GetHandTypeName(chi3Cards),
                IsSpecialCase = false
            };
        }

        #endregion

        #endregion
    }
}
