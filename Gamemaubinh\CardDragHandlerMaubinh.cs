using System;
using System.Threading.Tasks;

namespace AutoGameBai.Gamemaubinh
{
    /// <summary>
    /// Temporary stub for CardDragHandlerMaubinh to maintain compatibility
    /// </summary>
    public class CardDragHandlerMaubinh
    {
        private readonly GameClientManager _gameClient;

        public CardDragHandlerMaubinh(GameClientManager gameClient)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
        }

        /// <summary>
        /// Handle card drag operations (stub implementation)
        /// </summary>
        public void HandleCardDrag(int cardId, int fromPosition, int toPosition)
        {
            // Stub implementation - does nothing for now
            // Can be implemented later if needed
        }

        /// <summary>
        /// Enable drag and drop functionality (stub implementation)
        /// </summary>
        public void EnableDragDrop(bool enable)
        {
            // Stub implementation - does nothing for now
        }

        /// <summary>
        /// Set drag mode (stub implementation)
        /// </summary>
        public void SetDragMode(string mode)
        {
            // Stub implementation - does nothing for now
        }

        /// <summary>
        /// Arrange cards with script async (stub implementation)
        /// </summary>
        public async Task ArrangeCardsWithScriptAsync(int[] chi1, int[] chi2, int[] chi3)
        {
            // Stub implementation - just delay to simulate async operation
            await Task.Delay(100);
        }
    }
}
