using System;
using System.Collections.Generic;
using System.Linq;

namespace AutoGameBai.Gamemaubinh
{
    /// <summary>
    /// Core Engine cho Mậu Binh - Tối ưu và đơn giản
    /// </summary>
    public class MauBinhEngine
    {
        private readonly UIManager _uiManager;

        public MauBinhEngine(UIManager uiManager)
        {
            _uiManager = uiManager;
        }

        #region Core Data Structures

        /// <summary>
        /// Gợi ý Mậu Binh đơn giản
        /// </summary>
        public class MauBinhSuggestion
        {
            public int[] Chi1 { get; set; } = new int[5];  // index 0-4
            public int[] Chi2 { get; set; } = new int[5];  // index 5-9
            public int[] Chi3 { get; set; } = new int[3];  // index 10-12
            public string Description { get; set; } = "";
            public double Score { get; set; }
            public bool IsSpecial { get; set; }
        }

        /// <summary>
        /// Thông tin lá bài
        /// </summary>
        public class Card
        {
            public int Id { get; set; }
            public int Rank { get; set; }
            public int Suit { get; set; }
            public int Value { get; set; }

            public Card(int id)
            {
                Id = id;
                Rank = CardUtilityMaubinh.GetCardRank(id);
                Suit = CardUtilityMaubinh.GetCardSuit(id);
                Value = CardUtilityMaubinh.GetCardValue(Rank);
            }
        }

        /// <summary>
        /// Kết quả so sánh chi
        /// </summary>
        public class ChiResult
        {
            public string Type { get; set; } = "";
            public double Strength { get; set; }
            public int[] Cards { get; set; } = Array.Empty<int>();
        }

        #endregion

        #region Main Algorithm

        /// <summary>
        /// Tạo gợi ý cho 1 user (13 lá)
        /// </summary>
        public List<MauBinhSuggestion> GenerateSuggestions(int[] cards)
        {
            try
            {
                _uiManager.AppendLog("🚀 Bắt đầu tạo gợi ý Mậu Binh", UIManager.LogLevel.Info);

                var cardList = cards.Select(id => new Card(id)).ToList();
                var suggestions = new List<MauBinhSuggestion>();

                // 1. Kiểm tra bài đặc biệt
                var specialSuggestion = CheckSpecialHands(cardList);
                if (specialSuggestion != null)
                {
                    suggestions.Add(specialSuggestion);
                    _uiManager.AppendLog("🏆 Tìm thấy bài đặc biệt", UIManager.LogLevel.Info);
                    return suggestions;
                }

                // 2. Tạo gợi ý Chi1 mạnh nhất (3 gợi ý)
                suggestions.AddRange(GenerateChi1Suggestions(cardList));

                // 3. Tạo gợi ý Chi2 mạnh nhất (3 gợi ý)
                suggestions.AddRange(GenerateChi2Suggestions(cardList));

                // 4. Tạo gợi ý Chi3 mạnh nhất (2 gợi ý)
                suggestions.AddRange(GenerateChi3Suggestions(cardList));

                // 5. Sắp xếp theo điểm
                suggestions = suggestions.OrderByDescending(s => s.Score).Take(8).ToList();

                _uiManager.AppendLog($"✅ Tạo thành công {suggestions.Count} gợi ý", UIManager.LogLevel.Info);
                return suggestions;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo gợi ý: {ex.Message}", UIManager.LogLevel.Error);
                return CreateFallbackSuggestion(cards);
            }
        }

        #endregion

        #region Special Hands

        /// <summary>
        /// Kiểm tra bài đặc biệt (tới trắng)
        /// </summary>
        private MauBinhSuggestion? CheckSpecialHands(List<Card> cards)
        {
            // Sảnh rồng đồng hoa (A-K-Q-J-10 cùng chất)
            if (HasStraightFlushRoyal(cards))
            {
                return CreateSpecialSuggestion(cards, "🏆 Sảnh Rồng Đồng Hoa", 10000);
            }

            // Sảnh rồng (A-K-Q-J-10 khác chất)
            if (HasStraightRoyal(cards))
            {
                return CreateSpecialSuggestion(cards, "👑 Sảnh Rồng", 9000);
            }

            // 6 đôi
            if (HasSixPairs(cards))
            {
                return CreateSpecialSuggestion(cards, "💎 6 Đôi", 8000);
            }

            // Đồng hoa (13 lá cùng chất)
            if (HasFlush13(cards))
            {
                return CreateSpecialSuggestion(cards, "🌊 Đồng Hoa", 7000);
            }

            return null;
        }

        private bool HasStraightFlushRoyal(List<Card> cards)
        {
            var suits = cards.GroupBy(c => c.Suit);
            foreach (var suit in suits)
            {
                var ranks = suit.Select(c => c.Rank).OrderBy(r => r).ToList();
                if (ranks.SequenceEqual(new[] { 1, 10, 11, 12, 13 })) // A, 10, J, Q, K
                    return true;
            }
            return false;
        }

        private bool HasStraightRoyal(List<Card> cards)
        {
            var ranks = cards.Select(c => c.Rank).OrderBy(r => r).ToList();
            return ranks.SequenceEqual(new[] { 1, 10, 11, 12, 13 });
        }

        private bool HasSixPairs(List<Card> cards)
        {
            var rankGroups = cards.GroupBy(c => c.Rank);
            var pairs = rankGroups.Where(g => g.Count() == 2).Count();
            var single = rankGroups.Where(g => g.Count() == 1).Count();
            return pairs == 6 && single == 1;
        }

        private bool HasFlush13(List<Card> cards)
        {
            return cards.GroupBy(c => c.Suit).Any(g => g.Count() == 13);
        }

        private MauBinhSuggestion CreateSpecialSuggestion(List<Card> cards, string description, double score)
        {
            var sortedCards = cards.OrderByDescending(c => c.Value).ToArray();
            return new MauBinhSuggestion
            {
                Chi1 = sortedCards.Take(5).Select(c => c.Id).ToArray(),
                Chi2 = sortedCards.Skip(5).Take(5).Select(c => c.Id).ToArray(),
                Chi3 = sortedCards.Skip(10).Take(3).Select(c => c.Id).ToArray(),
                Description = description,
                Score = score,
                IsSpecial = true
            };
        }

        #endregion

        #region Chi Suggestions

        /// <summary>
        /// Tạo 3 gợi ý Chi1 mạnh nhất
        /// </summary>
        private List<MauBinhSuggestion> GenerateChi1Suggestions(List<Card> cards)
        {
            var suggestions = new List<MauBinhSuggestion>();

            // Tìm tất cả combinations 5 lá cho Chi1
            var chi1Combos = GetCombinations(cards, 5);

            // Đánh giá và sắp xếp theo độ mạnh
            var evaluatedCombos = chi1Combos
                .Select(combo => new
                {
                    Cards = combo,
                    Strength = CardUtilityMaubinh.GetChiStrength(combo.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList()),
                    Type = GetHandType(combo)
                })
                .OrderByDescending(c => c.Strength)
                .Take(3)
                .ToList();

            foreach (var combo in evaluatedCombos)
            {
                var remainingCards = cards.Except(combo.Cards).ToList();
                var suggestion = CreateSuggestionFromChi1(combo.Cards, remainingCards, combo.Type, combo.Strength);
                if (IsValidSuggestion(suggestion))
                {
                    suggestions.Add(suggestion);
                }
            }

            return suggestions;
        }

        /// <summary>
        /// Tạo 3 gợi ý Chi2 mạnh nhất
        /// </summary>
        private List<MauBinhSuggestion> GenerateChi2Suggestions(List<Card> cards)
        {
            var suggestions = new List<MauBinhSuggestion>();

            // Tìm tất cả combinations 5 lá cho Chi2
            var chi2Combos = GetCombinations(cards, 5);

            var evaluatedCombos = chi2Combos
                .Select(combo => new
                {
                    Cards = combo,
                    Strength = CardUtilityMaubinh.GetChiStrength(combo.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList()),
                    Type = GetHandType(combo)
                })
                .OrderByDescending(c => c.Strength)
                .Take(3)
                .ToList();

            foreach (var combo in evaluatedCombos)
            {
                var remainingCards = cards.Except(combo.Cards).ToList();
                var suggestion = CreateSuggestionFromChi2(remainingCards, combo.Cards, combo.Type, combo.Strength);
                if (IsValidSuggestion(suggestion))
                {
                    suggestions.Add(suggestion);
                }
            }

            return suggestions;
        }

        /// <summary>
        /// Tạo 2 gợi ý Chi3 mạnh nhất
        /// </summary>
        private List<MauBinhSuggestion> GenerateChi3Suggestions(List<Card> cards)
        {
            var suggestions = new List<MauBinhSuggestion>();

            // Tìm tất cả combinations 3 lá cho Chi3
            var chi3Combos = GetCombinations(cards, 3);

            var evaluatedCombos = chi3Combos
                .Select(combo => new
                {
                    Cards = combo,
                    Strength = CardUtilityMaubinh.GetChiStrength(combo.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList()),
                    Type = GetHandType(combo)
                })
                .OrderByDescending(c => c.Strength)
                .Take(2)
                .ToList();

            foreach (var combo in evaluatedCombos)
            {
                var remainingCards = cards.Except(combo.Cards).ToList();
                var suggestion = CreateSuggestionFromChi3(remainingCards, combo.Cards, combo.Type, combo.Strength);
                if (IsValidSuggestion(suggestion))
                {
                    suggestions.Add(suggestion);
                }
            }

            return suggestions;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Tạo tất cả combinations
        /// </summary>
        private List<List<Card>> GetCombinations(List<Card> cards, int count)
        {
            if (count == 0) return new List<List<Card>> { new List<Card>() };
            if (cards.Count < count) return new List<List<Card>>();

            var result = new List<List<Card>>();
            var first = cards[0];
            var rest = cards.Skip(1).ToList();

            // Include first card
            foreach (var combo in GetCombinations(rest, count - 1))
            {
                var newCombo = new List<Card> { first };
                newCombo.AddRange(combo);
                result.Add(newCombo);
            }

            // Exclude first card
            result.AddRange(GetCombinations(rest, count));

            return result;
        }

        /// <summary>
        /// Lấy loại bài
        /// </summary>
        private string GetHandType(List<Card> cards)
        {
            var handType = CardUtilityMaubinh.EvaluateHand(cards.Select(c => c.Id).ToArray());
            return handType switch
            {
                CardUtilityMaubinh.HandType.StraightFlush => "🔥 Thùng Phá Sảnh",
                CardUtilityMaubinh.HandType.FourOfAKind => "💎 Tứ Quý",
                CardUtilityMaubinh.HandType.FullHouse => "🏠 Cù Lũ",
                CardUtilityMaubinh.HandType.Flush => "🌊 Thùng",
                CardUtilityMaubinh.HandType.Straight => "📈 Sảnh",
                CardUtilityMaubinh.HandType.ThreeOfAKind => "🎯 Xám",
                CardUtilityMaubinh.HandType.TwoPair => "👥 Thú",
                CardUtilityMaubinh.HandType.OnePair => "💫 Đôi",
                _ => "🃏 Mậu Thầu"
            };
        }

        /// <summary>
        /// Tạo fallback suggestion
        /// </summary>
        private List<MauBinhSuggestion> CreateFallbackSuggestion(int[] cards)
        {
            var cardList = cards.Select(id => new Card(id)).OrderByDescending(c => c.Value).ToList();

            return new List<MauBinhSuggestion>
            {
                new MauBinhSuggestion
                {
                    Chi1 = cardList.Take(5).Select(c => c.Id).ToArray(),
                    Chi2 = cardList.Skip(5).Take(5).Select(c => c.Id).ToArray(),
                    Chi3 = cardList.Skip(10).Take(3).Select(c => c.Id).ToArray(),
                    Description = "🔧 Gợi ý cơ bản",
                    Score = 100
                }
            };
        }

        /// <summary>
        /// Tạo suggestion từ Chi1
        /// </summary>
        private MauBinhSuggestion CreateSuggestionFromChi1(List<Card> chi1Cards, List<Card> remainingCards, string chi1Type, double chi1Strength)
        {
            // Tìm Chi2 và Chi3 tốt nhất từ 8 lá còn lại
            var chi2Combos = GetCombinations(remainingCards, 5);
            var bestChi2 = chi2Combos
                .Select(combo => new
                {
                    Cards = combo,
                    Strength = CardUtilityMaubinh.GetChiStrength(combo.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList())
                })
                .OrderByDescending(c => c.Strength)
                .First();

            var chi3Cards = remainingCards.Except(bestChi2.Cards).ToList();

            return new MauBinhSuggestion
            {
                Chi1 = chi1Cards.Select(c => c.Id).ToArray(),
                Chi2 = bestChi2.Cards.Select(c => c.Id).ToArray(),
                Chi3 = chi3Cards.Select(c => c.Id).ToArray(),
                Description = $"Chi1: {chi1Type}",
                Score = chi1Strength * 3 + bestChi2.Strength * 2 + CardUtilityMaubinh.GetChiStrength(chi3Cards.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList())
            };
        }

        /// <summary>
        /// Tạo suggestion từ Chi2
        /// </summary>
        private MauBinhSuggestion CreateSuggestionFromChi2(List<Card> remainingCards, List<Card> chi2Cards, string chi2Type, double chi2Strength)
        {
            var otherCards = remainingCards.ToList();

            // Tìm Chi1 mạnh nhất từ 8 lá còn lại
            var chi1Combos = GetCombinations(otherCards, 5);
            var validChi1 = chi1Combos
                .Where(combo => CardUtilityMaubinh.GetChiStrength(combo.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList()) >= chi2Strength)
                .OrderByDescending(combo => CardUtilityMaubinh.GetChiStrength(combo.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList()))
                .FirstOrDefault();

            if (validChi1 == null) return null;

            var chi3Cards = otherCards.Except(validChi1).ToList();

            return new MauBinhSuggestion
            {
                Chi1 = validChi1.Select(c => c.Id).ToArray(),
                Chi2 = chi2Cards.Select(c => c.Id).ToArray(),
                Chi3 = chi3Cards.Select(c => c.Id).ToArray(),
                Description = $"Chi2: {chi2Type}",
                Score = CardUtilityMaubinh.GetChiStrength(validChi1.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList()) * 3 + chi2Strength * 2 + CardUtilityMaubinh.GetChiStrength(chi3Cards.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList())
            };
        }

        /// <summary>
        /// Tạo suggestion từ Chi3
        /// </summary>
        private MauBinhSuggestion CreateSuggestionFromChi3(List<Card> remainingCards, List<Card> chi3Cards, string chi3Type, double chi3Strength)
        {
            var otherCards = remainingCards.ToList();

            // Tìm Chi2 mạnh hơn Chi3
            var chi2Combos = GetCombinations(otherCards, 5);
            var validChi2 = chi2Combos
                .Where(combo => CardUtilityMaubinh.GetChiStrength(combo.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList()) >= chi3Strength)
                .OrderByDescending(combo => CardUtilityMaubinh.GetChiStrength(combo.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList()))
                .FirstOrDefault();

            if (validChi2 == null) return null;

            var chi1Cards = otherCards.Except(validChi2).ToList();
            var chi2Strength = CardUtilityMaubinh.GetChiStrength(validChi2.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList());
            var chi1Strength = CardUtilityMaubinh.GetChiStrength(chi1Cards.Select(c => new CardUtilityMaubinh.CardInfo(c.Id)).ToList());

            // Kiểm tra Chi1 > Chi2
            if (chi1Strength < chi2Strength) return null;

            return new MauBinhSuggestion
            {
                Chi1 = chi1Cards.Select(c => c.Id).ToArray(),
                Chi2 = validChi2.Select(c => c.Id).ToArray(),
                Chi3 = chi3Cards.Select(c => c.Id).ToArray(),
                Description = $"Chi3: {chi3Type}",
                Score = chi1Strength * 3 + chi2Strength * 2 + chi3Strength
            };
        }

        /// <summary>
        /// Kiểm tra suggestion hợp lệ
        /// </summary>
        private bool IsValidSuggestion(MauBinhSuggestion suggestion)
        {
            if (suggestion == null) return false;

            var chi1Strength = CardUtilityMaubinh.GetChiStrength(suggestion.Chi1.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());
            var chi2Strength = CardUtilityMaubinh.GetChiStrength(suggestion.Chi2.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());
            var chi3Strength = CardUtilityMaubinh.GetChiStrength(suggestion.Chi3.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());

            return chi1Strength >= chi2Strength && chi2Strength >= chi3Strength;
        }

        #endregion
    }
}
