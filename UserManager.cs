﻿using System;
using System.Collections.Generic;
using System.IO;
using AutoGameBai.Models;

namespace AutoGameBai
{
    public class UserManager
    {
        private readonly Dictionary<string, User> _hitClubUsers;
        private readonly Dictionary<string, User> _sunWinUsers;
        private readonly Dictionary<string, int> _userRooms;
        private readonly UIManager _uiManager;
        private readonly object _lock = new object();

        public UserManager(UIManager uiManager)
        {
            _hitClubUsers = new Dictionary<string, User>();
            _sunWinUsers = new Dictionary<string, User>();
            _userRooms = new Dictionary<string, int>();
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
        }

        public Dictionary<string, User> GetUsers(string selectedGame)
        {
            lock (_lock)
            {
                return selectedGame == "HitClub" ? _hitClubUsers : _sunWinUsers;
            }
        }

        public Dictionary<string, int> GetUserRooms()
        {
            lock (_lock)
            {
                return _userRooms;
            }
        }

        public void LoadUsersFromFile()
        {
            try
            {
                string hitClubTokenFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "hitclub_token.txt");
                string sunWinTokenFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "sunwin_token.txt");

                lock (_lock)
                {
                    _hitClubUsers.Clear();
                    _sunWinUsers.Clear();

                    if (!File.Exists(hitClubTokenFile) || new FileInfo(hitClubTokenFile).Length == 0)
                    {
                        File.WriteAllLines(hitClubTokenFile, new[] { "nhatrang345", "phanthiet989", "kimnamzhuc" });
                        _uiManager.AppendLog("Tạo hitclub_token.txt mặc định với 3 user", UIManager.LogLevel.Info);
                    }

                    if (File.Exists(hitClubTokenFile))
                    {
                        var lines = File.ReadAllLines(hitClubTokenFile);
                        int userCount = 0;
                        foreach (var line in lines)
                        {
                            if (string.IsNullOrWhiteSpace(line)) continue;
                            string username = line.Trim();
                            if (!_hitClubUsers.ContainsKey(username))
                            {
                                _hitClubUsers.Add(username, new User { Username = username });
                                userCount++;
                            }
                        }
                        _uiManager.AppendLog($"Đã tải {userCount} user từ hitclub_token.txt", UIManager.LogLevel.Info);
                    }

                    if (!File.Exists(sunWinTokenFile) || new FileInfo(sunWinTokenFile).Length == 0)
                    {
                        File.WriteAllLines(sunWinTokenFile, new[] { "user_sunwin1" });
                        _uiManager.AppendLog("Tạo sunwin_token.txt mặc định với 1 user", UIManager.LogLevel.Info);
                    }

                    if (File.Exists(sunWinTokenFile))
                    {
                        var lines = File.ReadAllLines(sunWinTokenFile);
                        int userCount = 0;
                        foreach (var line in lines)
                        {
                            if (string.IsNullOrWhiteSpace(line)) continue;
                            string username = line.Trim();
                            if (!_sunWinUsers.ContainsKey(username))
                            {
                                _sunWinUsers.Add(username, new User { Username = username });
                                userCount++;
                            }
                        }
                        _uiManager.AppendLog($"Đã tải {userCount} user từ sunwin_token.txt", UIManager.LogLevel.Info);
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi tải user từ file: {ex.Message}\nStackTrace: {ex.StackTrace}", UIManager.LogLevel.Error);
            }
        }

        public void ReloadUsersFromFile()
        {
            LoadUsersFromFile();
            _uiManager.AppendLog("Đã tải lại token.txt", UIManager.LogLevel.Info);
        }

        public void UpdateTokenFile()
        {
            try
            {
                string hitClubTokenFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "hitclub_token.txt");
                string sunWinTokenFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "sunwin_token.txt");

                lock (_lock)
                {
                    File.WriteAllLines(hitClubTokenFile, _hitClubUsers.Keys);
                    File.WriteAllLines(sunWinTokenFile, _sunWinUsers.Keys);
                }

                _uiManager.AppendLog("Đã cập nhật token.txt", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi cập nhật token.txt: {ex.Message}\nStackTrace: {ex.StackTrace}", UIManager.LogLevel.Error);
            }
        }

        public void UpdateUserRoom(string username, int roomId)
        {
            lock (_lock)
            {
                _userRooms[username] = roomId;
            }
        }

        public void ClearUserRoom(string username)
        {
            lock (_lock)
            {
                _userRooms.Remove(username);
            }
        }
    }
}