using System;
using System.Collections.Generic;
using System.Linq;

namespace AutoGameBai.Gamephom
{
    /// <summary>
    /// Thuật toán Phỏm tối ưu hóa với GoiYBaiTeam và GoiYDoiThu
    /// </summary>
    public class PhomOptimizedGameLogic
    {
        private readonly GameClientManager _gameClient;
        private readonly PhomCardManager _phomCardManager;
        private readonly UIManager _uiManager;

        public PhomOptimizedGameLogic(GameClientManager gameClient, PhomCardManager phomCardManager, UIManager uiManager)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _phomCardManager = phomCardManager ?? throw new ArgumentNullException(nameof(phomCardManager));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
        }

        /// <summary>
        /// Gợi ý lá bài ch<PERSON>h - phân biệt Team và Solo
        /// </summary>
        public (int suggestedCard, string analysis) SuggestCard(string username, bool isFirstPlayer)
        {
            try
            {
                var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
                if (!userCardsDict.ContainsKey(username))
                {
                    return (-1, "Không tìm thấy bài của user");
                }

                var userCards = userCardsDict[username];
                bool isSolo = userCardsDict.Count == 1;
                bool isTeamMode = userCardsDict.Count >= 2;

                _uiManager.AppendLog($"🎯 Gợi ý cho {username}: {(isSolo ? "Solo" : "Team")} mode với {userCardsDict.Count} users", UIManager.LogLevel.Info);

                if (isSolo)
                {
                    // Solo mode - luôn gợi ý chống đối thủ
                    _uiManager.AppendLog($"⚔️ Solo mode: Gọi GoiYDoiThu cho {username}", UIManager.LogLevel.Info);
                    return GoiYDoiThu(username, userCards);
                }
                else if (isTeamMode)
                {
                    // Team mode - kiểm tra lượt tiếp theo
                    string nextPlayer = GetNextPlayer(username);
                    bool nextPlayerIsTeammate = IsTeammate(username, nextPlayer);

                    _uiManager.AppendLog($"🤝 Team mode ({userCardsDict.Count} players): {username} → {nextPlayer}, isTeammate={nextPlayerIsTeammate}", UIManager.LogLevel.Info);

                    if (nextPlayerIsTeammate)
                    {
                        _uiManager.AppendLog($"🤝 Gọi GoiYBaiTeam cho {username} → teammate {nextPlayer}", UIManager.LogLevel.Info);
                        return GoiYBaiTeam(username, userCards, nextPlayer);
                    }
                    else
                    {
                        _uiManager.AppendLog($"⚔️ Gọi GoiYDoiThu cho {username} → opponent {nextPlayer}", UIManager.LogLevel.Info);
                        return GoiYDoiThu(username, userCards);
                    }
                }

                return (-1, "Không xác định được chế độ chơi");
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi gợi ý cho {username}: {ex.Message}", UIManager.LogLevel.Error);
                return (-1, $"Lỗi: {ex.Message}");
            }
        }

        /// <summary>
        /// Gợi ý bài cho teammate - ƯU TIÊN TẠO PHỎM/Ù CHO TEAMMATE
        /// </summary>
        private (int suggestedCard, string analysis) GoiYBaiTeam(string username, int[] userCards, string nextPlayer)
        {
            try
            {
                _uiManager.AppendLog($"🤝 GoiYBaiTeam: {username} → {nextPlayer}", UIManager.LogLevel.Info);

                // Tìm các lá bài trong phỏm của user (KHÔNG được đánh)
                var userPhomCards = FindUserPhomCards(userCards);
                var availableCards = userCards.Where(c => !userPhomCards.Contains(c)).ToArray();

                _uiManager.AppendLog($"Team {username}: {userCards.Length} lá bài, {userPhomCards.Count} lá trong phỏm, {availableCards.Length} lá có thể đánh", UIManager.LogLevel.Info);

                if (availableCards.Length == 0)
                {
                    return (-1, "❌ Không có lá bài nào có thể đánh (tất cả đều trong phỏm)");
                }

                var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
                if (!userCardsDict.ContainsKey(nextPlayer))
                {
                    return (-1, "Không tìm thấy bài của teammate");
                }

                var teammateCards = userCardsDict[nextPlayer];
                var teammateCardsInfo = teammateCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();
                var availableCardsInfo = availableCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();

                _uiManager.AppendLog($"🎯 BƯỚC 1: Kiểm tra lá tạo PHỎM cho teammate {nextPlayer}", UIManager.LogLevel.Info);

                // BƯỚC 1: ƯU TIÊN TUYỆT ĐỐI - Tìm lá tạo PHỎM cho teammate
                var cardToCreatePhom = FindCardToCreatePhomForTeammate(availableCardsInfo, teammateCardsInfo);
                if (cardToCreatePhom != -1)
                {
                    return (cardToCreatePhom, $"🏆 TẠO PHỎM cho teammate {nextPlayer} - Ưu tiên cao nhất!");
                }

                _uiManager.AppendLog($"🎯 BƯỚC 2: Kiểm tra lá giúp Ù cho teammate {nextPlayer}", UIManager.LogLevel.Info);

                // BƯỚC 2: Kiểm tra lá giúp teammate Ù (nếu teammate còn ít bài)
                if (teammateCards.Length <= 3)
                {
                    var cardToHelp = FindCardToHelpTeammateWin(availableCardsInfo, teammateCardsInfo);
                    if (cardToHelp != -1)
                    {
                        return (cardToHelp, $"🎉 GIÚP Ù cho teammate {nextPlayer} - Còn {teammateCards.Length} lá!");
                    }
                }

                _uiManager.AppendLog($"🎯 BƯỚC 3: Kiểm tra lá giúp CẠ của teammate {nextPlayer}", UIManager.LogLevel.Info);

                // BƯỚC 3: Tìm "cạ" của teammate và gợi ý lá tạo phỏm cho cạ
                var teammateCa = FindCaCards(teammateCardsInfo);
                if (teammateCa.Any())
                {
                    var suggestedCard = FindCardToHelpCa(availableCardsInfo, teammateCa);
                    if (suggestedCard != -1)
                    {
                        string caNames = string.Join(", ", teammateCa.Select(c => CardUtilityPhom.ConvertCardsToString(new[] { c.Id })));
                        return (suggestedCard, $"🤝 Nhường bài cho CẠ teammate {nextPlayer} - Cạ: {caNames}");
                    }
                }

                _uiManager.AppendLog($"🎯 BƯỚC 4: Không có lá tạo phỏm/ù → Kiểm tra CẠ của user hiện tại", UIManager.LogLevel.Info);

                // BƯỚC 4: Không có lá tạo phỏm cho teammate → Kiểm tra "cạ" của user hiện tại
                var userCa = FindCaCards(availableCardsInfo);
                if (userCa.Any())
                {
                    // Tìm lá rác (không phải cạ) để đánh
                    var trashCard = FindTrashCardAvoidingCa(availableCardsInfo, userCa);
                    if (trashCard != -1)
                    {
                        string caNames = string.Join(", ", userCa.Select(c => CardUtilityPhom.ConvertCardsToString(new[] { c.Id })));
                        return (trashCard, $"🗑️ Đánh lá rác, giữ CẠ của {username} - Cạ: {caNames}");
                    }
                }

                _uiManager.AppendLog($"🎯 BƯỚC 5: Tìm bài an toàn cho team", UIManager.LogLevel.Info);

                // BƯỚC 5: Tránh đánh bài nguy hiểm cho team
                var safeCard = FindSafeCardForTeam(availableCards, teammateCards);
                if (safeCard != -1)
                {
                    return (safeCard, $"🛡️ Đánh bài an toàn cho team với {nextPlayer}");
                }

                // BƯỚC 6: Fallback - đánh bài nhỏ nhất
                var smallestCard = availableCards.Min();
                return (smallestCard, $"🤝 Đánh bài nhỏ nhất cho team với {nextPlayer}");
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GoiYBaiTeam: {ex.Message}", UIManager.LogLevel.Error);
                return (-1, $"Lỗi team: {ex.Message}");
            }
        }

        /// <summary>
        /// Gợi ý bài chống đối thủ
        /// </summary>
        private (int suggestedCard, string analysis) GoiYDoiThu(string username, int[] userCards)
        {
            try
            {
                _uiManager.AppendLog($"⚔️ GoiYDoiThu cho {username}", UIManager.LogLevel.Info);

                // Tìm các lá bài trong phỏm của user (KHÔNG được đánh)
                var userPhomCards = FindUserPhomCards(userCards);
                var availableCards = userCards.Where(c => !userPhomCards.Contains(c)).ToArray();

                _uiManager.AppendLog($"User {username}: {userCards.Length} lá bài, {userPhomCards.Count} lá trong phỏm, {availableCards.Length} lá có thể đánh", UIManager.LogLevel.Info);

                if (availableCards.Length == 0)
                {
                    return (-1, "❌ Không có lá bài nào có thể đánh (tất cả đều trong phỏm)");
                }

                // Lấy danh sách bài để phân tích đối thủ (bao gồm team cards)
                var listCardGoiYDoiThu = GetOpponentAnalysisCards(username);

                // Tìm "cạ" của đối thủ trong listCardGoiYDoiThu
                var opponentCa = FindCaCards(listCardGoiYDoiThu.Select(c => new CardUtilityPhom.CardInfo(c)).ToList());

                if (opponentCa.Any())
                {
                    _uiManager.AppendLog($"🎯 Tìm thấy {opponentCa.Count} lá cạ trong listCardGoiYDoiThu", UIManager.LogLevel.Info);

                    // CHIẾN THUẬT MỚI: Tìm lá tạo phỏm cho listCardGoiYDoiThu
                    var cardToHelpTeam = FindCardToCreatePhomForTeam(availableCards, listCardGoiYDoiThu);
                    if (cardToHelpTeam != -1)
                    {
                        return (cardToHelpTeam, $"🤝 Đánh bài tạo phỏm cho team/đối thủ - Phân tích từ {listCardGoiYDoiThu.Count} lá bài");
                    }

                    // Đối thủ có "cạ" - tìm lá có thể tạo phỏm cho cạ (trong available cards)
                    var availableCardsInfo = availableCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();
                    var dangerousCards = FindCardsThatHelpCa(availableCardsInfo, opponentCa);

                    if (dangerousCards.Any())
                    {
                        // Có lá nguy hiểm - kiểm tra xem có lá nào không phải cạ của mình
                        var userCa = FindCaCards(availableCardsInfo);
                        var safeDangerousCards = dangerousCards.Where(c => !userCa.Any(uc => uc.Id == c)).ToList();

                        if (safeDangerousCards.Any())
                        {
                            var suggestedCard = safeDangerousCards.First();
                            string caNames = string.Join(", ", opponentCa.Select(c => CardUtilityPhom.ConvertCardsToString(new[] { c.Id })));
                            return (suggestedCard, $"⚔️ Đánh bài tạo phỏm cho cạ - Cạ: {caNames} (Chiến thuật tấn công)");
                        }
                    }

                    // Tránh đánh bài giúp đối thủ (chỉ trong available cards)
                    var safeCard = FindSafeCardAgainstCa(availableCards, opponentCa);
                    if (safeCard != -1)
                    {
                        string caNames = string.Join(", ", opponentCa.Select(c => CardUtilityPhom.ConvertCardsToString(new[] { c.Id })));
                        return (safeCard, $"🛡️ Tránh giúp cạ - Cạ: {caNames}");
                    }
                }

                // Không có cạ hoặc không xác định được - tính toán bài chưa biết
                var unknownCards = CalculateUnknownCards();
                var conservativeCard = FindConservativeCard(availableCards, unknownCards);

                if (conservativeCard != -1)
                {
                    return (conservativeCard, $"🎯 Đánh bài bảo thủ - Tránh tạo phỏm cho bài chưa biết ({unknownCards.Count} lá)");
                }

                // Fallback - đánh lá lẻ thay vì bài lớn nhất
                var oddCards = FindOddCards(availableCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList());
                if (oddCards.Any())
                {
                    var largestOddCard = oddCards.OrderByDescending(c => CardUtilityPhom.GetCardValue(c.Rank)).First();
                    return (largestOddCard.Id, $"🎯 Đánh lá lẻ {CardUtilityPhom.GetRankName(largestOddCard.Rank)}{GetSuitSymbol(largestOddCard.Suit)} - Tối ưu");
                }

                // Nếu không có lá lẻ, đánh bài lớn nhất
                var largestCard = availableCards.Max();
                return (largestCard, "⚔️ Đánh bài lớn nhất - Chiến thuật tấn công");
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GoiYDoiThu: {ex.Message}", UIManager.LogLevel.Error);
                return (-1, $"Lỗi đối thủ: {ex.Message}");
            }
        }

        /// <summary>
        /// Tìm các lá bài trong phỏm của user (không được đánh)
        /// </summary>
        private List<int> FindUserPhomCards(int[] userCards)
        {
            var phomCards = new List<int>();
            var cardsInfo = userCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();

            // Tìm các phỏm sảnh (3+ lá liên tiếp cùng chất)
            var suitGroups = cardsInfo.GroupBy(c => c.Suit);
            foreach (var suitGroup in suitGroups)
            {
                var sortedCards = suitGroup.OrderBy(c => c.Rank).ToList();
                if (sortedCards.Count >= 3)
                {
                    // Tìm chuỗi liên tiếp
                    for (int i = 0; i <= sortedCards.Count - 3; i++)
                    {
                        var consecutiveCards = new List<CardUtilityPhom.CardInfo>();
                        consecutiveCards.Add(sortedCards[i]);

                        for (int j = i + 1; j < sortedCards.Count; j++)
                        {
                            if (sortedCards[j].Rank == consecutiveCards.Last().Rank + 1)
                            {
                                consecutiveCards.Add(sortedCards[j]);
                            }
                            else
                            {
                                break;
                            }
                        }

                        // Nếu có 3+ lá liên tiếp thì là phỏm sảnh
                        if (consecutiveCards.Count >= 3)
                        {
                            phomCards.AddRange(consecutiveCards.Select(c => c.Id));
                            _uiManager.AppendLog($"Tìm thấy phỏm sảnh: {string.Join(", ", consecutiveCards.Select(c => CardUtilityPhom.ConvertCardsToString(new[] { c.Id })))}", UIManager.LogLevel.Info);
                        }
                    }
                }
            }

            // Tìm các phỏm đôi/ba/tứ (3+ lá cùng rank)
            var rankGroups = cardsInfo.GroupBy(c => c.Rank).Where(g => g.Count() >= 3);
            foreach (var rankGroup in rankGroups)
            {
                var groupCards = rankGroup.ToList();
                phomCards.AddRange(groupCards.Select(c => c.Id));
                _uiManager.AppendLog($"Tìm thấy phỏm đôi/ba: {string.Join(", ", groupCards.Select(c => CardUtilityPhom.ConvertCardsToString(new[] { c.Id })))}", UIManager.LogLevel.Info);
            }

            return phomCards.Distinct().ToList();
        }

        /// <summary>
        /// Lấy danh sách bài để phân tích đối thủ - CẢI THIỆN THEO YÊU CẦU
        /// listcardgoiydoithu = các lá bài đã đánh + list bài user hiện tại + list bài user team
        /// </summary>
        private List<int> GetOpponentAnalysisCards(string currentUsername = null)
        {
            var result = new List<int>();

            try
            {
                // 1. Thêm các lá bài đã đánh
                var playedCards = _gameClient.GetPlayedCards();
                if (playedCards != null)
                {
                    result.AddRange(playedCards);
                    _uiManager.AppendLog($"📋 Thêm {playedCards.Count} lá bài đã đánh vào phân tích", UIManager.LogLevel.Debug);
                }

                // 2. Thêm bài của user hiện tại (nếu có)
                if (!string.IsNullOrEmpty(currentUsername))
                {
                    var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
                    if (userCardsDict.ContainsKey(currentUsername))
                    {
                        var currentUserCards = userCardsDict[currentUsername];
                        result.AddRange(currentUserCards);
                        _uiManager.AppendLog($"👤 Thêm {currentUserCards.Length} lá bài của user {currentUsername} vào phân tích", UIManager.LogLevel.Debug);
                    }

                    // 3. Thêm bài của team (nếu có)
                    var teamCards = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
                    foreach (var teamUser in teamCards.Keys)
                    {
                        if (teamUser != currentUsername) // Không thêm lại bài của chính mình
                        {
                            var teammateCards = teamCards[teamUser];
                            result.AddRange(teammateCards);
                            _uiManager.AppendLog($"🤝 Thêm {teammateCards.Length} lá bài của teammate {teamUser} vào phân tích", UIManager.LogLevel.Debug);
                        }
                    }
                }
                else
                {
                    // Fallback: Nếu không có currentUsername, thêm tất cả user cards
                    var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
                    if (userCardsDict.Count >= 2)
                    {
                        foreach (var kvp in userCardsDict)
                        {
                            result.AddRange(kvp.Value);
                            _uiManager.AppendLog($"👥 Thêm {kvp.Value.Length} lá bài của {kvp.Key} vào phân tích (fallback)", UIManager.LogLevel.Debug);
                        }
                    }
                }

                // 4. Thêm bài từ các phỏm đã hạ (nếu có) - TẠM THỜI COMMENT VÌ CHƯA CÓ METHOD
                // var droppedPhoms = _gameClient.GetWebSocketManager().GetPhomHandler().GetDroppedPhoms();
                // if (droppedPhoms != null)
                // {
                //     foreach (var phom in droppedPhoms)
                //     {
                //         result.AddRange(phom);
                //     }
                //     _uiManager.AppendLog($"🎯 Thêm {droppedPhoms.Sum(p => p.Count)} lá bài từ phỏm đã hạ vào phân tích", UIManager.LogLevel.Debug);
                // }

                var uniqueResult = result.Distinct().ToList();
                _uiManager.AppendLog($"📊 Tổng cộng có {uniqueResult.Count} lá bài để phân tích đối thủ (từ {result.Count} lá ban đầu)", UIManager.LogLevel.Info);

                return uniqueResult;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi lấy danh sách bài phân tích đối thủ: {ex.Message}", UIManager.LogLevel.Error);
                return result.Distinct().ToList();
            }
        }

        /// <summary>
        /// Tìm các lá "cạ" (có thể tạo phỏm) - CẢI THIỆN LOGIC CẠ DỌC VÀ CẠ NGANG
        /// </summary>
        private List<CardUtilityPhom.CardInfo> FindCaCards(List<CardUtilityPhom.CardInfo> cards)
        {
            var caCards = new List<CardUtilityPhom.CardInfo>();

            _uiManager.AppendLog($"🔍 Tìm cạ trong {cards.Count} lá bài: {string.Join(", ", cards.Select(c => c.Id))}", UIManager.LogLevel.Debug);

            // 1. CẠ NGANG (đôi - 2 lá cùng rank)
            var rankGroups = cards.GroupBy(c => c.Rank).ToList();
            foreach (var rankGroup in rankGroups)
            {
                if (rankGroup.Count() == 2)
                {
                    var pairCards = rankGroup.ToList();
                    caCards.AddRange(pairCards);
                    _uiManager.AppendLog($"✅ Tìm thấy cạ ngang (đôi): {CardUtilityPhom.GetRankName(rankGroup.Key)} - {string.Join(", ", pairCards.Select(c => c.Id))}", UIManager.LogLevel.Debug);
                }
                else if (rankGroup.Count() >= 3)
                {
                    // Nếu có 3+ lá cùng rank, chọn 2 lá để tạo cạ
                    var selectedCards = rankGroup.Take(2).ToList();
                    caCards.AddRange(selectedCards);
                    _uiManager.AppendLog($"✅ Tìm thấy cạ ngang (từ xám): {CardUtilityPhom.GetRankName(rankGroup.Key)} - {string.Join(", ", selectedCards.Select(c => c.Id))}", UIManager.LogLevel.Debug);
                }
            }

            // 2. CẠ DỌC (sảnh - 2 lá có thể tạo sảnh cùng chất)
            var suitGroups = cards.GroupBy(c => c.Suit).ToList();
            foreach (var suitGroup in suitGroups)
            {
                if (suitGroup.Count() >= 2)
                {
                    var sortedCards = suitGroup.OrderBy(c => CardUtilityPhom.GetCardValue(c.Rank)).ToList();

                    // Tìm tất cả các cặp có thể tạo sảnh (liên tiếp hoặc cách 1 lá)
                    for (int i = 0; i < sortedCards.Count - 1; i++)
                    {
                        for (int j = i + 1; j < sortedCards.Count; j++)
                        {
                            var card1 = sortedCards[i];
                            var card2 = sortedCards[j];

                            // Kiểm tra xem đã có trong caCards chưa
                            if (caCards.Any(c => c.Id == card1.Id) || caCards.Any(c => c.Id == card2.Id))
                                continue;

                            var value1 = CardUtilityPhom.GetCardValue(card1.Rank);
                            var value2 = CardUtilityPhom.GetCardValue(card2.Rank);
                            var diff = Math.Abs(value2 - value1);

                            // Kiểm tra có thể tạo sảnh không (liên tiếp hoặc cách 1 lá)
                            bool canFormSequence = false;
                            string sequenceType = "";

                            if (diff == 1)
                            {
                                canFormSequence = true;
                                sequenceType = "liên tiếp";
                            }
                            else if (diff == 2)
                            {
                                canFormSequence = true;
                                sequenceType = "cách 1 lá";
                            }
                            // Xử lý trường hợp đặc biệt A-K (A=1, K=13)
                            else if ((value1 == 1 && value2 == 13) || (value1 == 13 && value2 == 1))
                            {
                                canFormSequence = true;
                                sequenceType = "A-K";
                            }

                            if (canFormSequence)
                            {
                                caCards.Add(card1);
                                caCards.Add(card2);
                                _uiManager.AppendLog($"✅ Tìm thấy cạ dọc ({sequenceType}): {GetSuitSymbol(card1.Suit)} {CardUtilityPhom.GetRankName(card1.Rank)}-{CardUtilityPhom.GetRankName(card2.Rank)} - {card1.Id}, {card2.Id}", UIManager.LogLevel.Debug);
                                break; // Thoát khỏi vòng lặp j để tránh sử dụng card1 nhiều lần
                            }
                        }
                    }
                }
            }

            var uniqueCaCards = caCards.Distinct().ToList();
            _uiManager.AppendLog($"🎯 Tổng cộng tìm thấy {uniqueCaCards.Count} lá cạ: {string.Join(", ", uniqueCaCards.Select(c => c.Id))}", UIManager.LogLevel.Info);

            return uniqueCaCards;
        }

        /// <summary>
        /// Tìm lá bài tạo phỏm cho team/đối thủ từ listCardGoiYDoiThu
        /// Ưu tiên kiểm tra phỏm dọc (sảnh), phỏm ngang (đôi/ba)
        /// </summary>
        private int FindCardToCreatePhomForTeam(int[] availableCards, List<int> listCardGoiYDoiThu)
        {
            try
            {
                var availableCardsInfo = availableCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();
                var teamCardsInfo = listCardGoiYDoiThu.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();

                _uiManager.AppendLog($"🔍 Tìm lá tạo phỏm cho team từ {availableCards.Length} lá available và {listCardGoiYDoiThu.Count} lá team", UIManager.LogLevel.Debug);

                // 1. PHỎM DỌC (SẢNH) - Ưu tiên cao nhất
                foreach (var availableCard in availableCardsInfo)
                {
                    // Tìm các lá cùng chất trong team
                    var sameSuitCards = teamCardsInfo.Where(c => c.Suit == availableCard.Suit).ToList();

                    if (sameSuitCards.Count >= 2)
                    {
                        // Kiểm tra có thể tạo sảnh không
                        var allCards = sameSuitCards.Concat(new[] { availableCard }).OrderBy(c => c.Rank).ToList();

                        // Tìm chuỗi liên tiếp dài nhất
                        var longestSequence = FindLongestSequence(allCards);

                        if (longestSequence.Count >= 3 && longestSequence.Any(c => c.Id == availableCard.Id))
                        {
                            _uiManager.AppendLog($"✅ Tìm thấy phỏm dọc (sảnh): {string.Join("-", longestSequence.Select(c => CardUtilityPhom.GetRankName(c.Rank)))} Suit{availableCard.Suit}", UIManager.LogLevel.Info);
                            return availableCard.Id;
                        }
                    }
                }

                // 2. PHỎM NGANG (ĐÔI/BA) - Ưu tiên thứ hai
                foreach (var availableCard in availableCardsInfo)
                {
                    // Tìm các lá cùng rank trong team
                    var sameRankCards = teamCardsInfo.Where(c => c.Rank == availableCard.Rank).ToList();

                    if (sameRankCards.Count >= 2)
                    {
                        _uiManager.AppendLog($"✅ Tìm thấy phỏm ngang (ba): {CardUtilityPhom.GetRankName(availableCard.Rank)} x{sameRankCards.Count + 1}", UIManager.LogLevel.Info);
                        return availableCard.Id;
                    }
                    else if (sameRankCards.Count == 1)
                    {
                        _uiManager.AppendLog($"✅ Tìm thấy phỏm ngang (đôi): {CardUtilityPhom.GetRankName(availableCard.Rank)} x2", UIManager.LogLevel.Info);
                        return availableCard.Id;
                    }
                }

                _uiManager.AppendLog($"❌ Không tìm thấy lá nào tạo phỏm cho team", UIManager.LogLevel.Debug);
                return -1;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi FindCardToCreatePhomForTeam: {ex.Message}", UIManager.LogLevel.Error);
                return -1;
            }
        }

        /// <summary>
        /// Tìm chuỗi liên tiếp dài nhất trong danh sách bài cùng chất
        /// </summary>
        private List<CardUtilityPhom.CardInfo> FindLongestSequence(List<CardUtilityPhom.CardInfo> cards)
        {
            if (cards.Count < 2) return cards;

            var sortedCards = cards.OrderBy(c => c.Rank).ToList();
            var longestSequence = new List<CardUtilityPhom.CardInfo>();
            var currentSequence = new List<CardUtilityPhom.CardInfo> { sortedCards[0] };

            for (int i = 1; i < sortedCards.Count; i++)
            {
                var prevCard = sortedCards[i - 1];
                var currentCard = sortedCards[i];

                // Kiểm tra liên tiếp (bao gồm A-2 và K-A)
                bool isConsecutive = false;
                if (currentCard.Rank - prevCard.Rank == 1)
                {
                    isConsecutive = true; // Bình thường: 2-3, 3-4, ..., Q-K
                }
                else if (prevCard.Rank == 1 && currentCard.Rank == 2)
                {
                    isConsecutive = true; // A-2
                }
                else if (prevCard.Rank == 13 && currentCard.Rank == 1)
                {
                    isConsecutive = true; // K-A
                }

                if (isConsecutive)
                {
                    currentSequence.Add(currentCard);
                }
                else
                {
                    if (currentSequence.Count > longestSequence.Count)
                    {
                        longestSequence = new List<CardUtilityPhom.CardInfo>(currentSequence);
                    }
                    currentSequence = new List<CardUtilityPhom.CardInfo> { currentCard };
                }
            }

            // Kiểm tra sequence cuối cùng
            if (currentSequence.Count > longestSequence.Count)
            {
                longestSequence = new List<CardUtilityPhom.CardInfo>(currentSequence);
            }

            return longestSequence;
        }

        /// <summary>
        /// Tìm lá bài tạo PHỎM trực tiếp cho teammate (ưu tiên cao nhất)
        /// </summary>
        private int FindCardToCreatePhomForTeammate(List<CardUtilityPhom.CardInfo> availableCards, List<CardUtilityPhom.CardInfo> teammateCards)
        {
            try
            {
                _uiManager.AppendLog($"🔍 Tìm lá tạo PHỎM cho teammate từ {availableCards.Count} lá available", UIManager.LogLevel.Debug);

                // 1. PHỎM NGANG (ĐÔI/BA) - Ưu tiên cao nhất vì dễ tạo
                foreach (var availableCard in availableCards)
                {
                    var sameRankCards = teammateCards.Where(c => c.Rank == availableCard.Rank).ToList();

                    if (sameRankCards.Count >= 2)
                    {
                        _uiManager.AppendLog($"✅ Tìm thấy PHỎM NGANG (BA): {CardUtilityPhom.GetRankName(availableCard.Rank)} x{sameRankCards.Count + 1}", UIManager.LogLevel.Info);
                        return availableCard.Id;
                    }
                    else if (sameRankCards.Count == 1)
                    {
                        _uiManager.AppendLog($"✅ Tìm thấy PHỎM NGANG (ĐÔI): {CardUtilityPhom.GetRankName(availableCard.Rank)} x2", UIManager.LogLevel.Info);
                        return availableCard.Id;
                    }
                }

                // 2. PHỎM DỌC (SẢNH) - Ưu tiên thứ hai
                foreach (var availableCard in availableCards)
                {
                    var sameSuitCards = teammateCards.Where(c => c.Suit == availableCard.Suit).ToList();

                    if (sameSuitCards.Count >= 2)
                    {
                        var allCards = sameSuitCards.Concat(new[] { availableCard }).OrderBy(c => c.Rank).ToList();
                        var longestSequence = FindLongestSequence(allCards);

                        if (longestSequence.Count >= 3 && longestSequence.Any(c => c.Id == availableCard.Id))
                        {
                            _uiManager.AppendLog($"✅ Tìm thấy PHỎM DỌC (SẢNH): {string.Join("-", longestSequence.Select(c => CardUtilityPhom.GetRankName(c.Rank)))} Suit{availableCard.Suit}", UIManager.LogLevel.Info);
                            return availableCard.Id;
                        }
                    }
                }

                _uiManager.AppendLog($"❌ Không tìm thấy lá nào tạo PHỎM trực tiếp cho teammate", UIManager.LogLevel.Debug);
                return -1;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi FindCardToCreatePhomForTeammate: {ex.Message}", UIManager.LogLevel.Error);
                return -1;
            }
        }

        /// <summary>
        /// Tìm lá bài giúp teammate Ù (khi teammate còn ít bài)
        /// </summary>
        private int FindCardToHelpTeammateWin(List<CardUtilityPhom.CardInfo> availableCards, List<CardUtilityPhom.CardInfo> teammateCards)
        {
            try
            {
                _uiManager.AppendLog($"🎉 Tìm lá giúp teammate Ù từ {availableCards.Count} lá available", UIManager.LogLevel.Debug);

                // Kiểm tra từng lá available xem có giúp teammate tạo phỏm để ù không
                foreach (var availableCard in availableCards)
                {
                    // Kiểm tra phỏm ngang
                    var sameRankCards = teammateCards.Where(c => c.Rank == availableCard.Rank).ToList();
                    if (sameRankCards.Count >= 1)
                    {
                        _uiManager.AppendLog($"✅ Lá giúp Ù (phỏm ngang): {CardUtilityPhom.GetRankName(availableCard.Rank)}", UIManager.LogLevel.Info);
                        return availableCard.Id;
                    }

                    // Kiểm tra phỏm dọc
                    var sameSuitCards = teammateCards.Where(c => c.Suit == availableCard.Suit).ToList();
                    foreach (var teammateCard in sameSuitCards)
                    {
                        if (Math.Abs(availableCard.Rank - teammateCard.Rank) <= 2)
                        {
                            _uiManager.AppendLog($"✅ Lá giúp Ù (phỏm dọc): {CardUtilityPhom.GetRankName(availableCard.Rank)}{GetSuitSymbol(availableCard.Suit)}", UIManager.LogLevel.Info);
                            return availableCard.Id;
                        }
                    }
                }

                _uiManager.AppendLog($"❌ Không tìm thấy lá nào giúp teammate Ù", UIManager.LogLevel.Debug);
                return -1;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi FindCardToHelpTeammateWin: {ex.Message}", UIManager.LogLevel.Error);
                return -1;
            }
        }

        /// <summary>
        /// Tìm lá rác (không phải cạ) để đánh
        /// </summary>
        private int FindTrashCardAvoidingCa(List<CardUtilityPhom.CardInfo> availableCards, List<CardUtilityPhom.CardInfo> userCa)
        {
            try
            {
                _uiManager.AppendLog($"🗑️ Tìm lá rác từ {availableCards.Count} lá available, tránh {userCa.Count} lá cạ", UIManager.LogLevel.Debug);

                // Tìm lá không phải cạ
                var trashCards = availableCards.Where(c => !userCa.Any(ca => ca.Id == c.Id)).ToList();

                if (trashCards.Any())
                {
                    // Ưu tiên lá có giá trị cao nhất trong lá rác
                    var bestTrashCard = trashCards.OrderByDescending(c => CardUtilityPhom.GetCardValue(c.Rank)).First();
                    _uiManager.AppendLog($"✅ Tìm thấy lá rác: {CardUtilityPhom.GetRankName(bestTrashCard.Rank)}{GetSuitSymbol(bestTrashCard.Suit)}", UIManager.LogLevel.Info);
                    return bestTrashCard.Id;
                }

                _uiManager.AppendLog($"❌ Không có lá rác, tất cả đều là cạ", UIManager.LogLevel.Debug);
                return -1;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi FindTrashCardAvoidingCa: {ex.Message}", UIManager.LogLevel.Error);
                return -1;
            }
        }

        /// <summary>
        /// Tìm lá bài có thể giúp tạo phỏm cho cạ
        /// </summary>
        private int FindCardToHelpCa(List<CardUtilityPhom.CardInfo> userCards, List<CardUtilityPhom.CardInfo> caCards)
        {
            foreach (var ca in caCards)
            {
                // Tìm lá thứ 3 cho đôi
                var sameRankCard = userCards.FirstOrDefault(c => c.Rank == ca.Rank && c.Id != ca.Id);
                if (sameRankCard != null)
                {
                    return sameRankCard.Id;
                }

                // Tìm lá tạo sảnh
                var sanhCard = userCards.FirstOrDefault(c =>
                    c.Suit == ca.Suit &&
                    (Math.Abs(c.Rank - ca.Rank) == 1 || Math.Abs(c.Rank - ca.Rank) == 2));
                if (sanhCard != null)
                {
                    return sanhCard.Id;
                }
            }

            return -1;
        }

        /// <summary>
        /// Tìm các lá bài có thể giúp tạo phỏm cho cạ
        /// </summary>
        private List<int> FindCardsThatHelpCa(List<CardUtilityPhom.CardInfo> userCards, List<CardUtilityPhom.CardInfo> caCards)
        {
            var helpfulCards = new List<int>();

            foreach (var userCard in userCards)
            {
                foreach (var ca in caCards)
                {
                    // Kiểm tra có thể tạo đôi/ba
                    if (userCard.Rank == ca.Rank)
                    {
                        helpfulCards.Add(userCard.Id);
                        continue;
                    }

                    // Kiểm tra có thể tạo sảnh
                    if (userCard.Suit == ca.Suit && Math.Abs(userCard.Rank - ca.Rank) <= 2)
                    {
                        helpfulCards.Add(userCard.Id);
                    }
                }
            }

            return helpfulCards.Distinct().ToList();
        }

        /// <summary>
        /// Tìm lá bài an toàn không giúp cạ
        /// </summary>
        private int FindSafeCardAgainstCa(int[] userCards, List<CardUtilityPhom.CardInfo> caCards)
        {
            var userCardsInfo = userCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();
            var dangerousCards = FindCardsThatHelpCa(userCardsInfo, caCards);

            var safeCards = userCards.Where(c => !dangerousCards.Contains(c)).ToList();
            return safeCards.Any() ? safeCards.Min() : -1;
        }

        /// <summary>
        /// Tìm lá bài an toàn cho team
        /// </summary>
        private int FindSafeCardForTeam(int[] userCards, int[] teammateCards)
        {
            // Tránh đánh lá có thể tạo phỏm cho teammate (trừ khi cần thiết)
            var userCardsInfo = userCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();
            var teammateCardsInfo = teammateCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();

            // Tìm lá không liên quan đến bài của teammate
            foreach (var userCard in userCardsInfo)
            {
                bool isSafe = true;
                foreach (var teammateCard in teammateCardsInfo)
                {
                    if (userCard.Rank == teammateCard.Rank ||
                        (userCard.Suit == teammateCard.Suit && Math.Abs(userCard.Rank - teammateCard.Rank) <= 2))
                    {
                        isSafe = false;
                        break;
                    }
                }

                if (isSafe)
                {
                    return userCard.Id;
                }
            }

            return -1;
        }

        /// <summary>
        /// Tìm lá lẻ (không thuộc cạ nào) - LOGIC CHÍNH XÁC THEO VÍ DỤ 8♦,8♣,9♦,5♥
        /// </summary>
        private List<CardUtilityPhom.CardInfo> FindOddCards(List<CardUtilityPhom.CardInfo> cards)
        {
            _uiManager.AppendLog($"🔍 Phân tích lá lẻ từ {cards.Count} lá: {string.Join(", ", cards.Select(c => $"{CardUtilityPhom.GetRankName(c.Rank)}{GetSuitSymbol(c.Suit)}"))}",  UIManager.LogLevel.Debug);

            var oddCards = new List<CardUtilityPhom.CardInfo>();
            var usedCards = new HashSet<int>(); // Theo dõi các lá đã được sử dụng trong cạ

            // 1. Tìm tất cả cạ ngang (đôi/ba)
            var rankGroups = cards.GroupBy(c => c.Rank).ToList();
            foreach (var rankGroup in rankGroups)
            {
                if (rankGroup.Count() >= 2)
                {
                    // Đánh dấu tất cả lá trong nhóm này là đã sử dụng
                    foreach (var card in rankGroup)
                    {
                        usedCards.Add(card.Id);
                    }
                    _uiManager.AppendLog($"✅ Cạ ngang {CardUtilityPhom.GetRankName(rankGroup.Key)}: {string.Join(", ", rankGroup.Select(c => $"{CardUtilityPhom.GetRankName(c.Rank)}{GetSuitSymbol(c.Suit)}"))}",  UIManager.LogLevel.Debug);
                }
            }

            // 2. Tìm tất cả cạ dọc (sảnh) từ các lá chưa sử dụng
            var remainingCards = cards.Where(c => !usedCards.Contains(c.Id)).ToList();
            var suitGroups = remainingCards.GroupBy(c => c.Suit).ToList();

            foreach (var suitGroup in suitGroups)
            {
                if (suitGroup.Count() >= 2)
                {
                    var sortedCards = suitGroup.OrderBy(c => CardUtilityPhom.GetCardValue(c.Rank)).ToList();

                    // Tìm tất cả các cặp có thể tạo sảnh (liên tiếp hoặc cách 1 lá)
                    for (int i = 0; i < sortedCards.Count - 1; i++)
                    {
                        for (int j = i + 1; j < sortedCards.Count; j++)
                        {
                            var card1 = sortedCards[i];
                            var card2 = sortedCards[j];

                            if (usedCards.Contains(card1.Id) || usedCards.Contains(card2.Id))
                                continue;

                            var value1 = CardUtilityPhom.GetCardValue(card1.Rank);
                            var value2 = CardUtilityPhom.GetCardValue(card2.Rank);
                            var diff = Math.Abs(value2 - value1);

                            // Kiểm tra có thể tạo sảnh không (liên tiếp hoặc cách 1 lá)
                            bool canFormSequence = false;
                            string sequenceType = "";

                            if (diff == 1)
                            {
                                canFormSequence = true;
                                sequenceType = "liên tiếp";
                            }
                            else if (diff == 2)
                            {
                                canFormSequence = true;
                                sequenceType = "cách 1 lá";
                            }
                            // Xử lý trường hợp đặc biệt A-K (A=14, K=13)
                            else if ((value1 == 1 && value2 == 13) || (value1 == 13 && value2 == 1))
                            {
                                canFormSequence = true;
                                sequenceType = "A-K";
                            }

                            if (canFormSequence)
                            {
                                usedCards.Add(card1.Id);
                                usedCards.Add(card2.Id);
                                _uiManager.AppendLog($"✅ Cạ dọc {GetSuitSymbol(card1.Suit)} ({sequenceType}): {CardUtilityPhom.GetRankName(card1.Rank)}-{CardUtilityPhom.GetRankName(card2.Rank)}", UIManager.LogLevel.Debug);
                                break; // Thoát khỏi vòng lặp j để tránh sử dụng card1 nhiều lần
                            }
                        }
                    }
                }
            }

            // 3. Các lá còn lại là lá lẻ
            foreach (var card in cards)
            {
                if (!usedCards.Contains(card.Id))
                {
                    oddCards.Add(card);
                }
            }

            _uiManager.AppendLog($"🎯 Lá lẻ: {string.Join(", ", oddCards.Select(c => $"{CardUtilityPhom.GetRankName(c.Rank)}{GetSuitSymbol(c.Suit)}"))} (Tổng: {oddCards.Count})", UIManager.LogLevel.Info);

            return oddCards;
        }

        /// <summary>
        /// Lấy ký hiệu chất bài
        /// </summary>
        private string GetSuitSymbol(int suit)
        {
            return suit switch
            {
                1 => "♠", // Bích
                2 => "♣", // Chuồng
                3 => "♦", // Rô
                4 => "♥", // Cơ
                _ => $"S{suit}"
            };
        }

        /// <summary>
        /// Tính toán bài chưa biết
        /// </summary>
        private List<int> CalculateUnknownCards()
        {
            var allCards = Enumerable.Range(1, 52).ToList();
            var knownCards = new List<int>();

            // Bài của team
            var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
            foreach (var kvp in userCardsDict)
            {
                knownCards.AddRange(kvp.Value);
            }

            // Bài đã đánh
            var playedCards = _gameClient.GetPlayedCards();
            if (playedCards != null)
            {
                knownCards.AddRange(playedCards);
            }

            return allCards.Except(knownCards.Distinct()).ToList();
        }

        /// <summary>
        /// Tìm lá bài bảo thủ
        /// </summary>
        private int FindConservativeCard(int[] userCards, List<int> unknownCards)
        {
            var userCardsInfo = userCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();

            // Tìm lá ít khả năng tạo phỏm với bài chưa biết
            var safestCard = userCardsInfo
                .OrderBy(c => CountPotentialPhoms(c, unknownCards))
                .ThenBy(c => c.Rank)
                .FirstOrDefault();

            return safestCard?.Id ?? userCards.Min();
        }

        /// <summary>
        /// Đếm số phỏm tiềm năng
        /// </summary>
        private int CountPotentialPhoms(CardUtilityPhom.CardInfo card, List<int> unknownCards)
        {
            int count = 0;
            var unknownCardsInfo = unknownCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();

            foreach (var unknownCard in unknownCardsInfo)
            {
                // Đếm khả năng tạo đôi/ba
                if (card.Rank == unknownCard.Rank) count++;

                // Đếm khả năng tạo sảnh
                if (card.Suit == unknownCard.Suit && Math.Abs(card.Rank - unknownCard.Rank) <= 2) count++;
            }

            return count;
        }

        /// <summary>
        /// Kiểm tra xem 2 người chơi có phải teammate không
        /// </summary>
        private bool IsTeammate(string player1, string player2)
        {
            try
            {
                var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
                var teamCards = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();

                // Kiểm tra trong teamCards trước
                if (teamCards.ContainsKey(player1) && teamCards.ContainsKey(player2))
                {
                    return true;
                }

                // Logic team cho game Phỏm:
                // - 3 người: người giữa (index 1) là đối thủ của 0 và 2
                // - 4 người: 0-2 là team, 1-3 là team
                var usernames = userCardsDict.Keys.ToList();
                int player1Index = usernames.IndexOf(player1);
                int player2Index = usernames.IndexOf(player2);

                if (player1Index == -1 || player2Index == -1) return false;

                if (usernames.Count == 3)
                {
                    // Với 3 người: 0 và 2 là team, 1 là đối thủ
                    return (player1Index == 0 && player2Index == 2) || (player1Index == 2 && player2Index == 0);
                }
                else if (usernames.Count == 4)
                {
                    // Với 4 người: 0-2 là team, 1-3 là team
                    return (player1Index == 0 && player2Index == 2) ||
                           (player1Index == 2 && player2Index == 0) ||
                           (player1Index == 1 && player2Index == 3) ||
                           (player1Index == 3 && player2Index == 1);
                }

                return false;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi kiểm tra teammate: {ex.Message}", UIManager.LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// Lấy người chơi tiếp theo
        /// </summary>
        private string GetNextPlayer(string currentPlayer)
        {
            var playerOrder = _gameClient.GetWebSocketManager().GetPhomHandler().GetPlayerOrder();
            if (playerOrder.Count == 0) return "";

            int currentIndex = playerOrder.IndexOf(currentPlayer);
            if (currentIndex == -1) return "";

            int nextIndex = (currentIndex + 1) % playerOrder.Count;
            return playerOrder[nextIndex];
        }
    }
}
