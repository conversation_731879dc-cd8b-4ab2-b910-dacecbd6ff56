<?xml version="1.0" encoding="utf-8"?>
<project outputDir="bin\Obfuscated" baseDir="bin\Release\net8.0-windows">
  <!-- Global rules for all modules -->
  <rule pattern="true" inherit="false">
    <!-- Name obfuscation - Rename all identifiers -->
    <protection id="rename">
      <argument name="mode" value="unicode" />
      <argument name="renameMode" value="letters" />
      <argument name="password" value="AutoGameBai2024" />
      <argument name="flatten" value="true" />
    </protection>
    
    <!-- Control flow obfuscation - Make code flow confusing -->
    <protection id="ctrl flow">
      <argument name="predicate" value="expression" />
      <argument name="intensity" value="60" />
      <argument name="depth" value="4" />
    </protection>
    
    <!-- String encryption - Encrypt all string literals -->
    <protection id="constants">
      <argument name="mode" value="dynamic" />
      <argument name="decoderCount" value="5" />
      <argument name="cfg" value="true" />
    </protection>
    
    <!-- Anti-debug protection -->
    <protection id="anti debug">
      <argument name="mode" value="safe" />
    </protection>
    
    <!-- Anti-dump protection -->
    <protection id="anti dump">
    </protection>
    
    <!-- Anti-tamper protection -->
    <protection id="anti tamper">
      <argument name="key" value="normal" />
    </protection>
    
    <!-- Resource encryption -->
    <protection id="resources">
      <argument name="mode" value="dynamic" />
    </protection>
    
    <!-- Reference proxy - Hide method calls -->
    <protection id="ref proxy">
      <argument name="mode" value="mild" />
      <argument name="encoding" value="expression" />
      <argument name="depth" value="3" />
    </protection>
  </rule>
  
  <!-- Specific rules for sensitive modules -->
  <rule pattern="namespace('AutoGameBai.Gamephom')" inherit="true">
    <protection id="ctrl flow">
      <argument name="intensity" value="80" />
      <argument name="depth" value="6" />
    </protection>
  </rule>
  
  <rule pattern="namespace('AutoGameBai.Gamemaubinh')" inherit="true">
    <protection id="ctrl flow">
      <argument name="intensity" value="80" />
      <argument name="depth" value="6" />
    </protection>
  </rule>
  
  <rule pattern="namespace('AutoGameBai.GameTienLen')" inherit="true">
    <protection id="ctrl flow">
      <argument name="intensity" value="80" />
      <argument name="depth" value="6" />
    </protection>
  </rule>
  
  <!-- Main assembly -->
  <module path="AutoGameBai.exe" />
</project>
