﻿using System;
using System.Windows.Forms;

namespace AutoGameBai.UI
{
    public static class ErrorHandler
    {
        public static void Handle(Form form, UIManager uiManager, string message, Exception ex = null, string title = "Lỗi")
        {
            string logMessage = ex != null ? $"{message}: {ex.Message}\nStackTrace: {ex.StackTrace}" : message;
            uiManager?.AppendLog(logMessage, UIManager.LogLevel.Error);
            MessageBoxHelper.ShowMessageBox(form, message, title, MessageBoxIcon.Error);
        }
    }
}