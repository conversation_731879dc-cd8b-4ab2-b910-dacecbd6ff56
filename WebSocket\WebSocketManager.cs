using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.DevTools;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoGameBai.Gamemaubinh;
using AutoGameBai.Gamephom;

namespace AutoGameBai.WebSocket
{
    /// <summary>
    /// WebSocket Manager quản lý tất cả game-specific handlers
    /// </summary>
    public class WebSocketManager : BaseWebSocketHandler
    {
        private readonly MauBinhWebSocketHandler _mauBinhHandler;
        private readonly PhomWebSocketHandler _phomHandler;
        private readonly TienLenWebSocketHandler _tienLenHandler;

        public WebSocketManager(GameClientManager gameClient, UIManager uiManager,
            MauBinhSuggestionHandler mauBinhSuggestionHandler, MauBinhCardManager mauBinhCardManager)
            : base(gameClient, uiManager)
        {
            // Initialize game-specific handlers
            _mauBinhHandler = new MauBinhWebSocketHandler(gameClient, uiManager, mauBinhSuggestionHandler, mauBinhCardManager);
            _phomHandler = new PhomWebSocketHandler(gameClient, uiManager);
            _tienLenHandler = new TienLenWebSocketHandler(gameClient, uiManager);

            // Register handlers
            RegisterGameHandler(_mauBinhHandler);
            RegisterGameHandler(_phomHandler);
            RegisterGameHandler(_tienLenHandler);

            _uiManager.AppendLog("WebSocketManager initialized with all game handlers", UIManager.LogLevel.Info);
        }

        /// <summary>
        /// Khởi tạo WebSocket cho user
        /// </summary>
        public async Task InitializeWebSocketAsync(string username, ChromeDriver driver)
        {
            try
            {
                var devTools = driver.GetDevToolsSession();
                var network = devTools.GetVersionSpecificDomains<OpenQA.Selenium.DevTools.V129.DevToolsSessionDomains>().Network;
                await network.Enable(new OpenQA.Selenium.DevTools.V129.Network.EnableCommandSettings
                {
                    MaxResourceBufferSize = 10000000,
                    MaxTotalBufferSize = 10000000
                });

                network.WebSocketFrameReceived += async (sender, e) =>
                {
                    try
                    {
                        if (e?.Response?.PayloadData != null && e.Response.PayloadData.StartsWith("["))
                        {
                            ProcessMessage(e.Response.PayloadData, username);
                        }
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Error processing WebSocket frame for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                    }
                };

                lock (_lock)
                {
                    _devToolsSessions[username] = devTools;
                }

                _uiManager.AppendLog($"WebSocket initialized for {username}", UIManager.LogLevel.Info, username);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Failed to initialize WebSocket for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                throw;
            }
        }

        /// <summary>
        /// Cleanup WebSocket cho user
        /// </summary>
        public void CleanupWebSocket(string username)
        {
            try
            {
                lock (_lock)
                {
                    if (_devToolsSessions.ContainsKey(username))
                    {
                        _devToolsSessions[username]?.Dispose();
                        _devToolsSessions.Remove(username);
                    }

                    _userCards.Remove(username);
                    _userRooms.Remove(username);
                    _roomPlayers.Remove(username);
                    _roomTimes.Remove(username);
                    _userSeats.Remove(username);
                }

                _uiManager.AppendLog($"WebSocket cleaned up for {username}", UIManager.LogLevel.Info, username);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error cleaning up WebSocket for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Set TaskCompletionSource cho room join
        /// </summary>
        public void SetRoomJoinedTcs(string username, TaskCompletionSource<bool> tcs)
        {
            lock (_lock)
            {
                _roomJoinedTcs[username] = tcs;
            }
        }

        /// <summary>
        /// Remove TaskCompletionSource cho room join
        /// </summary>
        public void RemoveRoomJoinedTcs(string username)
        {
            lock (_lock)
            {
                _roomJoinedTcs.Remove(username);
            }
        }

        /// <summary>
        /// Lấy room players
        /// </summary>
        public List<Models.User>? GetRoomPlayersForUser(string username)
        {
            lock (_lock)
            {
                return _roomPlayers.ContainsKey(username) ? _roomPlayers[username] : null;
            }
        }

        /// <summary>
        /// Lấy room time
        /// </summary>
        public int GetRoomTimeForUser(string username)
        {
            lock (_lock)
            {
                return _roomTimes.ContainsKey(username) ? _roomTimes[username] : -1;
            }
        }

        /// <summary>
        /// Lấy user seat
        /// </summary>
        public int GetUserSeat(string username)
        {
            lock (_lock)
            {
                return _userSeats.ContainsKey(username) ? _userSeats[username] : -1;
            }
        }

        /// <summary>
        /// Lấy user cards
        /// </summary>
        public int[]? GetUserCards(string username)
        {
            lock (_lock)
            {
                return _userCards.ContainsKey(username) ? _userCards[username] : null;
            }
        }

        /// <summary>
        /// Set user room
        /// </summary>
        public void SetUserRoom(string username, string roomId)
        {
            lock (_lock)
            {
                _userRooms[username] = roomId;
            }
        }

        /// <summary>
        /// Get user room
        /// </summary>
        public string? GetUserRoom(string username)
        {
            lock (_lock)
            {
                return _userRooms.ContainsKey(username) ? _userRooms[username] : null;
            }
        }

        // Game-specific handler getters
        public MauBinhWebSocketHandler GetMauBinhHandler() => _mauBinhHandler;
        public PhomWebSocketHandler GetPhomHandler() => _phomHandler;
        public TienLenWebSocketHandler GetTienLenHandler() => _tienLenHandler;

        /// <summary>
        /// Reset tất cả game data
        /// </summary>
        public void ResetAllGameData()
        {
            _mauBinhHandler.ResetGameData();
            _phomHandler.ResetGameData();
            _tienLenHandler.ResetGameData();
            _uiManager.AppendLog("Reset all game data", UIManager.LogLevel.Info);
        }

        /// <summary>
        /// Kiểm tra team mode cho game hiện tại
        /// </summary>
        public bool IsCurrentGameTeamMode()
        {
            string selectedGame = _gameClient.GetSelectedGameName();
            return selectedGame switch
            {
                "Mậu Binh" => _mauBinhHandler.IsTeamMode(),
                "Phỏm" => _phomHandler.IsTeamMode(),
                "Tiến Lên" => _tienLenHandler.IsTeamMode(),
                _ => false
            };
        }

        /// <summary>
        /// Dispose tất cả resources
        /// </summary>
        public void Dispose()
        {
            try
            {
                lock (_lock)
                {
                    foreach (var session in _devToolsSessions.Values)
                    {
                        session?.Dispose();
                    }
                    _devToolsSessions.Clear();
                    _roomJoinedTcs.Clear();
                    _roomPlayers.Clear();
                    _roomTimes.Clear();
                    _userSeats.Clear();
                    _userCards.Clear();
                    _userRooms.Clear();
                }

                ResetAllGameData();
                _uiManager.AppendLog("WebSocketManager disposed", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error disposing WebSocketManager: {ex.Message}", UIManager.LogLevel.Error);
            }
        }
    }
}
