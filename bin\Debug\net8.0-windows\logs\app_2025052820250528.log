2025-05-28 13:36:17.969 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 13:36:20.154 +07:00 [INF] User selected: HitClub - M<PERSON><PERSON>h
2025-05-28 13:36:20.158 +07:00 [INF] Form1 constructor started.
2025-05-28 13:36:20.175 +07:00 [DBG] [2025-05-28 13:36:20.175] [DEBUG]: Gọi InitializeComponent
2025-05-28 13:36:20.184 +07:00 [INF] [2025-05-28 13:36:20.184] [INFO]: Khởi tạo UIManager thành công
2025-05-28 13:36:20.185 +07:00 [DBG] [2025-05-28 13:36:20.185] [DEBUG]: <PERSON><PERSON><PERSON> đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 13:36:20.187 +07:00 [INF] [2025-05-28 13:36:20.187] [INFO]: Đ<PERSON> khởi tạo cột cho dataGridViewUsers
2025-05-28 13:36:20.187 +07:00 [DBG] [2025-05-28 13:36:20.187] [DEBUG]: <PERSON><PERSON><PERSON> đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 13:36:20.187 +07:00 [INF] [2025-05-28 13:36:20.187] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 13:36:20.188 +07:00 [INF] [2025-05-28 13:36:20.188] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 13:36:20.200 +07:00 [DBG] [2025-05-28 13:36:20.200] [DEBUG]: Bắt đầu OnLoad
2025-05-28 13:36:20.200 +07:00 [DBG] [2025-05-28 13:36:20.200] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 13:36:20.217 +07:00 [INF] [2025-05-28 13:36:20.217] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11823
2025-05-28 13:36:20.217 +07:00 [INF] [2025-05-28 13:36:20.217] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 13:36:20.233 +07:00 [INF] [2025-05-28 13:36:20.233] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 13:36:20.234 +07:00 [INF] [2025-05-28 13:36:20.234] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 13:36:20.235 +07:00 [INF] [2025-05-28 13:36:20.235] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 13:36:20.235 +07:00 [INF] [2025-05-28 13:36:20.235] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 13:36:20.235 +07:00 [INF] [2025-05-28 13:36:20.235] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 13:36:20.235 +07:00 [INF] [2025-05-28 13:36:20.235] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 13:36:20.235 +07:00 [INF] InitializeAsync started.
2025-05-28 13:36:20.235 +07:00 [INF] [2025-05-28 13:36:20.235] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 13:36:20.240 +07:00 [DBG] [2025-05-28 13:36:20.240] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 13:36:20.242 +07:00 [DBG] [2025-05-28 13:36:20.242] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 13:36:20.243 +07:00 [DBG] [2025-05-28 13:36:20.243] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:20.243 +07:00 [DBG] [2025-05-28 13:36:20.243] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:20.251 +07:00 [DBG] [2025-05-28 13:36:20.251] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 13:36:20.251 +07:00 [INF] [2025-05-28 13:36:20.251] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 13:36:20.251 +07:00 [DBG] [2025-05-28 13:36:20.251] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:20.253 +07:00 [INF] [2025-05-28 13:36:20.253] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:20.253 +07:00 [INF] [2025-05-28 13:36:20.253] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:20.295 +07:00 [INF] [2025-05-28 13:36:20.295] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:20.304 +07:00 [INF] [2025-05-28 13:36:20.304] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 13:36:20.304 +07:00 [DBG] [2025-05-28 13:36:20.304] [DEBUG]: OnLoad hoàn tất
2025-05-28 13:36:21.616 +07:00 [INF] [2025-05-28 13:36:21.616] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 13:36:21.622 +07:00 [INF] [2025-05-28 13:36:21.622] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 13:36:21.623 +07:00 [INF] [2025-05-28 13:36:21.623] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 13:36:21.635 +07:00 [DBG] [2025-05-28 13:36:21.635] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 13:36:21.677 +07:00 [INF] [2025-05-28 13:36:21.677] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 53ms
2025-05-28 13:36:22.049 +07:00 [DBG] [2025-05-28 13:36:22.049] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 13:36:22.049 +07:00 [INF] [2025-05-28 13:36:22.049] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:51752. Thời gian: 371ms
2025-05-28 13:36:22.049 +07:00 [DBG] [2025-05-28 13:36:22.049] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:22.049 +07:00 [DBG] [2025-05-28 13:36:22.049] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:22.050 +07:00 [INF] [2025-05-28 13:36:22.050] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 426ms
2025-05-28 13:36:22.050 +07:00 [DBG] [2025-05-28 13:36:22.050] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:22.050 +07:00 [DBG] [2025-05-28 13:36:22.050] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:23.166 +07:00 [INF] [2025-05-28 13:36:23.166] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 13:36:23.536 +07:00 [INF] [2025-05-28 13:36:23.536] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 13:36:23.618 +07:00 [INF] [2025-05-28 13:36:23.617] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 13:36:23.630 +07:00 [DBG] [2025-05-28 13:36:23.630] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 13:36:23.636 +07:00 [INF] [2025-05-28 13:36:23.636] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 13:36:23.639 +07:00 [INF] [2025-05-28 13:36:23.639] [INFO] [nhatrang345]: ✅ Đã inject C# methods cho nhatrang345
2025-05-28 13:36:23.640 +07:00 [INF] [2025-05-28 13:36:23.640] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.640 +07:00 [INF] [2025-05-28 13:36:23.640] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.640 +07:00 [INF] [2025-05-28 13:36:23.640] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.667 +07:00 [INF] [2025-05-28 13:36:23.667] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:23.667 +07:00 [INF] [2025-05-28 13:36:23.667] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:36:23.667 +07:00 [DBG] [2025-05-28 13:36:23.667] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:23.668 +07:00 [DBG] [2025-05-28 13:36:23.668] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.668 +07:00 [INF] [2025-05-28 13:36:23.668] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:23.668 +07:00 [DBG] [2025-05-28 13:36:23.668] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:23.668 +07:00 [DBG] [2025-05-28 13:36:23.668] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.668 +07:00 [INF] [2025-05-28 13:36:23.668] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:23.668 +07:00 [DBG] [2025-05-28 13:36:23.668] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:23.668 +07:00 [DBG] [2025-05-28 13:36:23.668] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.694 +07:00 [INF] [2025-05-28 13:36:23.694] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:23.702 +07:00 [INF] [2025-05-28 13:36:23.702] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.65 giây
2025-05-28 13:36:23.710 +07:00 [INF] [2025-05-28 13:36:23.710] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.66 giây
2025-05-28 13:36:25.636 +07:00 [INF] [2025-05-28 13:36:25.636] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 13:36:26.647 +07:00 [INF] [2025-05-28 13:36:26.647] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 13:36:26.647 +07:00 [DBG] [2025-05-28 13:36:26.647] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:26.647 +07:00 [DBG] [2025-05-28 13:36:26.647] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:26.647 +07:00 [INF] [2025-05-28 13:36:26.647] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:26.648 +07:00 [INF] [2025-05-28 13:36:26.648] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.648 +07:00 [INF] [2025-05-28 13:36:26.648] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.648 +07:00 [INF] [2025-05-28 13:36:26.648] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.697 +07:00 [INF] [2025-05-28 13:36:26.697] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:26.697 +07:00 [INF] [2025-05-28 13:36:26.697] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:36:26.697 +07:00 [DBG] [2025-05-28 13:36:26.697] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:26.697 +07:00 [DBG] [2025-05-28 13:36:26.697] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.697 +07:00 [INF] [2025-05-28 13:36:26.697] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:26.697 +07:00 [DBG] [2025-05-28 13:36:26.697] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:26.697 +07:00 [DBG] [2025-05-28 13:36:26.697] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.697 +07:00 [INF] [2025-05-28 13:36:26.697] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:26.697 +07:00 [DBG] [2025-05-28 13:36:26.697] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:26.698 +07:00 [DBG] [2025-05-28 13:36:26.698] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.749 +07:00 [INF] [2025-05-28 13:36:26.749] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:26.768 +07:00 [INF] [2025-05-28 13:36:26.768] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.12 giây
2025-05-28 13:36:26.785 +07:00 [INF] [2025-05-28 13:36:26.785] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.14 giây
2025-05-28 13:36:27.660 +07:00 [INF] [2025-05-28 13:36:27.660] [INFO] [nhatrang345]: ✅ Đã inject profile buttons thành công cho nhatrang345
2025-05-28 13:36:27.723 +07:00 [DBG] [2025-05-28 13:36:27.723] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 13:36:27.910 +07:00 [INF] [2025-05-28 13:36:27.910] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:36:27.911 +07:00 [INF] [2025-05-28 13:36:27.911] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:36:27.911 +07:00 [INF] [2025-05-28 13:36:27.911] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 13:36:27.911 +07:00 [DBG] [2025-05-28 13:36:27.911] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 13:36:27.914 +07:00 [INF] [2025-05-28 13:36:27.914] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 13:36:27.915 +07:00 [INF] [2025-05-28 13:36:27.915] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 13:36:27.918 +07:00 [INF] [2025-05-28 13:36:27.918] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 13:36:27.928 +07:00 [INF] [2025-05-28 13:36:27.928] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 13:36:27.935 +07:00 [INF] [2025-05-28 13:36:27.935] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 13:36:27.940 +07:00 [INF] [2025-05-28 13:36:27.940] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 13:36:27.943 +07:00 [DBG] [2025-05-28 13:36:27.943] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 13:36:27.943
2025-05-28 13:36:27.943 +07:00 [INF] [2025-05-28 13:36:27.943] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:36:27.946 +07:00 [INF] [2025-05-28 13:36:27.946] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:36:28.021 +07:00 [DBG] [2025-05-28 13:36:28.021] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:36:28.023 +07:00 [INF] [2025-05-28 13:36:28.023] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 3, số người: 4), tự động thoát phòng
2025-05-28 13:36:28.023 +07:00 [INF] [2025-05-28 13:36:28.023] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:36:28.023 +07:00 [WRN] [2025-05-28 13:36:28.023] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:36:28.024 +07:00 [INF] [2025-05-28 13:36:28.024] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:36:28.035 +07:00 [INF] [2025-05-28 13:36:28.035] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:36:28.040 +07:00 [WRN] [2025-05-28 13:36:28.040] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:36:28.040 +07:00 [INF] [2025-05-28 13:36:28.040] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 6.6777ms
2025-05-28 13:36:28.040 +07:00 [INF] [2025-05-28 13:36:28.040] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 15.2952ms
2025-05-28 13:36:28.040 +07:00 [DBG] [2025-05-28 13:36:28.040] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:28.040 +07:00 [INF] [2025-05-28 13:36:28.040] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:36:28.041 +07:00 [DBG] [2025-05-28 13:36:28.041] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:28.041 +07:00 [DBG] [2025-05-28 13:36:28.041] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:28.042 +07:00 [INF] [2025-05-28 13:36:28.042] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:28.042 +07:00 [INF] [2025-05-28 13:36:28.042] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:28.042 +07:00 [INF] [2025-05-28 13:36:28.042] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:28.090 +07:00 [INF] [2025-05-28 13:36:28.090] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:36:28.117 +07:00 [INF] [2025-05-28 13:36:28.117] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:28.138 +07:00 [INF] [2025-05-28 13:36:28.138] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 13:36:28.201 +07:00 [INF] [2025-05-28 13:36:28.201] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:36:28.201 +07:00 [INF] [2025-05-28 13:36:28.201] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:36:29.583 +07:00 [INF] [2025-05-28 13:36:29.583] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:36:29.584 +07:00 [INF] [2025-05-28 13:36:29.584] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:36:29.589 +07:00 [INF] [2025-05-28 13:36:29.589] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:36:29.679 +07:00 [INF] [2025-05-28 13:36:29.679] [INFO] [nhatrang345]: Buttons đã xuất hiện, test chúng cho nhatrang345
2025-05-28 13:36:29.686 +07:00 [INF] [2025-05-28 13:36:29.686] [INFO] [nhatrang345]: 🧪 Test results cho nhatrang345: System.Collections.Generic.Dictionary`2[System.String,System.Object]
2025-05-28 13:36:29.689 +07:00 [DBG] [2025-05-28 13:36:29.689] [DEBUG] [nhatrang345]: ✅ Đã setup reload handler cho nhatrang345
2025-05-28 13:36:34.583 +07:00 [INF] [2025-05-28 13:36:34.583] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:36:34.583 +07:00 [INF] [2025-05-28 13:36:34.583] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:36:34.583 +07:00 [INF] [2025-05-28 13:36:34.583] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:36:35.952 +07:00 [WRN] [2025-05-28 13:36:35.952] [WARNING] [nhatrang345]: Timeout 8000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:36:36.457 +07:00 [INF] [2025-05-28 13:36:36.457] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:36:36.461 +07:00 [INF] [2025-05-28 13:36:36.461] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:36:36.523 +07:00 [DBG] [2025-05-28 13:36:36.523] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:36:36.523 +07:00 [INF] [2025-05-28 13:36:36.523] [INFO] [nhatrang345]: ✅ Tìm thấy bàn ít người hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 13:36:36.523 +07:00 [WRN] [2025-05-28 13:36:36.523] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:36:39.582 +07:00 [INF] [2025-05-28 13:36:39.582] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:36:39.582 +07:00 [INF] [2025-05-28 13:36:39.582] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:36:39.586 +07:00 [INF] [2025-05-28 13:36:39.586] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:40:56.873 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 13:40:59.063 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 13:40:59.067 +07:00 [INF] Form1 constructor started.
2025-05-28 13:40:59.084 +07:00 [DBG] [2025-05-28 13:40:59.084] [DEBUG]: Gọi InitializeComponent
2025-05-28 13:40:59.093 +07:00 [INF] [2025-05-28 13:40:59.093] [INFO]: Khởi tạo UIManager thành công
2025-05-28 13:40:59.094 +07:00 [DBG] [2025-05-28 13:40:59.094] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 13:40:59.096 +07:00 [INF] [2025-05-28 13:40:59.096] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 13:40:59.096 +07:00 [DBG] [2025-05-28 13:40:59.096] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 13:40:59.096 +07:00 [INF] [2025-05-28 13:40:59.096] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 13:40:59.097 +07:00 [INF] [2025-05-28 13:40:59.097] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 13:40:59.110 +07:00 [DBG] [2025-05-28 13:40:59.110] [DEBUG]: Bắt đầu OnLoad
2025-05-28 13:40:59.111 +07:00 [DBG] [2025-05-28 13:40:59.111] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 13:40:59.127 +07:00 [INF] [2025-05-28 13:40:59.127] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11823
2025-05-28 13:40:59.127 +07:00 [INF] [2025-05-28 13:40:59.127] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 13:40:59.143 +07:00 [INF] [2025-05-28 13:40:59.143] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 13:40:59.144 +07:00 [INF] [2025-05-28 13:40:59.144] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 13:40:59.145 +07:00 [INF] [2025-05-28 13:40:59.145] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 13:40:59.145 +07:00 [INF] [2025-05-28 13:40:59.145] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 13:40:59.145 +07:00 [INF] [2025-05-28 13:40:59.145] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 13:40:59.145 +07:00 [INF] [2025-05-28 13:40:59.145] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 13:40:59.145 +07:00 [INF] InitializeAsync started.
2025-05-28 13:40:59.146 +07:00 [INF] [2025-05-28 13:40:59.146] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 13:40:59.150 +07:00 [DBG] [2025-05-28 13:40:59.150] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 13:40:59.152 +07:00 [DBG] [2025-05-28 13:40:59.152] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 13:40:59.153 +07:00 [DBG] [2025-05-28 13:40:59.153] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:40:59.153 +07:00 [DBG] [2025-05-28 13:40:59.153] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:40:59.160 +07:00 [DBG] [2025-05-28 13:40:59.160] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 13:40:59.160 +07:00 [INF] [2025-05-28 13:40:59.160] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 13:40:59.161 +07:00 [DBG] [2025-05-28 13:40:59.161] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:40:59.162 +07:00 [INF] [2025-05-28 13:40:59.162] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:40:59.162 +07:00 [INF] [2025-05-28 13:40:59.162] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:40:59.204 +07:00 [INF] [2025-05-28 13:40:59.204] [INFO]: Tải danh sách user thành công
2025-05-28 13:40:59.216 +07:00 [INF] [2025-05-28 13:40:59.216] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 13:40:59.216 +07:00 [DBG] [2025-05-28 13:40:59.216] [DEBUG]: OnLoad hoàn tất
2025-05-28 13:41:00.253 +07:00 [INF] [2025-05-28 13:41:00.253] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 13:41:00.259 +07:00 [INF] [2025-05-28 13:41:00.259] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 13:41:00.261 +07:00 [INF] [2025-05-28 13:41:00.261] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 13:41:00.271 +07:00 [DBG] [2025-05-28 13:41:00.271] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 13:41:00.314 +07:00 [INF] [2025-05-28 13:41:00.314] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 52ms
2025-05-28 13:41:00.692 +07:00 [DBG] [2025-05-28 13:41:00.692] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 13:41:00.692 +07:00 [INF] [2025-05-28 13:41:00.692] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:61868. Thời gian: 377ms
2025-05-28 13:41:00.693 +07:00 [DBG] [2025-05-28 13:41:00.693] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:00.693 +07:00 [DBG] [2025-05-28 13:41:00.693] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:00.693 +07:00 [INF] [2025-05-28 13:41:00.693] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 432ms
2025-05-28 13:41:00.693 +07:00 [DBG] [2025-05-28 13:41:00.693] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:00.693 +07:00 [DBG] [2025-05-28 13:41:00.693] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:01.934 +07:00 [INF] [2025-05-28 13:41:01.934] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 13:41:02.301 +07:00 [INF] [2025-05-28 13:41:02.301] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 13:41:02.385 +07:00 [INF] [2025-05-28 13:41:02.385] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 13:41:02.397 +07:00 [DBG] [2025-05-28 13:41:02.397] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 13:41:02.403 +07:00 [INF] [2025-05-28 13:41:02.403] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 13:41:02.406 +07:00 [INF] [2025-05-28 13:41:02.406] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:02.406 +07:00 [DBG] [2025-05-28 13:41:02.406] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:02.407 +07:00 [DBG] [2025-05-28 13:41:02.407] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.407 +07:00 [INF] [2025-05-28 13:41:02.407] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:02.407 +07:00 [DBG] [2025-05-28 13:41:02.407] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:02.407 +07:00 [DBG] [2025-05-28 13:41:02.407] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.407 +07:00 [INF] [2025-05-28 13:41:02.407] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:02.407 +07:00 [DBG] [2025-05-28 13:41:02.407] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:02.407 +07:00 [DBG] [2025-05-28 13:41:02.407] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.407 +07:00 [INF] [2025-05-28 13:41:02.407] [INFO] [nhatrang345]: ✅ Đã inject C# methods cho nhatrang345
2025-05-28 13:41:02.434 +07:00 [INF] [2025-05-28 13:41:02.434] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:02.434 +07:00 [INF] [2025-05-28 13:41:02.434] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.434 +07:00 [INF] [2025-05-28 13:41:02.434] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.434 +07:00 [INF] [2025-05-28 13:41:02.434] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.460 +07:00 [INF] [2025-05-28 13:41:02.460] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:02.469 +07:00 [INF] [2025-05-28 13:41:02.469] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.78 giây
2025-05-28 13:41:02.476 +07:00 [INF] [2025-05-28 13:41:02.476] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.78 giây
2025-05-28 13:41:04.395 +07:00 [INF] [2025-05-28 13:41:04.395] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 13:41:05.417 +07:00 [INF] [2025-05-28 13:41:05.417] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 13:41:05.417 +07:00 [DBG] [2025-05-28 13:41:05.417] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:05.417 +07:00 [DBG] [2025-05-28 13:41:05.417] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:05.418 +07:00 [INF] [2025-05-28 13:41:05.418] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:05.418 +07:00 [INF] [2025-05-28 13:41:05.418] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.418 +07:00 [INF] [2025-05-28 13:41:05.418] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.418 +07:00 [INF] [2025-05-28 13:41:05.418] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.523 +07:00 [INF] [2025-05-28 13:41:05.523] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:05.523 +07:00 [INF] [2025-05-28 13:41:05.523] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.523 +07:00 [INF] [2025-05-28 13:41:05.523] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.523 +07:00 [INF] [2025-05-28 13:41:05.523] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.595 +07:00 [INF] [2025-05-28 13:41:05.595] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:05.613 +07:00 [INF] [2025-05-28 13:41:05.613] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.20 giây
2025-05-28 13:41:05.641 +07:00 [INF] [2025-05-28 13:41:05.641] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.22 giây
2025-05-28 13:41:06.441 +07:00 [INF] [2025-05-28 13:41:06.441] [INFO] [nhatrang345]: ✅ Đã inject profile buttons thành công cho nhatrang345
2025-05-28 13:41:07.360 +07:00 [DBG] [2025-05-28 13:41:07.360] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 13:41:07.550 +07:00 [INF] [2025-05-28 13:41:07.550] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:07.551 +07:00 [INF] [2025-05-28 13:41:07.551] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:41:07.551 +07:00 [INF] [2025-05-28 13:41:07.551] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 13:41:07.551 +07:00 [DBG] [2025-05-28 13:41:07.551] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 13:41:07.555 +07:00 [INF] [2025-05-28 13:41:07.555] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 13:41:07.555 +07:00 [INF] [2025-05-28 13:41:07.555] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 13:41:07.558 +07:00 [INF] [2025-05-28 13:41:07.558] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 13:41:07.569 +07:00 [INF] [2025-05-28 13:41:07.569] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 13:41:07.575 +07:00 [INF] [2025-05-28 13:41:07.575] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 13:41:07.581 +07:00 [INF] [2025-05-28 13:41:07.581] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 13:41:07.584 +07:00 [DBG] [2025-05-28 13:41:07.584] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 13:41:07.584
2025-05-28 13:41:07.584 +07:00 [INF] [2025-05-28 13:41:07.584] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:41:07.587 +07:00 [INF] [2025-05-28 13:41:07.587] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:41:07.716 +07:00 [DBG] [2025-05-28 13:41:07.716] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:41:07.718 +07:00 [INF] [2025-05-28 13:41:07.718] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 2, số người: 3), tự động thoát phòng
2025-05-28 13:41:07.719 +07:00 [INF] [2025-05-28 13:41:07.719] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:41:07.719 +07:00 [WRN] [2025-05-28 13:41:07.719] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:41:07.719 +07:00 [INF] [2025-05-28 13:41:07.719] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:41:07.725 +07:00 [INF] [2025-05-28 13:41:07.725] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:41:07.729 +07:00 [WRN] [2025-05-28 13:41:07.729] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:41:07.729 +07:00 [INF] [2025-05-28 13:41:07.729] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 6.4683ms
2025-05-28 13:41:07.729 +07:00 [INF] [2025-05-28 13:41:07.729] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 9.9345ms
2025-05-28 13:41:07.729 +07:00 [DBG] [2025-05-28 13:41:07.729] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:07.730 +07:00 [INF] [2025-05-28 13:41:07.730] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:41:07.731 +07:00 [DBG] [2025-05-28 13:41:07.731] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:07.731 +07:00 [DBG] [2025-05-28 13:41:07.731] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:07.732 +07:00 [INF] [2025-05-28 13:41:07.732] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:07.732 +07:00 [INF] [2025-05-28 13:41:07.732] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:07.732 +07:00 [INF] [2025-05-28 13:41:07.732] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:07.772 +07:00 [INF] [2025-05-28 13:41:07.772] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:41:07.809 +07:00 [INF] [2025-05-28 13:41:07.809] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:07.827 +07:00 [INF] [2025-05-28 13:41:07.827] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 13:41:07.882 +07:00 [INF] [2025-05-28 13:41:07.882] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:07.882 +07:00 [INF] [2025-05-28 13:41:07.882] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:41:08.387 +07:00 [INF] [2025-05-28 13:41:08.387] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:41:08.388 +07:00 [INF] [2025-05-28 13:41:08.388] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:41:08.390 +07:00 [INF] [2025-05-28 13:41:08.390] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:41:08.459 +07:00 [INF] [2025-05-28 13:41:08.459] [INFO] [nhatrang345]: Buttons đã xuất hiện, test chúng cho nhatrang345
2025-05-28 13:41:08.466 +07:00 [INF] [2025-05-28 13:41:08.466] [INFO] [nhatrang345]: 🧪 Test results cho nhatrang345: System.Collections.Generic.Dictionary`2[System.String,System.Object]
2025-05-28 13:41:08.470 +07:00 [DBG] [2025-05-28 13:41:08.470] [DEBUG] [nhatrang345]: ✅ Đã setup reload handler cho nhatrang345
2025-05-28 13:41:10.595 +07:00 [WRN] [2025-05-28 13:41:10.595] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:41:11.099 +07:00 [INF] [2025-05-28 13:41:11.099] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:41:11.103 +07:00 [INF] [2025-05-28 13:41:11.103] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:41:11.160 +07:00 [DBG] [2025-05-28 13:41:11.160] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:41:11.160 +07:00 [INF] [2025-05-28 13:41:11.160] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 2, số người: 3), tự động thoát phòng
2025-05-28 13:41:11.160 +07:00 [INF] [2025-05-28 13:41:11.160] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:41:11.160 +07:00 [WRN] [2025-05-28 13:41:11.160] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:41:11.160 +07:00 [INF] [2025-05-28 13:41:11.160] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:41:11.167 +07:00 [INF] [2025-05-28 13:41:11.167] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:41:11.170 +07:00 [WRN] [2025-05-28 13:41:11.170] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:41:11.170 +07:00 [INF] [2025-05-28 13:41:11.170] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 7.0791ms
2025-05-28 13:41:11.170 +07:00 [INF] [2025-05-28 13:41:11.170] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 9.763ms
2025-05-28 13:41:11.170 +07:00 [DBG] [2025-05-28 13:41:11.170] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:11.170 +07:00 [INF] [2025-05-28 13:41:11.170] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:41:11.170 +07:00 [DBG] [2025-05-28 13:41:11.170] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:11.170 +07:00 [DBG] [2025-05-28 13:41:11.170] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:11.172 +07:00 [INF] [2025-05-28 13:41:11.172] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:11.172 +07:00 [INF] [2025-05-28 13:41:11.172] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:11.172 +07:00 [INF] [2025-05-28 13:41:11.172] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:11.213 +07:00 [INF] [2025-05-28 13:41:11.212] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:41:11.241 +07:00 [INF] [2025-05-28 13:41:11.241] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:11.267 +07:00 [INF] [2025-05-28 13:41:11.267] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 13:41:11.306 +07:00 [INF] [2025-05-28 13:41:11.306] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:11.306 +07:00 [INF] [2025-05-28 13:41:11.306] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:41:13.388 +07:00 [INF] [2025-05-28 13:41:13.388] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:41:13.388 +07:00 [INF] [2025-05-28 13:41:13.388] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:41:13.390 +07:00 [INF] [2025-05-28 13:41:13.390] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:41:14.112 +07:00 [WRN] [2025-05-28 13:41:14.112] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:41:14.112 +07:00 [ERR] [2025-05-28 13:41:14.112] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 13:41:14.112 +07:00 [INF] [2025-05-28 13:41:14.112] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 6549.5517ms
2025-05-28 13:41:14.112 +07:00 [WRN] [2025-05-28 13:41:14.112] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 13:41:14.823 +07:00 [INF] [2025-05-28 13:41:14.823] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 13:41:14.832 +07:00 [INF] [2025-05-28 13:41:14.832] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 13:41:14.836 +07:00 [INF] [2025-05-28 13:41:14.836] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 13:41:14.841 +07:00 [INF] [2025-05-28 13:41:14.841] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 13:41:14.843 +07:00 [DBG] [2025-05-28 13:41:14.843] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 13:41:14.843
2025-05-28 13:41:14.843 +07:00 [INF] [2025-05-28 13:41:14.843] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:41:14.847 +07:00 [INF] [2025-05-28 13:41:14.847] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:41:14.905 +07:00 [DBG] [2025-05-28 13:41:14.905] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:41:14.905 +07:00 [INF] [2025-05-28 13:41:14.905] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 2, số người: 3), tự động thoát phòng
2025-05-28 13:41:14.905 +07:00 [INF] [2025-05-28 13:41:14.905] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:41:14.905 +07:00 [WRN] [2025-05-28 13:41:14.905] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:41:14.905 +07:00 [INF] [2025-05-28 13:41:14.905] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:41:14.911 +07:00 [INF] [2025-05-28 13:41:14.911] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:41:14.915 +07:00 [WRN] [2025-05-28 13:41:14.915] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:41:14.915 +07:00 [INF] [2025-05-28 13:41:14.915] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 6.7156ms
2025-05-28 13:41:14.915 +07:00 [INF] [2025-05-28 13:41:14.915] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 9.4494ms
2025-05-28 13:41:14.915 +07:00 [DBG] [2025-05-28 13:41:14.915] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:14.915 +07:00 [INF] [2025-05-28 13:41:14.915] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:41:14.915 +07:00 [DBG] [2025-05-28 13:41:14.915] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:14.915 +07:00 [DBG] [2025-05-28 13:41:14.915] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:14.917 +07:00 [INF] [2025-05-28 13:41:14.917] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:14.917 +07:00 [INF] [2025-05-28 13:41:14.917] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:14.917 +07:00 [INF] [2025-05-28 13:41:14.917] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:14.959 +07:00 [INF] [2025-05-28 13:41:14.959] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:41:14.995 +07:00 [INF] [2025-05-28 13:41:14.995] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:15.011 +07:00 [INF] [2025-05-28 13:41:15.011] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 13:41:15.068 +07:00 [INF] [2025-05-28 13:41:15.068] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:15.068 +07:00 [INF] [2025-05-28 13:41:15.068] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:41:17.860 +07:00 [WRN] [2025-05-28 13:41:17.860] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:41:18.369 +07:00 [INF] [2025-05-28 13:41:18.369] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:41:18.373 +07:00 [INF] [2025-05-28 13:41:18.373] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:41:18.386 +07:00 [INF] [2025-05-28 13:41:18.386] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:41:18.386 +07:00 [INF] [2025-05-28 13:41:18.386] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:41:18.390 +07:00 [INF] [2025-05-28 13:41:18.390] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:41:18.431 +07:00 [DBG] [2025-05-28 13:41:18.431] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:41:18.431 +07:00 [INF] [2025-05-28 13:41:18.431] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 2, số người: 3), tự động thoát phòng
2025-05-28 13:41:18.431 +07:00 [INF] [2025-05-28 13:41:18.431] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:41:18.431 +07:00 [WRN] [2025-05-28 13:41:18.431] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:41:18.431 +07:00 [INF] [2025-05-28 13:41:18.431] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:41:18.437 +07:00 [INF] [2025-05-28 13:41:18.437] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:41:18.440 +07:00 [WRN] [2025-05-28 13:41:18.440] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:41:18.440 +07:00 [INF] [2025-05-28 13:41:18.440] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 5.708ms
2025-05-28 13:41:18.440 +07:00 [INF] [2025-05-28 13:41:18.440] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 8.7217ms
2025-05-28 13:41:18.440 +07:00 [DBG] [2025-05-28 13:41:18.440] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:18.440 +07:00 [INF] [2025-05-28 13:41:18.440] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:41:18.440 +07:00 [DBG] [2025-05-28 13:41:18.440] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:18.440 +07:00 [DBG] [2025-05-28 13:41:18.440] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:18.441 +07:00 [INF] [2025-05-28 13:41:18.441] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:18.441 +07:00 [DBG] [2025-05-28 13:41:18.441] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:18.441 +07:00 [DBG] [2025-05-28 13:41:18.441] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:18.441 +07:00 [INF] [2025-05-28 13:41:18.441] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:18.441 +07:00 [DBG] [2025-05-28 13:41:18.441] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:18.441 +07:00 [DBG] [2025-05-28 13:41:18.441] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:18.441 +07:00 [INF] [2025-05-28 13:41:18.441] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:18.442 +07:00 [DBG] [2025-05-28 13:41:18.442] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:18.442 +07:00 [DBG] [2025-05-28 13:41:18.442] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:18.486 +07:00 [INF] [2025-05-28 13:41:18.486] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:41:18.513 +07:00 [INF] [2025-05-28 13:41:18.513] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:18.537 +07:00 [INF] [2025-05-28 13:41:18.537] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 13:41:18.577 +07:00 [INF] [2025-05-28 13:41:18.577] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:18.577 +07:00 [INF] [2025-05-28 13:41:18.577] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5797524452209473, Vị trí: (35, 51)
2025-05-28 13:41:21.387 +07:00 [WRN] [2025-05-28 13:41:21.387] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:41:21.387 +07:00 [ERR] [2025-05-28 13:41:21.387] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 13:41:21.387 +07:00 [INF] [2025-05-28 13:41:21.387] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 6560.4274ms
2025-05-28 13:41:21.387 +07:00 [WRN] [2025-05-28 13:41:21.387] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 13:41:22.102 +07:00 [INF] [2025-05-28 13:41:22.102] [INFO] [nhatrang345]: Thử vào phòng lần 3/3 cho nhatrang345
2025-05-28 13:41:22.110 +07:00 [INF] [2025-05-28 13:41:22.110] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 13:41:22.114 +07:00 [INF] [2025-05-28 13:41:22.114] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 13:41:22.119 +07:00 [INF] [2025-05-28 13:41:22.119] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 13:41:22.121 +07:00 [DBG] [2025-05-28 13:41:22.121] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 13:41:22.121
2025-05-28 13:41:22.121 +07:00 [INF] [2025-05-28 13:41:22.121] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:41:22.125 +07:00 [INF] [2025-05-28 13:41:22.125] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:41:22.383 +07:00 [DBG] [2025-05-28 13:41:22.383] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:41:22.383 +07:00 [INF] [2025-05-28 13:41:22.383] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 2, số người: 3), tự động thoát phòng
2025-05-28 13:41:22.383 +07:00 [INF] [2025-05-28 13:41:22.383] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:41:22.383 +07:00 [WRN] [2025-05-28 13:41:22.383] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:41:22.383 +07:00 [INF] [2025-05-28 13:41:22.383] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:41:22.392 +07:00 [INF] [2025-05-28 13:41:22.392] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:41:22.395 +07:00 [WRN] [2025-05-28 13:41:22.395] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:41:22.395 +07:00 [INF] [2025-05-28 13:41:22.395] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 6.8486ms
2025-05-28 13:41:22.395 +07:00 [INF] [2025-05-28 13:41:22.395] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 12.0102ms
2025-05-28 13:41:22.395 +07:00 [DBG] [2025-05-28 13:41:22.395] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:22.395 +07:00 [INF] [2025-05-28 13:41:22.395] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:41:22.395 +07:00 [DBG] [2025-05-28 13:41:22.395] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:22.395 +07:00 [DBG] [2025-05-28 13:41:22.395] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:22.397 +07:00 [INF] [2025-05-28 13:41:22.397] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:22.397 +07:00 [INF] [2025-05-28 13:41:22.397] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:22.397 +07:00 [INF] [2025-05-28 13:41:22.397] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:22.446 +07:00 [INF] [2025-05-28 13:41:22.446] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:41:22.475 +07:00 [INF] [2025-05-28 13:41:22.475] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:22.504 +07:00 [INF] [2025-05-28 13:41:22.504] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 13:41:22.543 +07:00 [INF] [2025-05-28 13:41:22.543] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:22.543 +07:00 [INF] [2025-05-28 13:41:22.543] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:41:23.392 +07:00 [INF] [2025-05-28 13:41:23.392] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 13:41:23.392 +07:00 [INF] [2025-05-28 13:41:23.392] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 13:41:23.396 +07:00 [INF] [2025-05-28 13:41:23.396] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 13:41:24.090 +07:00 [INF] [2025-05-28 13:41:24.090] [INFO] [nhatrang345]: Đã xóa trạng thái phòng cho nhatrang345
2025-05-28 13:41:24.090 +07:00 [INF] [2025-05-28 13:41:24.090] [INFO]: Đã hủy lấy bàn trống
2025-05-28 13:41:24.090 +07:00 [WRN] [2025-05-28 13:41:24.090] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:41:24.106 +07:00 [INF] [2025-05-28 13:41:24.106] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 13:41:24.113 +07:00 [INF] [2025-05-28 13:41:24.113] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 2008.253ms
2025-05-28 13:41:24.124 +07:00 [INF] [2025-05-28 13:41:24.124] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 13:41:24.132 +07:00 [ERR] [2025-05-28 13:41:24.132] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 13:41:24.140 +07:00 [INF] [2025-05-28 13:41:24.140] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 16582.2361ms
2025-05-28 13:41:24.140 +07:00 [DBG] [2025-05-28 13:41:24.140] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:24.140 +07:00 [DBG] [2025-05-28 13:41:24.140] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:24.153 +07:00 [INF] [2025-05-28 13:41:24.153] [INFO] [nhatrang345]: Đã hủy lấy bàn trống cho nhatrang345
2025-05-28 13:41:24.153 +07:00 [INF] [2025-05-28 13:41:24.153] [INFO] [nhatrang345]: Hoàn thành xử lý cho nhatrang345, thời gian: 16598.0457ms
2025-05-28 13:41:24.153 +07:00 [WRN] [2025-05-28 13:41:24.153] [WARNING]: Đã thử hết user mà không tìm thấy bàn trống
2025-05-28 13:41:24.159 +07:00 [INF] [2025-05-28 13:41:24.159] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:24.160 +07:00 [INF] [2025-05-28 13:41:24.160] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:24.160 +07:00 [INF] [2025-05-28 13:41:24.160] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:24.230 +07:00 [INF] [2025-05-28 13:41:24.230] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:24.257 +07:00 [INF] [2025-05-28 13:41:24.257] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.12 giây
2025-05-28 13:41:25.067 +07:00 [DBG] [2025-05-28 13:41:25.067] [DEBUG] [nhatrang345]: ❌ Tắt chế độ Get Empty Table cho nhatrang345
2025-05-28 13:41:25.067 +07:00 [DBG] [2025-05-28 13:41:25.067] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:25.067 +07:00 [DBG] [2025-05-28 13:41:25.067] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:25.067 +07:00 [INF] [2025-05-28 13:41:25.067] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:25.067 +07:00 [DBG] [2025-05-28 13:41:25.067] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:25.068 +07:00 [DBG] [2025-05-28 13:41:25.068] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:25.068 +07:00 [INF] [2025-05-28 13:41:25.068] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:25.068 +07:00 [DBG] [2025-05-28 13:41:25.068] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:25.068 +07:00 [DBG] [2025-05-28 13:41:25.068] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:25.068 +07:00 [INF] [2025-05-28 13:41:25.068] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:25.068 +07:00 [DBG] [2025-05-28 13:41:25.068] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:25.068 +07:00 [DBG] [2025-05-28 13:41:25.068] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:25.145 +07:00 [INF] [2025-05-28 13:41:25.145] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:25.174 +07:00 [INF] [2025-05-28 13:41:25.174] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 13:41:28.406 +07:00 [INF] [2025-05-28 13:41:28.406] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 13:41:28.406 +07:00 [INF] [2025-05-28 13:41:28.406] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 13:41:28.416 +07:00 [INF] [2025-05-28 13:41:28.416] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 13:41:33.402 +07:00 [INF] [2025-05-28 13:41:33.402] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 13:41:33.402 +07:00 [INF] [2025-05-28 13:41:33.402] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 13:41:33.406 +07:00 [INF] [2025-05-28 13:41:33.406] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 13:41:38.403 +07:00 [INF] [2025-05-28 13:41:38.403] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 13:41:38.403 +07:00 [INF] [2025-05-28 13:41:38.403] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 13:41:38.415 +07:00 [INF] [2025-05-28 13:41:38.415] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 13:41:43.405 +07:00 [INF] [2025-05-28 13:41:43.405] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 13:41:43.405 +07:00 [INF] [2025-05-28 13:41:43.405] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 13:41:43.409 +07:00 [INF] [2025-05-28 13:41:43.409] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 13:41:48.421 +07:00 [INF] [2025-05-28 13:41:48.421] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 13:41:48.421 +07:00 [INF] [2025-05-28 13:41:48.421] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 13:41:48.422 +07:00 [INF] [2025-05-28 13:41:48.422] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 13:41:53.423 +07:00 [INF] [2025-05-28 13:41:53.423] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 13:41:53.423 +07:00 [INF] [2025-05-28 13:41:53.423] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 13:41:53.426 +07:00 [INF] [2025-05-28 13:41:53.426] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 13:43:47.907 +07:00 [INF] [2025-05-28 13:43:47.907] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 13:43:47.907 +07:00 [INF] [2025-05-28 13:43:47.907] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 13:43:52.640 +07:00 [INF] [2025-05-28 13:43:52.640] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:43:52.910 +07:00 [INF] [2025-05-28 13:43:52.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 13:43:52.910 +07:00 [INF] [2025-05-28 13:43:52.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 13:43:57.466 +07:00 [INF] [2025-05-28 13:43:57.466] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:43:57.909 +07:00 [INF] [2025-05-28 13:43:57.909] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 13:43:57.910 +07:00 [INF] [2025-05-28 13:43:57.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 13:44:02.908 +07:00 [INF] [2025-05-28 13:44:02.908] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 13:44:02.908 +07:00 [INF] [2025-05-28 13:44:02.908] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 13:44:06.682 +07:00 [INF] [2025-05-28 13:44:06.682] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:44:07.461 +07:00 [INF] [2025-05-28 13:44:07.461] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 13:44:07.910 +07:00 [INF] [2025-05-28 13:44:07.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 13:44:07.910 +07:00 [INF] [2025-05-28 13:44:07.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 13:44:12.460 +07:00 [INF] [2025-05-28 13:44:12.460] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 13:44:12.915 +07:00 [INF] [2025-05-28 13:44:12.915] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 13:44:12.915 +07:00 [INF] [2025-05-28 13:44:12.915] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 13:44:17.461 +07:00 [INF] [2025-05-28 13:44:17.461] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 13:44:17.914 +07:00 [INF] [2025-05-28 13:44:17.914] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 13:44:17.914 +07:00 [INF] [2025-05-28 13:44:17.914] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 13:44:22.462 +07:00 [INF] [2025-05-28 13:44:22.462] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 13:44:22.908 +07:00 [INF] [2025-05-28 13:44:22.908] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 13:44:22.908 +07:00 [INF] [2025-05-28 13:44:22.908] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 13:44:27.461 +07:00 [INF] [2025-05-28 13:44:27.461] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 13:44:27.910 +07:00 [INF] [2025-05-28 13:44:27.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 13:44:27.910 +07:00 [INF] [2025-05-28 13:44:27.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 13:44:32.476 +07:00 [INF] [2025-05-28 13:44:32.476] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 13:44:32.914 +07:00 [INF] [2025-05-28 13:44:32.914] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 13:44:32.914 +07:00 [INF] [2025-05-28 13:44:32.914] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 13:44:37.476 +07:00 [INF] [2025-05-28 13:44:37.476] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 13:44:37.910 +07:00 [INF] [2025-05-28 13:44:37.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,21]
2025-05-28 13:44:37.910 +07:00 [INF] [2025-05-28 13:44:37.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,21]
2025-05-28 13:44:42.477 +07:00 [INF] [2025-05-28 13:44:42.477] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 13:44:42.913 +07:00 [INF] [2025-05-28 13:44:42.913] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,22]
2025-05-28 13:44:42.913 +07:00 [INF] [2025-05-28 13:44:42.913] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,22]
2025-05-28 13:44:47.476 +07:00 [INF] [2025-05-28 13:44:47.476] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 13:44:47.913 +07:00 [INF] [2025-05-28 13:44:47.913] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,23]
2025-05-28 13:44:47.913 +07:00 [INF] [2025-05-28 13:44:47.913] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,23]
2025-05-28 13:44:52.482 +07:00 [INF] [2025-05-28 13:44:52.482] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 13:44:52.911 +07:00 [INF] [2025-05-28 13:44:52.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,24]
2025-05-28 13:44:52.911 +07:00 [INF] [2025-05-28 13:44:52.911] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,24]
2025-05-28 13:44:57.495 +07:00 [INF] [2025-05-28 13:44:57.495] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 13:44:57.910 +07:00 [INF] [2025-05-28 13:44:57.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,25]
2025-05-28 13:44:57.910 +07:00 [INF] [2025-05-28 13:44:57.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,25]
2025-05-28 13:45:02.514 +07:00 [INF] [2025-05-28 13:45:02.514] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 13:45:02.927 +07:00 [INF] [2025-05-28 13:45:02.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,26]
2025-05-28 13:45:02.927 +07:00 [INF] [2025-05-28 13:45:02.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,26]
2025-05-28 13:45:07.512 +07:00 [INF] [2025-05-28 13:45:07.512] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 13:45:07.928 +07:00 [INF] [2025-05-28 13:45:07.928] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,27]
2025-05-28 13:45:07.928 +07:00 [INF] [2025-05-28 13:45:07.928] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,27]
2025-05-28 13:45:12.527 +07:00 [INF] [2025-05-28 13:45:12.527] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 13:45:12.927 +07:00 [INF] [2025-05-28 13:45:12.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,28]
2025-05-28 13:45:12.927 +07:00 [INF] [2025-05-28 13:45:12.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,28]
2025-05-28 13:45:17.658 +07:00 [INF] [2025-05-28 13:45:17.658] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 13:45:17.929 +07:00 [INF] [2025-05-28 13:45:17.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,29]
2025-05-28 13:45:17.929 +07:00 [INF] [2025-05-28 13:45:17.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,29]
2025-05-28 13:45:22.755 +07:00 [INF] [2025-05-28 13:45:22.755] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 13:45:22.929 +07:00 [INF] [2025-05-28 13:45:22.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,30]
2025-05-28 13:45:22.930 +07:00 [INF] [2025-05-28 13:45:22.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,30]
2025-05-28 13:45:27.543 +07:00 [INF] [2025-05-28 13:45:27.543] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 13:45:27.927 +07:00 [INF] [2025-05-28 13:45:27.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,31]
2025-05-28 13:45:27.927 +07:00 [INF] [2025-05-28 13:45:27.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,31]
2025-05-28 13:45:32.551 +07:00 [INF] [2025-05-28 13:45:32.551] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,21]
2025-05-28 13:45:32.927 +07:00 [INF] [2025-05-28 13:45:32.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,32]
2025-05-28 13:45:32.927 +07:00 [INF] [2025-05-28 13:45:32.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,32]
2025-05-28 13:45:37.544 +07:00 [INF] [2025-05-28 13:45:37.544] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,22]
2025-05-28 13:45:37.930 +07:00 [INF] [2025-05-28 13:45:37.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,33]
2025-05-28 13:45:37.930 +07:00 [INF] [2025-05-28 13:45:37.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,33]
2025-05-28 13:45:42.548 +07:00 [INF] [2025-05-28 13:45:42.548] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,23]
2025-05-28 13:45:42.929 +07:00 [INF] [2025-05-28 13:45:42.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,34]
2025-05-28 13:45:42.929 +07:00 [INF] [2025-05-28 13:45:42.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,34]
2025-05-28 13:45:47.545 +07:00 [INF] [2025-05-28 13:45:47.545] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,24]
2025-05-28 13:45:47.929 +07:00 [INF] [2025-05-28 13:45:47.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,35]
2025-05-28 13:45:47.929 +07:00 [INF] [2025-05-28 13:45:47.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,35]
2025-05-28 13:45:52.544 +07:00 [INF] [2025-05-28 13:45:52.544] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,25]
2025-05-28 13:45:52.928 +07:00 [INF] [2025-05-28 13:45:52.928] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,36]
2025-05-28 13:45:52.928 +07:00 [INF] [2025-05-28 13:45:52.928] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,36]
2025-05-28 13:45:57.545 +07:00 [INF] [2025-05-28 13:45:57.545] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,26]
2025-05-28 13:45:57.929 +07:00 [INF] [2025-05-28 13:45:57.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,37]
2025-05-28 13:45:57.929 +07:00 [INF] [2025-05-28 13:45:57.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,37]
2025-05-28 13:46:02.549 +07:00 [INF] [2025-05-28 13:46:02.549] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,27]
2025-05-28 13:46:02.929 +07:00 [INF] [2025-05-28 13:46:02.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,38]
2025-05-28 13:46:02.929 +07:00 [INF] [2025-05-28 13:46:02.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,38]
2025-05-28 13:46:07.547 +07:00 [INF] [2025-05-28 13:46:07.547] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,28]
2025-05-28 13:46:07.929 +07:00 [INF] [2025-05-28 13:46:07.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,39]
2025-05-28 13:46:07.929 +07:00 [INF] [2025-05-28 13:46:07.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,39]
2025-05-28 13:46:12.560 +07:00 [INF] [2025-05-28 13:46:12.560] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,29]
2025-05-28 13:46:12.929 +07:00 [INF] [2025-05-28 13:46:12.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,40]
2025-05-28 13:46:12.929 +07:00 [INF] [2025-05-28 13:46:12.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,40]
2025-05-28 13:46:17.572 +07:00 [INF] [2025-05-28 13:46:17.572] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,30]
2025-05-28 13:46:17.940 +07:00 [INF] [2025-05-28 13:46:17.940] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,41]
2025-05-28 13:46:17.940 +07:00 [INF] [2025-05-28 13:46:17.940] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,41]
2025-05-28 13:46:22.632 +07:00 [INF] [2025-05-28 13:46:22.632] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,31]
2025-05-28 13:46:22.934 +07:00 [INF] [2025-05-28 13:46:22.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,42]
2025-05-28 13:46:22.934 +07:00 [INF] [2025-05-28 13:46:22.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,42]
2025-05-28 13:46:27.565 +07:00 [INF] [2025-05-28 13:46:27.565] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,32]
2025-05-28 13:46:27.929 +07:00 [INF] [2025-05-28 13:46:27.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,43]
2025-05-28 13:46:27.929 +07:00 [INF] [2025-05-28 13:46:27.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,43]
2025-05-28 13:46:32.571 +07:00 [INF] [2025-05-28 13:46:32.571] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,33]
2025-05-28 13:46:32.932 +07:00 [INF] [2025-05-28 13:46:32.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,44]
2025-05-28 13:46:32.932 +07:00 [INF] [2025-05-28 13:46:32.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,44]
2025-05-28 13:46:37.735 +07:00 [INF] [2025-05-28 13:46:37.735] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,34]
2025-05-28 13:46:37.931 +07:00 [INF] [2025-05-28 13:46:37.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,45]
2025-05-28 13:46:37.931 +07:00 [INF] [2025-05-28 13:46:37.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,45]
2025-05-28 13:46:42.562 +07:00 [INF] [2025-05-28 13:46:42.562] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,35]
2025-05-28 13:46:42.930 +07:00 [INF] [2025-05-28 13:46:42.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,46]
2025-05-28 13:46:42.930 +07:00 [INF] [2025-05-28 13:46:42.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,46]
2025-05-28 13:46:47.564 +07:00 [INF] [2025-05-28 13:46:47.564] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,36]
2025-05-28 13:46:47.935 +07:00 [INF] [2025-05-28 13:46:47.935] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,47]
2025-05-28 13:46:47.935 +07:00 [INF] [2025-05-28 13:46:47.935] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,47]
2025-05-28 13:46:52.565 +07:00 [INF] [2025-05-28 13:46:52.565] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,37]
2025-05-28 13:46:52.930 +07:00 [INF] [2025-05-28 13:46:52.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,48]
2025-05-28 13:46:52.930 +07:00 [INF] [2025-05-28 13:46:52.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,48]
2025-05-28 13:46:57.563 +07:00 [INF] [2025-05-28 13:46:57.562] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,38]
2025-05-28 13:46:57.936 +07:00 [INF] [2025-05-28 13:46:57.936] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,49]
2025-05-28 13:46:57.936 +07:00 [INF] [2025-05-28 13:46:57.936] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,49]
2025-05-28 13:47:02.568 +07:00 [INF] [2025-05-28 13:47:02.568] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,39]
2025-05-28 13:47:02.931 +07:00 [INF] [2025-05-28 13:47:02.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,50]
2025-05-28 13:47:02.931 +07:00 [INF] [2025-05-28 13:47:02.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,50]
2025-05-28 13:47:07.573 +07:00 [INF] [2025-05-28 13:47:07.573] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,40]
2025-05-28 13:47:07.943 +07:00 [INF] [2025-05-28 13:47:07.943] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,51]
2025-05-28 13:47:07.943 +07:00 [INF] [2025-05-28 13:47:07.943] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,51]
2025-05-28 13:47:12.568 +07:00 [INF] [2025-05-28 13:47:12.568] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,41]
2025-05-28 13:47:12.929 +07:00 [INF] [2025-05-28 13:47:12.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,52]
2025-05-28 13:47:12.929 +07:00 [INF] [2025-05-28 13:47:12.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,52]
2025-05-28 13:47:17.563 +07:00 [INF] [2025-05-28 13:47:17.563] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,42]
2025-05-28 13:47:17.942 +07:00 [INF] [2025-05-28 13:47:17.942] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,53]
2025-05-28 13:47:17.942 +07:00 [INF] [2025-05-28 13:47:17.942] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,53]
2025-05-28 13:47:22.566 +07:00 [INF] [2025-05-28 13:47:22.566] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,43]
2025-05-28 13:47:22.931 +07:00 [INF] [2025-05-28 13:47:22.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,54]
2025-05-28 13:47:22.931 +07:00 [INF] [2025-05-28 13:47:22.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,54]
2025-05-28 13:47:27.585 +07:00 [INF] [2025-05-28 13:47:27.585] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,44]
2025-05-28 13:47:27.931 +07:00 [INF] [2025-05-28 13:47:27.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,55]
2025-05-28 13:47:27.931 +07:00 [INF] [2025-05-28 13:47:27.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,55]
2025-05-28 13:47:32.563 +07:00 [INF] [2025-05-28 13:47:32.563] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,45]
2025-05-28 13:47:32.931 +07:00 [INF] [2025-05-28 13:47:32.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,56]
2025-05-28 13:47:32.931 +07:00 [INF] [2025-05-28 13:47:32.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,56]
2025-05-28 13:47:37.931 +07:00 [INF] [2025-05-28 13:47:37.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,57]
2025-05-28 13:47:37.932 +07:00 [INF] [2025-05-28 13:47:37.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,57]
2025-05-28 13:47:38.187 +07:00 [INF] [2025-05-28 13:47:38.187] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,46]
2025-05-28 13:47:42.597 +07:00 [INF] [2025-05-28 13:47:42.597] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,47]
2025-05-28 13:47:42.931 +07:00 [INF] [2025-05-28 13:47:42.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,58]
2025-05-28 13:47:42.931 +07:00 [INF] [2025-05-28 13:47:42.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,58]
2025-05-28 13:47:47.607 +07:00 [INF] [2025-05-28 13:47:47.607] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,48]
2025-05-28 13:47:47.932 +07:00 [INF] [2025-05-28 13:47:47.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,59]
2025-05-28 13:47:47.932 +07:00 [INF] [2025-05-28 13:47:47.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,59]
2025-05-28 13:47:52.598 +07:00 [INF] [2025-05-28 13:47:52.598] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,49]
2025-05-28 13:47:52.930 +07:00 [INF] [2025-05-28 13:47:52.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,60]
2025-05-28 13:47:52.931 +07:00 [INF] [2025-05-28 13:47:52.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,60]
2025-05-28 13:47:57.611 +07:00 [INF] [2025-05-28 13:47:57.611] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,50]
2025-05-28 13:47:57.932 +07:00 [INF] [2025-05-28 13:47:57.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,61]
2025-05-28 13:47:57.932 +07:00 [INF] [2025-05-28 13:47:57.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,61]
2025-05-28 13:48:02.598 +07:00 [INF] [2025-05-28 13:48:02.598] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,51]
2025-05-28 13:48:02.934 +07:00 [INF] [2025-05-28 13:48:02.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,62]
2025-05-28 13:48:02.934 +07:00 [INF] [2025-05-28 13:48:02.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,62]
2025-05-28 13:48:07.598 +07:00 [INF] [2025-05-28 13:48:07.598] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,52]
2025-05-28 13:48:07.945 +07:00 [INF] [2025-05-28 13:48:07.945] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,63]
2025-05-28 13:48:07.945 +07:00 [INF] [2025-05-28 13:48:07.945] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,63]
2025-05-28 13:48:12.604 +07:00 [INF] [2025-05-28 13:48:12.604] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,53]
2025-05-28 13:48:12.933 +07:00 [INF] [2025-05-28 13:48:12.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,64]
2025-05-28 13:48:12.933 +07:00 [INF] [2025-05-28 13:48:12.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,64]
2025-05-28 13:48:17.600 +07:00 [INF] [2025-05-28 13:48:17.600] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,54]
2025-05-28 13:48:17.934 +07:00 [INF] [2025-05-28 13:48:17.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,65]
2025-05-28 13:48:17.934 +07:00 [INF] [2025-05-28 13:48:17.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,65]
2025-05-28 13:48:22.599 +07:00 [INF] [2025-05-28 13:48:22.599] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,55]
2025-05-28 13:48:22.934 +07:00 [INF] [2025-05-28 13:48:22.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,66]
2025-05-28 13:48:22.934 +07:00 [INF] [2025-05-28 13:48:22.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,66]
2025-05-28 13:48:27.599 +07:00 [INF] [2025-05-28 13:48:27.599] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,56]
2025-05-28 13:48:27.933 +07:00 [INF] [2025-05-28 13:48:27.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,67]
2025-05-28 13:48:27.933 +07:00 [INF] [2025-05-28 13:48:27.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,67]
2025-05-28 13:48:32.604 +07:00 [INF] [2025-05-28 13:48:32.604] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,57]
2025-05-28 13:48:32.932 +07:00 [INF] [2025-05-28 13:48:32.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,68]
2025-05-28 13:48:32.932 +07:00 [INF] [2025-05-28 13:48:32.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,68]
2025-05-28 13:48:37.599 +07:00 [INF] [2025-05-28 13:48:37.599] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,58]
2025-05-28 13:48:37.935 +07:00 [INF] [2025-05-28 13:48:37.935] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,69]
2025-05-28 13:48:37.935 +07:00 [INF] [2025-05-28 13:48:37.935] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,69]
2025-05-28 13:48:42.600 +07:00 [INF] [2025-05-28 13:48:42.600] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,59]
2025-05-28 13:48:42.945 +07:00 [INF] [2025-05-28 13:48:42.945] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,70]
2025-05-28 13:48:42.945 +07:00 [INF] [2025-05-28 13:48:42.945] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,70]
2025-05-28 13:48:47.602 +07:00 [INF] [2025-05-28 13:48:47.602] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,60]
2025-05-28 13:48:47.933 +07:00 [INF] [2025-05-28 13:48:47.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,71]
2025-05-28 13:48:47.933 +07:00 [INF] [2025-05-28 13:48:47.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,71]
2025-05-28 14:57:24.961 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 14:57:27.758 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 14:57:27.762 +07:00 [INF] Form1 constructor started.
2025-05-28 14:57:27.778 +07:00 [DBG] [2025-05-28 14:57:27.778] [DEBUG]: Gọi InitializeComponent
2025-05-28 14:57:27.788 +07:00 [INF] [2025-05-28 14:57:27.788] [INFO]: Khởi tạo UIManager thành công
2025-05-28 14:57:27.788 +07:00 [DBG] [2025-05-28 14:57:27.788] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 14:57:27.790 +07:00 [INF] [2025-05-28 14:57:27.790] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 14:57:27.790 +07:00 [DBG] [2025-05-28 14:57:27.790] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 14:57:27.790 +07:00 [INF] [2025-05-28 14:57:27.790] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 14:57:27.791 +07:00 [INF] [2025-05-28 14:57:27.791] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 14:57:27.807 +07:00 [DBG] [2025-05-28 14:57:27.807] [DEBUG]: Bắt đầu OnLoad
2025-05-28 14:57:27.807 +07:00 [DBG] [2025-05-28 14:57:27.807] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 14:57:27.823 +07:00 [INF] [2025-05-28 14:57:27.822] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11823
2025-05-28 14:57:27.823 +07:00 [INF] [2025-05-28 14:57:27.823] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 14:57:27.838 +07:00 [INF] [2025-05-28 14:57:27.838] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 14:57:27.840 +07:00 [INF] [2025-05-28 14:57:27.840] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 14:57:27.840 +07:00 [INF] [2025-05-28 14:57:27.840] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 14:57:27.840 +07:00 [INF] [2025-05-28 14:57:27.840] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 14:57:27.841 +07:00 [INF] [2025-05-28 14:57:27.841] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 14:57:27.841 +07:00 [INF] [2025-05-28 14:57:27.841] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 14:57:27.841 +07:00 [INF] InitializeAsync started.
2025-05-28 14:57:27.841 +07:00 [INF] [2025-05-28 14:57:27.841] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 14:57:27.845 +07:00 [DBG] [2025-05-28 14:57:27.845] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 14:57:27.847 +07:00 [DBG] [2025-05-28 14:57:27.847] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 14:57:27.848 +07:00 [DBG] [2025-05-28 14:57:27.848] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:27.848 +07:00 [DBG] [2025-05-28 14:57:27.848] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:27.855 +07:00 [DBG] [2025-05-28 14:57:27.855] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 14:57:27.855 +07:00 [INF] [2025-05-28 14:57:27.855] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 14:57:27.855 +07:00 [DBG] [2025-05-28 14:57:27.855] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:27.857 +07:00 [DBG] [2025-05-28 14:57:27.857] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:27.857 +07:00 [DBG] [2025-05-28 14:57:27.857] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:27.857 +07:00 [INF] [2025-05-28 14:57:27.857] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:27.857 +07:00 [DBG] [2025-05-28 14:57:27.857] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:27.857 +07:00 [DBG] [2025-05-28 14:57:27.857] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:27.857 +07:00 [DBG] [2025-05-28 14:57:27.857] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:27.857 +07:00 [INF] [2025-05-28 14:57:27.857] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:27.857 +07:00 [DBG] [2025-05-28 14:57:27.857] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:27.857 +07:00 [DBG] [2025-05-28 14:57:27.857] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:27.904 +07:00 [INF] [2025-05-28 14:57:27.904] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:27.913 +07:00 [INF] [2025-05-28 14:57:27.913] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 14:57:27.913 +07:00 [DBG] [2025-05-28 14:57:27.913] [DEBUG]: OnLoad hoàn tất
2025-05-28 14:57:29.075 +07:00 [INF] [2025-05-28 14:57:29.075] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 14:57:29.081 +07:00 [INF] [2025-05-28 14:57:29.081] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 14:57:29.082 +07:00 [INF] [2025-05-28 14:57:29.082] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 14:57:29.085 +07:00 [DBG] [2025-05-28 14:57:29.085] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 14:57:29.127 +07:00 [INF] [2025-05-28 14:57:29.127] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 43ms
2025-05-28 14:57:29.552 +07:00 [DBG] [2025-05-28 14:57:29.552] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 14:57:29.553 +07:00 [INF] [2025-05-28 14:57:29.553] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:59492. Thời gian: 424ms
2025-05-28 14:57:29.553 +07:00 [DBG] [2025-05-28 14:57:29.553] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:29.553 +07:00 [DBG] [2025-05-28 14:57:29.553] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:29.553 +07:00 [INF] [2025-05-28 14:57:29.553] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 470ms
2025-05-28 14:57:29.553 +07:00 [DBG] [2025-05-28 14:57:29.553] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:29.553 +07:00 [DBG] [2025-05-28 14:57:29.553] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:31.173 +07:00 [INF] [2025-05-28 14:57:31.173] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 14:57:31.185 +07:00 [INF] [2025-05-28 14:57:31.185] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 14:57:31.277 +07:00 [INF] [2025-05-28 14:57:31.277] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 14:57:31.288 +07:00 [DBG] [2025-05-28 14:57:31.288] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 14:57:31.293 +07:00 [INF] [2025-05-28 14:57:31.293] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 14:57:31.298 +07:00 [INF] [2025-05-28 14:57:31.298] [INFO] [nhatrang345]: ✅ Đã inject C# methods cho nhatrang345
2025-05-28 14:57:31.300 +07:00 [INF] [2025-05-28 14:57:31.300] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:31.300 +07:00 [DBG] [2025-05-28 14:57:31.300] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:31.301 +07:00 [DBG] [2025-05-28 14:57:31.301] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:31.301 +07:00 [INF] [2025-05-28 14:57:31.301] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:31.301 +07:00 [DBG] [2025-05-28 14:57:31.301] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:31.301 +07:00 [DBG] [2025-05-28 14:57:31.301] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:31.301 +07:00 [INF] [2025-05-28 14:57:31.301] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:31.301 +07:00 [DBG] [2025-05-28 14:57:31.301] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:31.301 +07:00 [DBG] [2025-05-28 14:57:31.301] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:31.331 +07:00 [INF] [2025-05-28 14:57:31.331] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:31.331 +07:00 [INF] [2025-05-28 14:57:31.331] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:31.331 +07:00 [DBG] [2025-05-28 14:57:31.331] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:31.331 +07:00 [DBG] [2025-05-28 14:57:31.331] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:31.331 +07:00 [INF] [2025-05-28 14:57:31.331] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:31.331 +07:00 [DBG] [2025-05-28 14:57:31.331] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:31.331 +07:00 [DBG] [2025-05-28 14:57:31.331] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:31.331 +07:00 [INF] [2025-05-28 14:57:31.331] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:31.331 +07:00 [DBG] [2025-05-28 14:57:31.331] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:31.332 +07:00 [DBG] [2025-05-28 14:57:31.332] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:31.358 +07:00 [INF] [2025-05-28 14:57:31.358] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:31.369 +07:00 [INF] [2025-05-28 14:57:31.369] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.82 giây
2025-05-28 14:57:31.377 +07:00 [INF] [2025-05-28 14:57:31.377] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.82 giây
2025-05-28 14:57:33.304 +07:00 [INF] [2025-05-28 14:57:33.304] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 14:57:34.302 +07:00 [INF] [2025-05-28 14:57:34.302] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 14:57:34.302 +07:00 [DBG] [2025-05-28 14:57:34.302] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:34.302 +07:00 [DBG] [2025-05-28 14:57:34.302] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:34.302 +07:00 [INF] [2025-05-28 14:57:34.302] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 14:57:34.302 +07:00 [DBG] [2025-05-28 14:57:34.302] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:34.302 +07:00 [DBG] [2025-05-28 14:57:34.302] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:34.302 +07:00 [INF] [2025-05-28 14:57:34.302] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:34.303 +07:00 [DBG] [2025-05-28 14:57:34.303] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:34.303 +07:00 [DBG] [2025-05-28 14:57:34.303] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:34.303 +07:00 [INF] [2025-05-28 14:57:34.303] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:34.303 +07:00 [DBG] [2025-05-28 14:57:34.303] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:34.303 +07:00 [DBG] [2025-05-28 14:57:34.303] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:34.303 +07:00 [INF] [2025-05-28 14:57:34.303] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:34.303 +07:00 [DBG] [2025-05-28 14:57:34.303] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:34.303 +07:00 [DBG] [2025-05-28 14:57:34.303] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:34.385 +07:00 [INF] [2025-05-28 14:57:34.385] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:34.385 +07:00 [INF] [2025-05-28 14:57:34.385] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:34.385 +07:00 [DBG] [2025-05-28 14:57:34.385] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:34.385 +07:00 [DBG] [2025-05-28 14:57:34.385] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:34.385 +07:00 [INF] [2025-05-28 14:57:34.385] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:34.385 +07:00 [DBG] [2025-05-28 14:57:34.385] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:34.385 +07:00 [DBG] [2025-05-28 14:57:34.385] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:34.385 +07:00 [INF] [2025-05-28 14:57:34.385] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:34.385 +07:00 [DBG] [2025-05-28 14:57:34.385] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:34.385 +07:00 [DBG] [2025-05-28 14:57:34.385] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:34.455 +07:00 [INF] [2025-05-28 14:57:34.455] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:34.472 +07:00 [INF] [2025-05-28 14:57:34.472] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.17 giây
2025-05-28 14:57:34.486 +07:00 [INF] [2025-05-28 14:57:34.486] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.18 giây
2025-05-28 14:57:35.319 +07:00 [INF] [2025-05-28 14:57:35.319] [INFO] [nhatrang345]: ✅ Đã inject profile buttons thành công cho nhatrang345
2025-05-28 14:57:37.203 +07:00 [INF] [2025-05-28 14:57:37.203] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 14:57:37.204 +07:00 [INF] [2025-05-28 14:57:37.204] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 14:57:37.212 +07:00 [INF] [2025-05-28 14:57:37.212] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 14:57:37.325 +07:00 [INF] [2025-05-28 14:57:37.325] [INFO] [nhatrang345]: Buttons đã xuất hiện, test chúng cho nhatrang345
2025-05-28 14:57:37.331 +07:00 [INF] [2025-05-28 14:57:37.331] [INFO] [nhatrang345]: 🧪 Test results cho nhatrang345: System.Collections.Generic.Dictionary`2[System.String,System.Object]
2025-05-28 14:57:37.334 +07:00 [DBG] [2025-05-28 14:57:37.334] [DEBUG] [nhatrang345]: ✅ Đã setup reload handler cho nhatrang345
2025-05-28 14:57:41.521 +07:00 [DBG] [2025-05-28 14:57:41.521] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 14:57:41.686 +07:00 [INF] [2025-05-28 14:57:41.686] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:57:41.686 +07:00 [INF] [2025-05-28 14:57:41.686] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 14:57:41.687 +07:00 [INF] [2025-05-28 14:57:41.687] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 14:57:41.687 +07:00 [DBG] [2025-05-28 14:57:41.687] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 14:57:41.690 +07:00 [INF] [2025-05-28 14:57:41.690] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 14:57:41.690 +07:00 [INF] [2025-05-28 14:57:41.690] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 14:57:41.694 +07:00 [INF] [2025-05-28 14:57:41.694] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 14:57:41.704 +07:00 [INF] [2025-05-28 14:57:41.703] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:57:41.710 +07:00 [INF] [2025-05-28 14:57:41.710] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:57:41.716 +07:00 [INF] [2025-05-28 14:57:41.716] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:57:41.719 +07:00 [DBG] [2025-05-28 14:57:41.719] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:57:41.719
2025-05-28 14:57:41.719 +07:00 [INF] [2025-05-28 14:57:41.719] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:41.722 +07:00 [INF] [2025-05-28 14:57:41.722] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:41.786 +07:00 [DBG] [2025-05-28 14:57:41.786] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:57:41.787 +07:00 [INF] [2025-05-28 14:57:41.787] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 1, số người: 3), tự động thoát phòng
2025-05-28 14:57:41.788 +07:00 [INF] [2025-05-28 14:57:41.788] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 14:57:41.788 +07:00 [DBG] [2025-05-28 14:57:41.788] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 14:57:41.788 +07:00 [INF] [2025-05-28 14:57:41.788] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:41.788 +07:00 [INF] [2025-05-28 14:57:41.788] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:41.788 +07:00 [INF] [2025-05-28 14:57:41.788] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 14:57:41.791 +07:00 [INF] [2025-05-28 14:57:41.791] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:41.791 +07:00 [INF] [2025-05-28 14:57:41.791] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:41.792 +07:00 [ERR] [2025-05-28 14:57:41.791] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 14:57:41.792 +07:00 [INF] [2025-05-28 14:57:41.792] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 93.8729ms
2025-05-28 14:57:41.792 +07:00 [WRN] [2025-05-28 14:57:41.792] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:57:41.796 +07:00 [INF] [2025-05-28 14:57:41.796] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 14:57:41.801 +07:00 [WRN] [2025-05-28 14:57:41.801] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 14:57:41.801 +07:00 [INF] [2025-05-28 14:57:41.801] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 7.4229ms
2025-05-28 14:57:41.801 +07:00 [INF] [2025-05-28 14:57:41.801] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 12.1783ms
2025-05-28 14:57:41.801 +07:00 [DBG] [2025-05-28 14:57:41.801] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:41.802 +07:00 [INF] [2025-05-28 14:57:41.802] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 14:57:41.803 +07:00 [DBG] [2025-05-28 14:57:41.803] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:41.803 +07:00 [DBG] [2025-05-28 14:57:41.803] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:41.804 +07:00 [INF] [2025-05-28 14:57:41.804] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:41.804 +07:00 [DBG] [2025-05-28 14:57:41.804] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:41.804 +07:00 [DBG] [2025-05-28 14:57:41.804] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:41.804 +07:00 [INF] [2025-05-28 14:57:41.804] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:41.804 +07:00 [DBG] [2025-05-28 14:57:41.804] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:41.804 +07:00 [DBG] [2025-05-28 14:57:41.804] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:41.804 +07:00 [INF] [2025-05-28 14:57:41.804] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:41.804 +07:00 [DBG] [2025-05-28 14:57:41.804] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:41.804 +07:00 [DBG] [2025-05-28 14:57:41.804] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:41.845 +07:00 [INF] [2025-05-28 14:57:41.845] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:57:41.892 +07:00 [INF] [2025-05-28 14:57:41.892] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:41.925 +07:00 [INF] [2025-05-28 14:57:41.925] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.12 giây
2025-05-28 14:57:41.929 +07:00 [INF] [2025-05-28 14:57:41.929] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:57:41.929 +07:00 [INF] [2025-05-28 14:57:41.929] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 14:57:42.206 +07:00 [INF] [2025-05-28 14:57:42.206] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 14:57:42.206 +07:00 [INF] [2025-05-28 14:57:42.206] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 14:57:42.218 +07:00 [INF] [2025-05-28 14:57:42.218] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 14:57:42.504 +07:00 [INF] [2025-05-28 14:57:42.504] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 14:57:42.512 +07:00 [INF] [2025-05-28 14:57:42.512] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:57:42.516 +07:00 [INF] [2025-05-28 14:57:42.516] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:57:42.521 +07:00 [INF] [2025-05-28 14:57:42.521] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:57:42.523 +07:00 [DBG] [2025-05-28 14:57:42.523] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:57:42.523
2025-05-28 14:57:42.523 +07:00 [INF] [2025-05-28 14:57:42.523] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:42.527 +07:00 [INF] [2025-05-28 14:57:42.527] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:42.591 +07:00 [DBG] [2025-05-28 14:57:42.591] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:57:42.591 +07:00 [INF] [2025-05-28 14:57:42.591] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 14:57:42.591 +07:00 [INF] [2025-05-28 14:57:42.591] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 14:57:42.591 +07:00 [DBG] [2025-05-28 14:57:42.591] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 14:57:42.591 +07:00 [INF] [2025-05-28 14:57:42.591] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 14:57:42.592 +07:00 [INF] [2025-05-28 14:57:42.592] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:42.592 +07:00 [INF] [2025-05-28 14:57:42.592] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:42.597 +07:00 [INF] [2025-05-28 14:57:42.597] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:42.597 +07:00 [INF] [2025-05-28 14:57:42.597] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:42.597 +07:00 [ERR] [2025-05-28 14:57:42.597] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 14:57:42.597 +07:00 [INF] [2025-05-28 14:57:42.597] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 89.4284ms
2025-05-28 14:57:42.597 +07:00 [WRN] [2025-05-28 14:57:42.597] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:57:42.600 +07:00 [INF] [2025-05-28 14:57:42.600] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 14:57:42.603 +07:00 [WRN] [2025-05-28 14:57:42.603] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 14:57:42.603 +07:00 [INF] [2025-05-28 14:57:42.603] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 8.9769ms
2025-05-28 14:57:42.603 +07:00 [INF] [2025-05-28 14:57:42.603] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 11.7024ms
2025-05-28 14:57:42.603 +07:00 [DBG] [2025-05-28 14:57:42.603] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:42.603 +07:00 [INF] [2025-05-28 14:57:42.603] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 14:57:42.603 +07:00 [DBG] [2025-05-28 14:57:42.603] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:42.603 +07:00 [DBG] [2025-05-28 14:57:42.603] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:42.604 +07:00 [INF] [2025-05-28 14:57:42.604] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:42.604 +07:00 [DBG] [2025-05-28 14:57:42.604] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:42.605 +07:00 [DBG] [2025-05-28 14:57:42.605] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:42.605 +07:00 [INF] [2025-05-28 14:57:42.605] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:42.605 +07:00 [DBG] [2025-05-28 14:57:42.605] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:42.605 +07:00 [DBG] [2025-05-28 14:57:42.605] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:42.605 +07:00 [INF] [2025-05-28 14:57:42.605] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:42.605 +07:00 [DBG] [2025-05-28 14:57:42.605] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:42.605 +07:00 [DBG] [2025-05-28 14:57:42.605] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:42.649 +07:00 [INF] [2025-05-28 14:57:42.649] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:57:42.680 +07:00 [INF] [2025-05-28 14:57:42.680] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:42.709 +07:00 [INF] [2025-05-28 14:57:42.709] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 14:57:42.732 +07:00 [INF] [2025-05-28 14:57:42.732] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:57:42.732 +07:00 [INF] [2025-05-28 14:57:42.732] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 14:57:43.316 +07:00 [INF] [2025-05-28 14:57:43.316] [INFO] [nhatrang345]: Thử vào phòng lần 3/3 cho nhatrang345
2025-05-28 14:57:43.323 +07:00 [INF] [2025-05-28 14:57:43.323] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:57:43.327 +07:00 [INF] [2025-05-28 14:57:43.327] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:57:43.333 +07:00 [INF] [2025-05-28 14:57:43.333] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:57:43.335 +07:00 [DBG] [2025-05-28 14:57:43.335] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:57:43.335
2025-05-28 14:57:43.335 +07:00 [INF] [2025-05-28 14:57:43.335] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:43.339 +07:00 [INF] [2025-05-28 14:57:43.339] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:43.420 +07:00 [DBG] [2025-05-28 14:57:43.420] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:57:43.421 +07:00 [INF] [2025-05-28 14:57:43.421] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 14:57:43.421 +07:00 [INF] [2025-05-28 14:57:43.421] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 14:57:43.421 +07:00 [DBG] [2025-05-28 14:57:43.421] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 14:57:43.421 +07:00 [INF] [2025-05-28 14:57:43.421] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 14:57:43.421 +07:00 [INF] [2025-05-28 14:57:43.421] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:43.421 +07:00 [INF] [2025-05-28 14:57:43.421] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:43.426 +07:00 [INF] [2025-05-28 14:57:43.426] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:43.426 +07:00 [INF] [2025-05-28 14:57:43.426] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:43.426 +07:00 [ERR] [2025-05-28 14:57:43.426] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 14:57:43.426 +07:00 [INF] [2025-05-28 14:57:43.426] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 107.704ms
2025-05-28 14:57:43.426 +07:00 [WRN] [2025-05-28 14:57:43.426] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:57:43.426 +07:00 [WRN] [2025-05-28 14:57:43.426] [WARNING] [nhatrang345]: Không thể vào phòng sau 3 lần thử cho nhatrang345
2025-05-28 14:57:43.426 +07:00 [INF] [2025-05-28 14:57:43.426] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 1732.7245ms
2025-05-28 14:57:43.426 +07:00 [DBG] [2025-05-28 14:57:43.426] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:43.426 +07:00 [DBG] [2025-05-28 14:57:43.426] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:43.427 +07:00 [WRN] [2025-05-28 14:57:43.427] [WARNING] [nhatrang345]: ❌ Vào phòng thất bại cho nhatrang345, thử lại sau 700ms
2025-05-28 14:57:43.427 +07:00 [INF] [2025-05-28 14:57:43.427] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:43.427 +07:00 [DBG] [2025-05-28 14:57:43.427] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:43.427 +07:00 [DBG] [2025-05-28 14:57:43.427] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:43.427 +07:00 [INF] [2025-05-28 14:57:43.427] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:43.427 +07:00 [DBG] [2025-05-28 14:57:43.427] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:43.427 +07:00 [DBG] [2025-05-28 14:57:43.427] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:43.427 +07:00 [INF] [2025-05-28 14:57:43.427] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:43.427 +07:00 [DBG] [2025-05-28 14:57:43.427] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:43.427 +07:00 [DBG] [2025-05-28 14:57:43.427] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:43.428 +07:00 [INF] [2025-05-28 14:57:43.428] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 14:57:43.433 +07:00 [WRN] [2025-05-28 14:57:43.432] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 14:57:43.433 +07:00 [INF] [2025-05-28 14:57:43.433] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 8.8622ms
2025-05-28 14:57:43.433 +07:00 [INF] [2025-05-28 14:57:43.433] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 11.9588ms
2025-05-28 14:57:43.433 +07:00 [DBG] [2025-05-28 14:57:43.433] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:43.433 +07:00 [INF] [2025-05-28 14:57:43.433] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 14:57:43.495 +07:00 [INF] [2025-05-28 14:57:43.495] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:57:43.507 +07:00 [INF] [2025-05-28 14:57:43.507] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:43.507 +07:00 [DBG] [2025-05-28 14:57:43.507] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:43.507 +07:00 [DBG] [2025-05-28 14:57:43.507] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:43.530 +07:00 [INF] [2025-05-28 14:57:43.530] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 14:57:43.531 +07:00 [INF] [2025-05-28 14:57:43.531] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:43.531 +07:00 [DBG] [2025-05-28 14:57:43.531] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:43.531 +07:00 [DBG] [2025-05-28 14:57:43.531] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:43.531 +07:00 [INF] [2025-05-28 14:57:43.531] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:43.531 +07:00 [DBG] [2025-05-28 14:57:43.531] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:43.531 +07:00 [DBG] [2025-05-28 14:57:43.531] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:43.531 +07:00 [INF] [2025-05-28 14:57:43.531] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:43.531 +07:00 [DBG] [2025-05-28 14:57:43.531] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:43.531 +07:00 [DBG] [2025-05-28 14:57:43.531] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:43.559 +07:00 [INF] [2025-05-28 14:57:43.559] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:57:43.559 +07:00 [INF] [2025-05-28 14:57:43.559] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5797524452209473, Vị trí: (35, 51)
2025-05-28 14:57:43.604 +07:00 [INF] [2025-05-28 14:57:43.604] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:43.628 +07:00 [INF] [2025-05-28 14:57:43.628] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.12 giây
2025-05-28 14:57:44.127 +07:00 [INF] [2025-05-28 14:57:44.127] [INFO] [nhatrang345]: Thử lần 2/10 cho nhatrang345
2025-05-28 14:57:44.130 +07:00 [INF] [2025-05-28 14:57:44.130] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 14:57:44.137 +07:00 [INF] [2025-05-28 14:57:44.137] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:57:44.141 +07:00 [INF] [2025-05-28 14:57:44.141] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:57:44.146 +07:00 [INF] [2025-05-28 14:57:44.146] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:57:44.149 +07:00 [DBG] [2025-05-28 14:57:44.149] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:57:44.149
2025-05-28 14:57:44.149 +07:00 [INF] [2025-05-28 14:57:44.149] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:44.152 +07:00 [INF] [2025-05-28 14:57:44.152] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:44.211 +07:00 [DBG] [2025-05-28 14:57:44.211] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:57:44.211 +07:00 [INF] [2025-05-28 14:57:44.211] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 1, số người: 3), tự động thoát phòng
2025-05-28 14:57:44.211 +07:00 [INF] [2025-05-28 14:57:44.211] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 14:57:44.211 +07:00 [DBG] [2025-05-28 14:57:44.211] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 14:57:44.211 +07:00 [INF] [2025-05-28 14:57:44.211] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 14:57:44.211 +07:00 [INF] [2025-05-28 14:57:44.211] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:44.211 +07:00 [INF] [2025-05-28 14:57:44.211] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:44.218 +07:00 [INF] [2025-05-28 14:57:44.218] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:44.218 +07:00 [INF] [2025-05-28 14:57:44.218] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:44.218 +07:00 [ERR] [2025-05-28 14:57:44.218] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 14:57:44.218 +07:00 [INF] [2025-05-28 14:57:44.218] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 84.9374ms
2025-05-28 14:57:44.218 +07:00 [WRN] [2025-05-28 14:57:44.218] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:57:44.220 +07:00 [INF] [2025-05-28 14:57:44.220] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 14:57:44.223 +07:00 [WRN] [2025-05-28 14:57:44.223] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 14:57:44.223 +07:00 [INF] [2025-05-28 14:57:44.223] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 8.5605ms
2025-05-28 14:57:44.223 +07:00 [INF] [2025-05-28 14:57:44.223] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 12.2939ms
2025-05-28 14:57:44.223 +07:00 [DBG] [2025-05-28 14:57:44.223] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:44.223 +07:00 [INF] [2025-05-28 14:57:44.223] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 14:57:44.223 +07:00 [DBG] [2025-05-28 14:57:44.223] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:44.224 +07:00 [DBG] [2025-05-28 14:57:44.224] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:44.224 +07:00 [INF] [2025-05-28 14:57:44.224] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:44.225 +07:00 [DBG] [2025-05-28 14:57:44.225] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:44.225 +07:00 [DBG] [2025-05-28 14:57:44.225] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:44.225 +07:00 [INF] [2025-05-28 14:57:44.225] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:44.225 +07:00 [DBG] [2025-05-28 14:57:44.225] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:44.225 +07:00 [DBG] [2025-05-28 14:57:44.225] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:44.225 +07:00 [INF] [2025-05-28 14:57:44.225] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:44.225 +07:00 [DBG] [2025-05-28 14:57:44.225] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:44.225 +07:00 [DBG] [2025-05-28 14:57:44.225] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:44.268 +07:00 [INF] [2025-05-28 14:57:44.268] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:44.278 +07:00 [INF] [2025-05-28 14:57:44.278] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.05 giây
2025-05-28 14:57:44.287 +07:00 [INF] [2025-05-28 14:57:44.287] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:57:44.348 +07:00 [INF] [2025-05-28 14:57:44.348] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:57:44.348 +07:00 [INF] [2025-05-28 14:57:44.348] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5797524452209473, Vị trí: (35, 51)
2025-05-28 14:57:44.923 +07:00 [INF] [2025-05-28 14:57:44.923] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 14:57:44.931 +07:00 [INF] [2025-05-28 14:57:44.931] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:57:44.935 +07:00 [INF] [2025-05-28 14:57:44.935] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:57:44.939 +07:00 [INF] [2025-05-28 14:57:44.939] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:57:44.942 +07:00 [DBG] [2025-05-28 14:57:44.942] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:57:44.942
2025-05-28 14:57:44.942 +07:00 [INF] [2025-05-28 14:57:44.942] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:44.945 +07:00 [INF] [2025-05-28 14:57:44.945] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:45.016 +07:00 [DBG] [2025-05-28 14:57:45.016] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:57:45.016 +07:00 [INF] [2025-05-28 14:57:45.016] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 14:57:45.016 +07:00 [INF] [2025-05-28 14:57:45.016] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 14:57:45.016 +07:00 [DBG] [2025-05-28 14:57:45.016] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 14:57:45.016 +07:00 [INF] [2025-05-28 14:57:45.016] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 14:57:45.016 +07:00 [INF] [2025-05-28 14:57:45.016] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:45.016 +07:00 [INF] [2025-05-28 14:57:45.016] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:45.022 +07:00 [INF] [2025-05-28 14:57:45.022] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:45.022 +07:00 [INF] [2025-05-28 14:57:45.022] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:45.022 +07:00 [ERR] [2025-05-28 14:57:45.022] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 14:57:45.022 +07:00 [INF] [2025-05-28 14:57:45.022] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 95.927ms
2025-05-28 14:57:45.022 +07:00 [WRN] [2025-05-28 14:57:45.022] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:57:45.024 +07:00 [INF] [2025-05-28 14:57:45.024] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 14:57:45.027 +07:00 [WRN] [2025-05-28 14:57:45.027] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 14:57:45.027 +07:00 [INF] [2025-05-28 14:57:45.027] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 8.1313ms
2025-05-28 14:57:45.027 +07:00 [INF] [2025-05-28 14:57:45.027] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 11.2178ms
2025-05-28 14:57:45.027 +07:00 [DBG] [2025-05-28 14:57:45.027] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:45.027 +07:00 [INF] [2025-05-28 14:57:45.027] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 14:57:45.027 +07:00 [DBG] [2025-05-28 14:57:45.027] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:45.027 +07:00 [DBG] [2025-05-28 14:57:45.027] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:45.029 +07:00 [INF] [2025-05-28 14:57:45.029] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:45.029 +07:00 [DBG] [2025-05-28 14:57:45.029] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:45.029 +07:00 [DBG] [2025-05-28 14:57:45.029] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:45.029 +07:00 [INF] [2025-05-28 14:57:45.029] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:45.029 +07:00 [DBG] [2025-05-28 14:57:45.029] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:45.029 +07:00 [DBG] [2025-05-28 14:57:45.029] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:45.029 +07:00 [INF] [2025-05-28 14:57:45.029] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:45.029 +07:00 [DBG] [2025-05-28 14:57:45.029] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:45.029 +07:00 [DBG] [2025-05-28 14:57:45.029] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:45.072 +07:00 [INF] [2025-05-28 14:57:45.072] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:57:45.109 +07:00 [INF] [2025-05-28 14:57:45.109] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:45.135 +07:00 [INF] [2025-05-28 14:57:45.135] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 14:57:45.164 +07:00 [INF] [2025-05-28 14:57:45.164] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:57:45.164 +07:00 [INF] [2025-05-28 14:57:45.164] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 14:57:45.728 +07:00 [INF] [2025-05-28 14:57:45.728] [INFO] [nhatrang345]: Thử vào phòng lần 3/3 cho nhatrang345
2025-05-28 14:57:45.735 +07:00 [INF] [2025-05-28 14:57:45.735] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:57:45.739 +07:00 [INF] [2025-05-28 14:57:45.739] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:57:45.744 +07:00 [INF] [2025-05-28 14:57:45.744] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:57:45.746 +07:00 [DBG] [2025-05-28 14:57:45.746] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:57:45.746
2025-05-28 14:57:45.746 +07:00 [INF] [2025-05-28 14:57:45.746] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:45.750 +07:00 [INF] [2025-05-28 14:57:45.750] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:45.811 +07:00 [DBG] [2025-05-28 14:57:45.811] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:57:45.811 +07:00 [INF] [2025-05-28 14:57:45.811] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 14:57:45.811 +07:00 [INF] [2025-05-28 14:57:45.811] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 14:57:45.811 +07:00 [DBG] [2025-05-28 14:57:45.811] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 14:57:45.811 +07:00 [INF] [2025-05-28 14:57:45.811] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 14:57:45.812 +07:00 [INF] [2025-05-28 14:57:45.812] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:45.812 +07:00 [INF] [2025-05-28 14:57:45.812] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:45.818 +07:00 [INF] [2025-05-28 14:57:45.818] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:45.818 +07:00 [INF] [2025-05-28 14:57:45.818] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:45.818 +07:00 [ERR] [2025-05-28 14:57:45.818] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 14:57:45.818 +07:00 [INF] [2025-05-28 14:57:45.818] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 86.8973ms
2025-05-28 14:57:45.818 +07:00 [WRN] [2025-05-28 14:57:45.818] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:57:45.818 +07:00 [WRN] [2025-05-28 14:57:45.818] [WARNING] [nhatrang345]: Không thể vào phòng sau 3 lần thử cho nhatrang345
2025-05-28 14:57:45.818 +07:00 [INF] [2025-05-28 14:57:45.818] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 1688.0155ms
2025-05-28 14:57:45.818 +07:00 [DBG] [2025-05-28 14:57:45.818] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:45.818 +07:00 [DBG] [2025-05-28 14:57:45.818] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:45.818 +07:00 [WRN] [2025-05-28 14:57:45.818] [WARNING] [nhatrang345]: ❌ Vào phòng thất bại cho nhatrang345, thử lại sau 700ms
2025-05-28 14:57:45.818 +07:00 [INF] [2025-05-28 14:57:45.818] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:45.818 +07:00 [DBG] [2025-05-28 14:57:45.818] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:45.818 +07:00 [DBG] [2025-05-28 14:57:45.818] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:45.819 +07:00 [INF] [2025-05-28 14:57:45.819] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:45.819 +07:00 [DBG] [2025-05-28 14:57:45.819] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:45.819 +07:00 [DBG] [2025-05-28 14:57:45.819] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:45.819 +07:00 [INF] [2025-05-28 14:57:45.819] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:45.819 +07:00 [DBG] [2025-05-28 14:57:45.819] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:45.819 +07:00 [DBG] [2025-05-28 14:57:45.819] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:45.820 +07:00 [INF] [2025-05-28 14:57:45.820] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 14:57:45.823 +07:00 [WRN] [2025-05-28 14:57:45.823] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 14:57:45.823 +07:00 [INF] [2025-05-28 14:57:45.823] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 7.9082ms
2025-05-28 14:57:45.823 +07:00 [INF] [2025-05-28 14:57:45.823] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 11.3654ms
2025-05-28 14:57:45.823 +07:00 [DBG] [2025-05-28 14:57:45.823] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:45.823 +07:00 [INF] [2025-05-28 14:57:45.823] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 14:57:45.861 +07:00 [INF] [2025-05-28 14:57:45.861] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:45.861 +07:00 [DBG] [2025-05-28 14:57:45.861] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:45.861 +07:00 [DBG] [2025-05-28 14:57:45.861] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:45.865 +07:00 [INF] [2025-05-28 14:57:45.865] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:57:45.874 +07:00 [INF] [2025-05-28 14:57:45.874] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 14:57:45.874 +07:00 [INF] [2025-05-28 14:57:45.874] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:45.874 +07:00 [DBG] [2025-05-28 14:57:45.874] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:45.874 +07:00 [DBG] [2025-05-28 14:57:45.874] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:45.874 +07:00 [INF] [2025-05-28 14:57:45.874] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:45.874 +07:00 [DBG] [2025-05-28 14:57:45.874] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:45.874 +07:00 [DBG] [2025-05-28 14:57:45.874] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:45.874 +07:00 [INF] [2025-05-28 14:57:45.874] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:45.874 +07:00 [DBG] [2025-05-28 14:57:45.874] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:45.875 +07:00 [DBG] [2025-05-28 14:57:45.875] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:45.919 +07:00 [INF] [2025-05-28 14:57:45.919] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:45.929 +07:00 [INF] [2025-05-28 14:57:45.929] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 14:57:45.952 +07:00 [INF] [2025-05-28 14:57:45.952] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:57:45.952 +07:00 [INF] [2025-05-28 14:57:45.952] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 14:57:46.532 +07:00 [INF] [2025-05-28 14:57:46.532] [INFO] [nhatrang345]: Thử lần 3/10 cho nhatrang345
2025-05-28 14:57:46.535 +07:00 [INF] [2025-05-28 14:57:46.535] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 14:57:46.542 +07:00 [INF] [2025-05-28 14:57:46.542] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:57:46.546 +07:00 [INF] [2025-05-28 14:57:46.546] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:57:46.551 +07:00 [INF] [2025-05-28 14:57:46.551] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:57:46.554 +07:00 [DBG] [2025-05-28 14:57:46.554] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:57:46.554
2025-05-28 14:57:46.554 +07:00 [INF] [2025-05-28 14:57:46.554] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:46.557 +07:00 [INF] [2025-05-28 14:57:46.557] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:46.631 +07:00 [DBG] [2025-05-28 14:57:46.631] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:57:46.631 +07:00 [INF] [2025-05-28 14:57:46.631] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 14:57:46.631 +07:00 [INF] [2025-05-28 14:57:46.631] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 14:57:46.631 +07:00 [DBG] [2025-05-28 14:57:46.631] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 14:57:46.631 +07:00 [INF] [2025-05-28 14:57:46.631] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 14:57:46.631 +07:00 [INF] [2025-05-28 14:57:46.631] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:46.631 +07:00 [INF] [2025-05-28 14:57:46.631] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:46.637 +07:00 [INF] [2025-05-28 14:57:46.637] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:46.637 +07:00 [INF] [2025-05-28 14:57:46.637] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:46.637 +07:00 [ERR] [2025-05-28 14:57:46.637] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 14:57:46.637 +07:00 [INF] [2025-05-28 14:57:46.637] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 99.2499ms
2025-05-28 14:57:46.637 +07:00 [WRN] [2025-05-28 14:57:46.637] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:57:46.639 +07:00 [INF] [2025-05-28 14:57:46.639] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 14:57:46.642 +07:00 [WRN] [2025-05-28 14:57:46.642] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 14:57:46.642 +07:00 [INF] [2025-05-28 14:57:46.642] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 7.7251ms
2025-05-28 14:57:46.642 +07:00 [INF] [2025-05-28 14:57:46.642] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 10.511ms
2025-05-28 14:57:46.642 +07:00 [DBG] [2025-05-28 14:57:46.642] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:46.642 +07:00 [INF] [2025-05-28 14:57:46.642] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 14:57:46.642 +07:00 [DBG] [2025-05-28 14:57:46.642] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:46.642 +07:00 [DBG] [2025-05-28 14:57:46.642] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:46.643 +07:00 [INF] [2025-05-28 14:57:46.643] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:46.643 +07:00 [DBG] [2025-05-28 14:57:46.643] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:46.644 +07:00 [DBG] [2025-05-28 14:57:46.644] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:46.644 +07:00 [INF] [2025-05-28 14:57:46.644] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:46.644 +07:00 [DBG] [2025-05-28 14:57:46.644] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:46.644 +07:00 [DBG] [2025-05-28 14:57:46.644] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:46.644 +07:00 [INF] [2025-05-28 14:57:46.644] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:46.644 +07:00 [DBG] [2025-05-28 14:57:46.644] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:46.644 +07:00 [DBG] [2025-05-28 14:57:46.644] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:46.683 +07:00 [INF] [2025-05-28 14:57:46.683] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:57:46.736 +07:00 [INF] [2025-05-28 14:57:46.736] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:46.758 +07:00 [INF] [2025-05-28 14:57:46.758] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.12 giây
2025-05-28 14:57:46.766 +07:00 [INF] [2025-05-28 14:57:46.766] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:57:46.766 +07:00 [INF] [2025-05-28 14:57:46.766] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5797524452209473, Vị trí: (35, 51)
2025-05-28 14:57:47.205 +07:00 [INF] [2025-05-28 14:57:47.205] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 14:57:47.205 +07:00 [INF] [2025-05-28 14:57:47.205] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 14:57:47.206 +07:00 [INF] [2025-05-28 14:57:47.206] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 14:57:47.338 +07:00 [INF] [2025-05-28 14:57:47.338] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 14:57:47.346 +07:00 [INF] [2025-05-28 14:57:47.346] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:57:47.349 +07:00 [INF] [2025-05-28 14:57:47.349] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:57:47.354 +07:00 [INF] [2025-05-28 14:57:47.354] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:57:47.356 +07:00 [DBG] [2025-05-28 14:57:47.356] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:57:47.356
2025-05-28 14:57:47.356 +07:00 [INF] [2025-05-28 14:57:47.356] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:47.360 +07:00 [INF] [2025-05-28 14:57:47.360] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:47.423 +07:00 [DBG] [2025-05-28 14:57:47.423] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:57:47.423 +07:00 [INF] [2025-05-28 14:57:47.423] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 14:57:47.423 +07:00 [INF] [2025-05-28 14:57:47.423] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 14:57:47.423 +07:00 [DBG] [2025-05-28 14:57:47.423] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 14:57:47.423 +07:00 [INF] [2025-05-28 14:57:47.423] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 14:57:47.423 +07:00 [INF] [2025-05-28 14:57:47.423] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:47.423 +07:00 [INF] [2025-05-28 14:57:47.423] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:47.428 +07:00 [INF] [2025-05-28 14:57:47.428] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:47.428 +07:00 [INF] [2025-05-28 14:57:47.428] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:47.428 +07:00 [ERR] [2025-05-28 14:57:47.428] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 14:57:47.428 +07:00 [INF] [2025-05-28 14:57:47.428] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 87.0604ms
2025-05-28 14:57:47.428 +07:00 [WRN] [2025-05-28 14:57:47.428] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:57:47.430 +07:00 [INF] [2025-05-28 14:57:47.430] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 14:57:47.434 +07:00 [WRN] [2025-05-28 14:57:47.434] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 14:57:47.434 +07:00 [INF] [2025-05-28 14:57:47.434] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 8.2747ms
2025-05-28 14:57:47.434 +07:00 [INF] [2025-05-28 14:57:47.434] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 10.9264ms
2025-05-28 14:57:47.434 +07:00 [DBG] [2025-05-28 14:57:47.434] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:47.434 +07:00 [INF] [2025-05-28 14:57:47.434] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 14:57:47.434 +07:00 [DBG] [2025-05-28 14:57:47.434] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:47.434 +07:00 [DBG] [2025-05-28 14:57:47.434] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:47.435 +07:00 [INF] [2025-05-28 14:57:47.435] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:47.435 +07:00 [DBG] [2025-05-28 14:57:47.435] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:47.435 +07:00 [DBG] [2025-05-28 14:57:47.435] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:47.435 +07:00 [INF] [2025-05-28 14:57:47.435] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:47.435 +07:00 [DBG] [2025-05-28 14:57:47.435] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:47.435 +07:00 [DBG] [2025-05-28 14:57:47.435] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:47.435 +07:00 [INF] [2025-05-28 14:57:47.435] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:47.435 +07:00 [DBG] [2025-05-28 14:57:47.435] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:47.435 +07:00 [DBG] [2025-05-28 14:57:47.435] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:47.476 +07:00 [INF] [2025-05-28 14:57:47.476] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:47.478 +07:00 [INF] [2025-05-28 14:57:47.478] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:57:47.492 +07:00 [INF] [2025-05-28 14:57:47.492] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 14:57:47.565 +07:00 [INF] [2025-05-28 14:57:47.565] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:57:47.565 +07:00 [INF] [2025-05-28 14:57:47.565] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 14:57:48.143 +07:00 [INF] [2025-05-28 14:57:48.143] [INFO] [nhatrang345]: Thử vào phòng lần 3/3 cho nhatrang345
2025-05-28 14:57:48.150 +07:00 [INF] [2025-05-28 14:57:48.150] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:57:48.154 +07:00 [INF] [2025-05-28 14:57:48.154] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:57:48.158 +07:00 [INF] [2025-05-28 14:57:48.158] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:57:48.161 +07:00 [DBG] [2025-05-28 14:57:48.161] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:57:48.161
2025-05-28 14:57:48.161 +07:00 [INF] [2025-05-28 14:57:48.161] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:48.164 +07:00 [INF] [2025-05-28 14:57:48.164] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:48.259 +07:00 [DBG] [2025-05-28 14:57:48.259] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:57:48.259 +07:00 [INF] [2025-05-28 14:57:48.259] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 14:57:48.259 +07:00 [INF] [2025-05-28 14:57:48.259] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 14:57:48.259 +07:00 [DBG] [2025-05-28 14:57:48.259] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 14:57:48.259 +07:00 [INF] [2025-05-28 14:57:48.259] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 14:57:48.259 +07:00 [INF] [2025-05-28 14:57:48.259] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:48.259 +07:00 [INF] [2025-05-28 14:57:48.259] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:48.264 +07:00 [INF] [2025-05-28 14:57:48.264] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:48.265 +07:00 [INF] [2025-05-28 14:57:48.265] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:48.265 +07:00 [ERR] [2025-05-28 14:57:48.265] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 14:57:48.265 +07:00 [INF] [2025-05-28 14:57:48.265] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 119.0055ms
2025-05-28 14:57:48.265 +07:00 [WRN] [2025-05-28 14:57:48.265] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:57:48.265 +07:00 [WRN] [2025-05-28 14:57:48.265] [WARNING] [nhatrang345]: Không thể vào phòng sau 3 lần thử cho nhatrang345
2025-05-28 14:57:48.265 +07:00 [INF] [2025-05-28 14:57:48.265] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 1729.4657ms
2025-05-28 14:57:48.265 +07:00 [DBG] [2025-05-28 14:57:48.265] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:48.265 +07:00 [DBG] [2025-05-28 14:57:48.265] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:48.265 +07:00 [WRN] [2025-05-28 14:57:48.265] [WARNING] [nhatrang345]: ❌ Vào phòng thất bại cho nhatrang345, thử lại sau 700ms
2025-05-28 14:57:48.265 +07:00 [INF] [2025-05-28 14:57:48.265] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:48.265 +07:00 [DBG] [2025-05-28 14:57:48.265] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:48.266 +07:00 [DBG] [2025-05-28 14:57:48.266] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:48.266 +07:00 [INF] [2025-05-28 14:57:48.266] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:48.266 +07:00 [DBG] [2025-05-28 14:57:48.266] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:48.266 +07:00 [DBG] [2025-05-28 14:57:48.266] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:48.266 +07:00 [INF] [2025-05-28 14:57:48.266] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:48.266 +07:00 [DBG] [2025-05-28 14:57:48.266] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:48.266 +07:00 [DBG] [2025-05-28 14:57:48.266] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:48.267 +07:00 [INF] [2025-05-28 14:57:48.267] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 14:57:48.270 +07:00 [WRN] [2025-05-28 14:57:48.270] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 14:57:48.270 +07:00 [INF] [2025-05-28 14:57:48.270] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 8.4982ms
2025-05-28 14:57:48.270 +07:00 [INF] [2025-05-28 14:57:48.270] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 11.0711ms
2025-05-28 14:57:48.270 +07:00 [DBG] [2025-05-28 14:57:48.270] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:48.270 +07:00 [INF] [2025-05-28 14:57:48.270] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 14:57:48.310 +07:00 [INF] [2025-05-28 14:57:48.310] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:48.310 +07:00 [DBG] [2025-05-28 14:57:48.310] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:48.310 +07:00 [DBG] [2025-05-28 14:57:48.310] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:48.313 +07:00 [INF] [2025-05-28 14:57:48.313] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:57:48.325 +07:00 [INF] [2025-05-28 14:57:48.325] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 14:57:48.325 +07:00 [INF] [2025-05-28 14:57:48.325] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:48.325 +07:00 [DBG] [2025-05-28 14:57:48.325] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:48.326 +07:00 [DBG] [2025-05-28 14:57:48.326] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:48.326 +07:00 [INF] [2025-05-28 14:57:48.326] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:48.326 +07:00 [DBG] [2025-05-28 14:57:48.326] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:48.326 +07:00 [DBG] [2025-05-28 14:57:48.326] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:48.326 +07:00 [INF] [2025-05-28 14:57:48.326] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:48.326 +07:00 [DBG] [2025-05-28 14:57:48.326] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:48.326 +07:00 [DBG] [2025-05-28 14:57:48.326] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:48.373 +07:00 [INF] [2025-05-28 14:57:48.373] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:48.388 +07:00 [INF] [2025-05-28 14:57:48.388] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.08 giây
2025-05-28 14:57:48.401 +07:00 [INF] [2025-05-28 14:57:48.401] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:57:48.401 +07:00 [INF] [2025-05-28 14:57:48.401] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 14:57:48.979 +07:00 [INF] [2025-05-28 14:57:48.979] [INFO] [nhatrang345]: Thử lần 4/10 cho nhatrang345
2025-05-28 14:57:48.982 +07:00 [INF] [2025-05-28 14:57:48.982] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 14:57:48.989 +07:00 [INF] [2025-05-28 14:57:48.989] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:57:48.993 +07:00 [INF] [2025-05-28 14:57:48.993] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:57:48.998 +07:00 [INF] [2025-05-28 14:57:48.998] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:57:49.000 +07:00 [DBG] [2025-05-28 14:57:49.000] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:57:49.000
2025-05-28 14:57:49.000 +07:00 [INF] [2025-05-28 14:57:49.000] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:49.003 +07:00 [INF] [2025-05-28 14:57:49.003] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:49.072 +07:00 [DBG] [2025-05-28 14:57:49.072] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:57:49.072 +07:00 [INF] [2025-05-28 14:57:49.072] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 1, số người: 3), tự động thoát phòng
2025-05-28 14:57:49.072 +07:00 [INF] [2025-05-28 14:57:49.072] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 14:57:49.072 +07:00 [DBG] [2025-05-28 14:57:49.072] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 14:57:49.072 +07:00 [INF] [2025-05-28 14:57:49.072] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 14:57:49.072 +07:00 [INF] [2025-05-28 14:57:49.072] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:49.072 +07:00 [INF] [2025-05-28 14:57:49.072] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:57:49.077 +07:00 [INF] [2025-05-28 14:57:49.077] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:57:49.077 +07:00 [INF] [2025-05-28 14:57:49.077] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 14:57:49.077 +07:00 [ERR] [2025-05-28 14:57:49.077] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 14:57:49.077 +07:00 [INF] [2025-05-28 14:57:49.077] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 92.8806ms
2025-05-28 14:57:49.077 +07:00 [WRN] [2025-05-28 14:57:49.077] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:57:49.079 +07:00 [INF] [2025-05-28 14:57:49.079] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 14:57:49.083 +07:00 [WRN] [2025-05-28 14:57:49.083] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 14:57:49.083 +07:00 [INF] [2025-05-28 14:57:49.083] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 8.9114ms
2025-05-28 14:57:49.083 +07:00 [INF] [2025-05-28 14:57:49.083] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 11.6017ms
2025-05-28 14:57:49.083 +07:00 [DBG] [2025-05-28 14:57:49.083] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:49.083 +07:00 [INF] [2025-05-28 14:57:49.083] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 14:57:49.083 +07:00 [DBG] [2025-05-28 14:57:49.083] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:49.084 +07:00 [DBG] [2025-05-28 14:57:49.084] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:49.085 +07:00 [INF] [2025-05-28 14:57:49.085] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:49.085 +07:00 [DBG] [2025-05-28 14:57:49.085] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:49.085 +07:00 [DBG] [2025-05-28 14:57:49.085] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:49.085 +07:00 [INF] [2025-05-28 14:57:49.085] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:49.085 +07:00 [DBG] [2025-05-28 14:57:49.085] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:49.085 +07:00 [DBG] [2025-05-28 14:57:49.085] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:49.085 +07:00 [INF] [2025-05-28 14:57:49.085] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:49.085 +07:00 [DBG] [2025-05-28 14:57:49.085] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:49.085 +07:00 [DBG] [2025-05-28 14:57:49.085] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:49.125 +07:00 [INF] [2025-05-28 14:57:49.125] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:57:49.160 +07:00 [INF] [2025-05-28 14:57:49.160] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:49.184 +07:00 [INF] [2025-05-28 14:57:49.184] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 14:57:49.218 +07:00 [INF] [2025-05-28 14:57:49.218] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:57:49.219 +07:00 [INF] [2025-05-28 14:57:49.219] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 14:57:49.674 +07:00 [INF] [2025-05-28 14:57:49.674] [INFO] [nhatrang345]: Đã xóa trạng thái phòng cho nhatrang345
2025-05-28 14:57:49.674 +07:00 [INF] [2025-05-28 14:57:49.674] [INFO]: Đã hủy lấy bàn trống
2025-05-28 14:57:49.689 +07:00 [INF] [2025-05-28 14:57:49.689] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 14:57:49.695 +07:00 [ERR] [2025-05-28 14:57:49.695] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 14:57:49.702 +07:00 [INF] [2025-05-28 14:57:49.702] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 719.8565ms
2025-05-28 14:57:49.702 +07:00 [DBG] [2025-05-28 14:57:49.702] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:49.702 +07:00 [DBG] [2025-05-28 14:57:49.702] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:49.715 +07:00 [INF] [2025-05-28 14:57:49.715] [INFO] [nhatrang345]: Đã hủy lấy bàn trống cho nhatrang345
2025-05-28 14:57:49.715 +07:00 [INF] [2025-05-28 14:57:49.715] [INFO] [nhatrang345]: Hoàn thành xử lý cho nhatrang345, thời gian: 8024.9843ms
2025-05-28 14:57:49.715 +07:00 [WRN] [2025-05-28 14:57:49.715] [WARNING]: Đã thử hết user mà không tìm thấy bàn trống
2025-05-28 14:57:49.722 +07:00 [INF] [2025-05-28 14:57:49.722] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:49.722 +07:00 [DBG] [2025-05-28 14:57:49.722] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:49.722 +07:00 [DBG] [2025-05-28 14:57:49.722] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:49.722 +07:00 [INF] [2025-05-28 14:57:49.722] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:49.722 +07:00 [DBG] [2025-05-28 14:57:49.722] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:49.722 +07:00 [DBG] [2025-05-28 14:57:49.722] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:49.723 +07:00 [INF] [2025-05-28 14:57:49.723] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:49.723 +07:00 [DBG] [2025-05-28 14:57:49.723] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:49.723 +07:00 [DBG] [2025-05-28 14:57:49.723] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:49.763 +07:00 [INF] [2025-05-28 14:57:49.763] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:49.777 +07:00 [INF] [2025-05-28 14:57:49.777] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.08 giây
2025-05-28 14:57:50.456 +07:00 [DBG] [2025-05-28 14:57:50.456] [DEBUG] [nhatrang345]: ❌ Tắt chế độ Get Empty Table cho nhatrang345
2025-05-28 14:57:50.456 +07:00 [DBG] [2025-05-28 14:57:50.456] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:50.456 +07:00 [DBG] [2025-05-28 14:57:50.456] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:50.457 +07:00 [INF] [2025-05-28 14:57:50.457] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:50.457 +07:00 [DBG] [2025-05-28 14:57:50.457] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:50.457 +07:00 [DBG] [2025-05-28 14:57:50.457] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:50.457 +07:00 [INF] [2025-05-28 14:57:50.457] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:57:50.457 +07:00 [DBG] [2025-05-28 14:57:50.457] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:50.457 +07:00 [DBG] [2025-05-28 14:57:50.457] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:50.457 +07:00 [INF] [2025-05-28 14:57:50.457] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:50.457 +07:00 [DBG] [2025-05-28 14:57:50.457] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:50.457 +07:00 [DBG] [2025-05-28 14:57:50.457] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:50.483 +07:00 [INF] [2025-05-28 14:57:50.483] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:50.490 +07:00 [INF] [2025-05-28 14:57:50.490] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.03 giây
2025-05-28 14:57:51.558 +07:00 [INF] [2025-05-28 14:57:51.558] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 14:57:51.558 +07:00 [INF] [2025-05-28 14:57:51.558] [INFO] [phanthiet989]: Đang mở profile cho phanthiet989...
2025-05-28 14:57:51.558 +07:00 [INF] [2025-05-28 14:57:51.558] [INFO] [phanthiet989]: Bắt đầu mở profile cho phanthiet989...
2025-05-28 14:57:51.559 +07:00 [DBG] [2025-05-28 14:57:51.559] [DEBUG] [phanthiet989]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 14:57:51.568 +07:00 [DBG] [2025-05-28 14:57:51.568] [DEBUG] [phanthiet989]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 14:57:52.204 +07:00 [INF] [2025-05-28 14:57:52.204] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 14:57:52.204 +07:00 [INF] [2025-05-28 14:57:52.204] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 14:57:52.206 +07:00 [INF] [2025-05-28 14:57:52.206] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 14:57:52.751 +07:00 [DBG] [2025-05-28 14:57:52.751] [DEBUG] [phanthiet989]: Gửi POST yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/create: Thành công
2025-05-28 14:57:52.752 +07:00 [INF] [2025-05-28 14:57:52.752] [INFO] [phanthiet989]: Đã tạo profile mới cho phanthiet989 với ID: 393edc55-e67b-4bc9-9708-c57613890575. Thời gian: 1191ms
2025-05-28 14:57:53.175 +07:00 [DBG] [2025-05-28 14:57:53.175] [DEBUG] [phanthiet989]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/393edc55-e67b-4bc9-9708-c57613890575: Thành công
2025-05-28 14:57:53.175 +07:00 [INF] [2025-05-28 14:57:53.175] [INFO] [phanthiet989]: Đã mở profile cho phanthiet989 với remote debugging: 127.0.0.1:59549. Thời gian: 423ms
2025-05-28 14:57:53.175 +07:00 [DBG] [2025-05-28 14:57:53.175] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:53.175 +07:00 [DBG] [2025-05-28 14:57:53.175] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:53.175 +07:00 [INF] [2025-05-28 14:57:53.175] [INFO] [phanthiet989]: Mở profile cho phanthiet989 thành công. Thời gian: 1616ms
2025-05-28 14:57:53.175 +07:00 [DBG] [2025-05-28 14:57:53.175] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:57:53.175 +07:00 [DBG] [2025-05-28 14:57:53.175] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:57:55.186 +07:00 [INF] [2025-05-28 14:57:55.186] [INFO] [phanthiet989]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho phanthiet989 và mở URL https://web.hit.club/
2025-05-28 14:57:55.190 +07:00 [INF] [2025-05-28 14:57:55.190] [INFO] [phanthiet989]: ✅ Đã setup WebView external handler cho phanthiet989
2025-05-28 14:57:55.195 +07:00 [INF] [2025-05-28 14:57:55.195] [INFO] [phanthiet989]: ✅ Đã setup console log listener cho phanthiet989
2025-05-28 14:57:55.198 +07:00 [DBG] [2025-05-28 14:57:55.198] [DEBUG] [phanthiet989]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 14:57:55.204 +07:00 [INF] [2025-05-28 14:57:55.204] [INFO] [phanthiet989]: ✅ Đã inject C# methods cho phanthiet989
2025-05-28 14:57:55.206 +07:00 [INF] [2025-05-28 14:57:55.206] [INFO] [phanthiet989]: Vui lòng đăng nhập thủ công tại https://web.hit.club/ trong trình duyệt GPM-Login cho phanthiet989, sau đó chờ token (lần thử 1/2).
2025-05-28 14:57:55.207 +07:00 [INF] [2025-05-28 14:57:55.207] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:55.207 +07:00 [DBG] [2025-05-28 14:57:55.207] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:55.207 +07:00 [DBG] [2025-05-28 14:57:55.207] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:55.207 +07:00 [INF] [2025-05-28 14:57:55.207] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 14:57:55.207 +07:00 [DBG] [2025-05-28 14:57:55.207] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:55.207 +07:00 [DBG] [2025-05-28 14:57:55.207] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:55.207 +07:00 [INF] [2025-05-28 14:57:55.207] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:55.207 +07:00 [DBG] [2025-05-28 14:57:55.207] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:55.207 +07:00 [DBG] [2025-05-28 14:57:55.207] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:55.233 +07:00 [INF] [2025-05-28 14:57:55.233] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:55.233 +07:00 [INF] [2025-05-28 14:57:55.233] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:57:55.233 +07:00 [DBG] [2025-05-28 14:57:55.233] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:57:55.233 +07:00 [DBG] [2025-05-28 14:57:55.233] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:55.233 +07:00 [INF] [2025-05-28 14:57:55.233] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 14:57:55.233 +07:00 [DBG] [2025-05-28 14:57:55.233] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:57:55.233 +07:00 [DBG] [2025-05-28 14:57:55.233] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:55.233 +07:00 [INF] [2025-05-28 14:57:55.233] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:57:55.233 +07:00 [DBG] [2025-05-28 14:57:55.233] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:57:55.233 +07:00 [DBG] [2025-05-28 14:57:55.233] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:57:55.258 +07:00 [INF] [2025-05-28 14:57:55.258] [INFO]: Tải danh sách user thành công
2025-05-28 14:57:55.266 +07:00 [INF] [2025-05-28 14:57:55.266] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.09 giây
2025-05-28 14:57:55.273 +07:00 [INF] [2025-05-28 14:57:55.273] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.10 giây
2025-05-28 14:57:56.384 +07:00 [DBG] [2025-05-28 14:57:56.384] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:57:57.201 +07:00 [INF] [2025-05-28 14:57:57.201] [INFO] [phanthiet989]: Đã set lại kích thước profile phanthiet989 về 700x500 sau khi load
2025-05-28 14:57:57.402 +07:00 [DBG] [2025-05-28 14:57:57.402] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:57:57.655 +07:00 [INF] [2025-05-28 14:57:57.655] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 14:57:57.655 +07:00 [INF] [2025-05-28 14:57:57.655] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 14:57:57.655 +07:00 [INF] [2025-05-28 14:57:57.655] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 14:57:58.423 +07:00 [DBG] [2025-05-28 14:57:58.423] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:57:59.216 +07:00 [INF] [2025-05-28 14:57:59.216] [INFO] [phanthiet989]: ✅ Đã inject profile buttons thành công cho phanthiet989
2025-05-28 14:57:59.432 +07:00 [DBG] [2025-05-28 14:57:59.432] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:00.450 +07:00 [DBG] [2025-05-28 14:58:00.450] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:01.224 +07:00 [INF] [2025-05-28 14:58:01.224] [INFO] [phanthiet989]: Buttons đã xuất hiện, test chúng cho phanthiet989
2025-05-28 14:58:01.229 +07:00 [INF] [2025-05-28 14:58:01.229] [INFO] [phanthiet989]: 🧪 Test results cho phanthiet989: System.Collections.Generic.Dictionary`2[System.String,System.Object]
2025-05-28 14:58:01.232 +07:00 [DBG] [2025-05-28 14:58:01.232] [DEBUG] [phanthiet989]: ✅ Đã setup reload handler cho phanthiet989
2025-05-28 14:58:01.470 +07:00 [DBG] [2025-05-28 14:58:01.470] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:02.492 +07:00 [DBG] [2025-05-28 14:58:02.492] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:02.658 +07:00 [INF] [2025-05-28 14:58:02.658] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 14:58:02.658 +07:00 [INF] [2025-05-28 14:58:02.658] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 14:58:02.660 +07:00 [INF] [2025-05-28 14:58:02.660] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 14:58:03.512 +07:00 [DBG] [2025-05-28 14:58:03.512] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:04.526 +07:00 [DBG] [2025-05-28 14:58:04.526] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:05.549 +07:00 [DBG] [2025-05-28 14:58:05.549] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:06.559 +07:00 [DBG] [2025-05-28 14:58:06.559] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:07.581 +07:00 [DBG] [2025-05-28 14:58:07.581] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:07.662 +07:00 [INF] [2025-05-28 14:58:07.662] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 14:58:07.662 +07:00 [INF] [2025-05-28 14:58:07.662] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 14:58:07.662 +07:00 [INF] [2025-05-28 14:58:07.662] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 14:58:08.596 +07:00 [DBG] [2025-05-28 14:58:08.596] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:09.609 +07:00 [DBG] [2025-05-28 14:58:09.609] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:10.616 +07:00 [DBG] [2025-05-28 14:58:10.616] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:11.627 +07:00 [DBG] [2025-05-28 14:58:11.627] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:12.634 +07:00 [DBG] [2025-05-28 14:58:12.634] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:12.654 +07:00 [INF] [2025-05-28 14:58:12.654] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 14:58:12.654 +07:00 [INF] [2025-05-28 14:58:12.654] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 14:58:12.673 +07:00 [INF] [2025-05-28 14:58:12.673] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 14:58:13.656 +07:00 [DBG] [2025-05-28 14:58:13.656] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:14.673 +07:00 [DBG] [2025-05-28 14:58:14.672] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:15.688 +07:00 [DBG] [2025-05-28 14:58:15.688] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:16.700 +07:00 [DBG] [2025-05-28 14:58:16.700] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:17.654 +07:00 [INF] [2025-05-28 14:58:17.654] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 14:58:17.654 +07:00 [INF] [2025-05-28 14:58:17.654] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 14:58:17.658 +07:00 [INF] [2025-05-28 14:58:17.658] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 14:58:17.711 +07:00 [DBG] [2025-05-28 14:58:17.711] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:18.724 +07:00 [DBG] [2025-05-28 14:58:18.724] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:19.740 +07:00 [DBG] [2025-05-28 14:58:19.740] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:20.751 +07:00 [DBG] [2025-05-28 14:58:20.751] [DEBUG] [phanthiet989]: URL hiện tại cho phanthiet989: https://i.hit.club/
2025-05-28 14:58:20.754 +07:00 [INF] [2025-05-28 14:58:20.754] [INFO] [phanthiet989]: Tìm thấy token (token) cho phanthiet989: 1-b56318d71797d99fa1ab774e7e57d73b
2025-05-28 14:58:22.656 +07:00 [INF] [2025-05-28 14:58:22.656] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 14:58:22.656 +07:00 [INF] [2025-05-28 14:58:22.656] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 14:58:22.665 +07:00 [INF] [2025-05-28 14:58:22.665] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 14:58:22.880 +07:00 [DBG] [2025-05-28 14:58:22.880] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 14:58:22.880 +07:00 [DBG] [2025-05-28 14:58:22.880] [DEBUG] [phanthiet989]: Làm mới trạng thái phòng cho phanthiet989
2025-05-28 14:58:23.062 +07:00 [INF] [2025-05-28 14:58:23.062] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:58:23.062 +07:00 [INF] [2025-05-28 14:58:23.062] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 14:58:23.434 +07:00 [INF] [2025-05-28 14:58:23.434] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 14:58:23.434 +07:00 [INF] [2025-05-28 14:58:23.434] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5973695516586304, Vị trí: (358, 226)
2025-05-28 14:58:23.434 +07:00 [INF] [2025-05-28 14:58:23.434] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 14:58:23.434 +07:00 [DBG] [2025-05-28 14:58:23.434] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 14:58:23.434 +07:00 [DBG] [2025-05-28 14:58:23.434] [DEBUG] [phanthiet989]: ✅ Đặt phanthiet989 vào chế độ Get Empty Table
2025-05-28 14:58:23.437 +07:00 [INF] [2025-05-28 14:58:23.437] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 14:58:23.437 +07:00 [INF] [2025-05-28 14:58:23.437] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 14:58:23.439 +07:00 [INF] [2025-05-28 14:58:23.439] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 14:58:23.446 +07:00 [INF] [2025-05-28 14:58:23.446] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:58:23.450 +07:00 [INF] [2025-05-28 14:58:23.450] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:58:23.455 +07:00 [INF] [2025-05-28 14:58:23.455] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:58:23.457 +07:00 [DBG] [2025-05-28 14:58:23.457] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:58:23.457
2025-05-28 14:58:23.457 +07:00 [INF] [2025-05-28 14:58:23.457] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:58:23.460 +07:00 [INF] [2025-05-28 14:58:23.460] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:58:23.528 +07:00 [DBG] [2025-05-28 14:58:23.528] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:58:23.528 +07:00 [INF] [2025-05-28 14:58:23.528] [INFO] [nhatrang345]: ✅ Tìm thấy bàn ít người hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 14:58:23.528 +07:00 [DBG] [2025-05-28 14:58:23.528] [DEBUG] [nhatrang345]: ✅ Báo TaskCompletionSource thành công cho nhatrang345 (shouldAutoLeave=false)
2025-05-28 14:58:23.528 +07:00 [INF] [2025-05-28 14:58:23.528] [INFO] [nhatrang345]: Nhận được cmd: 202 cho nhatrang345 (thời gian: 86.9319ms)
2025-05-28 14:58:23.528 +07:00 [WRN] [2025-05-28 14:58:23.528] [WARNING] [nhatrang345]: Dữ liệu phòng không hợp lệ sau cmd 202 cho nhatrang345
2025-05-28 14:58:23.528 +07:00 [INF] [2025-05-28 14:58:23.528] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 86.9775ms
2025-05-28 14:58:23.528 +07:00 [WRN] [2025-05-28 14:58:23.528] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:58:23.769 +07:00 [INF] [2025-05-28 14:58:23.769] [INFO] [phanthiet989]: WebSocket initialized for phanthiet989
2025-05-28 14:58:23.769 +07:00 [DBG] [2025-05-28 14:58:23.769] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:58:23.769 +07:00 [DBG] [2025-05-28 14:58:23.769] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:58:23.769 +07:00 [INF] [2025-05-28 14:58:23.769] [INFO] [phanthiet989]: Đã mở profile cho phanthiet989
2025-05-28 14:58:23.769 +07:00 [DBG] [2025-05-28 14:58:23.769] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:58:23.769 +07:00 [DBG] [2025-05-28 14:58:23.769] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:58:23.769 +07:00 [INF] [2025-05-28 14:58:23.769] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:58:23.769 +07:00 [DBG] [2025-05-28 14:58:23.769] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:58:23.769 +07:00 [DBG] [2025-05-28 14:58:23.769] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:23.769 +07:00 [INF] [2025-05-28 14:58:23.769] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 14:58:23.769 +07:00 [DBG] [2025-05-28 14:58:23.769] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:58:23.769 +07:00 [DBG] [2025-05-28 14:58:23.769] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:23.769 +07:00 [INF] [2025-05-28 14:58:23.769] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:58:23.769 +07:00 [DBG] [2025-05-28 14:58:23.769] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:58:23.770 +07:00 [DBG] [2025-05-28 14:58:23.770] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:23.796 +07:00 [INF] [2025-05-28 14:58:23.796] [INFO]: Tải danh sách user thành công
2025-05-28 14:58:23.796 +07:00 [INF] [2025-05-28 14:58:23.796] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:58:23.796 +07:00 [DBG] [2025-05-28 14:58:23.796] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:58:23.796 +07:00 [DBG] [2025-05-28 14:58:23.796] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:23.796 +07:00 [INF] [2025-05-28 14:58:23.796] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 14:58:23.796 +07:00 [DBG] [2025-05-28 14:58:23.796] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:58:23.796 +07:00 [DBG] [2025-05-28 14:58:23.796] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:23.796 +07:00 [INF] [2025-05-28 14:58:23.796] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:58:23.796 +07:00 [DBG] [2025-05-28 14:58:23.796] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:58:23.796 +07:00 [DBG] [2025-05-28 14:58:23.796] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:23.823 +07:00 [INF] [2025-05-28 14:58:23.823] [INFO]: Tải danh sách user thành công
2025-05-28 14:58:23.830 +07:00 [INF] [2025-05-28 14:58:23.830] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 14:58:23.838 +07:00 [INF] [2025-05-28 14:58:23.838] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 14:58:24.233 +07:00 [INF] [2025-05-28 14:58:24.233] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 14:58:24.241 +07:00 [INF] [2025-05-28 14:58:24.241] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:58:24.244 +07:00 [INF] [2025-05-28 14:58:24.244] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:58:24.249 +07:00 [INF] [2025-05-28 14:58:24.249] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:58:24.252 +07:00 [DBG] [2025-05-28 14:58:24.252] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:58:24.252
2025-05-28 14:58:24.252 +07:00 [INF] [2025-05-28 14:58:24.252] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:58:24.254 +07:00 [INF] [2025-05-28 14:58:24.254] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:58:26.655 +07:00 [INF] [2025-05-28 14:58:26.655] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,1]
2025-05-28 14:58:26.655 +07:00 [INF] [2025-05-28 14:58:26.655] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,1]
2025-05-28 14:58:26.656 +07:00 [INF] [2025-05-28 14:58:26.656] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,1]
2025-05-28 14:58:27.280 +07:00 [WRN] [2025-05-28 14:58:27.280] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 14:58:27.656 +07:00 [INF] [2025-05-28 14:58:27.656] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 14:58:27.656 +07:00 [INF] [2025-05-28 14:58:27.656] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 14:58:27.656 +07:00 [INF] [2025-05-28 14:58:27.656] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 14:58:27.795 +07:00 [INF] [2025-05-28 14:58:27.795] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:58:27.799 +07:00 [INF] [2025-05-28 14:58:27.799] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:58:30.460 +07:00 [INF] [2025-05-28 14:58:30.460] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:58:30.460 +07:00 [INF] [2025-05-28 14:58:30.460] [INFO] [nhatrang345]: Nhận được cmd: 202 cho nhatrang345 (thời gian: 6223.6981ms)
2025-05-28 14:58:30.460 +07:00 [WRN] [2025-05-28 14:58:30.460] [WARNING] [nhatrang345]: Dữ liệu phòng không hợp lệ sau cmd 202 cho nhatrang345
2025-05-28 14:58:30.460 +07:00 [INF] [2025-05-28 14:58:30.460] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 6223.7209ms
2025-05-28 14:58:30.460 +07:00 [WRN] [2025-05-28 14:58:30.460] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:58:31.160 +07:00 [INF] [2025-05-28 14:58:31.160] [INFO] [nhatrang345]: Thử vào phòng lần 3/3 cho nhatrang345
2025-05-28 14:58:31.168 +07:00 [INF] [2025-05-28 14:58:31.168] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:58:31.172 +07:00 [INF] [2025-05-28 14:58:31.172] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:58:31.176 +07:00 [INF] [2025-05-28 14:58:31.176] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:58:31.178 +07:00 [DBG] [2025-05-28 14:58:31.178] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:58:31.178
2025-05-28 14:58:31.178 +07:00 [INF] [2025-05-28 14:58:31.178] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:58:31.181 +07:00 [INF] [2025-05-28 14:58:31.181] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:58:31.240 +07:00 [DBG] [2025-05-28 14:58:31.240] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 14:58:31.240 +07:00 [INF] [2025-05-28 14:58:31.240] [INFO] [nhatrang345]: ✅ Tìm thấy bàn ít người hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 14:58:31.240 +07:00 [DBG] [2025-05-28 14:58:31.240] [DEBUG] [nhatrang345]: ✅ Báo TaskCompletionSource thành công cho nhatrang345 (shouldAutoLeave=false)
2025-05-28 14:58:31.240 +07:00 [INF] [2025-05-28 14:58:31.240] [INFO] [nhatrang345]: Nhận được cmd: 202 cho nhatrang345 (thời gian: 76.8659ms)
2025-05-28 14:58:31.240 +07:00 [WRN] [2025-05-28 14:58:31.240] [WARNING] [nhatrang345]: Dữ liệu phòng không hợp lệ sau cmd 202 cho nhatrang345
2025-05-28 14:58:31.240 +07:00 [INF] [2025-05-28 14:58:31.240] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 76.8892ms
2025-05-28 14:58:31.240 +07:00 [WRN] [2025-05-28 14:58:31.240] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 14:58:31.240 +07:00 [WRN] [2025-05-28 14:58:31.240] [WARNING] [nhatrang345]: Không thể vào phòng sau 3 lần thử cho nhatrang345
2025-05-28 14:58:31.240 +07:00 [INF] [2025-05-28 14:58:31.240] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 7800.8737ms
2025-05-28 14:58:31.240 +07:00 [DBG] [2025-05-28 14:58:31.240] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:58:31.240 +07:00 [DBG] [2025-05-28 14:58:31.240] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:58:31.240 +07:00 [WRN] [2025-05-28 14:58:31.240] [WARNING] [nhatrang345]: ❌ Vào phòng thất bại cho nhatrang345, thử lại sau 700ms
2025-05-28 14:58:31.240 +07:00 [INF] [2025-05-28 14:58:31.240] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:58:31.240 +07:00 [DBG] [2025-05-28 14:58:31.240] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:58:31.241 +07:00 [DBG] [2025-05-28 14:58:31.241] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:31.241 +07:00 [INF] [2025-05-28 14:58:31.241] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 14:58:31.241 +07:00 [DBG] [2025-05-28 14:58:31.241] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:58:31.241 +07:00 [DBG] [2025-05-28 14:58:31.241] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:31.241 +07:00 [INF] [2025-05-28 14:58:31.241] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:58:31.241 +07:00 [DBG] [2025-05-28 14:58:31.241] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:58:31.241 +07:00 [DBG] [2025-05-28 14:58:31.241] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:31.313 +07:00 [INF] [2025-05-28 14:58:31.313] [INFO]: Tải danh sách user thành công
2025-05-28 14:58:31.332 +07:00 [INF] [2025-05-28 14:58:31.332] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.09 giây
2025-05-28 14:58:31.655 +07:00 [INF] [2025-05-28 14:58:31.655] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,2]
2025-05-28 14:58:31.655 +07:00 [INF] [2025-05-28 14:58:31.655] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,2]
2025-05-28 14:58:31.663 +07:00 [INF] [2025-05-28 14:58:31.663] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,2]
2025-05-28 14:58:31.943 +07:00 [INF] [2025-05-28 14:58:31.943] [INFO] [nhatrang345]: Thử lần 2/10 cho nhatrang345
2025-05-28 14:58:31.946 +07:00 [INF] [2025-05-28 14:58:31.946] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 14:58:31.953 +07:00 [INF] [2025-05-28 14:58:31.953] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 14:58:31.957 +07:00 [INF] [2025-05-28 14:58:31.957] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 14:58:31.961 +07:00 [INF] [2025-05-28 14:58:31.961] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 14:58:31.964 +07:00 [DBG] [2025-05-28 14:58:31.964] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 14:58:31.964
2025-05-28 14:58:31.964 +07:00 [INF] [2025-05-28 14:58:31.964] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 14:58:31.967 +07:00 [INF] [2025-05-28 14:58:31.967] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 14:58:32.655 +07:00 [INF] [2025-05-28 14:58:32.655] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 14:58:32.655 +07:00 [INF] [2025-05-28 14:58:32.655] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 14:58:32.658 +07:00 [INF] [2025-05-28 14:58:32.658] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 14:58:33.171 +07:00 [INF] [2025-05-28 14:58:33.171] [INFO] [nhatrang345]: Đã xóa trạng thái phòng cho nhatrang345
2025-05-28 14:58:33.171 +07:00 [INF] [2025-05-28 14:58:33.171] [INFO] [phanthiet989]: Đã xóa trạng thái phòng cho phanthiet989
2025-05-28 14:58:33.171 +07:00 [INF] [2025-05-28 14:58:33.171] [INFO]: Đã hủy lấy bàn trống
2025-05-28 14:58:33.172 +07:00 [WRN] [2025-05-28 14:58:33.172] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 14:58:33.182 +07:00 [INF] [2025-05-28 14:58:33.182] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 14:58:33.189 +07:00 [INF] [2025-05-28 14:58:33.189] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 1240.0248ms
2025-05-28 14:58:33.196 +07:00 [INF] [2025-05-28 14:58:33.196] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 14:58:33.203 +07:00 [ERR] [2025-05-28 14:58:33.203] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 14:58:33.211 +07:00 [INF] [2025-05-28 14:58:33.211] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 1265.0495ms
2025-05-28 14:58:33.211 +07:00 [DBG] [2025-05-28 14:58:33.211] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:58:33.211 +07:00 [DBG] [2025-05-28 14:58:33.211] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:58:33.220 +07:00 [INF] [2025-05-28 14:58:33.220] [INFO] [nhatrang345]: Đã hủy lấy bàn trống cho nhatrang345
2025-05-28 14:58:33.220 +07:00 [INF] [2025-05-28 14:58:33.220] [INFO] [nhatrang345]: Hoàn thành xử lý cho nhatrang345, thời gian: 9782.8452ms
2025-05-28 14:58:33.220 +07:00 [WRN] [2025-05-28 14:58:33.220] [WARNING]: Đã thử hết user mà không tìm thấy bàn trống
2025-05-28 14:58:33.226 +07:00 [INF] [2025-05-28 14:58:33.226] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:58:33.226 +07:00 [DBG] [2025-05-28 14:58:33.226] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:58:33.226 +07:00 [DBG] [2025-05-28 14:58:33.226] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:33.226 +07:00 [INF] [2025-05-28 14:58:33.226] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 14:58:33.226 +07:00 [DBG] [2025-05-28 14:58:33.226] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:58:33.226 +07:00 [DBG] [2025-05-28 14:58:33.226] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:33.226 +07:00 [INF] [2025-05-28 14:58:33.226] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:58:33.226 +07:00 [DBG] [2025-05-28 14:58:33.226] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:58:33.226 +07:00 [DBG] [2025-05-28 14:58:33.226] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:33.323 +07:00 [INF] [2025-05-28 14:58:33.323] [INFO]: Tải danh sách user thành công
2025-05-28 14:58:33.345 +07:00 [INF] [2025-05-28 14:58:33.345] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.13 giây
2025-05-28 14:58:33.901 +07:00 [DBG] [2025-05-28 14:58:33.901] [DEBUG] [nhatrang345]: ❌ Tắt chế độ Get Empty Table cho nhatrang345
2025-05-28 14:58:33.901 +07:00 [DBG] [2025-05-28 14:58:33.901] [DEBUG] [phanthiet989]: ❌ Tắt chế độ Get Empty Table cho phanthiet989
2025-05-28 14:58:33.901 +07:00 [DBG] [2025-05-28 14:58:33.901] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:58:33.901 +07:00 [DBG] [2025-05-28 14:58:33.901] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:58:33.902 +07:00 [INF] [2025-05-28 14:58:33.902] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:58:33.902 +07:00 [DBG] [2025-05-28 14:58:33.902] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:58:33.902 +07:00 [DBG] [2025-05-28 14:58:33.902] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:33.902 +07:00 [INF] [2025-05-28 14:58:33.902] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 14:58:33.902 +07:00 [DBG] [2025-05-28 14:58:33.902] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:58:33.902 +07:00 [DBG] [2025-05-28 14:58:33.902] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:33.902 +07:00 [INF] [2025-05-28 14:58:33.902] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:58:33.902 +07:00 [DBG] [2025-05-28 14:58:33.902] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:58:33.902 +07:00 [DBG] [2025-05-28 14:58:33.902] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:58:33.927 +07:00 [INF] [2025-05-28 14:58:33.927] [INFO]: Tải danh sách user thành công
2025-05-28 14:58:33.936 +07:00 [INF] [2025-05-28 14:58:33.936] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.03 giây
2025-05-28 14:58:36.207 +07:00 [INF] [2025-05-28 14:58:36.207] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 14:58:36.656 +07:00 [INF] [2025-05-28 14:58:36.656] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,3]
2025-05-28 14:58:36.656 +07:00 [INF] [2025-05-28 14:58:36.656] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,3]
2025-05-28 14:58:36.656 +07:00 [INF] [2025-05-28 14:58:36.656] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,3]
2025-05-28 14:58:37.689 +07:00 [INF] [2025-05-28 14:58:37.689] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 14:58:37.689 +07:00 [INF] [2025-05-28 14:58:37.689] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 14:58:37.690 +07:00 [INF] [2025-05-28 14:58:37.690] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 14:58:41.658 +07:00 [INF] [2025-05-28 14:58:41.658] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,4]
2025-05-28 14:58:41.658 +07:00 [INF] [2025-05-28 14:58:41.658] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,4]
2025-05-28 14:58:41.658 +07:00 [INF] [2025-05-28 14:58:41.658] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,4]
2025-05-28 14:58:42.695 +07:00 [INF] [2025-05-28 14:58:42.695] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 14:58:42.695 +07:00 [INF] [2025-05-28 14:58:42.695] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 14:58:42.712 +07:00 [INF] [2025-05-28 14:58:42.712] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 14:58:46.657 +07:00 [INF] [2025-05-28 14:58:46.657] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,5]
2025-05-28 14:58:46.657 +07:00 [INF] [2025-05-28 14:58:46.657] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,5]
2025-05-28 14:58:46.661 +07:00 [INF] [2025-05-28 14:58:46.661] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,5]
2025-05-28 14:58:47.692 +07:00 [INF] [2025-05-28 14:58:47.692] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 14:58:47.692 +07:00 [INF] [2025-05-28 14:58:47.692] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 14:58:47.695 +07:00 [INF] [2025-05-28 14:58:47.695] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 14:58:51.673 +07:00 [INF] [2025-05-28 14:58:51.673] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,6]
2025-05-28 14:58:51.673 +07:00 [INF] [2025-05-28 14:58:51.673] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,6]
2025-05-28 14:58:51.675 +07:00 [INF] [2025-05-28 14:58:51.675] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,6]
2025-05-28 14:58:52.690 +07:00 [INF] [2025-05-28 14:58:52.690] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 14:58:52.690 +07:00 [INF] [2025-05-28 14:58:52.690] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 14:58:52.695 +07:00 [INF] [2025-05-28 14:58:52.695] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 14:58:56.675 +07:00 [INF] [2025-05-28 14:58:56.675] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,7]
2025-05-28 14:58:56.675 +07:00 [INF] [2025-05-28 14:58:56.675] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,7]
2025-05-28 14:58:56.680 +07:00 [INF] [2025-05-28 14:58:56.680] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,7]
2025-05-28 14:58:57.699 +07:00 [INF] [2025-05-28 14:58:57.699] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 14:58:57.699 +07:00 [INF] [2025-05-28 14:58:57.699] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 14:58:57.699 +07:00 [INF] [2025-05-28 14:58:57.699] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 14:59:01.691 +07:00 [INF] [2025-05-28 14:59:01.691] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,8]
2025-05-28 14:59:01.691 +07:00 [INF] [2025-05-28 14:59:01.691] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,8]
2025-05-28 14:59:02.671 +07:00 [INF] [2025-05-28 14:59:02.671] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,8]
2025-05-28 14:59:02.707 +07:00 [INF] [2025-05-28 14:59:02.707] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 14:59:02.707 +07:00 [INF] [2025-05-28 14:59:02.707] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 14:59:06.263 +07:00 [INF] [2025-05-28 14:59:06.263] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 14:59:06.690 +07:00 [INF] [2025-05-28 14:59:06.690] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,9]
2025-05-28 14:59:06.690 +07:00 [INF] [2025-05-28 14:59:06.690] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,9]
2025-05-28 14:59:07.433 +07:00 [INF] [2025-05-28 14:59:07.433] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,9]
2025-05-28 14:59:07.709 +07:00 [INF] [2025-05-28 14:59:07.709] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 14:59:07.709 +07:00 [INF] [2025-05-28 14:59:07.709] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 14:59:08.021 +07:00 [INF] [2025-05-28 14:59:08.021] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 14:59:11.688 +07:00 [INF] [2025-05-28 14:59:11.688] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,10]
2025-05-28 14:59:11.689 +07:00 [INF] [2025-05-28 14:59:11.689] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,10]
2025-05-28 14:59:11.911 +07:00 [INF] [2025-05-28 14:59:11.911] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,10]
2025-05-28 14:59:12.706 +07:00 [INF] [2025-05-28 14:59:12.706] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 14:59:12.706 +07:00 [INF] [2025-05-28 14:59:12.706] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 14:59:12.713 +07:00 [INF] [2025-05-28 14:59:12.713] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 14:59:16.689 +07:00 [INF] [2025-05-28 14:59:16.689] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,11]
2025-05-28 14:59:16.689 +07:00 [INF] [2025-05-28 14:59:16.689] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,11]
2025-05-28 14:59:16.689 +07:00 [INF] [2025-05-28 14:59:16.689] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,11]
2025-05-28 14:59:50.383 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 14:59:52.960 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 14:59:52.963 +07:00 [INF] Form1 constructor started.
2025-05-28 14:59:52.980 +07:00 [DBG] [2025-05-28 14:59:52.980] [DEBUG]: Gọi InitializeComponent
2025-05-28 14:59:52.990 +07:00 [INF] [2025-05-28 14:59:52.989] [INFO]: Khởi tạo UIManager thành công
2025-05-28 14:59:52.990 +07:00 [DBG] [2025-05-28 14:59:52.990] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 14:59:52.992 +07:00 [INF] [2025-05-28 14:59:52.992] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 14:59:52.992 +07:00 [DBG] [2025-05-28 14:59:52.992] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 14:59:52.992 +07:00 [INF] [2025-05-28 14:59:52.992] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 14:59:52.994 +07:00 [INF] [2025-05-28 14:59:52.994] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 14:59:53.006 +07:00 [DBG] [2025-05-28 14:59:53.006] [DEBUG]: Bắt đầu OnLoad
2025-05-28 14:59:53.006 +07:00 [DBG] [2025-05-28 14:59:53.006] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 14:59:53.023 +07:00 [INF] [2025-05-28 14:59:53.023] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11823
2025-05-28 14:59:53.023 +07:00 [INF] [2025-05-28 14:59:53.023] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 14:59:53.038 +07:00 [INF] [2025-05-28 14:59:53.038] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 14:59:53.040 +07:00 [INF] [2025-05-28 14:59:53.040] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 14:59:53.041 +07:00 [INF] [2025-05-28 14:59:53.041] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 14:59:53.041 +07:00 [INF] [2025-05-28 14:59:53.041] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 14:59:53.041 +07:00 [INF] [2025-05-28 14:59:53.041] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 14:59:53.041 +07:00 [INF] [2025-05-28 14:59:53.041] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 14:59:53.041 +07:00 [INF] InitializeAsync started.
2025-05-28 14:59:53.041 +07:00 [INF] [2025-05-28 14:59:53.041] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 14:59:53.046 +07:00 [DBG] [2025-05-28 14:59:53.046] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 14:59:53.049 +07:00 [DBG] [2025-05-28 14:59:53.049] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 14:59:53.049 +07:00 [DBG] [2025-05-28 14:59:53.049] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:59:53.050 +07:00 [DBG] [2025-05-28 14:59:53.050] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:59:53.057 +07:00 [DBG] [2025-05-28 14:59:53.057] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 14:59:53.057 +07:00 [INF] [2025-05-28 14:59:53.057] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 14:59:53.057 +07:00 [DBG] [2025-05-28 14:59:53.057] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:59:53.058 +07:00 [DBG] [2025-05-28 14:59:53.058] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:53.058 +07:00 [DBG] [2025-05-28 14:59:53.058] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:59:53.058 +07:00 [INF] [2025-05-28 14:59:53.058] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:59:53.058 +07:00 [DBG] [2025-05-28 14:59:53.058] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:59:53.058 +07:00 [DBG] [2025-05-28 14:59:53.058] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:53.058 +07:00 [DBG] [2025-05-28 14:59:53.058] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:59:53.058 +07:00 [INF] [2025-05-28 14:59:53.058] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:59:53.058 +07:00 [DBG] [2025-05-28 14:59:53.058] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:59:53.059 +07:00 [DBG] [2025-05-28 14:59:53.059] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:53.108 +07:00 [INF] [2025-05-28 14:59:53.108] [INFO]: Tải danh sách user thành công
2025-05-28 14:59:53.121 +07:00 [INF] [2025-05-28 14:59:53.121] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 14:59:53.121 +07:00 [DBG] [2025-05-28 14:59:53.121] [DEBUG]: OnLoad hoàn tất
2025-05-28 14:59:54.421 +07:00 [INF] [2025-05-28 14:59:54.421] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 14:59:54.427 +07:00 [INF] [2025-05-28 14:59:54.427] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 14:59:54.429 +07:00 [INF] [2025-05-28 14:59:54.429] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 14:59:54.431 +07:00 [DBG] [2025-05-28 14:59:54.431] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 14:59:54.475 +07:00 [INF] [2025-05-28 14:59:54.475] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 45ms
2025-05-28 14:59:54.488 +07:00 [DBG] [2025-05-28 14:59:54.488] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 14:59:54.488 +07:00 [INF] [2025-05-28 14:59:54.488] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:59492. Thời gian: 12ms
2025-05-28 14:59:54.488 +07:00 [DBG] [2025-05-28 14:59:54.488] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:59:54.488 +07:00 [DBG] [2025-05-28 14:59:54.488] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:59:54.489 +07:00 [INF] [2025-05-28 14:59:54.489] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 59ms
2025-05-28 14:59:54.489 +07:00 [DBG] [2025-05-28 14:59:54.489] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:59:54.489 +07:00 [DBG] [2025-05-28 14:59:54.489] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:59:55.557 +07:00 [INF] [2025-05-28 14:59:55.557] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 14:59:55.691 +07:00 [INF] [2025-05-28 14:59:55.691] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 14:59:55.818 +07:00 [INF] [2025-05-28 14:59:55.818] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 14:59:55.889 +07:00 [DBG] [2025-05-28 14:59:55.889] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 14:59:55.897 +07:00 [INF] [2025-05-28 14:59:55.897] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 14:59:55.898 +07:00 [INF] [2025-05-28 14:59:55.898] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:59:55.898 +07:00 [DBG] [2025-05-28 14:59:55.898] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:59:55.898 +07:00 [DBG] [2025-05-28 14:59:55.898] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:55.898 +07:00 [INF] [2025-05-28 14:59:55.898] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:59:55.898 +07:00 [DBG] [2025-05-28 14:59:55.898] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:59:55.898 +07:00 [DBG] [2025-05-28 14:59:55.898] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:55.898 +07:00 [INF] [2025-05-28 14:59:55.898] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:59:55.898 +07:00 [DBG] [2025-05-28 14:59:55.898] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:59:55.898 +07:00 [DBG] [2025-05-28 14:59:55.898] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:55.931 +07:00 [INF] [2025-05-28 14:59:55.931] [INFO]: Tải danh sách user thành công
2025-05-28 14:59:55.931 +07:00 [INF] [2025-05-28 14:59:55.931] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:59:55.931 +07:00 [DBG] [2025-05-28 14:59:55.931] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:59:55.931 +07:00 [DBG] [2025-05-28 14:59:55.931] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:55.932 +07:00 [INF] [2025-05-28 14:59:55.932] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:59:55.932 +07:00 [DBG] [2025-05-28 14:59:55.932] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:59:55.932 +07:00 [DBG] [2025-05-28 14:59:55.932] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:55.932 +07:00 [INF] [2025-05-28 14:59:55.932] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:59:55.932 +07:00 [DBG] [2025-05-28 14:59:55.932] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:59:55.932 +07:00 [DBG] [2025-05-28 14:59:55.932] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:55.945 +07:00 [INF] [2025-05-28 14:59:55.945] [INFO] [nhatrang345]: ✅ Đã inject C# methods cho nhatrang345
2025-05-28 14:59:55.963 +07:00 [INF] [2025-05-28 14:59:55.963] [INFO]: Tải danh sách user thành công
2025-05-28 14:59:55.973 +07:00 [INF] [2025-05-28 14:59:55.973] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.48 giây
2025-05-28 14:59:55.981 +07:00 [INF] [2025-05-28 14:59:55.981] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.49 giây
2025-05-28 14:59:57.840 +07:00 [INF] [2025-05-28 14:59:57.840] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 14:59:59.491 +07:00 [INF] [2025-05-28 14:59:59.491] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 14:59:59.491 +07:00 [DBG] [2025-05-28 14:59:59.491] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:59:59.491 +07:00 [DBG] [2025-05-28 14:59:59.491] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:59:59.491 +07:00 [INF] [2025-05-28 14:59:59.491] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 14:59:59.491 +07:00 [DBG] [2025-05-28 14:59:59.491] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 14:59:59.491 +07:00 [DBG] [2025-05-28 14:59:59.491] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 14:59:59.492 +07:00 [INF] [2025-05-28 14:59:59.492] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:59:59.492 +07:00 [DBG] [2025-05-28 14:59:59.492] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:59:59.492 +07:00 [DBG] [2025-05-28 14:59:59.492] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:59.492 +07:00 [INF] [2025-05-28 14:59:59.492] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:59:59.492 +07:00 [DBG] [2025-05-28 14:59:59.492] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:59:59.492 +07:00 [DBG] [2025-05-28 14:59:59.492] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:59.492 +07:00 [INF] [2025-05-28 14:59:59.492] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:59:59.492 +07:00 [DBG] [2025-05-28 14:59:59.492] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:59:59.492 +07:00 [DBG] [2025-05-28 14:59:59.492] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:59.642 +07:00 [INF] [2025-05-28 14:59:59.642] [INFO]: Tải danh sách user thành công
2025-05-28 14:59:59.643 +07:00 [INF] [2025-05-28 14:59:59.643] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 14:59:59.643 +07:00 [DBG] [2025-05-28 14:59:59.643] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 14:59:59.643 +07:00 [DBG] [2025-05-28 14:59:59.643] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:59.643 +07:00 [INF] [2025-05-28 14:59:59.643] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 14:59:59.643 +07:00 [DBG] [2025-05-28 14:59:59.643] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 14:59:59.643 +07:00 [DBG] [2025-05-28 14:59:59.643] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:59.643 +07:00 [INF] [2025-05-28 14:59:59.643] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 14:59:59.643 +07:00 [DBG] [2025-05-28 14:59:59.643] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 14:59:59.643 +07:00 [DBG] [2025-05-28 14:59:59.643] [DEBUG]: Số cột trong hàng: 5
2025-05-28 14:59:59.703 +07:00 [INF] [2025-05-28 14:59:59.703] [INFO]: Tải danh sách user thành công
2025-05-28 14:59:59.710 +07:00 [INF] [2025-05-28 14:59:59.710] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.22 giây
2025-05-28 14:59:59.717 +07:00 [INF] [2025-05-28 14:59:59.717] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.23 giây
2025-05-28 15:00:00.579 +07:00 [INF] [2025-05-28 15:00:00.579] [INFO] [nhatrang345]: ✅ Đã inject profile buttons thành công cho nhatrang345
2025-05-28 15:00:02.584 +07:00 [INF] [2025-05-28 15:00:02.584] [INFO] [nhatrang345]: Buttons đã xuất hiện, test chúng cho nhatrang345
2025-05-28 15:00:02.590 +07:00 [INF] [2025-05-28 15:00:02.590] [INFO] [nhatrang345]: 🧪 Test results cho nhatrang345: System.Collections.Generic.Dictionary`2[System.String,System.Object]
2025-05-28 15:00:02.593 +07:00 [DBG] [2025-05-28 15:00:02.593] [DEBUG] [nhatrang345]: ✅ Đã setup reload handler cho nhatrang345
2025-05-28 15:00:03.777 +07:00 [INF] [2025-05-28 15:00:03.777] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:00:03.778 +07:00 [INF] [2025-05-28 15:00:03.778] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:00:03.787 +07:00 [INF] [2025-05-28 15:00:03.787] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:00:04.307 +07:00 [DBG] [2025-05-28 15:00:04.307] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 15:00:04.474 +07:00 [INF] [2025-05-28 15:00:04.474] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:00:04.474 +07:00 [INF] [2025-05-28 15:00:04.474] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 15:00:04.475 +07:00 [INF] [2025-05-28 15:00:04.475] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 15:00:04.475 +07:00 [DBG] [2025-05-28 15:00:04.475] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 15:00:04.478 +07:00 [INF] [2025-05-28 15:00:04.478] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 15:00:04.478 +07:00 [INF] [2025-05-28 15:00:04.478] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 15:00:04.481 +07:00 [INF] [2025-05-28 15:00:04.481] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 15:00:04.488 +07:00 [INF] [2025-05-28 15:00:04.488] [INFO] [nhatrang345]: 🔧 Thử join room bằng JavaScript cho nhatrang345, roomId: 100
2025-05-28 15:00:04.492 +07:00 [WRN] [2025-05-28 15:00:04.492] [WARNING] [nhatrang345]: ❌ Không tìm thấy JavaScript function phù hợp cho nhatrang345
2025-05-28 15:00:04.492 +07:00 [WRN] [2025-05-28 15:00:04.492] [WARNING] [nhatrang345]: JavaScript join room không thành công, fallback về click tọa độ cho nhatrang345
2025-05-28 15:00:04.497 +07:00 [INF] [2025-05-28 15:00:04.497] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:00:04.503 +07:00 [INF] [2025-05-28 15:00:04.503] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:00:04.508 +07:00 [INF] [2025-05-28 15:00:04.508] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 15:00:04.511 +07:00 [DBG] [2025-05-28 15:00:04.511] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:00:04.511
2025-05-28 15:00:04.511 +07:00 [INF] [2025-05-28 15:00:04.511] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:00:04.515 +07:00 [INF] [2025-05-28 15:00:04.515] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:00:04.590 +07:00 [DBG] [2025-05-28 15:00:04.590] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 15:00:04.592 +07:00 [INF] [2025-05-28 15:00:04.592] [INFO] [nhatrang345]: ✅ Tìm thấy bàn ít người hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 15:00:04.592 +07:00 [DBG] [2025-05-28 15:00:04.592] [DEBUG] [nhatrang345]: ✅ Báo TaskCompletionSource thành công cho nhatrang345 (shouldAutoLeave=false)
2025-05-28 15:00:04.592 +07:00 [INF] [2025-05-28 15:00:04.592] [INFO] [nhatrang345]: Nhận được cmd: 202 cho nhatrang345 (thời gian: 98.7142ms)
2025-05-28 15:00:04.592 +07:00 [WRN] [2025-05-28 15:00:04.592] [WARNING] [nhatrang345]: Dữ liệu phòng không hợp lệ sau cmd 202 cho nhatrang345
2025-05-28 15:00:04.592 +07:00 [INF] [2025-05-28 15:00:04.592] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 98.7516ms
2025-05-28 15:00:04.592 +07:00 [INF] [2025-05-28 15:00:04.592] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 107.2681ms
2025-05-28 15:00:04.592 +07:00 [WRN] [2025-05-28 15:00:04.592] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 15:00:05.302 +07:00 [INF] [2025-05-28 15:00:05.302] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 15:00:05.307 +07:00 [INF] [2025-05-28 15:00:05.307] [INFO] [nhatrang345]: 🔧 Thử join room bằng JavaScript cho nhatrang345, roomId: 100
2025-05-28 15:00:05.309 +07:00 [WRN] [2025-05-28 15:00:05.309] [WARNING] [nhatrang345]: ❌ Không tìm thấy JavaScript function phù hợp cho nhatrang345
2025-05-28 15:00:05.310 +07:00 [WRN] [2025-05-28 15:00:05.310] [WARNING] [nhatrang345]: JavaScript join room không thành công, fallback về click tọa độ cho nhatrang345
2025-05-28 15:00:05.312 +07:00 [INF] [2025-05-28 15:00:05.312] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:00:05.316 +07:00 [INF] [2025-05-28 15:00:05.316] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:00:05.321 +07:00 [INF] [2025-05-28 15:00:05.321] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 15:00:05.323 +07:00 [DBG] [2025-05-28 15:00:05.323] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:00:05.323
2025-05-28 15:00:05.323 +07:00 [INF] [2025-05-28 15:00:05.323] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:00:05.326 +07:00 [INF] [2025-05-28 15:00:05.326] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:00:05.865 +07:00 [INF] [2025-05-28 15:00:05.865] [INFO] [nhatrang345]: Đã xóa trạng thái phòng cho nhatrang345
2025-05-28 15:00:05.865 +07:00 [INF] [2025-05-28 15:00:05.865] [INFO]: Đã hủy lấy bàn trống
2025-05-28 15:00:05.865 +07:00 [WRN] [2025-05-28 15:00:05.865] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 15:00:05.880 +07:00 [INF] [2025-05-28 15:00:05.880] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 15:00:05.887 +07:00 [INF] [2025-05-28 15:00:05.887] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 577.9656ms
2025-05-28 15:00:05.897 +07:00 [ERR] [2025-05-28 15:00:05.897] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 15:00:05.905 +07:00 [INF] [2025-05-28 15:00:05.905] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 600.8774ms
2025-05-28 15:00:05.915 +07:00 [INF] [2025-05-28 15:00:05.915] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 15:00:05.923 +07:00 [ERR] [2025-05-28 15:00:05.923] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 15:00:05.931 +07:00 [INF] [2025-05-28 15:00:05.931] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 1449.4366ms
2025-05-28 15:00:05.931 +07:00 [DBG] [2025-05-28 15:00:05.931] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:00:05.931 +07:00 [DBG] [2025-05-28 15:00:05.931] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:00:05.943 +07:00 [INF] [2025-05-28 15:00:05.943] [INFO] [nhatrang345]: Đã hủy lấy bàn trống cho nhatrang345
2025-05-28 15:00:05.944 +07:00 [INF] [2025-05-28 15:00:05.944] [INFO] [nhatrang345]: Hoàn thành xử lý cho nhatrang345, thời gian: 1465.2637ms
2025-05-28 15:00:05.944 +07:00 [WRN] [2025-05-28 15:00:05.944] [WARNING]: Đã thử hết user mà không tìm thấy bàn trống
2025-05-28 15:00:05.951 +07:00 [INF] [2025-05-28 15:00:05.951] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:00:05.951 +07:00 [DBG] [2025-05-28 15:00:05.951] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:00:05.951 +07:00 [DBG] [2025-05-28 15:00:05.951] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:05.951 +07:00 [INF] [2025-05-28 15:00:05.951] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:00:05.951 +07:00 [DBG] [2025-05-28 15:00:05.951] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:00:05.951 +07:00 [DBG] [2025-05-28 15:00:05.951] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:05.951 +07:00 [INF] [2025-05-28 15:00:05.951] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:00:05.951 +07:00 [DBG] [2025-05-28 15:00:05.951] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:00:05.951 +07:00 [DBG] [2025-05-28 15:00:05.951] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:06.016 +07:00 [INF] [2025-05-28 15:00:06.016] [INFO]: Tải danh sách user thành công
2025-05-28 15:00:06.046 +07:00 [INF] [2025-05-28 15:00:06.046] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 15:00:06.870 +07:00 [DBG] [2025-05-28 15:00:06.870] [DEBUG] [nhatrang345]: ❌ Tắt chế độ Get Empty Table cho nhatrang345
2025-05-28 15:00:06.870 +07:00 [DBG] [2025-05-28 15:00:06.870] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:00:06.870 +07:00 [DBG] [2025-05-28 15:00:06.870] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:00:06.871 +07:00 [INF] [2025-05-28 15:00:06.871] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:00:06.871 +07:00 [DBG] [2025-05-28 15:00:06.871] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:00:06.871 +07:00 [DBG] [2025-05-28 15:00:06.871] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:06.871 +07:00 [INF] [2025-05-28 15:00:06.871] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:00:06.871 +07:00 [DBG] [2025-05-28 15:00:06.871] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:00:06.871 +07:00 [DBG] [2025-05-28 15:00:06.871] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:06.871 +07:00 [INF] [2025-05-28 15:00:06.871] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:00:06.871 +07:00 [DBG] [2025-05-28 15:00:06.871] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:00:06.871 +07:00 [DBG] [2025-05-28 15:00:06.871] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:06.915 +07:00 [INF] [2025-05-28 15:00:06.915] [INFO]: Tải danh sách user thành công
2025-05-28 15:00:06.931 +07:00 [INF] [2025-05-28 15:00:06.931] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 15:00:07.831 +07:00 [INF] [2025-05-28 15:00:07.831] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 15:00:08.533 +07:00 [DBG] [2025-05-28 15:00:08.533] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 15:00:08.690 +07:00 [INF] [2025-05-28 15:00:08.690] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:00:08.690 +07:00 [INF] [2025-05-28 15:00:08.690] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 15:00:08.690 +07:00 [INF] [2025-05-28 15:00:08.690] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 15:00:08.690 +07:00 [DBG] [2025-05-28 15:00:08.690] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 15:00:08.693 +07:00 [INF] [2025-05-28 15:00:08.693] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 15:00:08.693 +07:00 [INF] [2025-05-28 15:00:08.693] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 15:00:08.695 +07:00 [INF] [2025-05-28 15:00:08.695] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 15:00:08.700 +07:00 [INF] [2025-05-28 15:00:08.700] [INFO] [nhatrang345]: 🔧 Thử join room bằng JavaScript cho nhatrang345, roomId: 100
2025-05-28 15:00:08.703 +07:00 [WRN] [2025-05-28 15:00:08.703] [WARNING] [nhatrang345]: ❌ Không tìm thấy JavaScript function phù hợp cho nhatrang345
2025-05-28 15:00:08.703 +07:00 [WRN] [2025-05-28 15:00:08.703] [WARNING] [nhatrang345]: JavaScript join room không thành công, fallback về click tọa độ cho nhatrang345
2025-05-28 15:00:08.705 +07:00 [INF] [2025-05-28 15:00:08.705] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:00:08.709 +07:00 [INF] [2025-05-28 15:00:08.709] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:00:08.713 +07:00 [INF] [2025-05-28 15:00:08.713] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 15:00:08.716 +07:00 [DBG] [2025-05-28 15:00:08.716] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:00:08.716
2025-05-28 15:00:08.716 +07:00 [INF] [2025-05-28 15:00:08.716] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:00:08.719 +07:00 [INF] [2025-05-28 15:00:08.719] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:00:08.790 +07:00 [DBG] [2025-05-28 15:00:08.790] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 15:00:08.790 +07:00 [INF] [2025-05-28 15:00:08.790] [INFO] [nhatrang345]: ✅ Tìm thấy bàn ít người hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 15:00:08.790 +07:00 [DBG] [2025-05-28 15:00:08.790] [DEBUG] [nhatrang345]: ✅ Báo TaskCompletionSource thành công cho nhatrang345 (shouldAutoLeave=false)
2025-05-28 15:00:08.790 +07:00 [INF] [2025-05-28 15:00:08.790] [INFO] [nhatrang345]: Nhận được cmd: 202 cho nhatrang345 (thời gian: 87.2986ms)
2025-05-28 15:00:08.790 +07:00 [WRN] [2025-05-28 15:00:08.790] [WARNING] [nhatrang345]: Dữ liệu phòng không hợp lệ sau cmd 202 cho nhatrang345
2025-05-28 15:00:08.790 +07:00 [INF] [2025-05-28 15:00:08.790] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 87.3255ms
2025-05-28 15:00:08.790 +07:00 [INF] [2025-05-28 15:00:08.790] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 92.5676ms
2025-05-28 15:00:08.790 +07:00 [WRN] [2025-05-28 15:00:08.790] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 15:00:08.793 +07:00 [INF] [2025-05-28 15:00:08.793] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 15:00:08.793 +07:00 [INF] [2025-05-28 15:00:08.793] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 15:00:08.794 +07:00 [INF] [2025-05-28 15:00:08.794] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 15:00:09.497 +07:00 [INF] [2025-05-28 15:00:09.497] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 15:00:09.502 +07:00 [INF] [2025-05-28 15:00:09.502] [INFO] [nhatrang345]: 🔧 Thử join room bằng JavaScript cho nhatrang345, roomId: 100
2025-05-28 15:00:09.504 +07:00 [WRN] [2025-05-28 15:00:09.504] [WARNING] [nhatrang345]: ❌ Không tìm thấy JavaScript function phù hợp cho nhatrang345
2025-05-28 15:00:09.504 +07:00 [WRN] [2025-05-28 15:00:09.504] [WARNING] [nhatrang345]: JavaScript join room không thành công, fallback về click tọa độ cho nhatrang345
2025-05-28 15:00:09.507 +07:00 [INF] [2025-05-28 15:00:09.507] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:00:09.511 +07:00 [INF] [2025-05-28 15:00:09.511] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:00:09.515 +07:00 [INF] [2025-05-28 15:00:09.515] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 15:00:09.518 +07:00 [DBG] [2025-05-28 15:00:09.518] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:00:09.518
2025-05-28 15:00:09.518 +07:00 [INF] [2025-05-28 15:00:09.518] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:00:09.521 +07:00 [INF] [2025-05-28 15:00:09.521] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:00:10.414 +07:00 [INF] [2025-05-28 15:00:10.414] [INFO] [nhatrang345]: Đã xóa trạng thái phòng cho nhatrang345
2025-05-28 15:00:10.414 +07:00 [INF] [2025-05-28 15:00:10.414] [INFO]: Đã hủy lấy bàn trống
2025-05-28 15:00:10.414 +07:00 [WRN] [2025-05-28 15:00:10.414] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 15:00:10.422 +07:00 [INF] [2025-05-28 15:00:10.422] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 15:00:10.429 +07:00 [INF] [2025-05-28 15:00:10.429] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 924.881ms
2025-05-28 15:00:10.437 +07:00 [ERR] [2025-05-28 15:00:10.437] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 15:00:10.444 +07:00 [INF] [2025-05-28 15:00:10.444] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 944.8684ms
2025-05-28 15:00:10.453 +07:00 [INF] [2025-05-28 15:00:10.453] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 15:00:10.461 +07:00 [ERR] [2025-05-28 15:00:10.461] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 15:00:10.469 +07:00 [INF] [2025-05-28 15:00:10.469] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 1774.1149ms
2025-05-28 15:00:10.469 +07:00 [DBG] [2025-05-28 15:00:10.469] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:00:10.469 +07:00 [DBG] [2025-05-28 15:00:10.469] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:00:10.478 +07:00 [INF] [2025-05-28 15:00:10.478] [INFO] [nhatrang345]: Đã hủy lấy bàn trống cho nhatrang345
2025-05-28 15:00:10.478 +07:00 [INF] [2025-05-28 15:00:10.478] [INFO] [nhatrang345]: Hoàn thành xử lý cho nhatrang345, thời gian: 1785.6808ms
2025-05-28 15:00:10.478 +07:00 [WRN] [2025-05-28 15:00:10.478] [WARNING]: Đã thử hết user mà không tìm thấy bàn trống
2025-05-28 15:00:10.484 +07:00 [INF] [2025-05-28 15:00:10.484] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:00:10.484 +07:00 [DBG] [2025-05-28 15:00:10.484] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:00:10.484 +07:00 [DBG] [2025-05-28 15:00:10.484] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:10.484 +07:00 [INF] [2025-05-28 15:00:10.484] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:00:10.484 +07:00 [DBG] [2025-05-28 15:00:10.484] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:00:10.484 +07:00 [DBG] [2025-05-28 15:00:10.484] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:10.484 +07:00 [INF] [2025-05-28 15:00:10.484] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:00:10.484 +07:00 [DBG] [2025-05-28 15:00:10.484] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:00:10.485 +07:00 [DBG] [2025-05-28 15:00:10.485] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:10.566 +07:00 [INF] [2025-05-28 15:00:10.566] [INFO]: Tải danh sách user thành công
2025-05-28 15:00:10.594 +07:00 [INF] [2025-05-28 15:00:10.594] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.12 giây
2025-05-28 15:00:11.482 +07:00 [DBG] [2025-05-28 15:00:11.482] [DEBUG] [nhatrang345]: ❌ Tắt chế độ Get Empty Table cho nhatrang345
2025-05-28 15:00:11.483 +07:00 [DBG] [2025-05-28 15:00:11.483] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:00:11.483 +07:00 [DBG] [2025-05-28 15:00:11.483] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:00:11.483 +07:00 [INF] [2025-05-28 15:00:11.483] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:00:11.483 +07:00 [DBG] [2025-05-28 15:00:11.483] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:00:11.483 +07:00 [DBG] [2025-05-28 15:00:11.483] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:11.483 +07:00 [INF] [2025-05-28 15:00:11.483] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:00:11.483 +07:00 [DBG] [2025-05-28 15:00:11.483] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:00:11.483 +07:00 [DBG] [2025-05-28 15:00:11.483] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:11.483 +07:00 [INF] [2025-05-28 15:00:11.483] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:00:11.483 +07:00 [DBG] [2025-05-28 15:00:11.483] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:00:11.483 +07:00 [DBG] [2025-05-28 15:00:11.483] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:11.573 +07:00 [INF] [2025-05-28 15:00:11.573] [INFO]: Tải danh sách user thành công
2025-05-28 15:00:11.599 +07:00 [INF] [2025-05-28 15:00:11.599] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.12 giây
2025-05-28 15:00:13.798 +07:00 [INF] [2025-05-28 15:00:13.798] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 15:00:13.798 +07:00 [INF] [2025-05-28 15:00:13.798] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 15:00:13.798 +07:00 [INF] [2025-05-28 15:00:13.798] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 15:00:15.018 +07:00 [INF] [2025-05-28 15:00:15.018] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 15:00:15.804 +07:00 [DBG] [2025-05-28 15:00:15.804] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 15:00:16.006 +07:00 [INF] [2025-05-28 15:00:16.006] [INFO]: Kích thước ảnh chụp: 1566x821, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:00:16.006 +07:00 [INF] [2025-05-28 15:00:16.006] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.6270249485969543, Vị trí: (702, 516)
2025-05-28 15:00:16.007 +07:00 [INF] [2025-05-28 15:00:16.007] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 15:00:16.007 +07:00 [DBG] [2025-05-28 15:00:16.007] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 15:00:16.010 +07:00 [INF] [2025-05-28 15:00:16.010] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 15:00:16.010 +07:00 [INF] [2025-05-28 15:00:16.010] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 15:00:16.013 +07:00 [INF] [2025-05-28 15:00:16.013] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 15:00:16.017 +07:00 [INF] [2025-05-28 15:00:16.017] [INFO] [nhatrang345]: 🔧 Thử join room bằng JavaScript cho nhatrang345, roomId: 100
2025-05-28 15:00:16.020 +07:00 [WRN] [2025-05-28 15:00:16.020] [WARNING] [nhatrang345]: ❌ Không tìm thấy JavaScript function phù hợp cho nhatrang345
2025-05-28 15:00:16.020 +07:00 [WRN] [2025-05-28 15:00:16.020] [WARNING] [nhatrang345]: JavaScript join room không thành công, fallback về click tọa độ cho nhatrang345
2025-05-28 15:00:16.022 +07:00 [INF] [2025-05-28 15:00:16.022] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:00:16.026 +07:00 [INF] [2025-05-28 15:00:16.026] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:00:16.031 +07:00 [INF] [2025-05-28 15:00:16.031] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 1566x821
2025-05-28 15:00:16.033 +07:00 [DBG] [2025-05-28 15:00:16.033] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:00:16.033
2025-05-28 15:00:16.033 +07:00 [INF] [2025-05-28 15:00:16.033] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:00:16.036 +07:00 [INF] [2025-05-28 15:00:16.036] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:00:17.200 +07:00 [INF] [2025-05-28 15:00:17.200] [INFO] [nhatrang345]: Đã xóa trạng thái phòng cho nhatrang345
2025-05-28 15:00:17.200 +07:00 [INF] [2025-05-28 15:00:17.200] [INFO]: Đã hủy lấy bàn trống
2025-05-28 15:00:17.200 +07:00 [WRN] [2025-05-28 15:00:17.200] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 15:00:17.209 +07:00 [INF] [2025-05-28 15:00:17.209] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 15:00:17.215 +07:00 [INF] [2025-05-28 15:00:17.215] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 1195.3909ms
2025-05-28 15:00:17.223 +07:00 [ERR] [2025-05-28 15:00:17.223] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 15:00:17.230 +07:00 [INF] [2025-05-28 15:00:17.230] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 1215.5952ms
2025-05-28 15:00:17.239 +07:00 [INF] [2025-05-28 15:00:17.239] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 15:00:17.247 +07:00 [ERR] [2025-05-28 15:00:17.247] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 15:00:17.255 +07:00 [INF] [2025-05-28 15:00:17.255] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 1242.2611ms
2025-05-28 15:00:17.255 +07:00 [DBG] [2025-05-28 15:00:17.255] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:00:17.255 +07:00 [DBG] [2025-05-28 15:00:17.255] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:00:17.264 +07:00 [INF] [2025-05-28 15:00:17.264] [INFO] [nhatrang345]: Đã hủy lấy bàn trống cho nhatrang345
2025-05-28 15:00:17.264 +07:00 [INF] [2025-05-28 15:00:17.264] [INFO] [nhatrang345]: Hoàn thành xử lý cho nhatrang345, thời gian: 1254.3276ms
2025-05-28 15:00:17.264 +07:00 [WRN] [2025-05-28 15:00:17.264] [WARNING]: Đã thử hết user mà không tìm thấy bàn trống
2025-05-28 15:00:17.270 +07:00 [INF] [2025-05-28 15:00:17.270] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:00:17.270 +07:00 [DBG] [2025-05-28 15:00:17.270] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:00:17.270 +07:00 [DBG] [2025-05-28 15:00:17.270] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:17.270 +07:00 [INF] [2025-05-28 15:00:17.270] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:00:17.270 +07:00 [DBG] [2025-05-28 15:00:17.270] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:00:17.270 +07:00 [DBG] [2025-05-28 15:00:17.270] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:17.270 +07:00 [INF] [2025-05-28 15:00:17.270] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:00:17.270 +07:00 [DBG] [2025-05-28 15:00:17.270] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:00:17.270 +07:00 [DBG] [2025-05-28 15:00:17.270] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:17.295 +07:00 [INF] [2025-05-28 15:00:17.295] [INFO]: Tải danh sách user thành công
2025-05-28 15:00:17.303 +07:00 [INF] [2025-05-28 15:00:17.303] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.05 giây
2025-05-28 15:00:18.244 +07:00 [DBG] [2025-05-28 15:00:18.244] [DEBUG] [nhatrang345]: ❌ Tắt chế độ Get Empty Table cho nhatrang345
2025-05-28 15:00:18.244 +07:00 [DBG] [2025-05-28 15:00:18.244] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:00:18.244 +07:00 [DBG] [2025-05-28 15:00:18.244] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:00:18.245 +07:00 [INF] [2025-05-28 15:00:18.245] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:00:18.245 +07:00 [DBG] [2025-05-28 15:00:18.245] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:00:18.245 +07:00 [DBG] [2025-05-28 15:00:18.245] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:18.245 +07:00 [INF] [2025-05-28 15:00:18.245] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:00:18.245 +07:00 [DBG] [2025-05-28 15:00:18.245] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:00:18.245 +07:00 [DBG] [2025-05-28 15:00:18.245] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:18.245 +07:00 [INF] [2025-05-28 15:00:18.245] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:00:18.245 +07:00 [DBG] [2025-05-28 15:00:18.245] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:00:18.245 +07:00 [DBG] [2025-05-28 15:00:18.245] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:18.270 +07:00 [INF] [2025-05-28 15:00:18.270] [INFO]: Tải danh sách user thành công
2025-05-28 15:00:18.277 +07:00 [INF] [2025-05-28 15:00:18.277] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.03 giây
2025-05-28 15:00:18.782 +07:00 [DBG] [2025-05-28 15:00:18.782] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 15:00:18.794 +07:00 [INF] [2025-05-28 15:00:18.794] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 15:00:18.794 +07:00 [INF] [2025-05-28 15:00:18.794] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 15:00:18.795 +07:00 [INF] [2025-05-28 15:00:18.795] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 15:00:19.039 +07:00 [INF] [2025-05-28 15:00:19.039] [INFO]: Kích thước ảnh chụp: 1566x821, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:00:19.040 +07:00 [INF] [2025-05-28 15:00:19.040] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.6270249485969543, Vị trí: (702, 516)
2025-05-28 15:00:19.040 +07:00 [INF] [2025-05-28 15:00:19.040] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 15:00:19.040 +07:00 [DBG] [2025-05-28 15:00:19.040] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 15:00:19.043 +07:00 [INF] [2025-05-28 15:00:19.043] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 15:00:19.043 +07:00 [INF] [2025-05-28 15:00:19.043] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 15:00:19.045 +07:00 [INF] [2025-05-28 15:00:19.045] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 15:00:19.050 +07:00 [INF] [2025-05-28 15:00:19.050] [INFO] [nhatrang345]: 🔧 Thử join room bằng JavaScript cho nhatrang345, roomId: 100
2025-05-28 15:00:19.053 +07:00 [WRN] [2025-05-28 15:00:19.053] [WARNING] [nhatrang345]: ❌ Không tìm thấy JavaScript function phù hợp cho nhatrang345
2025-05-28 15:00:19.053 +07:00 [WRN] [2025-05-28 15:00:19.053] [WARNING] [nhatrang345]: JavaScript join room không thành công, fallback về click tọa độ cho nhatrang345
2025-05-28 15:00:19.055 +07:00 [INF] [2025-05-28 15:00:19.055] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:00:19.059 +07:00 [INF] [2025-05-28 15:00:19.059] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:00:19.064 +07:00 [INF] [2025-05-28 15:00:19.064] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 1566x821
2025-05-28 15:00:19.066 +07:00 [DBG] [2025-05-28 15:00:19.066] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:00:19.066
2025-05-28 15:00:19.066 +07:00 [INF] [2025-05-28 15:00:19.066] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:00:19.069 +07:00 [INF] [2025-05-28 15:00:19.069] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:00:19.824 +07:00 [INF] [2025-05-28 15:00:19.824] [INFO] [nhatrang345]: Đã xóa trạng thái phòng cho nhatrang345
2025-05-28 15:00:19.824 +07:00 [INF] [2025-05-28 15:00:19.824] [INFO]: Đã hủy lấy bàn trống
2025-05-28 15:00:19.824 +07:00 [WRN] [2025-05-28 15:00:19.824] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 15:00:19.831 +07:00 [INF] [2025-05-28 15:00:19.831] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 15:00:19.838 +07:00 [INF] [2025-05-28 15:00:19.838] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 785.1887ms
2025-05-28 15:00:19.845 +07:00 [ERR] [2025-05-28 15:00:19.845] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 15:00:19.853 +07:00 [INF] [2025-05-28 15:00:19.853] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 805.1528ms
2025-05-28 15:00:19.861 +07:00 [INF] [2025-05-28 15:00:19.861] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 15:00:19.869 +07:00 [ERR] [2025-05-28 15:00:19.869] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 15:00:19.878 +07:00 [INF] [2025-05-28 15:00:19.878] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 832.2462ms
2025-05-28 15:00:19.878 +07:00 [DBG] [2025-05-28 15:00:19.878] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:00:19.878 +07:00 [DBG] [2025-05-28 15:00:19.878] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:00:19.887 +07:00 [INF] [2025-05-28 15:00:19.887] [INFO] [nhatrang345]: Đã hủy lấy bàn trống cho nhatrang345
2025-05-28 15:00:19.887 +07:00 [INF] [2025-05-28 15:00:19.887] [INFO] [nhatrang345]: Hoàn thành xử lý cho nhatrang345, thời gian: 843.8132ms
2025-05-28 15:00:19.887 +07:00 [WRN] [2025-05-28 15:00:19.887] [WARNING]: Đã thử hết user mà không tìm thấy bàn trống
2025-05-28 15:00:19.892 +07:00 [INF] [2025-05-28 15:00:19.892] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:00:19.892 +07:00 [DBG] [2025-05-28 15:00:19.892] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:00:19.892 +07:00 [DBG] [2025-05-28 15:00:19.892] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:19.892 +07:00 [INF] [2025-05-28 15:00:19.892] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:00:19.892 +07:00 [DBG] [2025-05-28 15:00:19.892] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:00:19.892 +07:00 [DBG] [2025-05-28 15:00:19.892] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:19.892 +07:00 [INF] [2025-05-28 15:00:19.892] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:00:19.892 +07:00 [DBG] [2025-05-28 15:00:19.892] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:00:19.892 +07:00 [DBG] [2025-05-28 15:00:19.892] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:19.924 +07:00 [INF] [2025-05-28 15:00:19.924] [INFO]: Tải danh sách user thành công
2025-05-28 15:00:19.932 +07:00 [INF] [2025-05-28 15:00:19.932] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.05 giây
2025-05-28 15:00:20.634 +07:00 [DBG] [2025-05-28 15:00:20.634] [DEBUG] [nhatrang345]: ❌ Tắt chế độ Get Empty Table cho nhatrang345
2025-05-28 15:00:20.634 +07:00 [DBG] [2025-05-28 15:00:20.634] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:00:20.634 +07:00 [DBG] [2025-05-28 15:00:20.634] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:00:20.634 +07:00 [INF] [2025-05-28 15:00:20.634] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:00:20.635 +07:00 [DBG] [2025-05-28 15:00:20.635] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:00:20.635 +07:00 [DBG] [2025-05-28 15:00:20.635] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:20.635 +07:00 [INF] [2025-05-28 15:00:20.635] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:00:20.635 +07:00 [DBG] [2025-05-28 15:00:20.635] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:00:20.635 +07:00 [DBG] [2025-05-28 15:00:20.635] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:20.635 +07:00 [INF] [2025-05-28 15:00:20.635] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:00:20.635 +07:00 [DBG] [2025-05-28 15:00:20.635] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:00:20.635 +07:00 [DBG] [2025-05-28 15:00:20.635] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:00:20.673 +07:00 [INF] [2025-05-28 15:00:20.673] [INFO]: Tải danh sách user thành công
2025-05-28 15:00:20.681 +07:00 [INF] [2025-05-28 15:00:20.681] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.05 giây
2025-05-28 15:03:05.166 +07:00 [INF] [2025-05-28 15:03:05.166] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 15:03:05.166 +07:00 [INF] [2025-05-28 15:03:05.166] [INFO] [nhatrang345]: Đang đóng profile cho nhatrang345...
2025-05-28 15:03:05.184 +07:00 [DBG] [2025-05-28 15:03:05.184] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/close/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 15:03:05.185 +07:00 [INF] [2025-05-28 15:03:05.185] [INFO] [nhatrang345]: Đã đóng profile cho nhatrang345. Thời gian: 17ms
2025-05-28 15:03:05.185 +07:00 [DBG] [2025-05-28 15:03:05.185] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:03:05.185 +07:00 [DBG] [2025-05-28 15:03:05.185] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:03:05.199 +07:00 [INF] [2025-05-28 15:03:05.199] [INFO] [nhatrang345]: Đã đóng ChromeDriver cho nhatrang345
2025-05-28 15:03:05.199 +07:00 [INF] [2025-05-28 15:03:05.199] [INFO] [nhatrang345]: Đã đóng driver và xóa trạng thái cho nhatrang345
2025-05-28 15:03:05.199 +07:00 [DBG] [2025-05-28 15:03:05.199] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:03:05.199 +07:00 [DBG] [2025-05-28 15:03:05.199] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:03:05.199 +07:00 [INF] [2025-05-28 15:03:05.199] [INFO] [nhatrang345]: Đã đóng profile cho nhatrang345
2025-05-28 15:03:05.199 +07:00 [DBG] [2025-05-28 15:03:05.199] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:03:05.199 +07:00 [DBG] [2025-05-28 15:03:05.199] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:03:05.200 +07:00 [INF] [2025-05-28 15:03:05.200] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:03:05.200 +07:00 [DBG] [2025-05-28 15:03:05.200] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:03:05.200 +07:00 [DBG] [2025-05-28 15:03:05.200] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:03:05.200 +07:00 [INF] [2025-05-28 15:03:05.200] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:03:05.200 +07:00 [DBG] [2025-05-28 15:03:05.200] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:03:05.200 +07:00 [DBG] [2025-05-28 15:03:05.200] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:03:05.200 +07:00 [INF] [2025-05-28 15:03:05.200] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:03:05.200 +07:00 [DBG] [2025-05-28 15:03:05.200] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:03:05.200 +07:00 [DBG] [2025-05-28 15:03:05.200] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:03:05.277 +07:00 [INF] [2025-05-28 15:03:05.277] [INFO]: Tải danh sách user thành công
2025-05-28 15:03:05.277 +07:00 [INF] [2025-05-28 15:03:05.277] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:03:05.277 +07:00 [DBG] [2025-05-28 15:03:05.277] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:03:05.278 +07:00 [DBG] [2025-05-28 15:03:05.278] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:03:05.278 +07:00 [INF] [2025-05-28 15:03:05.278] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:03:05.278 +07:00 [DBG] [2025-05-28 15:03:05.278] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:03:05.278 +07:00 [DBG] [2025-05-28 15:03:05.278] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:03:05.278 +07:00 [INF] [2025-05-28 15:03:05.278] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:03:05.278 +07:00 [DBG] [2025-05-28 15:03:05.278] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:03:05.278 +07:00 [DBG] [2025-05-28 15:03:05.278] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:03:05.347 +07:00 [INF] [2025-05-28 15:03:05.347] [INFO]: Tải danh sách user thành công
2025-05-28 15:03:05.347 +07:00 [INF] [2025-05-28 15:03:05.347] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:03:05.347 +07:00 [DBG] [2025-05-28 15:03:05.347] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:03:05.347 +07:00 [DBG] [2025-05-28 15:03:05.347] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:03:05.347 +07:00 [INF] [2025-05-28 15:03:05.347] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:03:05.347 +07:00 [DBG] [2025-05-28 15:03:05.347] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:03:05.347 +07:00 [DBG] [2025-05-28 15:03:05.347] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:03:05.347 +07:00 [INF] [2025-05-28 15:03:05.347] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:03:05.348 +07:00 [DBG] [2025-05-28 15:03:05.348] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:03:05.348 +07:00 [DBG] [2025-05-28 15:03:05.348] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:03:05.386 +07:00 [INF] [2025-05-28 15:03:05.386] [INFO]: Tải danh sách user thành công
2025-05-28 15:03:05.399 +07:00 [INF] [2025-05-28 15:03:05.399] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.21 giây
2025-05-28 15:03:05.411 +07:00 [INF] [2025-05-28 15:03:05.411] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.21 giây
2025-05-28 15:03:05.421 +07:00 [INF] [2025-05-28 15:03:05.421] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.22 giây
2025-05-28 15:03:06.231 +07:00 [INF] [2025-05-28 15:03:06.231] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 15:03:06.231 +07:00 [INF] [2025-05-28 15:03:06.231] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 15:03:06.231 +07:00 [INF] [2025-05-28 15:03:06.231] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 15:03:06.244 +07:00 [DBG] [2025-05-28 15:03:06.244] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 15:03:06.244 +07:00 [INF] [2025-05-28 15:03:06.244] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 12ms
2025-05-28 15:03:06.629 +07:00 [DBG] [2025-05-28 15:03:06.629] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 15:03:06.629 +07:00 [INF] [2025-05-28 15:03:06.629] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:59770. Thời gian: 385ms
2025-05-28 15:03:06.629 +07:00 [DBG] [2025-05-28 15:03:06.629] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:03:06.629 +07:00 [DBG] [2025-05-28 15:03:06.629] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:03:06.630 +07:00 [INF] [2025-05-28 15:03:06.630] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 398ms
2025-05-28 15:03:06.630 +07:00 [DBG] [2025-05-28 15:03:06.630] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:03:06.630 +07:00 [DBG] [2025-05-28 15:03:06.630] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:03:08.175 +07:00 [INF] [2025-05-28 15:03:08.175] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 15:03:08.197 +07:00 [INF] [2025-05-28 15:03:08.197] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
