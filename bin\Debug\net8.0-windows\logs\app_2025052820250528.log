2025-05-28 15:17:13.691 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 15:17:16.170 +07:00 [INF] User selected: HitClub - M<PERSON><PERSON> Binh
2025-05-28 15:17:16.173 +07:00 [INF] Form1 constructor started.
2025-05-28 15:17:16.190 +07:00 [DBG] [2025-05-28 15:17:16.190] [DEBUG]: Gọi InitializeComponent
2025-05-28 15:17:16.200 +07:00 [INF] [2025-05-28 15:17:16.200] [INFO]: Khởi tạo UIManager thành công
2025-05-28 15:17:16.201 +07:00 [DBG] [2025-05-28 15:17:16.201] [DEBUG]: <PERSON><PERSON><PERSON> đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 15:17:16.203 +07:00 [INF] [2025-05-28 15:17:16.203] [INFO]: Đ<PERSON> khởi tạo cột cho dataGridViewUsers
2025-05-28 15:17:16.203 +07:00 [DBG] [2025-05-28 15:17:16.203] [DEBUG]: <PERSON><PERSON><PERSON> đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 15:17:16.203 +07:00 [INF] [2025-05-28 15:17:16.203] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 15:17:16.204 +07:00 [INF] [2025-05-28 15:17:16.204] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 15:17:16.216 +07:00 [DBG] [2025-05-28 15:17:16.216] [DEBUG]: Bắt đầu OnLoad
2025-05-28 15:17:16.216 +07:00 [DBG] [2025-05-28 15:17:16.216] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 15:17:16.232 +07:00 [INF] [2025-05-28 15:17:16.232] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11823
2025-05-28 15:17:16.232 +07:00 [INF] [2025-05-28 15:17:16.232] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 15:17:16.247 +07:00 [INF] [2025-05-28 15:17:16.247] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 15:17:16.249 +07:00 [INF] [2025-05-28 15:17:16.249] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 15:17:16.249 +07:00 [INF] [2025-05-28 15:17:16.249] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 15:17:16.249 +07:00 [INF] [2025-05-28 15:17:16.249] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 15:17:16.250 +07:00 [INF] [2025-05-28 15:17:16.250] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 15:17:16.250 +07:00 [INF] [2025-05-28 15:17:16.250] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 15:17:16.250 +07:00 [INF] InitializeAsync started.
2025-05-28 15:17:16.250 +07:00 [INF] [2025-05-28 15:17:16.250] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 15:17:16.255 +07:00 [DBG] [2025-05-28 15:17:16.255] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 15:17:16.257 +07:00 [DBG] [2025-05-28 15:17:16.257] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 15:17:16.258 +07:00 [DBG] [2025-05-28 15:17:16.258] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:17:16.258 +07:00 [DBG] [2025-05-28 15:17:16.258] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:17:16.265 +07:00 [DBG] [2025-05-28 15:17:16.265] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:17:16.265 +07:00 [INF] [2025-05-28 15:17:16.265] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:17:16.265 +07:00 [DBG] [2025-05-28 15:17:16.265] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:16.267 +07:00 [INF] [2025-05-28 15:17:16.267] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:16.267 +07:00 [INF] [2025-05-28 15:17:16.267] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:16.313 +07:00 [INF] [2025-05-28 15:17:16.313] [INFO]: Tải danh sách user thành công
2025-05-28 15:17:16.322 +07:00 [INF] [2025-05-28 15:17:16.322] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 15:17:16.322 +07:00 [DBG] [2025-05-28 15:17:16.322] [DEBUG]: OnLoad hoàn tất
2025-05-28 15:17:17.358 +07:00 [INF] [2025-05-28 15:17:17.358] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 15:17:17.365 +07:00 [INF] [2025-05-28 15:17:17.365] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 15:17:17.366 +07:00 [INF] [2025-05-28 15:17:17.366] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 15:17:17.368 +07:00 [DBG] [2025-05-28 15:17:17.368] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 15:17:17.410 +07:00 [INF] [2025-05-28 15:17:17.410] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 43ms
2025-05-28 15:17:17.427 +07:00 [DBG] [2025-05-28 15:17:17.427] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 15:17:17.428 +07:00 [INF] [2025-05-28 15:17:17.428] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:60861. Thời gian: 16ms
2025-05-28 15:17:17.428 +07:00 [DBG] [2025-05-28 15:17:17.428] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:17:17.428 +07:00 [DBG] [2025-05-28 15:17:17.428] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:17:17.428 +07:00 [INF] [2025-05-28 15:17:17.428] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 62ms
2025-05-28 15:17:17.428 +07:00 [DBG] [2025-05-28 15:17:17.428] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:17:17.428 +07:00 [DBG] [2025-05-28 15:17:17.428] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:17:18.565 +07:00 [INF] [2025-05-28 15:17:18.564] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 15:17:18.865 +07:00 [INF] [2025-05-28 15:17:18.865] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 15:17:19.000 +07:00 [INF] [2025-05-28 15:17:18.999] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 15:17:19.044 +07:00 [DBG] [2025-05-28 15:17:19.044] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 15:17:19.047 +07:00 [INF] [2025-05-28 15:17:19.047] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 15:17:19.048 +07:00 [INF] [2025-05-28 15:17:19.048] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 15:17:19.051 +07:00 [INF] [2025-05-28 15:17:19.051] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 15:17:19.051 +07:00 [INF] [2025-05-28 15:17:19.051] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:17:19.051 +07:00 [DBG] [2025-05-28 15:17:19.051] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:17:19.051 +07:00 [DBG] [2025-05-28 15:17:19.051] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.051 +07:00 [INF] [2025-05-28 15:17:19.051] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:19.051 +07:00 [DBG] [2025-05-28 15:17:19.051] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:17:19.052 +07:00 [DBG] [2025-05-28 15:17:19.052] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.052 +07:00 [INF] [2025-05-28 15:17:19.052] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:19.052 +07:00 [DBG] [2025-05-28 15:17:19.052] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:17:19.052 +07:00 [DBG] [2025-05-28 15:17:19.052] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.097 +07:00 [INF] [2025-05-28 15:17:19.097] [INFO]: Tải danh sách user thành công
2025-05-28 15:17:19.097 +07:00 [INF] [2025-05-28 15:17:19.097] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.097 +07:00 [INF] [2025-05-28 15:17:19.097] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.097 +07:00 [INF] [2025-05-28 15:17:19.097] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.138 +07:00 [INF] [2025-05-28 15:17:19.138] [INFO]: Tải danh sách user thành công
2025-05-28 15:17:19.151 +07:00 [INF] [2025-05-28 15:17:19.151] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.72 giây
2025-05-28 15:17:19.163 +07:00 [INF] [2025-05-28 15:17:19.163] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.74 giây
2025-05-28 15:17:21.014 +07:00 [INF] [2025-05-28 15:17:21.014] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 15:17:22.490 +07:00 [INF] [2025-05-28 15:17:22.490] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 15:17:22.490 +07:00 [DBG] [2025-05-28 15:17:22.490] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:17:22.490 +07:00 [DBG] [2025-05-28 15:17:22.490] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:17:22.491 +07:00 [INF] [2025-05-28 15:17:22.491] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:17:22.491 +07:00 [INF] [2025-05-28 15:17:22.491] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.491 +07:00 [INF] [2025-05-28 15:17:22.491] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.491 +07:00 [INF] [2025-05-28 15:17:22.491] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.594 +07:00 [INF] [2025-05-28 15:17:22.594] [INFO]: Tải danh sách user thành công
2025-05-28 15:17:22.594 +07:00 [INF] [2025-05-28 15:17:22.594] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:17:22.594 +07:00 [DBG] [2025-05-28 15:17:22.594] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:17:22.594 +07:00 [DBG] [2025-05-28 15:17:22.594] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.594 +07:00 [INF] [2025-05-28 15:17:22.594] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:22.594 +07:00 [DBG] [2025-05-28 15:17:22.594] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:17:22.594 +07:00 [DBG] [2025-05-28 15:17:22.594] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.594 +07:00 [INF] [2025-05-28 15:17:22.594] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:22.594 +07:00 [DBG] [2025-05-28 15:17:22.594] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:17:22.595 +07:00 [DBG] [2025-05-28 15:17:22.595] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.648 +07:00 [INF] [2025-05-28 15:17:22.648] [INFO]: Tải danh sách user thành công
2025-05-28 15:17:22.664 +07:00 [INF] [2025-05-28 15:17:22.664] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.17 giây
2025-05-28 15:17:22.675 +07:00 [INF] [2025-05-28 15:17:22.675] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.18 giây
2025-05-28 15:17:26.556 +07:00 [DBG] [2025-05-28 15:17:26.556] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 15:17:26.749 +07:00 [INF] [2025-05-28 15:17:26.749] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:17:26.749 +07:00 [INF] [2025-05-28 15:17:26.749] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 15:17:26.750 +07:00 [INF] [2025-05-28 15:17:26.750] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 15:17:26.750 +07:00 [DBG] [2025-05-28 15:17:26.750] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 15:17:26.753 +07:00 [INF] [2025-05-28 15:17:26.753] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 15:17:26.753 +07:00 [INF] [2025-05-28 15:17:26.753] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 15:17:26.756 +07:00 [INF] [2025-05-28 15:17:26.756] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 15:17:26.767 +07:00 [INF] [2025-05-28 15:17:26.767] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:17:26.773 +07:00 [INF] [2025-05-28 15:17:26.773] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:17:26.779 +07:00 [INF] [2025-05-28 15:17:26.779] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 15:17:26.782 +07:00 [DBG] [2025-05-28 15:17:26.782] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:17:26.782
2025-05-28 15:17:26.782 +07:00 [INF] [2025-05-28 15:17:26.782] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:17:26.785 +07:00 [INF] [2025-05-28 15:17:26.785] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:17:26.786 +07:00 [INF] [2025-05-28 15:17:26.786] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:17:26.787 +07:00 [INF] [2025-05-28 15:17:26.787] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:17:26.790 +07:00 [INF] [2025-05-28 15:17:26.790] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:17:26.856 +07:00 [DBG] [2025-05-28 15:17:26.856] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 15:17:26.858 +07:00 [INF] [2025-05-28 15:17:26.858] [INFO] [nhatrang345]: ✅ Tìm thấy bàn ít người hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 15:17:26.858 +07:00 [DBG] [2025-05-28 15:17:26.858] [DEBUG] [nhatrang345]: ✅ Báo TaskCompletionSource thành công cho nhatrang345 (shouldAutoLeave=false)
2025-05-28 15:17:26.858 +07:00 [INF] [2025-05-28 15:17:26.858] [INFO] [nhatrang345]: Nhận được cmd: 202 cho nhatrang345 (thời gian: 97.183ms)
2025-05-28 15:17:26.858 +07:00 [WRN] [2025-05-28 15:17:26.858] [WARNING] [nhatrang345]: Dữ liệu phòng không hợp lệ sau cmd 202 cho nhatrang345
2025-05-28 15:17:26.858 +07:00 [INF] [2025-05-28 15:17:26.858] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 97.2417ms
2025-05-28 15:17:26.858 +07:00 [WRN] [2025-05-28 15:17:26.858] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 15:17:27.570 +07:00 [INF] [2025-05-28 15:17:27.570] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 15:17:27.578 +07:00 [INF] [2025-05-28 15:17:27.578] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:17:27.582 +07:00 [INF] [2025-05-28 15:17:27.582] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:17:27.586 +07:00 [INF] [2025-05-28 15:17:27.586] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 15:17:27.589 +07:00 [DBG] [2025-05-28 15:17:27.589] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:17:27.589
2025-05-28 15:17:27.589 +07:00 [INF] [2025-05-28 15:17:27.589] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:17:27.592 +07:00 [INF] [2025-05-28 15:17:27.592] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:17:30.603 +07:00 [WRN] [2025-05-28 15:17:30.603] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 15:17:31.107 +07:00 [INF] [2025-05-28 15:17:31.107] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:17:31.110 +07:00 [INF] [2025-05-28 15:17:31.110] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:17:31.256 +07:00 [DBG] [2025-05-28 15:17:31.256] [DEBUG]: Bắt đầu đóng Form1
2025-05-28 15:17:31.285 +07:00 [INF] [2025-05-28 15:17:31.285] [INFO]: Đã đóng driver cho OpenQA.Selenium.Chrome.ChromeDriver
2025-05-28 15:17:31.301 +07:00 [INF] [2025-05-28 15:17:31.301] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 29312)
2025-05-28 15:17:31.301 +07:00 [INF] [2025-05-28 15:17:31.301] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 25700)
2025-05-28 15:17:31.301 +07:00 [INF] [2025-05-28 15:17:31.301] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 9084)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 28372)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 20244)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 25040)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 32180)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 18084)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 22616)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 27176)
2025-05-28 15:17:31.303 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 17348)
2025-05-28 15:17:31.303 +07:00 [INF] [2025-05-28 15:17:31.303] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 26540)
2025-05-28 15:17:31.303 +07:00 [INF] [2025-05-28 15:17:31.303] [INFO]: MauBinhCardManager disposed
2025-05-28 15:17:31.303 +07:00 [INF] [2025-05-28 15:17:31.303] [INFO]: Đã đóng Form1 thành công
2025-05-28 15:17:31.311 +07:00 [INF] Application started successfully.
2025-05-28 15:21:59.089 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 15:22:02.012 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 15:22:02.015 +07:00 [INF] Form1 constructor started.
2025-05-28 15:22:02.033 +07:00 [DBG] [2025-05-28 15:22:02.033] [DEBUG]: Gọi InitializeComponent
2025-05-28 15:22:02.042 +07:00 [INF] [2025-05-28 15:22:02.042] [INFO]: Khởi tạo UIManager thành công
2025-05-28 15:22:02.043 +07:00 [DBG] [2025-05-28 15:22:02.043] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 15:22:02.045 +07:00 [INF] [2025-05-28 15:22:02.045] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 15:22:02.045 +07:00 [DBG] [2025-05-28 15:22:02.045] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 15:22:02.045 +07:00 [INF] [2025-05-28 15:22:02.045] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 15:22:02.046 +07:00 [INF] [2025-05-28 15:22:02.046] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 15:22:02.057 +07:00 [DBG] [2025-05-28 15:22:02.057] [DEBUG]: Bắt đầu OnLoad
2025-05-28 15:22:02.058 +07:00 [DBG] [2025-05-28 15:22:02.058] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 15:22:02.074 +07:00 [INF] [2025-05-28 15:22:02.074] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11823
2025-05-28 15:22:02.074 +07:00 [INF] [2025-05-28 15:22:02.074] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 15:22:02.090 +07:00 [INF] [2025-05-28 15:22:02.090] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 15:22:02.092 +07:00 [INF] [2025-05-28 15:22:02.092] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 15:22:02.092 +07:00 [INF] [2025-05-28 15:22:02.092] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 15:22:02.093 +07:00 [INF] [2025-05-28 15:22:02.093] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 15:22:02.093 +07:00 [INF] [2025-05-28 15:22:02.093] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 15:22:02.093 +07:00 [INF] [2025-05-28 15:22:02.093] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 15:22:02.093 +07:00 [INF] InitializeAsync started.
2025-05-28 15:22:02.094 +07:00 [INF] [2025-05-28 15:22:02.094] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 15:22:02.098 +07:00 [DBG] [2025-05-28 15:22:02.098] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 15:22:02.100 +07:00 [DBG] [2025-05-28 15:22:02.100] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 15:22:02.101 +07:00 [DBG] [2025-05-28 15:22:02.101] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:02.101 +07:00 [DBG] [2025-05-28 15:22:02.101] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:02.108 +07:00 [DBG] [2025-05-28 15:22:02.108] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:22:02.108 +07:00 [INF] [2025-05-28 15:22:02.108] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:22:02.108 +07:00 [DBG] [2025-05-28 15:22:02.108] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:02.110 +07:00 [DBG] [2025-05-28 15:22:02.110] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:02.110 +07:00 [DBG] [2025-05-28 15:22:02.110] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:22:02.110 +07:00 [INF] [2025-05-28 15:22:02.110] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:22:02.110 +07:00 [DBG] [2025-05-28 15:22:02.110] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:02.110 +07:00 [DBG] [2025-05-28 15:22:02.110] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:02.110 +07:00 [DBG] [2025-05-28 15:22:02.110] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:02.110 +07:00 [INF] [2025-05-28 15:22:02.110] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:02.110 +07:00 [DBG] [2025-05-28 15:22:02.110] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:02.110 +07:00 [DBG] [2025-05-28 15:22:02.110] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:02.158 +07:00 [INF] [2025-05-28 15:22:02.158] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:02.168 +07:00 [INF] [2025-05-28 15:22:02.168] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 15:22:02.169 +07:00 [DBG] [2025-05-28 15:22:02.169] [DEBUG]: OnLoad hoàn tất
2025-05-28 15:22:03.401 +07:00 [INF] [2025-05-28 15:22:03.401] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 15:22:03.406 +07:00 [INF] [2025-05-28 15:22:03.406] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 15:22:03.408 +07:00 [INF] [2025-05-28 15:22:03.408] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 15:22:03.410 +07:00 [DBG] [2025-05-28 15:22:03.410] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 15:22:03.453 +07:00 [INF] [2025-05-28 15:22:03.453] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 44ms
2025-05-28 15:22:03.881 +07:00 [DBG] [2025-05-28 15:22:03.881] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 15:22:03.882 +07:00 [INF] [2025-05-28 15:22:03.882] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:61413. Thời gian: 427ms
2025-05-28 15:22:03.882 +07:00 [DBG] [2025-05-28 15:22:03.882] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:03.882 +07:00 [DBG] [2025-05-28 15:22:03.882] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:03.882 +07:00 [INF] [2025-05-28 15:22:03.882] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 474ms
2025-05-28 15:22:03.882 +07:00 [DBG] [2025-05-28 15:22:03.882] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:03.882 +07:00 [DBG] [2025-05-28 15:22:03.882] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:05.480 +07:00 [INF] [2025-05-28 15:22:05.480] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 15:22:05.556 +07:00 [INF] [2025-05-28 15:22:05.556] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 15:22:05.865 +07:00 [INF] [2025-05-28 15:22:05.864] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 15:22:05.924 +07:00 [DBG] [2025-05-28 15:22:05.924] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 15:22:05.929 +07:00 [INF] [2025-05-28 15:22:05.929] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 15:22:05.931 +07:00 [INF] [2025-05-28 15:22:05.931] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 15:22:05.935 +07:00 [INF] [2025-05-28 15:22:05.935] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 15:22:05.936 +07:00 [INF] [2025-05-28 15:22:05.936] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:05.936 +07:00 [DBG] [2025-05-28 15:22:05.936] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:05.936 +07:00 [DBG] [2025-05-28 15:22:05.936] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:05.936 +07:00 [INF] [2025-05-28 15:22:05.936] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:22:05.936 +07:00 [DBG] [2025-05-28 15:22:05.936] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:05.936 +07:00 [DBG] [2025-05-28 15:22:05.936] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:05.936 +07:00 [INF] [2025-05-28 15:22:05.936] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:05.936 +07:00 [DBG] [2025-05-28 15:22:05.936] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:05.936 +07:00 [DBG] [2025-05-28 15:22:05.936] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:05.964 +07:00 [INF] [2025-05-28 15:22:05.964] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:05.964 +07:00 [INF] [2025-05-28 15:22:05.964] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:05.964 +07:00 [DBG] [2025-05-28 15:22:05.964] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:05.965 +07:00 [DBG] [2025-05-28 15:22:05.965] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:05.965 +07:00 [INF] [2025-05-28 15:22:05.965] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:22:05.965 +07:00 [DBG] [2025-05-28 15:22:05.965] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:05.965 +07:00 [DBG] [2025-05-28 15:22:05.965] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:05.965 +07:00 [INF] [2025-05-28 15:22:05.965] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:05.965 +07:00 [DBG] [2025-05-28 15:22:05.965] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:05.965 +07:00 [DBG] [2025-05-28 15:22:05.965] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:05.992 +07:00 [INF] [2025-05-28 15:22:05.992] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:06.002 +07:00 [INF] [2025-05-28 15:22:06.002] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.12 giây
2025-05-28 15:22:06.010 +07:00 [INF] [2025-05-28 15:22:06.010] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.13 giây
2025-05-28 15:22:07.876 +07:00 [INF] [2025-05-28 15:22:07.876] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 15:22:08.951 +07:00 [INF] [2025-05-28 15:22:08.951] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 15:22:08.952 +07:00 [DBG] [2025-05-28 15:22:08.952] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:08.952 +07:00 [DBG] [2025-05-28 15:22:08.952] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:08.952 +07:00 [INF] [2025-05-28 15:22:08.952] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 15:22:08.952 +07:00 [DBG] [2025-05-28 15:22:08.952] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:08.952 +07:00 [DBG] [2025-05-28 15:22:08.952] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:08.952 +07:00 [INF] [2025-05-28 15:22:08.952] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:08.952 +07:00 [DBG] [2025-05-28 15:22:08.952] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:08.953 +07:00 [DBG] [2025-05-28 15:22:08.953] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:08.953 +07:00 [INF] [2025-05-28 15:22:08.953] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:22:08.953 +07:00 [DBG] [2025-05-28 15:22:08.953] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:08.953 +07:00 [DBG] [2025-05-28 15:22:08.953] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:08.953 +07:00 [INF] [2025-05-28 15:22:08.953] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:08.953 +07:00 [DBG] [2025-05-28 15:22:08.953] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:08.953 +07:00 [DBG] [2025-05-28 15:22:08.953] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:09.007 +07:00 [INF] [2025-05-28 15:22:09.007] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:09.007 +07:00 [INF] [2025-05-28 15:22:09.007] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:09.007 +07:00 [DBG] [2025-05-28 15:22:09.007] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:09.007 +07:00 [DBG] [2025-05-28 15:22:09.007] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:09.007 +07:00 [INF] [2025-05-28 15:22:09.007] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:22:09.007 +07:00 [DBG] [2025-05-28 15:22:09.007] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:09.007 +07:00 [DBG] [2025-05-28 15:22:09.007] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:09.007 +07:00 [INF] [2025-05-28 15:22:09.007] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:09.007 +07:00 [DBG] [2025-05-28 15:22:09.007] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:09.007 +07:00 [DBG] [2025-05-28 15:22:09.007] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:09.050 +07:00 [INF] [2025-05-28 15:22:09.050] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:09.064 +07:00 [INF] [2025-05-28 15:22:09.064] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 15:22:09.077 +07:00 [INF] [2025-05-28 15:22:09.077] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.13 giây
2025-05-28 15:22:10.615 +07:00 [DBG] [2025-05-28 15:22:10.615] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 15:22:10.763 +07:00 [INF] [2025-05-28 15:22:10.763] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:22:10.763 +07:00 [INF] [2025-05-28 15:22:10.763] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 15:22:10.764 +07:00 [INF] [2025-05-28 15:22:10.764] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 15:22:10.764 +07:00 [DBG] [2025-05-28 15:22:10.764] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 15:22:10.767 +07:00 [INF] [2025-05-28 15:22:10.767] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 15:22:10.767 +07:00 [INF] [2025-05-28 15:22:10.767] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 15:22:10.771 +07:00 [INF] [2025-05-28 15:22:10.771] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 15:22:10.781 +07:00 [INF] [2025-05-28 15:22:10.781] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:22:10.788 +07:00 [INF] [2025-05-28 15:22:10.788] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:22:10.794 +07:00 [INF] [2025-05-28 15:22:10.794] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 15:22:10.797 +07:00 [DBG] [2025-05-28 15:22:10.797] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:22:10.797
2025-05-28 15:22:10.797 +07:00 [INF] [2025-05-28 15:22:10.797] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:22:10.800 +07:00 [INF] [2025-05-28 15:22:10.800] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:22:10.861 +07:00 [DBG] [2025-05-28 15:22:10.861] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 15:22:10.862 +07:00 [INF] [2025-05-28 15:22:10.862] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 15:22:10.862 +07:00 [INF] [2025-05-28 15:22:10.862] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 15:22:10.862 +07:00 [DBG] [2025-05-28 15:22:10.862] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 15:22:10.863 +07:00 [INF] [2025-05-28 15:22:10.863] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 15:22:10.863 +07:00 [INF] [2025-05-28 15:22:10.863] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:22:10.863 +07:00 [INF] [2025-05-28 15:22:10.863] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 15:22:10.867 +07:00 [INF] [2025-05-28 15:22:10.867] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:22:10.867 +07:00 [INF] [2025-05-28 15:22:10.867] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 15:22:10.867 +07:00 [ERR] [2025-05-28 15:22:10.867] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 15:22:10.867 +07:00 [INF] [2025-05-28 15:22:10.867] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 91.6032ms
2025-05-28 15:22:10.867 +07:00 [WRN] [2025-05-28 15:22:10.867] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 15:22:10.874 +07:00 [INF] [2025-05-28 15:22:10.874] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 15:22:10.878 +07:00 [WRN] [2025-05-28 15:22:10.878] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 15:22:10.878 +07:00 [INF] [2025-05-28 15:22:10.878] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 6.5108ms
2025-05-28 15:22:10.878 +07:00 [INF] [2025-05-28 15:22:10.878] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 13.9546ms
2025-05-28 15:22:10.878 +07:00 [DBG] [2025-05-28 15:22:10.878] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:10.879 +07:00 [INF] [2025-05-28 15:22:10.879] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 15:22:10.879 +07:00 [DBG] [2025-05-28 15:22:10.879] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:10.879 +07:00 [DBG] [2025-05-28 15:22:10.879] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:10.880 +07:00 [INF] [2025-05-28 15:22:10.880] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:10.880 +07:00 [DBG] [2025-05-28 15:22:10.880] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:10.880 +07:00 [DBG] [2025-05-28 15:22:10.880] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:10.880 +07:00 [INF] [2025-05-28 15:22:10.880] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:22:10.880 +07:00 [DBG] [2025-05-28 15:22:10.880] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:10.880 +07:00 [DBG] [2025-05-28 15:22:10.880] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:10.880 +07:00 [INF] [2025-05-28 15:22:10.880] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:10.880 +07:00 [DBG] [2025-05-28 15:22:10.880] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:10.881 +07:00 [DBG] [2025-05-28 15:22:10.881] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:10.923 +07:00 [INF] [2025-05-28 15:22:10.923] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 15:22:10.963 +07:00 [INF] [2025-05-28 15:22:10.963] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:10.980 +07:00 [INF] [2025-05-28 15:22:10.980] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 15:22:11.001 +07:00 [INF] [2025-05-28 15:22:11.001] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:22:11.001 +07:00 [INF] [2025-05-28 15:22:11.001] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5797524452209473, Vị trí: (35, 51)
2025-05-28 15:22:11.580 +07:00 [INF] [2025-05-28 15:22:11.580] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 15:22:11.588 +07:00 [INF] [2025-05-28 15:22:11.588] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:22:11.592 +07:00 [INF] [2025-05-28 15:22:11.592] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:22:11.596 +07:00 [INF] [2025-05-28 15:22:11.596] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 15:22:11.599 +07:00 [DBG] [2025-05-28 15:22:11.599] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:22:11.599
2025-05-28 15:22:11.599 +07:00 [INF] [2025-05-28 15:22:11.599] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:22:11.602 +07:00 [INF] [2025-05-28 15:22:11.602] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:22:11.658 +07:00 [DBG] [2025-05-28 15:22:11.658] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 15:22:11.659 +07:00 [INF] [2025-05-28 15:22:11.659] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 2, số người: 3), tự động thoát phòng
2025-05-28 15:22:11.659 +07:00 [INF] [2025-05-28 15:22:11.659] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 15:22:11.659 +07:00 [DBG] [2025-05-28 15:22:11.659] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 15:22:11.659 +07:00 [INF] [2025-05-28 15:22:11.659] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 15:22:11.660 +07:00 [INF] [2025-05-28 15:22:11.660] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 15:22:11.661 +07:00 [INF] [2025-05-28 15:22:11.661] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:22:11.665 +07:00 [INF] [2025-05-28 15:22:11.665] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:22:11.665 +07:00 [INF] [2025-05-28 15:22:11.665] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 15:22:11.665 +07:00 [ERR] [2025-05-28 15:22:11.665] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 15:22:11.665 +07:00 [INF] [2025-05-28 15:22:11.665] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 82.3841ms
2025-05-28 15:22:11.665 +07:00 [WRN] [2025-05-28 15:22:11.665] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 15:22:11.667 +07:00 [INF] [2025-05-28 15:22:11.667] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 15:22:11.671 +07:00 [WRN] [2025-05-28 15:22:11.671] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 15:22:11.672 +07:00 [INF] [2025-05-28 15:22:11.672] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 9.168ms
2025-05-28 15:22:11.672 +07:00 [INF] [2025-05-28 15:22:11.672] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 12.4854ms
2025-05-28 15:22:11.672 +07:00 [DBG] [2025-05-28 15:22:11.672] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:11.672 +07:00 [INF] [2025-05-28 15:22:11.672] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 15:22:11.672 +07:00 [DBG] [2025-05-28 15:22:11.672] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:11.672 +07:00 [DBG] [2025-05-28 15:22:11.672] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:11.673 +07:00 [INF] [2025-05-28 15:22:11.673] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:11.673 +07:00 [DBG] [2025-05-28 15:22:11.673] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:11.673 +07:00 [DBG] [2025-05-28 15:22:11.673] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:11.673 +07:00 [INF] [2025-05-28 15:22:11.673] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:22:11.673 +07:00 [DBG] [2025-05-28 15:22:11.673] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:11.673 +07:00 [DBG] [2025-05-28 15:22:11.673] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:11.673 +07:00 [INF] [2025-05-28 15:22:11.673] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:11.673 +07:00 [DBG] [2025-05-28 15:22:11.673] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:11.673 +07:00 [DBG] [2025-05-28 15:22:11.673] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:11.714 +07:00 [INF] [2025-05-28 15:22:11.714] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 15:22:11.747 +07:00 [INF] [2025-05-28 15:22:11.747] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:11.774 +07:00 [INF] [2025-05-28 15:22:11.774] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 15:22:11.800 +07:00 [INF] [2025-05-28 15:22:11.800] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:22:11.800 +07:00 [INF] [2025-05-28 15:22:11.800] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 15:22:11.925 +07:00 [INF] [2025-05-28 15:22:11.925] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:22:11.926 +07:00 [INF] [2025-05-28 15:22:11.926] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:22:11.932 +07:00 [INF] [2025-05-28 15:22:11.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:22:12.367 +07:00 [INF] [2025-05-28 15:22:12.367] [INFO] [nhatrang345]: Thử vào phòng lần 3/3 cho nhatrang345
2025-05-28 15:22:12.375 +07:00 [INF] [2025-05-28 15:22:12.375] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:22:12.379 +07:00 [INF] [2025-05-28 15:22:12.379] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:22:12.384 +07:00 [INF] [2025-05-28 15:22:12.384] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 15:22:12.387 +07:00 [DBG] [2025-05-28 15:22:12.387] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:22:12.387
2025-05-28 15:22:12.387 +07:00 [INF] [2025-05-28 15:22:12.387] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:22:12.390 +07:00 [INF] [2025-05-28 15:22:12.390] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:22:12.454 +07:00 [DBG] [2025-05-28 15:22:12.454] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 15:22:12.454 +07:00 [INF] [2025-05-28 15:22:12.454] [INFO] [nhatrang345]: ✅ Tìm thấy bàn ít người hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 15:22:12.454 +07:00 [DBG] [2025-05-28 15:22:12.454] [DEBUG] [nhatrang345]: ✅ Báo TaskCompletionSource thành công cho nhatrang345 (shouldAutoLeave=false)
2025-05-28 15:22:12.454 +07:00 [INF] [2025-05-28 15:22:12.454] [INFO] [nhatrang345]: Nhận được cmd: 202 cho nhatrang345 (thời gian: 83.4867ms)
2025-05-28 15:22:12.454 +07:00 [INF] [2025-05-28 15:22:12.454] [INFO] [nhatrang345]: 🔄 Bắt đầu sync data từ WebSocketManager cho nhatrang345
2025-05-28 15:22:12.454 +07:00 [INF] [2025-05-28 15:22:12.454] [INFO] [nhatrang345]: ✅ Synced room players for nhatrang345: 1 players
2025-05-28 15:22:12.454 +07:00 [INF] [2025-05-28 15:22:12.454] [INFO] [nhatrang345]: ✅ Synced user seat for nhatrang345: 0
2025-05-28 15:22:12.454 +07:00 [INF] [2025-05-28 15:22:12.454] [INFO] [nhatrang345]: ✅ Synced room time for nhatrang345: 0
2025-05-28 15:22:12.454 +07:00 [INF] [2025-05-28 15:22:12.454] [INFO] [nhatrang345]: 🔄 Hoàn thành sync data cho nhatrang345
2025-05-28 15:22:12.454 +07:00 [INF] [2025-05-28 15:22:12.454] [INFO] [nhatrang345]: ✅ Xác nhận trạng thái phòng cho nhatrang345: sit=0, số người=1
2025-05-28 15:22:12.454 +07:00 [INF] [2025-05-28 15:22:12.454] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 84.1612ms
2025-05-28 15:22:12.454 +07:00 [INF] [2025-05-28 15:22:12.454] [INFO] [nhatrang345]: Vào phòng thành công cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 15:22:12.454 +07:00 [INF] [2025-05-28 15:22:12.454] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 1683.2777ms
2025-05-28 15:22:12.454 +07:00 [DBG] [2025-05-28 15:22:12.454] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:12.454 +07:00 [DBG] [2025-05-28 15:22:12.454] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:12.455 +07:00 [INF] [2025-05-28 15:22:12.455] [INFO]: Đã đặt nhatrang345 làm MainUser với seat 0
2025-05-28 15:22:12.456 +07:00 [INF] [2025-05-28 15:22:12.456] [INFO]: Đã cập nhật token.txt
2025-05-28 15:22:12.456 +07:00 [INF] [2025-05-28 15:22:12.456] [INFO]: Đã cập nhật trạng thái IsMainUser cho nhatrang345
2025-05-28 15:22:12.456 +07:00 [INF] [2025-05-28 15:22:12.456] [INFO] [nhatrang345]: ✅ Tìm thấy bàn trống hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 15:22:12.463 +07:00 [INF] [2025-05-28 15:22:12.463] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:12.463 +07:00 [DBG] [2025-05-28 15:22:12.463] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:12.463 +07:00 [DBG] [2025-05-28 15:22:12.463] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:12.463 +07:00 [INF] [2025-05-28 15:22:12.463] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:22:12.463 +07:00 [DBG] [2025-05-28 15:22:12.463] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:12.463 +07:00 [DBG] [2025-05-28 15:22:12.463] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:12.463 +07:00 [INF] [2025-05-28 15:22:12.463] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:12.463 +07:00 [DBG] [2025-05-28 15:22:12.463] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:12.463 +07:00 [DBG] [2025-05-28 15:22:12.463] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:12.508 +07:00 [INF] [2025-05-28 15:22:12.508] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:12.520 +07:00 [INF] [2025-05-28 15:22:12.520] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 15:22:13.593 +07:00 [INF] [2025-05-28 15:22:13.593] [INFO] [nhatrang345]: Đã xóa trạng thái phòng cho nhatrang345
2025-05-28 15:22:13.593 +07:00 [INF] [2025-05-28 15:22:13.593] [INFO]: Đã hủy lấy bàn trống
2025-05-28 15:22:13.593 +07:00 [INF] [2025-05-28 15:22:13.593] [INFO] [nhatrang345]: Hoàn thành xử lý cho nhatrang345, thời gian: 2825.6005ms
2025-05-28 15:22:13.593 +07:00 [DBG] [2025-05-28 15:22:13.593] [DEBUG] [nhatrang345]: ❌ Tắt chế độ Get Empty Table cho nhatrang345
2025-05-28 15:22:13.593 +07:00 [DBG] [2025-05-28 15:22:13.593] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:13.593 +07:00 [DBG] [2025-05-28 15:22:13.593] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:13.594 +07:00 [INF] [2025-05-28 15:22:13.594] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:13.594 +07:00 [DBG] [2025-05-28 15:22:13.594] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:13.594 +07:00 [DBG] [2025-05-28 15:22:13.594] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:13.594 +07:00 [INF] [2025-05-28 15:22:13.594] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:22:13.594 +07:00 [DBG] [2025-05-28 15:22:13.594] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:13.594 +07:00 [DBG] [2025-05-28 15:22:13.594] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:13.594 +07:00 [INF] [2025-05-28 15:22:13.594] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:13.594 +07:00 [DBG] [2025-05-28 15:22:13.594] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:13.594 +07:00 [DBG] [2025-05-28 15:22:13.594] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:13.619 +07:00 [INF] [2025-05-28 15:22:13.619] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:13.626 +07:00 [INF] [2025-05-28 15:22:13.626] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.03 giây
2025-05-28 15:22:16.925 +07:00 [INF] [2025-05-28 15:22:16.925] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 15:22:16.925 +07:00 [INF] [2025-05-28 15:22:16.925] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 15:22:16.925 +07:00 [INF] [2025-05-28 15:22:16.925] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 15:22:18.157 +07:00 [INF] [2025-05-28 15:22:18.157] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 15:22:18.157 +07:00 [INF] [2025-05-28 15:22:18.157] [INFO] [phanthiet989]: Đang mở profile cho phanthiet989...
2025-05-28 15:22:18.157 +07:00 [INF] [2025-05-28 15:22:18.157] [INFO] [phanthiet989]: Bắt đầu mở profile cho phanthiet989...
2025-05-28 15:22:18.158 +07:00 [DBG] [2025-05-28 15:22:18.158] [DEBUG] [phanthiet989]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 15:22:18.158 +07:00 [INF] [2025-05-28 15:22:18.158] [INFO] [phanthiet989]: Tìm thấy profile cho phanthiet989 với ID: 393edc55-e67b-4bc9-9708-c57613890575. Thời gian: 1ms
2025-05-28 15:22:18.487 +07:00 [DBG] [2025-05-28 15:22:18.487] [DEBUG] [phanthiet989]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/393edc55-e67b-4bc9-9708-c57613890575: Thành công
2025-05-28 15:22:18.487 +07:00 [INF] [2025-05-28 15:22:18.487] [INFO] [phanthiet989]: Đã mở profile cho phanthiet989 với remote debugging: 127.0.0.1:61467. Thời gian: 328ms
2025-05-28 15:22:18.487 +07:00 [DBG] [2025-05-28 15:22:18.487] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:18.487 +07:00 [DBG] [2025-05-28 15:22:18.487] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:18.487 +07:00 [INF] [2025-05-28 15:22:18.487] [INFO] [phanthiet989]: Mở profile cho phanthiet989 thành công. Thời gian: 329ms
2025-05-28 15:22:18.487 +07:00 [DBG] [2025-05-28 15:22:18.487] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:18.487 +07:00 [DBG] [2025-05-28 15:22:18.487] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:20.035 +07:00 [INF] [2025-05-28 15:22:20.035] [INFO] [phanthiet989]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho phanthiet989 và mở URL https://web.hit.club/
2025-05-28 15:22:20.350 +07:00 [INF] [2025-05-28 15:22:20.350] [INFO] [phanthiet989]: ✅ Đã setup WebView external handler cho phanthiet989
2025-05-28 15:22:20.418 +07:00 [INF] [2025-05-28 15:22:20.418] [INFO] [phanthiet989]: ✅ Đã setup console log listener cho phanthiet989
2025-05-28 15:22:20.422 +07:00 [DBG] [2025-05-28 15:22:20.422] [DEBUG] [phanthiet989]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 15:22:20.425 +07:00 [INF] [2025-05-28 15:22:20.425] [INFO] [phanthiet989]: ✅ Đã setup WebView external handler cho phanthiet989
2025-05-28 15:22:20.425 +07:00 [INF] [2025-05-28 15:22:20.425] [INFO] [phanthiet989]: ✅ Đã setup console log listener cho phanthiet989
2025-05-28 15:22:20.428 +07:00 [INF] [2025-05-28 15:22:20.428] [INFO] [phanthiet989]: Tìm thấy token (token) cho phanthiet989: 1-b56318d71797d99fa1ab774e7e57d73b
2025-05-28 15:22:20.428 +07:00 [INF] [2025-05-28 15:22:20.428] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:20.428 +07:00 [DBG] [2025-05-28 15:22:20.428] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:20.428 +07:00 [DBG] [2025-05-28 15:22:20.428] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:20.428 +07:00 [INF] [2025-05-28 15:22:20.428] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:20.428 +07:00 [DBG] [2025-05-28 15:22:20.428] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:20.428 +07:00 [DBG] [2025-05-28 15:22:20.428] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:20.428 +07:00 [INF] [2025-05-28 15:22:20.428] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:20.428 +07:00 [DBG] [2025-05-28 15:22:20.428] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:20.428 +07:00 [DBG] [2025-05-28 15:22:20.428] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:20.454 +07:00 [INF] [2025-05-28 15:22:20.454] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:20.455 +07:00 [INF] [2025-05-28 15:22:20.455] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:20.455 +07:00 [DBG] [2025-05-28 15:22:20.455] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:20.455 +07:00 [DBG] [2025-05-28 15:22:20.455] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:20.455 +07:00 [INF] [2025-05-28 15:22:20.455] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:20.455 +07:00 [DBG] [2025-05-28 15:22:20.455] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:20.455 +07:00 [DBG] [2025-05-28 15:22:20.455] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:20.455 +07:00 [INF] [2025-05-28 15:22:20.455] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:20.455 +07:00 [DBG] [2025-05-28 15:22:20.455] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:20.455 +07:00 [DBG] [2025-05-28 15:22:20.455] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:20.481 +07:00 [INF] [2025-05-28 15:22:20.481] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:20.490 +07:00 [INF] [2025-05-28 15:22:20.490] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.00 giây
2025-05-28 15:22:20.497 +07:00 [INF] [2025-05-28 15:22:20.497] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.01 giây
2025-05-28 15:22:21.997 +07:00 [INF] [2025-05-28 15:22:21.997] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 15:22:21.997 +07:00 [INF] [2025-05-28 15:22:21.997] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 15:22:22.000 +07:00 [INF] [2025-05-28 15:22:22.000] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 15:22:22.422 +07:00 [INF] [2025-05-28 15:22:22.422] [INFO] [phanthiet989]: Đã set lại kích thước profile phanthiet989 về 700x500 sau khi load
2025-05-28 15:22:23.431 +07:00 [INF] [2025-05-28 15:22:23.431] [INFO] [phanthiet989]: WebSocket initialized for phanthiet989
2025-05-28 15:22:23.431 +07:00 [DBG] [2025-05-28 15:22:23.431] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:23.431 +07:00 [DBG] [2025-05-28 15:22:23.431] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:23.431 +07:00 [INF] [2025-05-28 15:22:23.431] [INFO] [phanthiet989]: Đã mở profile cho phanthiet989
2025-05-28 15:22:23.431 +07:00 [DBG] [2025-05-28 15:22:23.431] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:23.431 +07:00 [DBG] [2025-05-28 15:22:23.431] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:23.432 +07:00 [INF] [2025-05-28 15:22:23.432] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:23.432 +07:00 [DBG] [2025-05-28 15:22:23.432] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:23.432 +07:00 [DBG] [2025-05-28 15:22:23.432] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:23.432 +07:00 [INF] [2025-05-28 15:22:23.432] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:23.432 +07:00 [DBG] [2025-05-28 15:22:23.432] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:23.432 +07:00 [DBG] [2025-05-28 15:22:23.432] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:23.432 +07:00 [INF] [2025-05-28 15:22:23.432] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:23.432 +07:00 [DBG] [2025-05-28 15:22:23.432] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:23.432 +07:00 [DBG] [2025-05-28 15:22:23.432] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:23.496 +07:00 [INF] [2025-05-28 15:22:23.496] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:23.497 +07:00 [INF] [2025-05-28 15:22:23.497] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:23.497 +07:00 [DBG] [2025-05-28 15:22:23.497] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:23.497 +07:00 [DBG] [2025-05-28 15:22:23.497] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:23.497 +07:00 [INF] [2025-05-28 15:22:23.497] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:23.497 +07:00 [DBG] [2025-05-28 15:22:23.497] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:23.497 +07:00 [DBG] [2025-05-28 15:22:23.497] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:23.497 +07:00 [INF] [2025-05-28 15:22:23.497] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:23.497 +07:00 [DBG] [2025-05-28 15:22:23.497] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:23.497 +07:00 [DBG] [2025-05-28 15:22:23.497] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:23.550 +07:00 [INF] [2025-05-28 15:22:23.550] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:23.564 +07:00 [INF] [2025-05-28 15:22:23.564] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.13 giây
2025-05-28 15:22:23.578 +07:00 [INF] [2025-05-28 15:22:23.578] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.15 giây
2025-05-28 15:22:25.565 +07:00 [INF] [2025-05-28 15:22:25.565] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:22:25.565 +07:00 [INF] [2025-05-28 15:22:25.565] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5738993287086487, Vị trí: (7, 381)
2025-05-28 15:22:25.565 +07:00 [INF] [2025-05-28 15:22:25.565] [INFO]: Bắt đầu nhảy bàn với Main User: nhatrang345, roomId: 100, maxAttempts: 10, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 15:22:25.565 +07:00 [DBG] [2025-05-28 15:22:25.565] [DEBUG] [phanthiet989]: ✅ Đặt phanthiet989 tìm mainUser: nhatrang345
2025-05-28 15:22:25.570 +07:00 [INF] [2025-05-28 15:22:25.570] [INFO] [phanthiet989]: Thử lần 1/10 nhảy bàn cho phanthiet989
2025-05-28 15:22:25.578 +07:00 [INF] [2025-05-28 15:22:25.578] [INFO] [phanthiet989]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho phanthiet989
2025-05-28 15:22:25.583 +07:00 [INF] [2025-05-28 15:22:25.583] [INFO] [phanthiet989]: Đã tìm thấy canvas cho phanthiet989
2025-05-28 15:22:25.589 +07:00 [INF] [2025-05-28 15:22:25.589] [INFO] [phanthiet989]: Kích thước canvas cho phanthiet989: 684x405
2025-05-28 15:22:25.591 +07:00 [DBG] [2025-05-28 15:22:25.591] [DEBUG] [phanthiet989]: Created TaskCompletionSource for phanthiet989 in JoinRoom at 15:22:25.591
2025-05-28 15:22:25.591 +07:00 [INF] [2025-05-28 15:22:25.591] [INFO] [phanthiet989]: Thử lần 1/2 click vào phòng 100 cho phanthiet989
2025-05-28 15:22:25.595 +07:00 [INF] [2025-05-28 15:22:25.595] [INFO] [phanthiet989]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho phanthiet989
2025-05-28 15:22:25.683 +07:00 [DBG] [2025-05-28 15:22:25.683] [DEBUG] [phanthiet989]: [WebSocket] phanthiet989: Processing cmd 202 (Join Room)
2025-05-28 15:22:25.683 +07:00 [INF] [2025-05-28 15:22:25.683] [INFO] [phanthiet989]: ❌ Không tìm thấy mainUser nhatrang345 cho phanthiet989, tự động thoát phòng
2025-05-28 15:22:25.683 +07:00 [INF] [2025-05-28 15:22:25.683] [INFO] [phanthiet989]: 🚪 Bắt đầu tự động thoát phòng cho phanthiet989 - Lý do: Không tìm thấy mainUser nhatrang345
2025-05-28 15:22:25.683 +07:00 [DBG] [2025-05-28 15:22:25.683] [DEBUG] [phanthiet989]: 🔄 Báo TaskCompletionSource thất bại cho phanthiet989 (shouldAutoLeave=true)
2025-05-28 15:22:25.683 +07:00 [INF] [2025-05-28 15:22:25.683] [INFO] [phanthiet989]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho phanthiet989, thử lại ngay
2025-05-28 15:22:25.683 +07:00 [INF] [2025-05-28 15:22:25.683] [INFO] [phanthiet989]: 🔄 Đang thực hiện thoát phòng cho phanthiet989...
2025-05-28 15:22:25.683 +07:00 [INF] [2025-05-28 15:22:25.683] [INFO] [phanthiet989]: Thử lần 2/2 click vào phòng 100 cho phanthiet989
2025-05-28 15:22:25.690 +07:00 [INF] [2025-05-28 15:22:25.690] [INFO] [phanthiet989]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho phanthiet989
2025-05-28 15:22:25.690 +07:00 [INF] [2025-05-28 15:22:25.690] [INFO] [phanthiet989]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho phanthiet989, thử lại ngay
2025-05-28 15:22:25.690 +07:00 [ERR] [2025-05-28 15:22:25.690] [ERROR] [phanthiet989]: Không thể vào phòng 100 sau 2 lần thử cho phanthiet989
2025-05-28 15:22:25.690 +07:00 [INF] [2025-05-28 15:22:25.690] [INFO] [phanthiet989]: Hoàn thành JoinRoom cho phanthiet989, thời gian: 116.9638ms
2025-05-28 15:22:25.690 +07:00 [WRN] [2025-05-28 15:22:25.690] [WARNING] [phanthiet989]: Vào phòng thất bại cho phanthiet989
2025-05-28 15:22:25.710 +07:00 [INF] [2025-05-28 15:22:25.710] [INFO] [phanthiet989]: Đang thử rời phòng bằng JavaScript cho phanthiet989
2025-05-28 15:22:25.710 +07:00 [ERR] [2025-05-28 15:22:25.710] [ERROR] [phanthiet989]: Lỗi khi vào phòng với mainUser cho phanthiet989: Không thể vào phòng
2025-05-28 15:22:25.717 +07:00 [INF] [2025-05-28 15:22:25.717] [INFO] [phanthiet989]: Hoàn thành JoinRoomCheckMainUserAsync cho phanthiet989, thời gian: 147.0385ms
2025-05-28 15:22:25.718 +07:00 [DBG] [2025-05-28 15:22:25.718] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:25.718 +07:00 [WRN] [2025-05-28 15:22:25.718] [WARNING] [phanthiet989]: JavaScript không thành công, thử phương pháp click thông thường cho phanthiet989
2025-05-28 15:22:25.718 +07:00 [INF] [2025-05-28 15:22:25.718] [INFO] [phanthiet989]: Hoàn thành LeaveRoomWithJavaScript cho phanthiet989, thời gian: 31.2789ms
2025-05-28 15:22:25.718 +07:00 [INF] [2025-05-28 15:22:25.718] [INFO] [phanthiet989]: Hoàn thành LeaveRoomAsync cho phanthiet989, thời gian: 35.0468ms
2025-05-28 15:22:25.718 +07:00 [DBG] [2025-05-28 15:22:25.718] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:25.718 +07:00 [DBG] [2025-05-28 15:22:25.718] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:25.718 +07:00 [DBG] [2025-05-28 15:22:25.718] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:25.718 +07:00 [INF] [2025-05-28 15:22:25.718] [INFO] [phanthiet989]: ✅ Tự động thoát phòng thành công cho phanthiet989
2025-05-28 15:22:25.719 +07:00 [DBG] [2025-05-28 15:22:25.719] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:25.719 +07:00 [DBG] [2025-05-28 15:22:25.719] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:25.729 +07:00 [WRN] [2025-05-28 15:22:25.729] [WARNING] [phanthiet989]: Lỗi khi nhảy bàn lần 1 cho phanthiet989: Không thể vào phòng
2025-05-28 15:22:25.729 +07:00 [INF] [2025-05-28 15:22:25.729] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:25.729 +07:00 [DBG] [2025-05-28 15:22:25.729] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:25.729 +07:00 [DBG] [2025-05-28 15:22:25.729] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:25.729 +07:00 [INF] [2025-05-28 15:22:25.729] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:25.730 +07:00 [DBG] [2025-05-28 15:22:25.730] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:25.730 +07:00 [DBG] [2025-05-28 15:22:25.730] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:25.730 +07:00 [INF] [2025-05-28 15:22:25.730] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:25.730 +07:00 [DBG] [2025-05-28 15:22:25.730] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:25.730 +07:00 [DBG] [2025-05-28 15:22:25.730] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:25.756 +07:00 [INF] [2025-05-28 15:22:25.756] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:25.756 +07:00 [INF] [2025-05-28 15:22:25.756] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:25.756 +07:00 [DBG] [2025-05-28 15:22:25.756] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:25.756 +07:00 [DBG] [2025-05-28 15:22:25.756] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:25.756 +07:00 [INF] [2025-05-28 15:22:25.756] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:25.756 +07:00 [DBG] [2025-05-28 15:22:25.756] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:25.756 +07:00 [DBG] [2025-05-28 15:22:25.756] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:25.756 +07:00 [INF] [2025-05-28 15:22:25.756] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:25.756 +07:00 [DBG] [2025-05-28 15:22:25.756] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:25.756 +07:00 [DBG] [2025-05-28 15:22:25.756] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:25.781 +07:00 [INF] [2025-05-28 15:22:25.781] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:25.788 +07:00 [INF] [2025-05-28 15:22:25.788] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 15:22:25.792 +07:00 [INF] [2025-05-28 15:22:25.792] [INFO] [phanthiet989]: Confirmed room leave for phanthiet989
2025-05-28 15:22:25.797 +07:00 [INF] [2025-05-28 15:22:25.797] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.08 giây
2025-05-28 15:22:25.904 +07:00 [INF] [2025-05-28 15:22:25.904] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:22:25.904 +07:00 [INF] [2025-05-28 15:22:25.904] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5797520875930786, Vị trí: (35, 51)
2025-05-28 15:22:26.435 +07:00 [INF] [2025-05-28 15:22:26.435] [INFO] [phanthiet989]: Thử lần 2/10 nhảy bàn cho phanthiet989
2025-05-28 15:22:26.443 +07:00 [INF] [2025-05-28 15:22:26.443] [INFO] [phanthiet989]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho phanthiet989
2025-05-28 15:22:26.447 +07:00 [INF] [2025-05-28 15:22:26.447] [INFO] [phanthiet989]: Đã tìm thấy canvas cho phanthiet989
2025-05-28 15:22:26.452 +07:00 [INF] [2025-05-28 15:22:26.452] [INFO] [phanthiet989]: Kích thước canvas cho phanthiet989: 684x405
2025-05-28 15:22:26.455 +07:00 [DBG] [2025-05-28 15:22:26.455] [DEBUG] [phanthiet989]: Created TaskCompletionSource for phanthiet989 in JoinRoom at 15:22:26.455
2025-05-28 15:22:26.455 +07:00 [INF] [2025-05-28 15:22:26.455] [INFO] [phanthiet989]: Thử lần 1/2 click vào phòng 100 cho phanthiet989
2025-05-28 15:22:26.458 +07:00 [INF] [2025-05-28 15:22:26.458] [INFO] [phanthiet989]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho phanthiet989
2025-05-28 15:22:26.475 +07:00 [INF] [2025-05-28 15:22:26.475] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,1]
2025-05-28 15:22:26.475 +07:00 [INF] [2025-05-28 15:22:26.475] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,1]
2025-05-28 15:22:26.478 +07:00 [INF] [2025-05-28 15:22:26.478] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,1]
2025-05-28 15:22:26.517 +07:00 [DBG] [2025-05-28 15:22:26.517] [DEBUG] [phanthiet989]: [WebSocket] phanthiet989: Processing cmd 202 (Join Room)
2025-05-28 15:22:26.517 +07:00 [INF] [2025-05-28 15:22:26.517] [INFO] [phanthiet989]: ❌ Không tìm thấy mainUser nhatrang345 cho phanthiet989, tự động thoát phòng
2025-05-28 15:22:26.517 +07:00 [INF] [2025-05-28 15:22:26.517] [INFO] [phanthiet989]: 🚪 Bắt đầu tự động thoát phòng cho phanthiet989 - Lý do: Không tìm thấy mainUser nhatrang345
2025-05-28 15:22:26.517 +07:00 [DBG] [2025-05-28 15:22:26.517] [DEBUG] [phanthiet989]: 🔄 Báo TaskCompletionSource thất bại cho phanthiet989 (shouldAutoLeave=true)
2025-05-28 15:22:26.517 +07:00 [INF] [2025-05-28 15:22:26.517] [INFO] [phanthiet989]: 🔄 Đang thực hiện thoát phòng cho phanthiet989...
2025-05-28 15:22:26.517 +07:00 [INF] [2025-05-28 15:22:26.517] [INFO] [phanthiet989]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho phanthiet989, thử lại ngay
2025-05-28 15:22:26.517 +07:00 [INF] [2025-05-28 15:22:26.517] [INFO] [phanthiet989]: Thử lần 2/2 click vào phòng 100 cho phanthiet989
2025-05-28 15:22:26.523 +07:00 [INF] [2025-05-28 15:22:26.523] [INFO] [phanthiet989]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho phanthiet989
2025-05-28 15:22:26.523 +07:00 [INF] [2025-05-28 15:22:26.523] [INFO] [phanthiet989]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho phanthiet989, thử lại ngay
2025-05-28 15:22:26.523 +07:00 [ERR] [2025-05-28 15:22:26.523] [ERROR] [phanthiet989]: Không thể vào phòng 100 sau 2 lần thử cho phanthiet989
2025-05-28 15:22:26.523 +07:00 [INF] [2025-05-28 15:22:26.523] [INFO] [phanthiet989]: Hoàn thành JoinRoom cho phanthiet989, thời gian: 84.3483ms
2025-05-28 15:22:26.523 +07:00 [WRN] [2025-05-28 15:22:26.523] [WARNING] [phanthiet989]: Vào phòng thất bại cho phanthiet989
2025-05-28 15:22:26.527 +07:00 [INF] [2025-05-28 15:22:26.527] [INFO] [phanthiet989]: Đang thử rời phòng bằng JavaScript cho phanthiet989
2025-05-28 15:22:26.530 +07:00 [ERR] [2025-05-28 15:22:26.530] [ERROR] [phanthiet989]: Lỗi khi vào phòng với mainUser cho phanthiet989: Không thể vào phòng
2025-05-28 15:22:26.537 +07:00 [INF] [2025-05-28 15:22:26.537] [INFO] [phanthiet989]: Hoàn thành JoinRoomCheckMainUserAsync cho phanthiet989, thời gian: 101.0868ms
2025-05-28 15:22:26.537 +07:00 [DBG] [2025-05-28 15:22:26.537] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:26.538 +07:00 [DBG] [2025-05-28 15:22:26.537] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:26.538 +07:00 [WRN] [2025-05-28 15:22:26.538] [WARNING] [phanthiet989]: JavaScript không thành công, thử phương pháp click thông thường cho phanthiet989
2025-05-28 15:22:26.538 +07:00 [DBG] [2025-05-28 15:22:26.538] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:26.538 +07:00 [INF] [2025-05-28 15:22:26.538] [INFO] [phanthiet989]: Hoàn thành LeaveRoomWithJavaScript cho phanthiet989, thời gian: 17.3661ms
2025-05-28 15:22:26.541 +07:00 [INF] [2025-05-28 15:22:26.541] [INFO] [phanthiet989]: Hoàn thành LeaveRoomAsync cho phanthiet989, thời gian: 24.3395ms
2025-05-28 15:22:26.542 +07:00 [DBG] [2025-05-28 15:22:26.542] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:26.545 +07:00 [INF] [2025-05-28 15:22:26.545] [INFO] [phanthiet989]: ✅ Tự động thoát phòng thành công cho phanthiet989
2025-05-28 15:22:26.545 +07:00 [WRN] [2025-05-28 15:22:26.545] [WARNING] [phanthiet989]: Lỗi khi nhảy bàn lần 2 cho phanthiet989: Không thể vào phòng
2025-05-28 15:22:26.546 +07:00 [DBG] [2025-05-28 15:22:26.546] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:26.546 +07:00 [DBG] [2025-05-28 15:22:26.546] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:26.547 +07:00 [INF] [2025-05-28 15:22:26.547] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:26.547 +07:00 [DBG] [2025-05-28 15:22:26.547] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:26.547 +07:00 [DBG] [2025-05-28 15:22:26.547] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:26.547 +07:00 [INF] [2025-05-28 15:22:26.547] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:26.547 +07:00 [DBG] [2025-05-28 15:22:26.547] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:26.547 +07:00 [DBG] [2025-05-28 15:22:26.547] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:26.547 +07:00 [INF] [2025-05-28 15:22:26.547] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:26.547 +07:00 [DBG] [2025-05-28 15:22:26.547] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:26.547 +07:00 [DBG] [2025-05-28 15:22:26.547] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:26.573 +07:00 [INF] [2025-05-28 15:22:26.573] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:26.573 +07:00 [INF] [2025-05-28 15:22:26.573] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:26.573 +07:00 [DBG] [2025-05-28 15:22:26.573] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:26.573 +07:00 [DBG] [2025-05-28 15:22:26.573] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:26.573 +07:00 [INF] [2025-05-28 15:22:26.573] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:26.573 +07:00 [DBG] [2025-05-28 15:22:26.573] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:26.573 +07:00 [DBG] [2025-05-28 15:22:26.573] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:26.573 +07:00 [INF] [2025-05-28 15:22:26.573] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:26.573 +07:00 [DBG] [2025-05-28 15:22:26.573] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:26.573 +07:00 [DBG] [2025-05-28 15:22:26.573] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:26.577 +07:00 [INF] [2025-05-28 15:22:26.577] [INFO] [phanthiet989]: Confirmed room leave for phanthiet989
2025-05-28 15:22:26.598 +07:00 [INF] [2025-05-28 15:22:26.598] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:26.606 +07:00 [INF] [2025-05-28 15:22:26.606] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 15:22:26.613 +07:00 [INF] [2025-05-28 15:22:26.613] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 15:22:26.720 +07:00 [INF] [2025-05-28 15:22:26.720] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:22:26.720 +07:00 [INF] [2025-05-28 15:22:26.720] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5738993287086487, Vị trí: (7, 381)
2025-05-28 15:22:26.993 +07:00 [INF] [2025-05-28 15:22:26.993] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 15:22:26.993 +07:00 [INF] [2025-05-28 15:22:26.993] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 15:22:26.996 +07:00 [INF] [2025-05-28 15:22:26.996] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 15:22:27.248 +07:00 [INF] [2025-05-28 15:22:27.248] [INFO] [phanthiet989]: Thử lần 3/10 nhảy bàn cho phanthiet989
2025-05-28 15:22:27.256 +07:00 [INF] [2025-05-28 15:22:27.256] [INFO] [phanthiet989]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho phanthiet989
2025-05-28 15:22:27.260 +07:00 [INF] [2025-05-28 15:22:27.260] [INFO] [phanthiet989]: Đã tìm thấy canvas cho phanthiet989
2025-05-28 15:22:27.265 +07:00 [INF] [2025-05-28 15:22:27.265] [INFO] [phanthiet989]: Kích thước canvas cho phanthiet989: 684x405
2025-05-28 15:22:27.267 +07:00 [DBG] [2025-05-28 15:22:27.267] [DEBUG] [phanthiet989]: Created TaskCompletionSource for phanthiet989 in JoinRoom at 15:22:27.267
2025-05-28 15:22:27.267 +07:00 [INF] [2025-05-28 15:22:27.267] [INFO] [phanthiet989]: Thử lần 1/2 click vào phòng 100 cho phanthiet989
2025-05-28 15:22:27.271 +07:00 [INF] [2025-05-28 15:22:27.271] [INFO] [phanthiet989]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho phanthiet989
2025-05-28 15:22:27.334 +07:00 [DBG] [2025-05-28 15:22:27.334] [DEBUG] [phanthiet989]: [WebSocket] phanthiet989: Processing cmd 202 (Join Room)
2025-05-28 15:22:27.334 +07:00 [INF] [2025-05-28 15:22:27.334] [INFO] [phanthiet989]: ✅ Tìm thấy mainUser nhatrang345 trong phòng cho phanthiet989 (số người: 2)
2025-05-28 15:22:27.334 +07:00 [DBG] [2025-05-28 15:22:27.334] [DEBUG] [phanthiet989]: ✅ Báo TaskCompletionSource thành công cho phanthiet989 (shouldAutoLeave=false)
2025-05-28 15:22:27.334 +07:00 [INF] [2025-05-28 15:22:27.334] [INFO] [phanthiet989]: Nhận được cmd: 202 cho phanthiet989 (thời gian: 82.4848ms)
2025-05-28 15:22:27.334 +07:00 [INF] [2025-05-28 15:22:27.334] [INFO] [phanthiet989]: 🔄 Bắt đầu sync data từ WebSocketManager cho phanthiet989
2025-05-28 15:22:27.334 +07:00 [INF] [2025-05-28 15:22:27.334] [INFO] [phanthiet989]: ✅ Synced room players for phanthiet989: 2 players
2025-05-28 15:22:27.334 +07:00 [INF] [2025-05-28 15:22:27.334] [INFO] [phanthiet989]: ✅ Synced user seat for phanthiet989: 1
2025-05-28 15:22:27.334 +07:00 [INF] [2025-05-28 15:22:27.334] [INFO] [phanthiet989]: ✅ Synced room time for phanthiet989: 0
2025-05-28 15:22:27.334 +07:00 [INF] [2025-05-28 15:22:27.334] [INFO] [phanthiet989]: 🔄 Hoàn thành sync data cho phanthiet989
2025-05-28 15:22:27.334 +07:00 [INF] [2025-05-28 15:22:27.334] [INFO] [phanthiet989]: ✅ Xác nhận trạng thái phòng cho phanthiet989: sit=1, số người=2
2025-05-28 15:22:27.334 +07:00 [INF] [2025-05-28 15:22:27.334] [INFO] [phanthiet989]: Hoàn thành JoinRoom cho phanthiet989, thời gian: 82.5166ms
2025-05-28 15:22:27.334 +07:00 [INF] [2025-05-28 15:22:27.334] [INFO] [phanthiet989]: User phanthiet989 đã vào phòng thành công với mainUser nhatrang345
2025-05-28 15:22:27.334 +07:00 [INF] [2025-05-28 15:22:27.334] [INFO] [phanthiet989]: Hoàn thành JoinRoomCheckMainUserAsync cho phanthiet989, thời gian: 85.5585ms
2025-05-28 15:22:27.334 +07:00 [DBG] [2025-05-28 15:22:27.334] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:27.334 +07:00 [DBG] [2025-05-28 15:22:27.334] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:27.334 +07:00 [DBG] [2025-05-28 15:22:27.334] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:27.335 +07:00 [INF] [2025-05-28 15:22:27.335] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:27.335 +07:00 [DBG] [2025-05-28 15:22:27.335] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:27.335 +07:00 [DBG] [2025-05-28 15:22:27.335] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:27.335 +07:00 [INF] [2025-05-28 15:22:27.335] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:27.335 +07:00 [DBG] [2025-05-28 15:22:27.335] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:27.335 +07:00 [DBG] [2025-05-28 15:22:27.335] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:27.335 +07:00 [INF] [2025-05-28 15:22:27.335] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:27.335 +07:00 [DBG] [2025-05-28 15:22:27.335] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:27.335 +07:00 [DBG] [2025-05-28 15:22:27.335] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:27.335 +07:00 [INF] [2025-05-28 15:22:27.335] [INFO] [phanthiet989]: User phanthiet989 đã vào bàn thành công với nhatrang345, foundCount = 1
2025-05-28 15:22:27.335 +07:00 [INF] [2025-05-28 15:22:27.335] [INFO] [phanthiet989]: ✅ User phanthiet989 đã vào bàn thành công với nhatrang345, foundCount = 1
2025-05-28 15:22:27.362 +07:00 [INF] [2025-05-28 15:22:27.362] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:27.362 +07:00 [WRN] [2025-05-28 15:22:27.362] [WARNING]: Nhảy bàn chưa hoàn thành, chỉ có 1 user tìm thấy mainUser nhatrang345
2025-05-28 15:22:27.375 +07:00 [INF] [2025-05-28 15:22:27.375] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.04 giây
2025-05-28 15:22:28.788 +07:00 [DBG] [2025-05-28 15:22:28.788] [DEBUG] [nhatrang345]: ❌ Xóa mainUser target cho nhatrang345
2025-05-28 15:22:28.788 +07:00 [DBG] [2025-05-28 15:22:28.788] [DEBUG] [phanthiet989]: ❌ Xóa mainUser target cho phanthiet989
2025-05-28 15:22:28.788 +07:00 [DBG] [2025-05-28 15:22:28.788] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:28.788 +07:00 [DBG] [2025-05-28 15:22:28.788] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:28.789 +07:00 [INF] [2025-05-28 15:22:28.789] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:28.789 +07:00 [DBG] [2025-05-28 15:22:28.789] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:28.789 +07:00 [DBG] [2025-05-28 15:22:28.789] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:28.789 +07:00 [INF] [2025-05-28 15:22:28.789] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:28.789 +07:00 [DBG] [2025-05-28 15:22:28.789] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:28.789 +07:00 [DBG] [2025-05-28 15:22:28.789] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:28.789 +07:00 [INF] [2025-05-28 15:22:28.789] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:28.789 +07:00 [DBG] [2025-05-28 15:22:28.789] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:28.789 +07:00 [DBG] [2025-05-28 15:22:28.789] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:28.814 +07:00 [INF] [2025-05-28 15:22:28.814] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:28.823 +07:00 [INF] [2025-05-28 15:22:28.823] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.03 giây
2025-05-28 15:22:31.422 +07:00 [DBG] [2025-05-28 15:22:31.422] [DEBUG]: [WebSocket] phanthiet989: Processing cmd 600 (Mau Binh cards)
2025-05-28 15:22:31.423 +07:00 [DBG] [2025-05-28 15:22:31.423] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 600 (Mau Binh cards)
2025-05-28 15:22:31.426 +07:00 [INF] [2025-05-28 15:22:31.426] [INFO]: Saved Mau Binh cards for nhatrang345: [9♥, 7♠, 9♠, 4♥, J♠, 3♣, A♥, J♥, 4♣, 6♥, Q♠, 8♠, J♦]
2025-05-28 15:22:31.426 +07:00 [INF] [2025-05-28 15:22:31.426] [INFO]: Saved Mau Binh cards for phanthiet989: [A♣, 9♣, 5♠, 5♣, 7♦, 2♥, 6♠, K♣, 5♥, 10♥, 4♠, 5♦, 4♦]
2025-05-28 15:22:31.492 +07:00 [INF] [2025-05-28 15:22:31.492] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,2]
2025-05-28 15:22:31.492 +07:00 [INF] [2025-05-28 15:22:31.492] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,2]
2025-05-28 15:22:31.492 +07:00 [INF] [2025-05-28 15:22:31.492] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,2]
2025-05-28 15:22:31.991 +07:00 [INF] [2025-05-28 15:22:31.991] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 15:22:31.991 +07:00 [INF] [2025-05-28 15:22:31.991] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 15:22:31.995 +07:00 [INF] [2025-05-28 15:22:31.995] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 15:22:32.033 +07:00 [DBG] [2025-05-28 15:22:32.033] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Mậu Binh
2025-05-28 15:22:32.040 +07:00 [INF] [2025-05-28 15:22:32.040] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 15:22:32.044 +07:00 [DBG] [2025-05-28 15:22:32.044] [DEBUG]: Đã khởi tạo giao diện với 4 panel
2025-05-28 15:22:32.044 +07:00 [DBG] [2025-05-28 15:22:32.044] [DEBUG]: Đã khởi tạo MauBinhSuggestionForm
2025-05-28 15:22:33.466 +07:00 [DBG] [2025-05-28 15:22:33.466] [DEBUG]: Bắt đầu quét bài, số user: 3
2025-05-28 15:22:33.473 +07:00 [INF] [2025-05-28 15:22:33.473] [INFO]: Updated Mau Binh cards for nhatrang345: [J♦, 8♠, Q♠, 6♥, 4♣, J♥, A♥, 3♣, J♠, 4♥, 9♠, 7♠, 9♥]
2025-05-28 15:22:33.473 +07:00 [INF] [2025-05-28 15:22:33.473] [INFO] [nhatrang345]: Đã quét bài cho nhatrang345 bằng script (MauBinhController): [J♦, 8♠, Q♠, 6♥, 4♣, J♥, A♥, 3♣, J♠, 4♥, 9♠, 7♠, 9♥]
2025-05-28 15:22:33.477 +07:00 [INF] [2025-05-28 15:22:33.477] [INFO]: Updated Mau Binh cards for phanthiet989: [4♦, 5♦, 4♠, 10♥, 5♥, K♣, 6♠, 2♥, 7♦, 5♣, 5♠, 9♣, A♣]
2025-05-28 15:22:33.477 +07:00 [INF] [2025-05-28 15:22:33.477] [INFO] [phanthiet989]: Đã quét bài cho phanthiet989 bằng script (MauBinhController): [4♦, 5♦, 4♠, 10♥, 5♥, K♣, 6♠, 2♥, 7♦, 5♣, 5♠, 9♣, A♣]
2025-05-28 15:22:33.477 +07:00 [ERR] [2025-05-28 15:22:33.477] [ERROR]: Không tìm thấy driver cho namdinhx852
2025-05-28 15:22:33.478 +07:00 [WRN] [2025-05-28 15:22:33.478] [WARNING]: Chỉ tìm thấy bài hợp lệ cho 2/3 user
2025-05-28 15:22:33.478 +07:00 [WRN] [2025-05-28 15:22:33.478] [WARNING]: Số user hợp lệ (2) không đủ 3 để tính bài đối thủ
2025-05-28 15:22:33.478 +07:00 [DBG] [2025-05-28 15:22:33.478] [DEBUG]: Bắt đầu UpdateCardDisplay, số user: 2
2025-05-28 15:22:33.478 +07:00 [DBG] [2025-05-28 15:22:33.478] [DEBUG]: Cập nhật bài cho user: nhatrang345, indexKey: user0
2025-05-28 15:22:33.484 +07:00 [DBG] [2025-05-28 15:22:33.484] [DEBUG]: Cập nhật bài cho user: phanthiet989, indexKey: user1
2025-05-28 15:22:33.491 +07:00 [INF] [2025-05-28 15:22:33.491] [INFO]: Quét bài thành công, số user: 2, tổng số lá: 26
2025-05-28 15:22:34.132 +07:00 [INF] [2025-05-28 15:22:34.132] [INFO]: 🚀 Tạo gợi ý với thuật toán OPTIMIZED cho 13 lá bài
2025-05-28 15:22:34.132 +07:00 [INF] [2025-05-28 15:22:34.132] [INFO]: 🚀 Bắt đầu thuật toán Mậu Binh mới theo yêu cầu
2025-05-28 15:22:34.132 +07:00 [INF] [2025-05-28 15:22:34.132] [INFO]: 🥇 Tạo gợi ý ưu tiên Chi 1 mạnh nhất
2025-05-28 15:22:34.187 +07:00 [ERR] [2025-05-28 15:22:34.187] [ERROR]: ❌ Lỗi phân tích Chi1: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.201 +07:00 [ERR] [2025-05-28 15:22:34.201] [ERROR]: ❌ Lỗi GenerateStrategy_Chi1First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.201 +07:00 [INF] [2025-05-28 15:22:34.201] [INFO]: ✅ Chi1 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.201 +07:00 [INF] [2025-05-28 15:22:34.201] [INFO]: 🥈 Tạo gợi ý ưu tiên Chi 2 mạnh nhất
2025-05-28 15:22:34.211 +07:00 [ERR] [2025-05-28 15:22:34.211] [ERROR]: ❌ Lỗi GenerateStrategy_Chi2First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.211 +07:00 [INF] [2025-05-28 15:22:34.211] [INFO]: ✅ Chi2 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.211 +07:00 [INF] [2025-05-28 15:22:34.211] [INFO]: 🥉 Tạo gợi ý ưu tiên Chi 3 mạnh nhất
2025-05-28 15:22:34.224 +07:00 [ERR] [2025-05-28 15:22:34.224] [ERROR]: ❌ Lỗi GenerateStrategy_Chi3First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.224 +07:00 [INF] [2025-05-28 15:22:34.224] [INFO]: ✅ Chi3 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.224 +07:00 [INF] [2025-05-28 15:22:34.224] [INFO]: 🎯 Hoàn thành: 0 gợi ý đa dạng (Chi1:0, Chi2:0, Chi3:0)
2025-05-28 15:22:34.224 +07:00 [WRN] [2025-05-28 15:22:34.224] [WARNING]: 🔄 Fallback sang gợi ý đơn giản
2025-05-28 15:22:34.226 +07:00 [INF] [2025-05-28 15:22:34.226] [INFO]: ✅ Đã tạo gợi ý mặc định
2025-05-28 15:22:34.226 +07:00 [INF] [2025-05-28 15:22:34.226] [INFO] [nhatrang345]: Đã tạo 1 gợi ý cho nhatrang345 (indexKey: user0)
2025-05-28 15:22:34.226 +07:00 [INF] [2025-05-28 15:22:34.226] [INFO]: 📝 AddSuggestion for nhatrang345
2025-05-28 15:22:34.226 +07:00 [INF] [2025-05-28 15:22:34.226] [INFO]: 🚀 Tạo gợi ý với thuật toán OPTIMIZED cho 13 lá bài
2025-05-28 15:22:34.226 +07:00 [INF] [2025-05-28 15:22:34.226] [INFO]: 🚀 Bắt đầu thuật toán Mậu Binh mới theo yêu cầu
2025-05-28 15:22:34.226 +07:00 [INF] [2025-05-28 15:22:34.226] [INFO]: 🥇 Tạo gợi ý ưu tiên Chi 1 mạnh nhất
2025-05-28 15:22:34.234 +07:00 [ERR] [2025-05-28 15:22:34.234] [ERROR]: ❌ Lỗi phân tích Chi1: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.241 +07:00 [ERR] [2025-05-28 15:22:34.241] [ERROR]: ❌ Lỗi GenerateStrategy_Chi1First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.241 +07:00 [INF] [2025-05-28 15:22:34.241] [INFO]: ✅ Chi1 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.241 +07:00 [INF] [2025-05-28 15:22:34.241] [INFO]: 🥈 Tạo gợi ý ưu tiên Chi 2 mạnh nhất
2025-05-28 15:22:34.249 +07:00 [ERR] [2025-05-28 15:22:34.249] [ERROR]: ❌ Lỗi GenerateStrategy_Chi2First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.249 +07:00 [INF] [2025-05-28 15:22:34.249] [INFO]: ✅ Chi2 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.249 +07:00 [INF] [2025-05-28 15:22:34.249] [INFO]: 🥉 Tạo gợi ý ưu tiên Chi 3 mạnh nhất
2025-05-28 15:22:34.257 +07:00 [ERR] [2025-05-28 15:22:34.257] [ERROR]: ❌ Lỗi GenerateStrategy_Chi3First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.257 +07:00 [INF] [2025-05-28 15:22:34.257] [INFO]: ✅ Chi3 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.257 +07:00 [INF] [2025-05-28 15:22:34.257] [INFO]: 🎯 Hoàn thành: 0 gợi ý đa dạng (Chi1:0, Chi2:0, Chi3:0)
2025-05-28 15:22:34.257 +07:00 [WRN] [2025-05-28 15:22:34.257] [WARNING]: 🔄 Fallback sang gợi ý đơn giản
2025-05-28 15:22:34.257 +07:00 [INF] [2025-05-28 15:22:34.257] [INFO]: ✅ Đã tạo gợi ý mặc định
2025-05-28 15:22:34.257 +07:00 [INF] [2025-05-28 15:22:34.257] [INFO] [phanthiet989]: Đã tạo 1 gợi ý cho phanthiet989 (indexKey: user1)
2025-05-28 15:22:34.257 +07:00 [INF] [2025-05-28 15:22:34.257] [INFO]: 📝 AddSuggestion for phanthiet989
2025-05-28 15:22:34.258 +07:00 [DBG] [2025-05-28 15:22:34.258] [DEBUG]: Cập nhật ListBox cho user0, số gợi ý: 1
2025-05-28 15:22:34.267 +07:00 [INF] [2025-05-28 15:22:34.267] [INFO]: 🤝 Team mode: Tự động chọn gợi ý tối ưu cho team dựa trên user0 index 0
2025-05-28 15:22:34.267 +07:00 [INF] [2025-05-28 15:22:34.267] [INFO]: ✅ Tạo gợi ý team tối ưu cho 2 users
2025-05-28 15:22:34.267 +07:00 [INF] [2025-05-28 15:22:34.267] [INFO]: 🚀 Tạo gợi ý với thuật toán OPTIMIZED cho 13 lá bài
2025-05-28 15:22:34.267 +07:00 [INF] [2025-05-28 15:22:34.267] [INFO]: 🚀 Bắt đầu thuật toán Mậu Binh mới theo yêu cầu
2025-05-28 15:22:34.267 +07:00 [INF] [2025-05-28 15:22:34.267] [INFO]: 🥇 Tạo gợi ý ưu tiên Chi 1 mạnh nhất
2025-05-28 15:22:34.278 +07:00 [ERR] [2025-05-28 15:22:34.278] [ERROR]: ❌ Lỗi phân tích Chi1: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.286 +07:00 [ERR] [2025-05-28 15:22:34.286] [ERROR]: ❌ Lỗi GenerateStrategy_Chi1First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.286 +07:00 [INF] [2025-05-28 15:22:34.286] [INFO]: ✅ Chi1 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.286 +07:00 [INF] [2025-05-28 15:22:34.286] [INFO]: 🥈 Tạo gợi ý ưu tiên Chi 2 mạnh nhất
2025-05-28 15:22:34.294 +07:00 [ERR] [2025-05-28 15:22:34.294] [ERROR]: ❌ Lỗi GenerateStrategy_Chi2First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.294 +07:00 [INF] [2025-05-28 15:22:34.294] [INFO]: ✅ Chi2 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.294 +07:00 [INF] [2025-05-28 15:22:34.294] [INFO]: 🥉 Tạo gợi ý ưu tiên Chi 3 mạnh nhất
2025-05-28 15:22:34.302 +07:00 [ERR] [2025-05-28 15:22:34.302] [ERROR]: ❌ Lỗi GenerateStrategy_Chi3First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.302 +07:00 [INF] [2025-05-28 15:22:34.302] [INFO]: ✅ Chi3 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.302 +07:00 [INF] [2025-05-28 15:22:34.302] [INFO]: 🎯 Hoàn thành: 0 gợi ý đa dạng (Chi1:0, Chi2:0, Chi3:0)
2025-05-28 15:22:34.302 +07:00 [WRN] [2025-05-28 15:22:34.302] [WARNING]: 🔄 Fallback sang gợi ý đơn giản
2025-05-28 15:22:34.302 +07:00 [INF] [2025-05-28 15:22:34.302] [INFO]: ✅ Đã tạo gợi ý mặc định
2025-05-28 15:22:34.312 +07:00 [ERR] [2025-05-28 15:22:34.312] [ERROR]: ❌ Lỗi auto-select team suggestions: InvalidArgument=Value of '0' is not valid for 'SelectedIndex'. (Parameter 'value')
Actual value was 0.
2025-05-28 15:22:34.312 +07:00 [DBG] [2025-05-28 15:22:34.312] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 15:22:34.312 +07:00 [INF] [2025-05-28 15:22:34.312] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 15:22:34.312 +07:00 [DBG] [2025-05-28 15:22:34.312] [DEBUG]: Đã chọn gợi ý đầu tiên cho user0
2025-05-28 15:22:34.312 +07:00 [DBG] [2025-05-28 15:22:34.312] [DEBUG]: Cập nhật ListBox cho user1, số gợi ý: 1
2025-05-28 15:22:34.320 +07:00 [INF] [2025-05-28 15:22:34.320] [INFO]: 🤝 Team mode: Tự động chọn gợi ý tối ưu cho team dựa trên user1 index 0
2025-05-28 15:22:34.320 +07:00 [INF] [2025-05-28 15:22:34.320] [INFO]: ✅ Tạo gợi ý team tối ưu cho 2 users
2025-05-28 15:22:34.320 +07:00 [INF] [2025-05-28 15:22:34.320] [INFO]: 🚀 Tạo gợi ý với thuật toán OPTIMIZED cho 13 lá bài
2025-05-28 15:22:34.320 +07:00 [INF] [2025-05-28 15:22:34.320] [INFO]: 🚀 Bắt đầu thuật toán Mậu Binh mới theo yêu cầu
2025-05-28 15:22:34.320 +07:00 [INF] [2025-05-28 15:22:34.320] [INFO]: 🥇 Tạo gợi ý ưu tiên Chi 1 mạnh nhất
2025-05-28 15:22:34.328 +07:00 [ERR] [2025-05-28 15:22:34.328] [ERROR]: ❌ Lỗi phân tích Chi1: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.336 +07:00 [ERR] [2025-05-28 15:22:34.336] [ERROR]: ❌ Lỗi GenerateStrategy_Chi1First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.336 +07:00 [INF] [2025-05-28 15:22:34.336] [INFO]: ✅ Chi1 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.336 +07:00 [INF] [2025-05-28 15:22:34.336] [INFO]: 🥈 Tạo gợi ý ưu tiên Chi 2 mạnh nhất
2025-05-28 15:22:34.344 +07:00 [ERR] [2025-05-28 15:22:34.344] [ERROR]: ❌ Lỗi GenerateStrategy_Chi2First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.344 +07:00 [INF] [2025-05-28 15:22:34.344] [INFO]: ✅ Chi2 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.344 +07:00 [INF] [2025-05-28 15:22:34.344] [INFO]: 🥉 Tạo gợi ý ưu tiên Chi 3 mạnh nhất
2025-05-28 15:22:34.352 +07:00 [ERR] [2025-05-28 15:22:34.352] [ERROR]: ❌ Lỗi GenerateStrategy_Chi3First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:34.352 +07:00 [INF] [2025-05-28 15:22:34.352] [INFO]: ✅ Chi3 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:34.352 +07:00 [INF] [2025-05-28 15:22:34.352] [INFO]: 🎯 Hoàn thành: 0 gợi ý đa dạng (Chi1:0, Chi2:0, Chi3:0)
2025-05-28 15:22:34.352 +07:00 [WRN] [2025-05-28 15:22:34.352] [WARNING]: 🔄 Fallback sang gợi ý đơn giản
2025-05-28 15:22:34.352 +07:00 [INF] [2025-05-28 15:22:34.352] [INFO]: ✅ Đã tạo gợi ý mặc định
2025-05-28 15:22:34.358 +07:00 [INF] [2025-05-28 15:22:34.358] [INFO]: 🎯 Auto-selected suggestion 1 for nhatrang345 (team optimization)
2025-05-28 15:22:34.358 +07:00 [DBG] [2025-05-28 15:22:34.358] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 15:22:34.358 +07:00 [INF] [2025-05-28 15:22:34.358] [INFO]: Đã chọn gợi ý 1 cho user1
2025-05-28 15:22:34.358 +07:00 [DBG] [2025-05-28 15:22:34.358] [DEBUG]: Đã chọn gợi ý đầu tiên cho user1
2025-05-28 15:22:34.358 +07:00 [DBG] [2025-05-28 15:22:34.358] [DEBUG]: Không có gợi ý để hiển thị cho user2
2025-05-28 15:22:34.358 +07:00 [DBG] [2025-05-28 15:22:34.358] [DEBUG]: Không có gợi ý để hiển thị cho opponent
2025-05-28 15:22:34.358 +07:00 [DBG] [2025-05-28 15:22:34.358] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 15:22:34.358 +07:00 [INF] [2025-05-28 15:22:34.358] [INFO]: Đã cập nhật danh sách gợi ý
2025-05-28 15:22:35.764 +07:00 [INF] [2025-05-28 15:22:35.764] [INFO]: 🤝 Team mode: Tự động chọn gợi ý tối ưu cho team dựa trên user1 index 0
2025-05-28 15:22:35.764 +07:00 [INF] [2025-05-28 15:22:35.764] [INFO]: ✅ Tạo gợi ý team tối ưu cho 2 users
2025-05-28 15:22:35.764 +07:00 [INF] [2025-05-28 15:22:35.764] [INFO]: 🚀 Tạo gợi ý với thuật toán OPTIMIZED cho 13 lá bài
2025-05-28 15:22:35.764 +07:00 [INF] [2025-05-28 15:22:35.764] [INFO]: 🚀 Bắt đầu thuật toán Mậu Binh mới theo yêu cầu
2025-05-28 15:22:35.764 +07:00 [INF] [2025-05-28 15:22:35.764] [INFO]: 🥇 Tạo gợi ý ưu tiên Chi 1 mạnh nhất
2025-05-28 15:22:35.775 +07:00 [ERR] [2025-05-28 15:22:35.775] [ERROR]: ❌ Lỗi phân tích Chi1: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:35.783 +07:00 [ERR] [2025-05-28 15:22:35.783] [ERROR]: ❌ Lỗi GenerateStrategy_Chi1First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:35.783 +07:00 [INF] [2025-05-28 15:22:35.783] [INFO]: ✅ Chi1 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:35.783 +07:00 [INF] [2025-05-28 15:22:35.783] [INFO]: 🥈 Tạo gợi ý ưu tiên Chi 2 mạnh nhất
2025-05-28 15:22:35.792 +07:00 [ERR] [2025-05-28 15:22:35.792] [ERROR]: ❌ Lỗi GenerateStrategy_Chi2First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:35.792 +07:00 [INF] [2025-05-28 15:22:35.792] [INFO]: ✅ Chi2 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:35.792 +07:00 [INF] [2025-05-28 15:22:35.792] [INFO]: 🥉 Tạo gợi ý ưu tiên Chi 3 mạnh nhất
2025-05-28 15:22:35.800 +07:00 [ERR] [2025-05-28 15:22:35.800] [ERROR]: ❌ Lỗi GenerateStrategy_Chi3First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 15:22:35.800 +07:00 [INF] [2025-05-28 15:22:35.800] [INFO]: ✅ Chi3 ưu tiên: 0/3 gợi ý hợp lệ
2025-05-28 15:22:35.800 +07:00 [INF] [2025-05-28 15:22:35.800] [INFO]: 🎯 Hoàn thành: 0 gợi ý đa dạng (Chi1:0, Chi2:0, Chi3:0)
2025-05-28 15:22:35.800 +07:00 [WRN] [2025-05-28 15:22:35.800] [WARNING]: 🔄 Fallback sang gợi ý đơn giản
2025-05-28 15:22:35.800 +07:00 [INF] [2025-05-28 15:22:35.800] [INFO]: ✅ Đã tạo gợi ý mặc định
2025-05-28 15:22:35.807 +07:00 [INF] [2025-05-28 15:22:35.807] [INFO]: 🎯 Auto-selected suggestion 1 for nhatrang345 (team optimization)
2025-05-28 15:22:35.807 +07:00 [DBG] [2025-05-28 15:22:35.807] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 15:22:35.807 +07:00 [INF] [2025-05-28 15:22:35.807] [INFO]: Đã chọn gợi ý 1 cho user1
2025-05-28 15:22:36.493 +07:00 [INF] [2025-05-28 15:22:36.492] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,3]
2025-05-28 15:22:36.493 +07:00 [INF] [2025-05-28 15:22:36.493] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,3]
2025-05-28 15:22:36.498 +07:00 [INF] [2025-05-28 15:22:36.498] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,3]
2025-05-28 15:22:36.992 +07:00 [INF] [2025-05-28 15:22:36.992] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 15:22:36.992 +07:00 [INF] [2025-05-28 15:22:36.992] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 15:22:36.993 +07:00 [INF] [2025-05-28 15:22:36.993] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 15:22:41.493 +07:00 [INF] [2025-05-28 15:22:41.493] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,4]
2025-05-28 15:22:41.493 +07:00 [INF] [2025-05-28 15:22:41.493] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,4]
2025-05-28 15:22:41.495 +07:00 [INF] [2025-05-28 15:22:41.495] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,4]
2025-05-28 15:22:41.992 +07:00 [INF] [2025-05-28 15:22:41.992] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 15:22:41.992 +07:00 [INF] [2025-05-28 15:22:41.992] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 15:22:41.993 +07:00 [INF] [2025-05-28 15:22:41.993] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 15:22:46.493 +07:00 [INF] [2025-05-28 15:22:46.493] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,5]
2025-05-28 15:22:46.493 +07:00 [INF] [2025-05-28 15:22:46.493] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,5]
2025-05-28 15:22:46.494 +07:00 [INF] [2025-05-28 15:22:46.494] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,5]
2025-05-28 15:22:46.993 +07:00 [INF] [2025-05-28 15:22:46.993] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 15:22:46.993 +07:00 [INF] [2025-05-28 15:22:46.993] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 15:22:46.994 +07:00 [INF] [2025-05-28 15:22:46.994] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 15:22:48.407 +07:00 [INF] [2025-05-28 15:22:48.407] [INFO] [nhatrang345]: Bắt đầu xếp bài cho nhatrang345 theo gợi ý: [A♥, Q♠, J♠, J♦, J♥, 9♠, 9♥, 8♠, 7♠, 6♥, 4♣, 4♥, 3♣]
2025-05-28 15:22:48.413 +07:00 [INF] [2025-05-28 15:22:48.413] [INFO]: Kết quả xếp bài cho nhatrang345: Đã xếp bài thành công và đang cố gắng cập nhật UI
2025-05-28 15:22:49.416 +07:00 [INF] [2025-05-28 15:22:49.416] [INFO]: Sắp xếp bài thành công cho nhatrang345: 3,44,40,42,43,32,35,28,24,23,13,15,9
2025-05-28 15:22:49.416 +07:00 [INF] [2025-05-28 15:22:49.416] [INFO] [nhatrang345]: Đã xếp bài cho nhatrang345 theo gợi ý: [A♥, Q♠, J♠, J♦, J♥, 9♠, 9♥, 8♠, 7♠, 6♥, 4♣, 4♥, 3♣]
2025-05-28 15:22:51.497 +07:00 [INF] [2025-05-28 15:22:51.497] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,6]
2025-05-28 15:22:51.497 +07:00 [INF] [2025-05-28 15:22:51.497] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,6]
2025-05-28 15:22:51.497 +07:00 [INF] [2025-05-28 15:22:51.497] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,6]
2025-05-28 15:22:51.994 +07:00 [INF] [2025-05-28 15:22:51.994] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 15:22:51.994 +07:00 [INF] [2025-05-28 15:22:51.994] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 15:22:51.994 +07:00 [INF] [2025-05-28 15:22:51.994] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 15:22:53.625 +07:00 [DBG] [2025-05-28 15:22:53.625] [DEBUG]: [WebSocket] phanthiet989: Processing cmd 603 (Mau Binh card update)
2025-05-28 15:22:53.625 +07:00 [WRN] [2025-05-28 15:22:53.625] [WARNING]: Cmd 603 for phanthiet989 lacks 'cs' field
2025-05-28 15:22:53.626 +07:00 [DBG] [2025-05-28 15:22:53.626] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 15:22:53.626 +07:00 [WRN] [2025-05-28 15:22:53.626] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 15:22:55.620 +07:00 [INF] [2025-05-28 15:22:55.620] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 602 (Mau Binh game end)
2025-05-28 15:22:55.620 +07:00 [INF] [2025-05-28 15:22:55.620] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Processing cmd 602 (Mau Binh game end)
2025-05-28 15:22:55.621 +07:00 [INF] [2025-05-28 15:22:55.621] [INFO] [nhatrang345]: Game result for nhatrang345: Lose: -800 (Chi: 3)
2025-05-28 15:22:55.621 +07:00 [INF] [2025-05-28 15:22:55.621] [INFO] [nhatrang345]: Game result for nhatrang345: Lose: -800 (Chi: 3)
2025-05-28 15:22:55.621 +07:00 [INF] [2025-05-28 15:22:55.621] [INFO] [phanthiet989]: Game result for phanthiet989: Win: +784 (Chi: 3)
2025-05-28 15:22:55.622 +07:00 [INF] [2025-05-28 15:22:55.622] [INFO] [phanthiet989]: Saved opponent hand for phanthiet989: Chi 1: [9♣, 5♥, 5♦, 5♣, 5♠], Chi 2: [7♦, 6♠, 2♥, 4♦, 4♠], Chi 3: [A♣, K♣, 10♥], Score: 784
2025-05-28 15:22:55.622 +07:00 [INF] [2025-05-28 15:22:55.622] [INFO] [nhatrang345]: Saved opponent hand for nhatrang345: Chi 1: [A♥, J♥, 9♥, 6♥, 4♥], Chi 2: [Q♠, J♠, 9♠, 8♠, 7♠], Chi 3: [J♦, 4♣, 3♣], Score: -800
2025-05-28 15:22:55.622 +07:00 [DBG] [2025-05-28 15:22:55.622] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:55.622 +07:00 [INF] [2025-05-28 15:22:55.622] [INFO] [phanthiet989]: Game result for phanthiet989: Win: +784 (Chi: 3)
2025-05-28 15:22:55.622 +07:00 [DBG] [2025-05-28 15:22:55.622] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:55.622 +07:00 [INF] [2025-05-28 15:22:55.622] [INFO]: Waiting 8 seconds before clearing Mau Binh data...
2025-05-28 15:22:55.622 +07:00 [INF] [2025-05-28 15:22:55.622] [INFO]: Waiting 8 seconds before clearing Mau Binh data...
2025-05-28 15:22:55.622 +07:00 [DBG] [2025-05-28 15:22:55.622] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:55.622 +07:00 [DBG] [2025-05-28 15:22:55.622] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:55.622 +07:00 [DBG] [2025-05-28 15:22:55.622] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:22:55.622 +07:00 [DBG] [2025-05-28 15:22:55.622] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:22:55.623 +07:00 [INF] [2025-05-28 15:22:55.623] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:55.623 +07:00 [DBG] [2025-05-28 15:22:55.623] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:55.623 +07:00 [DBG] [2025-05-28 15:22:55.623] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:55.623 +07:00 [INF] [2025-05-28 15:22:55.623] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:55.623 +07:00 [DBG] [2025-05-28 15:22:55.623] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:55.623 +07:00 [DBG] [2025-05-28 15:22:55.623] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:55.623 +07:00 [INF] [2025-05-28 15:22:55.623] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:55.623 +07:00 [DBG] [2025-05-28 15:22:55.623] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:55.623 +07:00 [DBG] [2025-05-28 15:22:55.623] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:55.696 +07:00 [INF] [2025-05-28 15:22:55.696] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:55.696 +07:00 [INF] [2025-05-28 15:22:55.696] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:22:55.696 +07:00 [DBG] [2025-05-28 15:22:55.696] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:22:55.696 +07:00 [DBG] [2025-05-28 15:22:55.696] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:55.696 +07:00 [INF] [2025-05-28 15:22:55.696] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Mở
2025-05-28 15:22:55.696 +07:00 [DBG] [2025-05-28 15:22:55.696] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:22:55.696 +07:00 [DBG] [2025-05-28 15:22:55.696] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:55.696 +07:00 [INF] [2025-05-28 15:22:55.696] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:22:55.696 +07:00 [DBG] [2025-05-28 15:22:55.696] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:22:55.696 +07:00 [DBG] [2025-05-28 15:22:55.696] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:22:55.773 +07:00 [INF] [2025-05-28 15:22:55.773] [INFO]: Tải danh sách user thành công
2025-05-28 15:22:55.793 +07:00 [INF] [2025-05-28 15:22:55.793] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.17 giây
2025-05-28 15:22:55.818 +07:00 [INF] [2025-05-28 15:22:55.818] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.20 giây
2025-05-28 15:22:56.494 +07:00 [INF] [2025-05-28 15:22:56.494] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,7]
2025-05-28 15:22:56.494 +07:00 [INF] [2025-05-28 15:22:56.494] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,7]
2025-05-28 15:22:56.509 +07:00 [INF] [2025-05-28 15:22:56.509] [INFO] [phanthiet989]: [WebSocket] phanthiet989: Skipping control message (type 6): [6,1,7]
2025-05-28 15:22:56.993 +07:00 [INF] [2025-05-28 15:22:56.993] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 15:22:56.993 +07:00 [INF] [2025-05-28 15:22:56.993] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 15:22:56.994 +07:00 [INF] [2025-05-28 15:22:56.994] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 15:23:03.636 +07:00 [INF] [2025-05-28 15:23:03.636] [INFO]: 🗑️ ClearSuggestions
2025-05-28 15:23:03.636 +07:00 [INF] [2025-05-28 15:23:03.636] [INFO]: 🗑️ ClearSuggestions
2025-05-28 15:23:10.375 +07:00 [INF] [2025-05-28 15:23:10.375] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 15:23:10.375 +07:00 [INF] [2025-05-28 15:23:10.375] [INFO] [phanthiet989]: Đang đóng profile cho phanthiet989...
2025-05-28 15:23:10.392 +07:00 [DBG] [2025-05-28 15:23:10.392] [DEBUG] [phanthiet989]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/close/393edc55-e67b-4bc9-9708-c57613890575: Thành công
2025-05-28 15:23:10.392 +07:00 [INF] [2025-05-28 15:23:10.392] [INFO] [phanthiet989]: Đã đóng profile cho phanthiet989. Thời gian: 16ms
2025-05-28 15:23:10.392 +07:00 [DBG] [2025-05-28 15:23:10.392] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:23:10.392 +07:00 [DBG] [2025-05-28 15:23:10.392] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:23:10.401 +07:00 [INF] [2025-05-28 15:23:10.401] [INFO] [phanthiet989]: Đã đóng ChromeDriver cho phanthiet989
2025-05-28 15:23:10.401 +07:00 [INF] [2025-05-28 15:23:10.401] [INFO] [phanthiet989]: Đã đóng driver và xóa trạng thái cho phanthiet989
2025-05-28 15:23:10.401 +07:00 [DBG] [2025-05-28 15:23:10.401] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:23:10.402 +07:00 [DBG] [2025-05-28 15:23:10.402] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:23:10.402 +07:00 [INF] [2025-05-28 15:23:10.402] [INFO] [phanthiet989]: Đã đóng profile cho phanthiet989
2025-05-28 15:23:10.402 +07:00 [DBG] [2025-05-28 15:23:10.402] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:23:10.402 +07:00 [DBG] [2025-05-28 15:23:10.402] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:23:10.402 +07:00 [INF] [2025-05-28 15:23:10.402] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:23:10.402 +07:00 [DBG] [2025-05-28 15:23:10.402] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:23:10.402 +07:00 [DBG] [2025-05-28 15:23:10.402] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.402 +07:00 [INF] [2025-05-28 15:23:10.402] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:23:10.402 +07:00 [DBG] [2025-05-28 15:23:10.402] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:23:10.402 +07:00 [DBG] [2025-05-28 15:23:10.402] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.402 +07:00 [INF] [2025-05-28 15:23:10.402] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:23:10.402 +07:00 [DBG] [2025-05-28 15:23:10.402] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:23:10.402 +07:00 [DBG] [2025-05-28 15:23:10.402] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.449 +07:00 [INF] [2025-05-28 15:23:10.449] [INFO]: Tải danh sách user thành công
2025-05-28 15:23:10.449 +07:00 [INF] [2025-05-28 15:23:10.449] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:23:10.449 +07:00 [DBG] [2025-05-28 15:23:10.449] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:23:10.449 +07:00 [DBG] [2025-05-28 15:23:10.449] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.449 +07:00 [INF] [2025-05-28 15:23:10.449] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:23:10.449 +07:00 [DBG] [2025-05-28 15:23:10.449] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:23:10.449 +07:00 [DBG] [2025-05-28 15:23:10.449] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.449 +07:00 [INF] [2025-05-28 15:23:10.449] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:23:10.449 +07:00 [DBG] [2025-05-28 15:23:10.449] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:23:10.449 +07:00 [DBG] [2025-05-28 15:23:10.449] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.489 +07:00 [INF] [2025-05-28 15:23:10.489] [INFO]: Tải danh sách user thành công
2025-05-28 15:23:10.489 +07:00 [INF] [2025-05-28 15:23:10.489] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:23:10.489 +07:00 [DBG] [2025-05-28 15:23:10.489] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:23:10.489 +07:00 [DBG] [2025-05-28 15:23:10.489] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.489 +07:00 [INF] [2025-05-28 15:23:10.489] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:23:10.489 +07:00 [DBG] [2025-05-28 15:23:10.489] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:23:10.489 +07:00 [DBG] [2025-05-28 15:23:10.489] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.489 +07:00 [INF] [2025-05-28 15:23:10.489] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:23:10.489 +07:00 [DBG] [2025-05-28 15:23:10.489] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:23:10.489 +07:00 [DBG] [2025-05-28 15:23:10.489] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.525 +07:00 [INF] [2025-05-28 15:23:10.525] [INFO]: Tải danh sách user thành công
2025-05-28 15:23:10.535 +07:00 [INF] [2025-05-28 15:23:10.535] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.14 giây
2025-05-28 15:23:10.546 +07:00 [INF] [2025-05-28 15:23:10.546] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.14 giây
2025-05-28 15:23:10.557 +07:00 [INF] [2025-05-28 15:23:10.557] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.16 giây
2025-05-28 15:23:10.764 +07:00 [INF] [2025-05-28 15:23:10.764] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 15:23:10.764 +07:00 [INF] [2025-05-28 15:23:10.764] [INFO] [nhatrang345]: Đang đóng profile cho nhatrang345...
2025-05-28 15:23:10.775 +07:00 [DBG] [2025-05-28 15:23:10.775] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/close/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 15:23:10.775 +07:00 [INF] [2025-05-28 15:23:10.775] [INFO] [nhatrang345]: Đã đóng profile cho nhatrang345. Thời gian: 11ms
2025-05-28 15:23:10.775 +07:00 [DBG] [2025-05-28 15:23:10.775] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:23:10.775 +07:00 [DBG] [2025-05-28 15:23:10.775] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:23:10.781 +07:00 [INF] [2025-05-28 15:23:10.781] [INFO] [nhatrang345]: Đã đóng ChromeDriver cho nhatrang345
2025-05-28 15:23:10.781 +07:00 [INF] [2025-05-28 15:23:10.781] [INFO] [nhatrang345]: Đã đóng driver và xóa trạng thái cho nhatrang345
2025-05-28 15:23:10.781 +07:00 [DBG] [2025-05-28 15:23:10.781] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:23:10.781 +07:00 [DBG] [2025-05-28 15:23:10.781] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:23:10.781 +07:00 [INF] [2025-05-28 15:23:10.781] [INFO] [nhatrang345]: Đã đóng profile cho nhatrang345
2025-05-28 15:23:10.781 +07:00 [DBG] [2025-05-28 15:23:10.781] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:23:10.781 +07:00 [DBG] [2025-05-28 15:23:10.781] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:23:10.781 +07:00 [INF] [2025-05-28 15:23:10.781] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:23:10.781 +07:00 [DBG] [2025-05-28 15:23:10.781] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:23:10.782 +07:00 [DBG] [2025-05-28 15:23:10.782] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.782 +07:00 [INF] [2025-05-28 15:23:10.782] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:23:10.782 +07:00 [DBG] [2025-05-28 15:23:10.782] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:23:10.782 +07:00 [DBG] [2025-05-28 15:23:10.782] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.782 +07:00 [INF] [2025-05-28 15:23:10.782] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:23:10.782 +07:00 [DBG] [2025-05-28 15:23:10.782] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:23:10.782 +07:00 [DBG] [2025-05-28 15:23:10.782] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.824 +07:00 [INF] [2025-05-28 15:23:10.824] [INFO]: Tải danh sách user thành công
2025-05-28 15:23:10.824 +07:00 [INF] [2025-05-28 15:23:10.824] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:23:10.824 +07:00 [DBG] [2025-05-28 15:23:10.824] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:23:10.824 +07:00 [DBG] [2025-05-28 15:23:10.824] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.824 +07:00 [INF] [2025-05-28 15:23:10.824] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:23:10.824 +07:00 [DBG] [2025-05-28 15:23:10.824] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:23:10.824 +07:00 [DBG] [2025-05-28 15:23:10.824] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.824 +07:00 [INF] [2025-05-28 15:23:10.824] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:23:10.824 +07:00 [DBG] [2025-05-28 15:23:10.824] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:23:10.824 +07:00 [DBG] [2025-05-28 15:23:10.824] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.861 +07:00 [INF] [2025-05-28 15:23:10.861] [INFO]: Tải danh sách user thành công
2025-05-28 15:23:10.861 +07:00 [INF] [2025-05-28 15:23:10.861] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:23:10.861 +07:00 [DBG] [2025-05-28 15:23:10.861] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:23:10.862 +07:00 [DBG] [2025-05-28 15:23:10.862] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.862 +07:00 [INF] [2025-05-28 15:23:10.862] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:23:10.862 +07:00 [DBG] [2025-05-28 15:23:10.862] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:23:10.862 +07:00 [DBG] [2025-05-28 15:23:10.862] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.862 +07:00 [INF] [2025-05-28 15:23:10.862] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:23:10.862 +07:00 [DBG] [2025-05-28 15:23:10.862] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:23:10.862 +07:00 [DBG] [2025-05-28 15:23:10.862] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:23:10.900 +07:00 [INF] [2025-05-28 15:23:10.900] [INFO]: Tải danh sách user thành công
2025-05-28 15:23:10.912 +07:00 [INF] [2025-05-28 15:23:10.912] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.14 giây
2025-05-28 15:23:10.923 +07:00 [INF] [2025-05-28 15:23:10.923] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.14 giây
2025-05-28 15:23:10.933 +07:00 [INF] [2025-05-28 15:23:10.933] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.15 giây
2025-05-28 15:23:11.897 +07:00 [DBG] [2025-05-28 15:23:11.897] [DEBUG]: Bắt đầu đóng Form1
2025-05-28 15:23:11.905 +07:00 [INF] [2025-05-28 15:23:11.905] [INFO]: MauBinhCardManager disposed
2025-05-28 15:23:11.905 +07:00 [INF] [2025-05-28 15:23:11.905] [INFO]: Đã đóng Form1 thành công
2025-05-28 15:23:11.914 +07:00 [INF] Application started successfully.
2025-05-28 15:23:13.300 +07:00 [INF] Starting AutoGameBai application...
