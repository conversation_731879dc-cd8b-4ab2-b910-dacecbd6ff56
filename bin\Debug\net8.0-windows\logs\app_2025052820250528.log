2025-05-28 15:17:13.691 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 15:17:16.170 +07:00 [INF] User selected: HitClub - M<PERSON><PERSON> Binh
2025-05-28 15:17:16.173 +07:00 [INF] Form1 constructor started.
2025-05-28 15:17:16.190 +07:00 [DBG] [2025-05-28 15:17:16.190] [DEBUG]: Gọi InitializeComponent
2025-05-28 15:17:16.200 +07:00 [INF] [2025-05-28 15:17:16.200] [INFO]: Khởi tạo UIManager thành công
2025-05-28 15:17:16.201 +07:00 [DBG] [2025-05-28 15:17:16.201] [DEBUG]: <PERSON><PERSON><PERSON> đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 15:17:16.203 +07:00 [INF] [2025-05-28 15:17:16.203] [INFO]: Đ<PERSON> khởi tạo cột cho dataGridViewUsers
2025-05-28 15:17:16.203 +07:00 [DBG] [2025-05-28 15:17:16.203] [DEBUG]: <PERSON><PERSON><PERSON> đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 15:17:16.203 +07:00 [INF] [2025-05-28 15:17:16.203] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 15:17:16.204 +07:00 [INF] [2025-05-28 15:17:16.204] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 15:17:16.216 +07:00 [DBG] [2025-05-28 15:17:16.216] [DEBUG]: Bắt đầu OnLoad
2025-05-28 15:17:16.216 +07:00 [DBG] [2025-05-28 15:17:16.216] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 15:17:16.232 +07:00 [INF] [2025-05-28 15:17:16.232] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11823
2025-05-28 15:17:16.232 +07:00 [INF] [2025-05-28 15:17:16.232] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 15:17:16.247 +07:00 [INF] [2025-05-28 15:17:16.247] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 15:17:16.249 +07:00 [INF] [2025-05-28 15:17:16.249] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 15:17:16.249 +07:00 [INF] [2025-05-28 15:17:16.249] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 15:17:16.249 +07:00 [INF] [2025-05-28 15:17:16.249] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 15:17:16.250 +07:00 [INF] [2025-05-28 15:17:16.250] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 15:17:16.250 +07:00 [INF] [2025-05-28 15:17:16.250] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 15:17:16.250 +07:00 [INF] InitializeAsync started.
2025-05-28 15:17:16.250 +07:00 [INF] [2025-05-28 15:17:16.250] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 15:17:16.255 +07:00 [DBG] [2025-05-28 15:17:16.255] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 15:17:16.257 +07:00 [DBG] [2025-05-28 15:17:16.257] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 15:17:16.258 +07:00 [DBG] [2025-05-28 15:17:16.258] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:17:16.258 +07:00 [DBG] [2025-05-28 15:17:16.258] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:17:16.265 +07:00 [DBG] [2025-05-28 15:17:16.265] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:17:16.265 +07:00 [INF] [2025-05-28 15:17:16.265] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 15:17:16.265 +07:00 [DBG] [2025-05-28 15:17:16.265] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:16.267 +07:00 [INF] [2025-05-28 15:17:16.267] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:16.267 +07:00 [INF] [2025-05-28 15:17:16.267] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:17:16.267 +07:00 [DBG] [2025-05-28 15:17:16.267] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:16.313 +07:00 [INF] [2025-05-28 15:17:16.313] [INFO]: Tải danh sách user thành công
2025-05-28 15:17:16.322 +07:00 [INF] [2025-05-28 15:17:16.322] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 15:17:16.322 +07:00 [DBG] [2025-05-28 15:17:16.322] [DEBUG]: OnLoad hoàn tất
2025-05-28 15:17:17.358 +07:00 [INF] [2025-05-28 15:17:17.358] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 15:17:17.365 +07:00 [INF] [2025-05-28 15:17:17.365] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 15:17:17.366 +07:00 [INF] [2025-05-28 15:17:17.366] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 15:17:17.368 +07:00 [DBG] [2025-05-28 15:17:17.368] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 15:17:17.410 +07:00 [INF] [2025-05-28 15:17:17.410] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 43ms
2025-05-28 15:17:17.427 +07:00 [DBG] [2025-05-28 15:17:17.427] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 15:17:17.428 +07:00 [INF] [2025-05-28 15:17:17.428] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:60861. Thời gian: 16ms
2025-05-28 15:17:17.428 +07:00 [DBG] [2025-05-28 15:17:17.428] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:17:17.428 +07:00 [DBG] [2025-05-28 15:17:17.428] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:17:17.428 +07:00 [INF] [2025-05-28 15:17:17.428] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 62ms
2025-05-28 15:17:17.428 +07:00 [DBG] [2025-05-28 15:17:17.428] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:17:17.428 +07:00 [DBG] [2025-05-28 15:17:17.428] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:17:18.565 +07:00 [INF] [2025-05-28 15:17:18.564] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 15:17:18.865 +07:00 [INF] [2025-05-28 15:17:18.865] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 15:17:19.000 +07:00 [INF] [2025-05-28 15:17:18.999] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 15:17:19.044 +07:00 [DBG] [2025-05-28 15:17:19.044] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 15:17:19.047 +07:00 [INF] [2025-05-28 15:17:19.047] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 15:17:19.048 +07:00 [INF] [2025-05-28 15:17:19.048] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 15:17:19.051 +07:00 [INF] [2025-05-28 15:17:19.051] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 15:17:19.051 +07:00 [INF] [2025-05-28 15:17:19.051] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:17:19.051 +07:00 [DBG] [2025-05-28 15:17:19.051] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:17:19.051 +07:00 [DBG] [2025-05-28 15:17:19.051] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.051 +07:00 [INF] [2025-05-28 15:17:19.051] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:19.051 +07:00 [DBG] [2025-05-28 15:17:19.051] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:17:19.052 +07:00 [DBG] [2025-05-28 15:17:19.052] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.052 +07:00 [INF] [2025-05-28 15:17:19.052] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:19.052 +07:00 [DBG] [2025-05-28 15:17:19.052] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:17:19.052 +07:00 [DBG] [2025-05-28 15:17:19.052] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.097 +07:00 [INF] [2025-05-28 15:17:19.097] [INFO]: Tải danh sách user thành công
2025-05-28 15:17:19.097 +07:00 [INF] [2025-05-28 15:17:19.097] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.097 +07:00 [INF] [2025-05-28 15:17:19.097] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.097 +07:00 [INF] [2025-05-28 15:17:19.097] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:17:19.097 +07:00 [DBG] [2025-05-28 15:17:19.097] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:19.138 +07:00 [INF] [2025-05-28 15:17:19.138] [INFO]: Tải danh sách user thành công
2025-05-28 15:17:19.151 +07:00 [INF] [2025-05-28 15:17:19.151] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.72 giây
2025-05-28 15:17:19.163 +07:00 [INF] [2025-05-28 15:17:19.163] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.74 giây
2025-05-28 15:17:21.014 +07:00 [INF] [2025-05-28 15:17:21.014] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 15:17:22.490 +07:00 [INF] [2025-05-28 15:17:22.490] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 15:17:22.490 +07:00 [DBG] [2025-05-28 15:17:22.490] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:17:22.490 +07:00 [DBG] [2025-05-28 15:17:22.490] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:17:22.491 +07:00 [INF] [2025-05-28 15:17:22.491] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 15:17:22.491 +07:00 [INF] [2025-05-28 15:17:22.491] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.491 +07:00 [INF] [2025-05-28 15:17:22.491] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.491 +07:00 [INF] [2025-05-28 15:17:22.491] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:17:22.491 +07:00 [DBG] [2025-05-28 15:17:22.491] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.594 +07:00 [INF] [2025-05-28 15:17:22.594] [INFO]: Tải danh sách user thành công
2025-05-28 15:17:22.594 +07:00 [INF] [2025-05-28 15:17:22.594] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 15:17:22.594 +07:00 [DBG] [2025-05-28 15:17:22.594] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 15:17:22.594 +07:00 [DBG] [2025-05-28 15:17:22.594] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.594 +07:00 [INF] [2025-05-28 15:17:22.594] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 15:17:22.594 +07:00 [DBG] [2025-05-28 15:17:22.594] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 15:17:22.594 +07:00 [DBG] [2025-05-28 15:17:22.594] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.594 +07:00 [INF] [2025-05-28 15:17:22.594] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 15:17:22.594 +07:00 [DBG] [2025-05-28 15:17:22.594] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 15:17:22.595 +07:00 [DBG] [2025-05-28 15:17:22.595] [DEBUG]: Số cột trong hàng: 5
2025-05-28 15:17:22.648 +07:00 [INF] [2025-05-28 15:17:22.648] [INFO]: Tải danh sách user thành công
2025-05-28 15:17:22.664 +07:00 [INF] [2025-05-28 15:17:22.664] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.17 giây
2025-05-28 15:17:22.675 +07:00 [INF] [2025-05-28 15:17:22.675] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.18 giây
2025-05-28 15:17:26.556 +07:00 [DBG] [2025-05-28 15:17:26.556] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 15:17:26.749 +07:00 [INF] [2025-05-28 15:17:26.749] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 15:17:26.749 +07:00 [INF] [2025-05-28 15:17:26.749] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 15:17:26.750 +07:00 [INF] [2025-05-28 15:17:26.750] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 15:17:26.750 +07:00 [DBG] [2025-05-28 15:17:26.750] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 15:17:26.753 +07:00 [INF] [2025-05-28 15:17:26.753] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 15:17:26.753 +07:00 [INF] [2025-05-28 15:17:26.753] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 15:17:26.756 +07:00 [INF] [2025-05-28 15:17:26.756] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 15:17:26.767 +07:00 [INF] [2025-05-28 15:17:26.767] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:17:26.773 +07:00 [INF] [2025-05-28 15:17:26.773] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:17:26.779 +07:00 [INF] [2025-05-28 15:17:26.779] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 15:17:26.782 +07:00 [DBG] [2025-05-28 15:17:26.782] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:17:26.782
2025-05-28 15:17:26.782 +07:00 [INF] [2025-05-28 15:17:26.782] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:17:26.785 +07:00 [INF] [2025-05-28 15:17:26.785] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:17:26.786 +07:00 [INF] [2025-05-28 15:17:26.786] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:17:26.787 +07:00 [INF] [2025-05-28 15:17:26.787] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:17:26.790 +07:00 [INF] [2025-05-28 15:17:26.790] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 15:17:26.856 +07:00 [DBG] [2025-05-28 15:17:26.856] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 15:17:26.858 +07:00 [INF] [2025-05-28 15:17:26.858] [INFO] [nhatrang345]: ✅ Tìm thấy bàn ít người hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 15:17:26.858 +07:00 [DBG] [2025-05-28 15:17:26.858] [DEBUG] [nhatrang345]: ✅ Báo TaskCompletionSource thành công cho nhatrang345 (shouldAutoLeave=false)
2025-05-28 15:17:26.858 +07:00 [INF] [2025-05-28 15:17:26.858] [INFO] [nhatrang345]: Nhận được cmd: 202 cho nhatrang345 (thời gian: 97.183ms)
2025-05-28 15:17:26.858 +07:00 [WRN] [2025-05-28 15:17:26.858] [WARNING] [nhatrang345]: Dữ liệu phòng không hợp lệ sau cmd 202 cho nhatrang345
2025-05-28 15:17:26.858 +07:00 [INF] [2025-05-28 15:17:26.858] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 97.2417ms
2025-05-28 15:17:26.858 +07:00 [WRN] [2025-05-28 15:17:26.858] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 15:17:27.570 +07:00 [INF] [2025-05-28 15:17:27.570] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 15:17:27.578 +07:00 [INF] [2025-05-28 15:17:27.578] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 15:17:27.582 +07:00 [INF] [2025-05-28 15:17:27.582] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 15:17:27.586 +07:00 [INF] [2025-05-28 15:17:27.586] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 15:17:27.589 +07:00 [DBG] [2025-05-28 15:17:27.589] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 15:17:27.589
2025-05-28 15:17:27.589 +07:00 [INF] [2025-05-28 15:17:27.589] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:17:27.592 +07:00 [INF] [2025-05-28 15:17:27.592] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:17:30.603 +07:00 [WRN] [2025-05-28 15:17:30.603] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 15:17:31.107 +07:00 [INF] [2025-05-28 15:17:31.107] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 15:17:31.110 +07:00 [INF] [2025-05-28 15:17:31.110] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 15:17:31.256 +07:00 [DBG] [2025-05-28 15:17:31.256] [DEBUG]: Bắt đầu đóng Form1
2025-05-28 15:17:31.285 +07:00 [INF] [2025-05-28 15:17:31.285] [INFO]: Đã đóng driver cho OpenQA.Selenium.Chrome.ChromeDriver
2025-05-28 15:17:31.301 +07:00 [INF] [2025-05-28 15:17:31.301] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 29312)
2025-05-28 15:17:31.301 +07:00 [INF] [2025-05-28 15:17:31.301] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 25700)
2025-05-28 15:17:31.301 +07:00 [INF] [2025-05-28 15:17:31.301] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 9084)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 28372)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 20244)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 25040)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 32180)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 18084)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 22616)
2025-05-28 15:17:31.302 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 27176)
2025-05-28 15:17:31.303 +07:00 [INF] [2025-05-28 15:17:31.302] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 17348)
2025-05-28 15:17:31.303 +07:00 [INF] [2025-05-28 15:17:31.303] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 26540)
2025-05-28 15:17:31.303 +07:00 [INF] [2025-05-28 15:17:31.303] [INFO]: MauBinhCardManager disposed
2025-05-28 15:17:31.303 +07:00 [INF] [2025-05-28 15:17:31.303] [INFO]: Đã đóng Form1 thành công
2025-05-28 15:17:31.311 +07:00 [INF] Application started successfully.
