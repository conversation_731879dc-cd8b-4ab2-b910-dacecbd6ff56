2025-05-28 20:43:38.488 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 20:43:40.365 +07:00 [INF] User selected: HitClub - <PERSON><PERSON><PERSON>h
2025-05-28 20:43:40.369 +07:00 [INF] Form1 constructor started.
2025-05-28 20:43:40.388 +07:00 [DBG] [2025-05-28 20:43:40.388] [DEBUG]: Gọi InitializeComponent
2025-05-28 20:43:40.399 +07:00 [INF] [2025-05-28 20:43:40.399] [INFO]: Khởi tạo UIManager thành công
2025-05-28 20:43:40.400 +07:00 [DBG] [2025-05-28 20:43:40.400] [DEBUG]: B<PERSON>t đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 20:43:40.402 +07:00 [INF] [2025-05-28 20:43:40.402] [INFO]: Đ<PERSON> khởi tạo cột cho dataGridViewUsers
2025-05-28 20:43:40.402 +07:00 [DBG] [2025-05-28 20:43:40.402] [DEBUG]: B<PERSON>t đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 20:43:40.402 +07:00 [INF] [2025-05-28 20:43:40.402] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 20:43:40.403 +07:00 [INF] [2025-05-28 20:43:40.403] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 20:43:40.419 +07:00 [DBG] [2025-05-28 20:43:40.419] [DEBUG]: Bắt đầu OnLoad
2025-05-28 20:43:40.419 +07:00 [DBG] [2025-05-28 20:43:40.419] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 20:43:40.439 +07:00 [INF] [2025-05-28 20:43:40.439] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 20:43:40.439 +07:00 [INF] [2025-05-28 20:43:40.439] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 20:43:40.457 +07:00 [INF] [2025-05-28 20:43:40.457] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 20:43:40.459 +07:00 [INF] [2025-05-28 20:43:40.459] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 20:43:40.459 +07:00 [INF] [2025-05-28 20:43:40.459] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 20:43:40.459 +07:00 [INF] [2025-05-28 20:43:40.459] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 20:43:40.460 +07:00 [INF] [2025-05-28 20:43:40.460] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 20:43:40.460 +07:00 [INF] [2025-05-28 20:43:40.460] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 20:43:40.460 +07:00 [INF] InitializeAsync started.
2025-05-28 20:43:40.460 +07:00 [INF] [2025-05-28 20:43:40.460] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 20:43:40.467 +07:00 [DBG] [2025-05-28 20:43:40.466] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 20:43:40.472 +07:00 [DBG] [2025-05-28 20:43:40.472] [DEBUG]: UpdateRoomList hoàn tất trong 0.01 giây
2025-05-28 20:43:40.473 +07:00 [DBG] [2025-05-28 20:43:40.473] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 20:43:40.473 +07:00 [DBG] [2025-05-28 20:43:40.473] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 20:43:40.481 +07:00 [DBG] [2025-05-28 20:43:40.481] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 20:43:40.481 +07:00 [INF] [2025-05-28 20:43:40.481] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 20:43:40.481 +07:00 [DBG] [2025-05-28 20:43:40.481] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 20:43:40.483 +07:00 [DBG] [2025-05-28 20:43:40.483] [DEBUG]: Số cột trong hàng: 5
2025-05-28 20:43:40.483 +07:00 [DBG] [2025-05-28 20:43:40.483] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 20:43:40.483 +07:00 [INF] [2025-05-28 20:43:40.483] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 20:43:40.483 +07:00 [DBG] [2025-05-28 20:43:40.483] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 20:43:40.483 +07:00 [DBG] [2025-05-28 20:43:40.483] [DEBUG]: Số cột trong hàng: 5
2025-05-28 20:43:40.483 +07:00 [DBG] [2025-05-28 20:43:40.483] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 20:43:40.483 +07:00 [INF] [2025-05-28 20:43:40.483] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 20:43:40.483 +07:00 [DBG] [2025-05-28 20:43:40.483] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 20:43:40.483 +07:00 [DBG] [2025-05-28 20:43:40.483] [DEBUG]: Số cột trong hàng: 5
2025-05-28 20:43:40.536 +07:00 [INF] [2025-05-28 20:43:40.536] [INFO]: Tải danh sách user thành công
2025-05-28 20:43:40.545 +07:00 [INF] [2025-05-28 20:43:40.545] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 20:43:40.545 +07:00 [DBG] [2025-05-28 20:43:40.545] [DEBUG]: OnLoad hoàn tất
2025-05-28 20:43:42.345 +07:00 [DBG] [2025-05-28 20:43:42.345] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Mậu Binh
2025-05-28 20:43:42.353 +07:00 [INF] [2025-05-28 20:43:42.352] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 20:43:42.353 +07:00 [INF] [2025-05-28 20:43:42.353] [INFO]: 🎨 InitializeUI
2025-05-28 20:43:42.353 +07:00 [DBG] [2025-05-28 20:43:42.353] [DEBUG]: Đã khởi tạo MauBinhSuggestionForm
2025-05-28 20:44:57.117 +07:00 [DBG] [2025-05-28 20:44:57.117] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Mậu Binh
2025-05-28 20:44:57.126 +07:00 [INF] [2025-05-28 20:44:57.126] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 20:44:57.126 +07:00 [INF] [2025-05-28 20:44:57.126] [INFO]: 🎨 InitializeUI
2025-05-28 20:44:57.126 +07:00 [DBG] [2025-05-28 20:44:57.126] [DEBUG]: Đã khởi tạo MauBinhSuggestionForm
2025-05-28 20:47:05.650 +07:00 [DBG] [2025-05-28 20:47:05.650] [DEBUG]: Bắt đầu đóng Form1
2025-05-28 20:47:05.674 +07:00 [INF] [2025-05-28 20:47:05.674] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 31172)
2025-05-28 20:47:05.674 +07:00 [INF] [2025-05-28 20:47:05.674] [INFO]: MauBinhCardManager disposed
2025-05-28 20:47:05.674 +07:00 [INF] [2025-05-28 20:47:05.674] [INFO]: Đã đóng Form1 thành công
2025-05-28 20:47:05.682 +07:00 [INF] Application started successfully.
