2025-05-28 17:12:56.296 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 17:12:58.229 +07:00 [INF] User cancelled game selection, exiting application.
2025-05-28 17:13:49.312 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 17:13:53.005 +07:00 [INF] User selected: HitClub - Phỏm
2025-05-28 17:13:53.009 +07:00 [INF] Form1 constructor started.
2025-05-28 17:13:53.026 +07:00 [DBG] [2025-05-28 17:13:53.026] [DEBUG]: Gọi InitializeComponent
2025-05-28 17:13:53.037 +07:00 [INF] [2025-05-28 17:13:53.037] [INFO]: Khởi tạo UIManager thành công
2025-05-28 17:13:53.038 +07:00 [DBG] [2025-05-28 17:13:53.038] [DEBUG]: <PERSON><PERSON><PERSON> đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 17:13:53.040 +07:00 [INF] [2025-05-28 17:13:53.040] [INFO]: Đ<PERSON> khởi tạo cột cho dataGridViewUsers
2025-05-28 17:13:53.040 +07:00 [DBG] [2025-05-28 17:13:53.040] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 17:13:53.040 +07:00 [INF] [2025-05-28 17:13:53.040] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 17:13:53.041 +07:00 [INF] [2025-05-28 17:13:53.041] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 17:13:53.053 +07:00 [DBG] [2025-05-28 17:13:53.053] [DEBUG]: Bắt đầu OnLoad
2025-05-28 17:13:53.053 +07:00 [DBG] [2025-05-28 17:13:53.053] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 17:13:53.069 +07:00 [INF] [2025-05-28 17:13:53.068] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 17:13:53.069 +07:00 [INF] [2025-05-28 17:13:53.069] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 17:13:53.085 +07:00 [INF] [2025-05-28 17:13:53.085] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 17:13:53.086 +07:00 [INF] [2025-05-28 17:13:53.086] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 17:13:53.087 +07:00 [INF] [2025-05-28 17:13:53.087] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 17:13:53.087 +07:00 [INF] [2025-05-28 17:13:53.087] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 17:13:53.087 +07:00 [INF] [2025-05-28 17:13:53.087] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 17:13:53.087 +07:00 [INF] [2025-05-28 17:13:53.087] [INFO]: Đã chọn card game: Phỏm
2025-05-28 17:13:53.088 +07:00 [INF] InitializeAsync started.
2025-05-28 17:13:53.088 +07:00 [INF] [2025-05-28 17:13:53.088] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 17:13:53.092 +07:00 [DBG] [2025-05-28 17:13:53.092] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 17:13:53.094 +07:00 [DBG] [2025-05-28 17:13:53.094] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 17:13:53.095 +07:00 [DBG] [2025-05-28 17:13:53.095] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:13:53.095 +07:00 [DBG] [2025-05-28 17:13:53.095] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 17:13:53.103 +07:00 [DBG] [2025-05-28 17:13:53.103] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 17:13:53.103 +07:00 [INF] [2025-05-28 17:13:53.103] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 17:13:53.103 +07:00 [DBG] [2025-05-28 17:13:53.103] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:13:53.105 +07:00 [INF] [2025-05-28 17:13:53.105] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:13:53.105 +07:00 [INF] [2025-05-28 17:13:53.105] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:13:53.160 +07:00 [INF] [2025-05-28 17:13:53.160] [INFO]: Tải danh sách user thành công
2025-05-28 17:13:53.174 +07:00 [INF] [2025-05-28 17:13:53.174] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.08 giây
2025-05-28 17:13:53.175 +07:00 [DBG] [2025-05-28 17:13:53.175] [DEBUG]: OnLoad hoàn tất
2025-05-28 17:13:54.945 +07:00 [DBG] [2025-05-28 17:13:54.945] [DEBUG]: Bắt đầu đóng Form1
2025-05-28 17:13:54.955 +07:00 [INF] [2025-05-28 17:13:54.955] [INFO]: MauBinhCardManager disposed
2025-05-28 17:13:54.955 +07:00 [INF] [2025-05-28 17:13:54.955] [INFO]: Đã đóng Form1 thành công
2025-05-28 17:13:54.964 +07:00 [INF] Application started successfully.
2025-05-28 17:17:54.467 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 17:17:55.889 +07:00 [INF] User cancelled game selection, exiting application.
