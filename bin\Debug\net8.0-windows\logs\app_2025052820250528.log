2025-05-28 13:36:17.969 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 13:36:20.154 +07:00 [INF] User selected: HitClub - M<PERSON><PERSON>h
2025-05-28 13:36:20.158 +07:00 [INF] Form1 constructor started.
2025-05-28 13:36:20.175 +07:00 [DBG] [2025-05-28 13:36:20.175] [DEBUG]: Gọi InitializeComponent
2025-05-28 13:36:20.184 +07:00 [INF] [2025-05-28 13:36:20.184] [INFO]: Khởi tạo UIManager thành công
2025-05-28 13:36:20.185 +07:00 [DBG] [2025-05-28 13:36:20.185] [DEBUG]: <PERSON><PERSON><PERSON> đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 13:36:20.187 +07:00 [INF] [2025-05-28 13:36:20.187] [INFO]: Đ<PERSON> khởi tạo cột cho dataGridViewUsers
2025-05-28 13:36:20.187 +07:00 [DBG] [2025-05-28 13:36:20.187] [DEBUG]: <PERSON><PERSON><PERSON> đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 13:36:20.187 +07:00 [INF] [2025-05-28 13:36:20.187] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 13:36:20.188 +07:00 [INF] [2025-05-28 13:36:20.188] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 13:36:20.200 +07:00 [DBG] [2025-05-28 13:36:20.200] [DEBUG]: Bắt đầu OnLoad
2025-05-28 13:36:20.200 +07:00 [DBG] [2025-05-28 13:36:20.200] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 13:36:20.217 +07:00 [INF] [2025-05-28 13:36:20.217] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11823
2025-05-28 13:36:20.217 +07:00 [INF] [2025-05-28 13:36:20.217] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 13:36:20.233 +07:00 [INF] [2025-05-28 13:36:20.233] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 13:36:20.234 +07:00 [INF] [2025-05-28 13:36:20.234] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 13:36:20.235 +07:00 [INF] [2025-05-28 13:36:20.235] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 13:36:20.235 +07:00 [INF] [2025-05-28 13:36:20.235] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 13:36:20.235 +07:00 [INF] [2025-05-28 13:36:20.235] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 13:36:20.235 +07:00 [INF] [2025-05-28 13:36:20.235] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 13:36:20.235 +07:00 [INF] InitializeAsync started.
2025-05-28 13:36:20.235 +07:00 [INF] [2025-05-28 13:36:20.235] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 13:36:20.240 +07:00 [DBG] [2025-05-28 13:36:20.240] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 13:36:20.242 +07:00 [DBG] [2025-05-28 13:36:20.242] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 13:36:20.243 +07:00 [DBG] [2025-05-28 13:36:20.243] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:20.243 +07:00 [DBG] [2025-05-28 13:36:20.243] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:20.251 +07:00 [DBG] [2025-05-28 13:36:20.251] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 13:36:20.251 +07:00 [INF] [2025-05-28 13:36:20.251] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 13:36:20.251 +07:00 [DBG] [2025-05-28 13:36:20.251] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:20.253 +07:00 [INF] [2025-05-28 13:36:20.253] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:20.253 +07:00 [INF] [2025-05-28 13:36:20.253] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:20.253 +07:00 [DBG] [2025-05-28 13:36:20.253] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:20.295 +07:00 [INF] [2025-05-28 13:36:20.295] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:20.304 +07:00 [INF] [2025-05-28 13:36:20.304] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 13:36:20.304 +07:00 [DBG] [2025-05-28 13:36:20.304] [DEBUG]: OnLoad hoàn tất
2025-05-28 13:36:21.616 +07:00 [INF] [2025-05-28 13:36:21.616] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 13:36:21.622 +07:00 [INF] [2025-05-28 13:36:21.622] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 13:36:21.623 +07:00 [INF] [2025-05-28 13:36:21.623] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 13:36:21.635 +07:00 [DBG] [2025-05-28 13:36:21.635] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 13:36:21.677 +07:00 [INF] [2025-05-28 13:36:21.677] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 53ms
2025-05-28 13:36:22.049 +07:00 [DBG] [2025-05-28 13:36:22.049] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 13:36:22.049 +07:00 [INF] [2025-05-28 13:36:22.049] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:51752. Thời gian: 371ms
2025-05-28 13:36:22.049 +07:00 [DBG] [2025-05-28 13:36:22.049] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:22.049 +07:00 [DBG] [2025-05-28 13:36:22.049] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:22.050 +07:00 [INF] [2025-05-28 13:36:22.050] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 426ms
2025-05-28 13:36:22.050 +07:00 [DBG] [2025-05-28 13:36:22.050] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:22.050 +07:00 [DBG] [2025-05-28 13:36:22.050] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:23.166 +07:00 [INF] [2025-05-28 13:36:23.166] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 13:36:23.536 +07:00 [INF] [2025-05-28 13:36:23.536] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 13:36:23.618 +07:00 [INF] [2025-05-28 13:36:23.617] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 13:36:23.630 +07:00 [DBG] [2025-05-28 13:36:23.630] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 13:36:23.636 +07:00 [INF] [2025-05-28 13:36:23.636] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 13:36:23.639 +07:00 [INF] [2025-05-28 13:36:23.639] [INFO] [nhatrang345]: ✅ Đã inject C# methods cho nhatrang345
2025-05-28 13:36:23.640 +07:00 [INF] [2025-05-28 13:36:23.640] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.640 +07:00 [INF] [2025-05-28 13:36:23.640] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.640 +07:00 [INF] [2025-05-28 13:36:23.640] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:23.640 +07:00 [DBG] [2025-05-28 13:36:23.640] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.667 +07:00 [INF] [2025-05-28 13:36:23.667] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:23.667 +07:00 [INF] [2025-05-28 13:36:23.667] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:36:23.667 +07:00 [DBG] [2025-05-28 13:36:23.667] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:23.668 +07:00 [DBG] [2025-05-28 13:36:23.668] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.668 +07:00 [INF] [2025-05-28 13:36:23.668] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:23.668 +07:00 [DBG] [2025-05-28 13:36:23.668] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:23.668 +07:00 [DBG] [2025-05-28 13:36:23.668] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.668 +07:00 [INF] [2025-05-28 13:36:23.668] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:23.668 +07:00 [DBG] [2025-05-28 13:36:23.668] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:23.668 +07:00 [DBG] [2025-05-28 13:36:23.668] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:23.694 +07:00 [INF] [2025-05-28 13:36:23.694] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:23.702 +07:00 [INF] [2025-05-28 13:36:23.702] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.65 giây
2025-05-28 13:36:23.710 +07:00 [INF] [2025-05-28 13:36:23.710] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.66 giây
2025-05-28 13:36:25.636 +07:00 [INF] [2025-05-28 13:36:25.636] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 13:36:26.647 +07:00 [INF] [2025-05-28 13:36:26.647] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 13:36:26.647 +07:00 [DBG] [2025-05-28 13:36:26.647] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:26.647 +07:00 [DBG] [2025-05-28 13:36:26.647] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:26.647 +07:00 [INF] [2025-05-28 13:36:26.647] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:26.648 +07:00 [INF] [2025-05-28 13:36:26.648] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.648 +07:00 [INF] [2025-05-28 13:36:26.648] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.648 +07:00 [INF] [2025-05-28 13:36:26.648] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:26.648 +07:00 [DBG] [2025-05-28 13:36:26.648] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.697 +07:00 [INF] [2025-05-28 13:36:26.697] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:26.697 +07:00 [INF] [2025-05-28 13:36:26.697] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:36:26.697 +07:00 [DBG] [2025-05-28 13:36:26.697] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:26.697 +07:00 [DBG] [2025-05-28 13:36:26.697] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.697 +07:00 [INF] [2025-05-28 13:36:26.697] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:26.697 +07:00 [DBG] [2025-05-28 13:36:26.697] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:26.697 +07:00 [DBG] [2025-05-28 13:36:26.697] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.697 +07:00 [INF] [2025-05-28 13:36:26.697] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:26.697 +07:00 [DBG] [2025-05-28 13:36:26.697] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:26.698 +07:00 [DBG] [2025-05-28 13:36:26.698] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:26.749 +07:00 [INF] [2025-05-28 13:36:26.749] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:26.768 +07:00 [INF] [2025-05-28 13:36:26.768] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.12 giây
2025-05-28 13:36:26.785 +07:00 [INF] [2025-05-28 13:36:26.785] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.14 giây
2025-05-28 13:36:27.660 +07:00 [INF] [2025-05-28 13:36:27.660] [INFO] [nhatrang345]: ✅ Đã inject profile buttons thành công cho nhatrang345
2025-05-28 13:36:27.723 +07:00 [DBG] [2025-05-28 13:36:27.723] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 13:36:27.910 +07:00 [INF] [2025-05-28 13:36:27.910] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:36:27.911 +07:00 [INF] [2025-05-28 13:36:27.911] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:36:27.911 +07:00 [INF] [2025-05-28 13:36:27.911] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 13:36:27.911 +07:00 [DBG] [2025-05-28 13:36:27.911] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 13:36:27.914 +07:00 [INF] [2025-05-28 13:36:27.914] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 13:36:27.915 +07:00 [INF] [2025-05-28 13:36:27.915] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 13:36:27.918 +07:00 [INF] [2025-05-28 13:36:27.918] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 13:36:27.928 +07:00 [INF] [2025-05-28 13:36:27.928] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 13:36:27.935 +07:00 [INF] [2025-05-28 13:36:27.935] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 13:36:27.940 +07:00 [INF] [2025-05-28 13:36:27.940] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 13:36:27.943 +07:00 [DBG] [2025-05-28 13:36:27.943] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 13:36:27.943
2025-05-28 13:36:27.943 +07:00 [INF] [2025-05-28 13:36:27.943] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:36:27.946 +07:00 [INF] [2025-05-28 13:36:27.946] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:36:28.021 +07:00 [DBG] [2025-05-28 13:36:28.021] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:36:28.023 +07:00 [INF] [2025-05-28 13:36:28.023] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 3, số người: 4), tự động thoát phòng
2025-05-28 13:36:28.023 +07:00 [INF] [2025-05-28 13:36:28.023] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:36:28.023 +07:00 [WRN] [2025-05-28 13:36:28.023] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:36:28.024 +07:00 [INF] [2025-05-28 13:36:28.024] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:36:28.035 +07:00 [INF] [2025-05-28 13:36:28.035] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:36:28.040 +07:00 [WRN] [2025-05-28 13:36:28.040] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:36:28.040 +07:00 [INF] [2025-05-28 13:36:28.040] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 6.6777ms
2025-05-28 13:36:28.040 +07:00 [INF] [2025-05-28 13:36:28.040] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 15.2952ms
2025-05-28 13:36:28.040 +07:00 [DBG] [2025-05-28 13:36:28.040] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:28.040 +07:00 [INF] [2025-05-28 13:36:28.040] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:36:28.041 +07:00 [DBG] [2025-05-28 13:36:28.041] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:36:28.041 +07:00 [DBG] [2025-05-28 13:36:28.041] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:36:28.042 +07:00 [INF] [2025-05-28 13:36:28.042] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:28.042 +07:00 [INF] [2025-05-28 13:36:28.042] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:28.042 +07:00 [INF] [2025-05-28 13:36:28.042] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:36:28.042 +07:00 [DBG] [2025-05-28 13:36:28.042] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:36:28.090 +07:00 [INF] [2025-05-28 13:36:28.090] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:36:28.117 +07:00 [INF] [2025-05-28 13:36:28.117] [INFO]: Tải danh sách user thành công
2025-05-28 13:36:28.138 +07:00 [INF] [2025-05-28 13:36:28.138] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 13:36:28.201 +07:00 [INF] [2025-05-28 13:36:28.201] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:36:28.201 +07:00 [INF] [2025-05-28 13:36:28.201] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:36:29.583 +07:00 [INF] [2025-05-28 13:36:29.583] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:36:29.584 +07:00 [INF] [2025-05-28 13:36:29.584] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:36:29.589 +07:00 [INF] [2025-05-28 13:36:29.589] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:36:29.679 +07:00 [INF] [2025-05-28 13:36:29.679] [INFO] [nhatrang345]: Buttons đã xuất hiện, test chúng cho nhatrang345
2025-05-28 13:36:29.686 +07:00 [INF] [2025-05-28 13:36:29.686] [INFO] [nhatrang345]: 🧪 Test results cho nhatrang345: System.Collections.Generic.Dictionary`2[System.String,System.Object]
2025-05-28 13:36:29.689 +07:00 [DBG] [2025-05-28 13:36:29.689] [DEBUG] [nhatrang345]: ✅ Đã setup reload handler cho nhatrang345
2025-05-28 13:36:34.583 +07:00 [INF] [2025-05-28 13:36:34.583] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:36:34.583 +07:00 [INF] [2025-05-28 13:36:34.583] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:36:34.583 +07:00 [INF] [2025-05-28 13:36:34.583] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:36:35.952 +07:00 [WRN] [2025-05-28 13:36:35.952] [WARNING] [nhatrang345]: Timeout 8000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:36:36.457 +07:00 [INF] [2025-05-28 13:36:36.457] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:36:36.461 +07:00 [INF] [2025-05-28 13:36:36.461] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:36:36.523 +07:00 [DBG] [2025-05-28 13:36:36.523] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:36:36.523 +07:00 [INF] [2025-05-28 13:36:36.523] [INFO] [nhatrang345]: ✅ Tìm thấy bàn ít người hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 13:36:36.523 +07:00 [WRN] [2025-05-28 13:36:36.523] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:36:39.582 +07:00 [INF] [2025-05-28 13:36:39.582] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:36:39.582 +07:00 [INF] [2025-05-28 13:36:39.582] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:36:39.586 +07:00 [INF] [2025-05-28 13:36:39.586] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:40:56.873 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 13:40:59.063 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 13:40:59.067 +07:00 [INF] Form1 constructor started.
2025-05-28 13:40:59.084 +07:00 [DBG] [2025-05-28 13:40:59.084] [DEBUG]: Gọi InitializeComponent
2025-05-28 13:40:59.093 +07:00 [INF] [2025-05-28 13:40:59.093] [INFO]: Khởi tạo UIManager thành công
2025-05-28 13:40:59.094 +07:00 [DBG] [2025-05-28 13:40:59.094] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 13:40:59.096 +07:00 [INF] [2025-05-28 13:40:59.096] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 13:40:59.096 +07:00 [DBG] [2025-05-28 13:40:59.096] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 13:40:59.096 +07:00 [INF] [2025-05-28 13:40:59.096] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 13:40:59.097 +07:00 [INF] [2025-05-28 13:40:59.097] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 13:40:59.110 +07:00 [DBG] [2025-05-28 13:40:59.110] [DEBUG]: Bắt đầu OnLoad
2025-05-28 13:40:59.111 +07:00 [DBG] [2025-05-28 13:40:59.111] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 13:40:59.127 +07:00 [INF] [2025-05-28 13:40:59.127] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11823
2025-05-28 13:40:59.127 +07:00 [INF] [2025-05-28 13:40:59.127] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 13:40:59.143 +07:00 [INF] [2025-05-28 13:40:59.143] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 13:40:59.144 +07:00 [INF] [2025-05-28 13:40:59.144] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 13:40:59.145 +07:00 [INF] [2025-05-28 13:40:59.145] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 13:40:59.145 +07:00 [INF] [2025-05-28 13:40:59.145] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 13:40:59.145 +07:00 [INF] [2025-05-28 13:40:59.145] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 13:40:59.145 +07:00 [INF] [2025-05-28 13:40:59.145] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 13:40:59.145 +07:00 [INF] InitializeAsync started.
2025-05-28 13:40:59.146 +07:00 [INF] [2025-05-28 13:40:59.146] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 13:40:59.150 +07:00 [DBG] [2025-05-28 13:40:59.150] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 13:40:59.152 +07:00 [DBG] [2025-05-28 13:40:59.152] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 13:40:59.153 +07:00 [DBG] [2025-05-28 13:40:59.153] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:40:59.153 +07:00 [DBG] [2025-05-28 13:40:59.153] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:40:59.160 +07:00 [DBG] [2025-05-28 13:40:59.160] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 13:40:59.160 +07:00 [INF] [2025-05-28 13:40:59.160] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 13:40:59.161 +07:00 [DBG] [2025-05-28 13:40:59.161] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:40:59.162 +07:00 [INF] [2025-05-28 13:40:59.162] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:40:59.162 +07:00 [INF] [2025-05-28 13:40:59.162] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:40:59.162 +07:00 [DBG] [2025-05-28 13:40:59.162] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:40:59.204 +07:00 [INF] [2025-05-28 13:40:59.204] [INFO]: Tải danh sách user thành công
2025-05-28 13:40:59.216 +07:00 [INF] [2025-05-28 13:40:59.216] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 13:40:59.216 +07:00 [DBG] [2025-05-28 13:40:59.216] [DEBUG]: OnLoad hoàn tất
2025-05-28 13:41:00.253 +07:00 [INF] [2025-05-28 13:41:00.253] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11823: Đang chạy
2025-05-28 13:41:00.259 +07:00 [INF] [2025-05-28 13:41:00.259] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 13:41:00.261 +07:00 [INF] [2025-05-28 13:41:00.261] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 13:41:00.271 +07:00 [DBG] [2025-05-28 13:41:00.271] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles: Thành công
2025-05-28 13:41:00.314 +07:00 [INF] [2025-05-28 13:41:00.314] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 52ms
2025-05-28 13:41:00.692 +07:00 [DBG] [2025-05-28 13:41:00.692] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11823/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 13:41:00.692 +07:00 [INF] [2025-05-28 13:41:00.692] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:61868. Thời gian: 377ms
2025-05-28 13:41:00.693 +07:00 [DBG] [2025-05-28 13:41:00.693] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:00.693 +07:00 [DBG] [2025-05-28 13:41:00.693] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:00.693 +07:00 [INF] [2025-05-28 13:41:00.693] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 432ms
2025-05-28 13:41:00.693 +07:00 [DBG] [2025-05-28 13:41:00.693] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:00.693 +07:00 [DBG] [2025-05-28 13:41:00.693] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:01.934 +07:00 [INF] [2025-05-28 13:41:01.934] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 13:41:02.301 +07:00 [INF] [2025-05-28 13:41:02.301] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 13:41:02.385 +07:00 [INF] [2025-05-28 13:41:02.385] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 13:41:02.397 +07:00 [DBG] [2025-05-28 13:41:02.397] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 13:41:02.403 +07:00 [INF] [2025-05-28 13:41:02.403] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 13:41:02.406 +07:00 [INF] [2025-05-28 13:41:02.406] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:02.406 +07:00 [DBG] [2025-05-28 13:41:02.406] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:02.407 +07:00 [DBG] [2025-05-28 13:41:02.407] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.407 +07:00 [INF] [2025-05-28 13:41:02.407] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:02.407 +07:00 [DBG] [2025-05-28 13:41:02.407] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:02.407 +07:00 [DBG] [2025-05-28 13:41:02.407] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.407 +07:00 [INF] [2025-05-28 13:41:02.407] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:02.407 +07:00 [DBG] [2025-05-28 13:41:02.407] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:02.407 +07:00 [DBG] [2025-05-28 13:41:02.407] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.407 +07:00 [INF] [2025-05-28 13:41:02.407] [INFO] [nhatrang345]: ✅ Đã inject C# methods cho nhatrang345
2025-05-28 13:41:02.434 +07:00 [INF] [2025-05-28 13:41:02.434] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:02.434 +07:00 [INF] [2025-05-28 13:41:02.434] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.434 +07:00 [INF] [2025-05-28 13:41:02.434] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.434 +07:00 [INF] [2025-05-28 13:41:02.434] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:02.434 +07:00 [DBG] [2025-05-28 13:41:02.434] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:02.460 +07:00 [INF] [2025-05-28 13:41:02.460] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:02.469 +07:00 [INF] [2025-05-28 13:41:02.469] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.78 giây
2025-05-28 13:41:02.476 +07:00 [INF] [2025-05-28 13:41:02.476] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.78 giây
2025-05-28 13:41:04.395 +07:00 [INF] [2025-05-28 13:41:04.395] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 13:41:05.417 +07:00 [INF] [2025-05-28 13:41:05.417] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 13:41:05.417 +07:00 [DBG] [2025-05-28 13:41:05.417] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:05.417 +07:00 [DBG] [2025-05-28 13:41:05.417] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:05.418 +07:00 [INF] [2025-05-28 13:41:05.418] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:05.418 +07:00 [INF] [2025-05-28 13:41:05.418] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.418 +07:00 [INF] [2025-05-28 13:41:05.418] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.418 +07:00 [INF] [2025-05-28 13:41:05.418] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:05.418 +07:00 [DBG] [2025-05-28 13:41:05.418] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.523 +07:00 [INF] [2025-05-28 13:41:05.523] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:05.523 +07:00 [INF] [2025-05-28 13:41:05.523] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.523 +07:00 [INF] [2025-05-28 13:41:05.523] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.523 +07:00 [INF] [2025-05-28 13:41:05.523] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:05.523 +07:00 [DBG] [2025-05-28 13:41:05.523] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:05.595 +07:00 [INF] [2025-05-28 13:41:05.595] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:05.613 +07:00 [INF] [2025-05-28 13:41:05.613] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.20 giây
2025-05-28 13:41:05.641 +07:00 [INF] [2025-05-28 13:41:05.641] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.22 giây
2025-05-28 13:41:06.441 +07:00 [INF] [2025-05-28 13:41:06.441] [INFO] [nhatrang345]: ✅ Đã inject profile buttons thành công cho nhatrang345
2025-05-28 13:41:07.360 +07:00 [DBG] [2025-05-28 13:41:07.360] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 13:41:07.550 +07:00 [INF] [2025-05-28 13:41:07.550] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:07.551 +07:00 [INF] [2025-05-28 13:41:07.551] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:41:07.551 +07:00 [INF] [2025-05-28 13:41:07.551] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 13:41:07.551 +07:00 [DBG] [2025-05-28 13:41:07.551] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 13:41:07.555 +07:00 [INF] [2025-05-28 13:41:07.555] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 13:41:07.555 +07:00 [INF] [2025-05-28 13:41:07.555] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 13:41:07.558 +07:00 [INF] [2025-05-28 13:41:07.558] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 13:41:07.569 +07:00 [INF] [2025-05-28 13:41:07.569] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 13:41:07.575 +07:00 [INF] [2025-05-28 13:41:07.575] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 13:41:07.581 +07:00 [INF] [2025-05-28 13:41:07.581] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 13:41:07.584 +07:00 [DBG] [2025-05-28 13:41:07.584] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 13:41:07.584
2025-05-28 13:41:07.584 +07:00 [INF] [2025-05-28 13:41:07.584] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:41:07.587 +07:00 [INF] [2025-05-28 13:41:07.587] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:41:07.716 +07:00 [DBG] [2025-05-28 13:41:07.716] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:41:07.718 +07:00 [INF] [2025-05-28 13:41:07.718] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 2, số người: 3), tự động thoát phòng
2025-05-28 13:41:07.719 +07:00 [INF] [2025-05-28 13:41:07.719] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:41:07.719 +07:00 [WRN] [2025-05-28 13:41:07.719] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:41:07.719 +07:00 [INF] [2025-05-28 13:41:07.719] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:41:07.725 +07:00 [INF] [2025-05-28 13:41:07.725] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:41:07.729 +07:00 [WRN] [2025-05-28 13:41:07.729] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:41:07.729 +07:00 [INF] [2025-05-28 13:41:07.729] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 6.4683ms
2025-05-28 13:41:07.729 +07:00 [INF] [2025-05-28 13:41:07.729] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 9.9345ms
2025-05-28 13:41:07.729 +07:00 [DBG] [2025-05-28 13:41:07.729] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:07.730 +07:00 [INF] [2025-05-28 13:41:07.730] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:41:07.731 +07:00 [DBG] [2025-05-28 13:41:07.731] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:07.731 +07:00 [DBG] [2025-05-28 13:41:07.731] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:07.732 +07:00 [INF] [2025-05-28 13:41:07.732] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:07.732 +07:00 [INF] [2025-05-28 13:41:07.732] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:07.732 +07:00 [INF] [2025-05-28 13:41:07.732] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:07.732 +07:00 [DBG] [2025-05-28 13:41:07.732] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:07.772 +07:00 [INF] [2025-05-28 13:41:07.772] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:41:07.809 +07:00 [INF] [2025-05-28 13:41:07.809] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:07.827 +07:00 [INF] [2025-05-28 13:41:07.827] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 13:41:07.882 +07:00 [INF] [2025-05-28 13:41:07.882] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:07.882 +07:00 [INF] [2025-05-28 13:41:07.882] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:41:08.387 +07:00 [INF] [2025-05-28 13:41:08.387] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:41:08.388 +07:00 [INF] [2025-05-28 13:41:08.388] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:41:08.390 +07:00 [INF] [2025-05-28 13:41:08.390] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:41:08.459 +07:00 [INF] [2025-05-28 13:41:08.459] [INFO] [nhatrang345]: Buttons đã xuất hiện, test chúng cho nhatrang345
2025-05-28 13:41:08.466 +07:00 [INF] [2025-05-28 13:41:08.466] [INFO] [nhatrang345]: 🧪 Test results cho nhatrang345: System.Collections.Generic.Dictionary`2[System.String,System.Object]
2025-05-28 13:41:08.470 +07:00 [DBG] [2025-05-28 13:41:08.470] [DEBUG] [nhatrang345]: ✅ Đã setup reload handler cho nhatrang345
2025-05-28 13:41:10.595 +07:00 [WRN] [2025-05-28 13:41:10.595] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:41:11.099 +07:00 [INF] [2025-05-28 13:41:11.099] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:41:11.103 +07:00 [INF] [2025-05-28 13:41:11.103] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:41:11.160 +07:00 [DBG] [2025-05-28 13:41:11.160] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:41:11.160 +07:00 [INF] [2025-05-28 13:41:11.160] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 2, số người: 3), tự động thoát phòng
2025-05-28 13:41:11.160 +07:00 [INF] [2025-05-28 13:41:11.160] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:41:11.160 +07:00 [WRN] [2025-05-28 13:41:11.160] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:41:11.160 +07:00 [INF] [2025-05-28 13:41:11.160] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:41:11.167 +07:00 [INF] [2025-05-28 13:41:11.167] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:41:11.170 +07:00 [WRN] [2025-05-28 13:41:11.170] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:41:11.170 +07:00 [INF] [2025-05-28 13:41:11.170] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 7.0791ms
2025-05-28 13:41:11.170 +07:00 [INF] [2025-05-28 13:41:11.170] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 9.763ms
2025-05-28 13:41:11.170 +07:00 [DBG] [2025-05-28 13:41:11.170] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:11.170 +07:00 [INF] [2025-05-28 13:41:11.170] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:41:11.170 +07:00 [DBG] [2025-05-28 13:41:11.170] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:11.170 +07:00 [DBG] [2025-05-28 13:41:11.170] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:11.172 +07:00 [INF] [2025-05-28 13:41:11.172] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:11.172 +07:00 [INF] [2025-05-28 13:41:11.172] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:11.172 +07:00 [INF] [2025-05-28 13:41:11.172] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:11.172 +07:00 [DBG] [2025-05-28 13:41:11.172] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:11.213 +07:00 [INF] [2025-05-28 13:41:11.212] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:41:11.241 +07:00 [INF] [2025-05-28 13:41:11.241] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:11.267 +07:00 [INF] [2025-05-28 13:41:11.267] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 13:41:11.306 +07:00 [INF] [2025-05-28 13:41:11.306] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:11.306 +07:00 [INF] [2025-05-28 13:41:11.306] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:41:13.388 +07:00 [INF] [2025-05-28 13:41:13.388] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:41:13.388 +07:00 [INF] [2025-05-28 13:41:13.388] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:41:13.390 +07:00 [INF] [2025-05-28 13:41:13.390] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:41:14.112 +07:00 [WRN] [2025-05-28 13:41:14.112] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:41:14.112 +07:00 [ERR] [2025-05-28 13:41:14.112] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 13:41:14.112 +07:00 [INF] [2025-05-28 13:41:14.112] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 6549.5517ms
2025-05-28 13:41:14.112 +07:00 [WRN] [2025-05-28 13:41:14.112] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 13:41:14.823 +07:00 [INF] [2025-05-28 13:41:14.823] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 13:41:14.832 +07:00 [INF] [2025-05-28 13:41:14.832] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 13:41:14.836 +07:00 [INF] [2025-05-28 13:41:14.836] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 13:41:14.841 +07:00 [INF] [2025-05-28 13:41:14.841] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 13:41:14.843 +07:00 [DBG] [2025-05-28 13:41:14.843] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 13:41:14.843
2025-05-28 13:41:14.843 +07:00 [INF] [2025-05-28 13:41:14.843] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:41:14.847 +07:00 [INF] [2025-05-28 13:41:14.847] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:41:14.905 +07:00 [DBG] [2025-05-28 13:41:14.905] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:41:14.905 +07:00 [INF] [2025-05-28 13:41:14.905] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 2, số người: 3), tự động thoát phòng
2025-05-28 13:41:14.905 +07:00 [INF] [2025-05-28 13:41:14.905] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:41:14.905 +07:00 [WRN] [2025-05-28 13:41:14.905] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:41:14.905 +07:00 [INF] [2025-05-28 13:41:14.905] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:41:14.911 +07:00 [INF] [2025-05-28 13:41:14.911] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:41:14.915 +07:00 [WRN] [2025-05-28 13:41:14.915] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:41:14.915 +07:00 [INF] [2025-05-28 13:41:14.915] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 6.7156ms
2025-05-28 13:41:14.915 +07:00 [INF] [2025-05-28 13:41:14.915] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 9.4494ms
2025-05-28 13:41:14.915 +07:00 [DBG] [2025-05-28 13:41:14.915] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:14.915 +07:00 [INF] [2025-05-28 13:41:14.915] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:41:14.915 +07:00 [DBG] [2025-05-28 13:41:14.915] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:14.915 +07:00 [DBG] [2025-05-28 13:41:14.915] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:14.917 +07:00 [INF] [2025-05-28 13:41:14.917] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:14.917 +07:00 [INF] [2025-05-28 13:41:14.917] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:14.917 +07:00 [INF] [2025-05-28 13:41:14.917] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:14.917 +07:00 [DBG] [2025-05-28 13:41:14.917] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:14.959 +07:00 [INF] [2025-05-28 13:41:14.959] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:41:14.995 +07:00 [INF] [2025-05-28 13:41:14.995] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:15.011 +07:00 [INF] [2025-05-28 13:41:15.011] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 13:41:15.068 +07:00 [INF] [2025-05-28 13:41:15.068] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:15.068 +07:00 [INF] [2025-05-28 13:41:15.068] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:41:17.860 +07:00 [WRN] [2025-05-28 13:41:17.860] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:41:18.369 +07:00 [INF] [2025-05-28 13:41:18.369] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:41:18.373 +07:00 [INF] [2025-05-28 13:41:18.373] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:41:18.386 +07:00 [INF] [2025-05-28 13:41:18.386] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:41:18.386 +07:00 [INF] [2025-05-28 13:41:18.386] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:41:18.390 +07:00 [INF] [2025-05-28 13:41:18.390] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:41:18.431 +07:00 [DBG] [2025-05-28 13:41:18.431] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:41:18.431 +07:00 [INF] [2025-05-28 13:41:18.431] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 2, số người: 3), tự động thoát phòng
2025-05-28 13:41:18.431 +07:00 [INF] [2025-05-28 13:41:18.431] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:41:18.431 +07:00 [WRN] [2025-05-28 13:41:18.431] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:41:18.431 +07:00 [INF] [2025-05-28 13:41:18.431] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:41:18.437 +07:00 [INF] [2025-05-28 13:41:18.437] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:41:18.440 +07:00 [WRN] [2025-05-28 13:41:18.440] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:41:18.440 +07:00 [INF] [2025-05-28 13:41:18.440] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 5.708ms
2025-05-28 13:41:18.440 +07:00 [INF] [2025-05-28 13:41:18.440] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 8.7217ms
2025-05-28 13:41:18.440 +07:00 [DBG] [2025-05-28 13:41:18.440] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:18.440 +07:00 [INF] [2025-05-28 13:41:18.440] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:41:18.440 +07:00 [DBG] [2025-05-28 13:41:18.440] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:18.440 +07:00 [DBG] [2025-05-28 13:41:18.440] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:18.441 +07:00 [INF] [2025-05-28 13:41:18.441] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:18.441 +07:00 [DBG] [2025-05-28 13:41:18.441] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:18.441 +07:00 [DBG] [2025-05-28 13:41:18.441] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:18.441 +07:00 [INF] [2025-05-28 13:41:18.441] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:18.441 +07:00 [DBG] [2025-05-28 13:41:18.441] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:18.441 +07:00 [DBG] [2025-05-28 13:41:18.441] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:18.441 +07:00 [INF] [2025-05-28 13:41:18.441] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:18.442 +07:00 [DBG] [2025-05-28 13:41:18.442] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:18.442 +07:00 [DBG] [2025-05-28 13:41:18.442] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:18.486 +07:00 [INF] [2025-05-28 13:41:18.486] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:41:18.513 +07:00 [INF] [2025-05-28 13:41:18.513] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:18.537 +07:00 [INF] [2025-05-28 13:41:18.537] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 13:41:18.577 +07:00 [INF] [2025-05-28 13:41:18.577] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:18.577 +07:00 [INF] [2025-05-28 13:41:18.577] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5797524452209473, Vị trí: (35, 51)
2025-05-28 13:41:21.387 +07:00 [WRN] [2025-05-28 13:41:21.387] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:41:21.387 +07:00 [ERR] [2025-05-28 13:41:21.387] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 13:41:21.387 +07:00 [INF] [2025-05-28 13:41:21.387] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 6560.4274ms
2025-05-28 13:41:21.387 +07:00 [WRN] [2025-05-28 13:41:21.387] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 13:41:22.102 +07:00 [INF] [2025-05-28 13:41:22.102] [INFO] [nhatrang345]: Thử vào phòng lần 3/3 cho nhatrang345
2025-05-28 13:41:22.110 +07:00 [INF] [2025-05-28 13:41:22.110] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 13:41:22.114 +07:00 [INF] [2025-05-28 13:41:22.114] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 13:41:22.119 +07:00 [INF] [2025-05-28 13:41:22.119] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 13:41:22.121 +07:00 [DBG] [2025-05-28 13:41:22.121] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 13:41:22.121
2025-05-28 13:41:22.121 +07:00 [INF] [2025-05-28 13:41:22.121] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 13:41:22.125 +07:00 [INF] [2025-05-28 13:41:22.125] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 13:41:22.383 +07:00 [DBG] [2025-05-28 13:41:22.383] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 13:41:22.383 +07:00 [INF] [2025-05-28 13:41:22.383] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 2, số người: 3), tự động thoát phòng
2025-05-28 13:41:22.383 +07:00 [INF] [2025-05-28 13:41:22.383] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 13:41:22.383 +07:00 [WRN] [2025-05-28 13:41:22.383] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 13:41:22.383 +07:00 [INF] [2025-05-28 13:41:22.383] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 13:41:22.392 +07:00 [INF] [2025-05-28 13:41:22.392] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 13:41:22.395 +07:00 [WRN] [2025-05-28 13:41:22.395] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 13:41:22.395 +07:00 [INF] [2025-05-28 13:41:22.395] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 6.8486ms
2025-05-28 13:41:22.395 +07:00 [INF] [2025-05-28 13:41:22.395] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 12.0102ms
2025-05-28 13:41:22.395 +07:00 [DBG] [2025-05-28 13:41:22.395] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:22.395 +07:00 [INF] [2025-05-28 13:41:22.395] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 13:41:22.395 +07:00 [DBG] [2025-05-28 13:41:22.395] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:22.395 +07:00 [DBG] [2025-05-28 13:41:22.395] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:22.397 +07:00 [INF] [2025-05-28 13:41:22.397] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:22.397 +07:00 [INF] [2025-05-28 13:41:22.397] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:22.397 +07:00 [INF] [2025-05-28 13:41:22.397] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:22.397 +07:00 [DBG] [2025-05-28 13:41:22.397] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:22.446 +07:00 [INF] [2025-05-28 13:41:22.446] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 13:41:22.475 +07:00 [INF] [2025-05-28 13:41:22.475] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:22.504 +07:00 [INF] [2025-05-28 13:41:22.504] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 13:41:22.543 +07:00 [INF] [2025-05-28 13:41:22.543] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 13:41:22.543 +07:00 [INF] [2025-05-28 13:41:22.543] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 13:41:23.392 +07:00 [INF] [2025-05-28 13:41:23.392] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 13:41:23.392 +07:00 [INF] [2025-05-28 13:41:23.392] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 13:41:23.396 +07:00 [INF] [2025-05-28 13:41:23.396] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 13:41:24.090 +07:00 [INF] [2025-05-28 13:41:24.090] [INFO] [nhatrang345]: Đã xóa trạng thái phòng cho nhatrang345
2025-05-28 13:41:24.090 +07:00 [INF] [2025-05-28 13:41:24.090] [INFO]: Đã hủy lấy bàn trống
2025-05-28 13:41:24.090 +07:00 [WRN] [2025-05-28 13:41:24.090] [WARNING] [nhatrang345]: Timeout 3000ms khi chờ cmd: 202 cho nhatrang345, thực hiện click 2 lần
2025-05-28 13:41:24.106 +07:00 [INF] [2025-05-28 13:41:24.106] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 13:41:24.113 +07:00 [INF] [2025-05-28 13:41:24.113] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 2008.253ms
2025-05-28 13:41:24.124 +07:00 [INF] [2025-05-28 13:41:24.124] [INFO] [nhatrang345]: Đã hủy vào phòng cho nhatrang345
2025-05-28 13:41:24.132 +07:00 [ERR] [2025-05-28 13:41:24.132] [ERROR] [nhatrang345]: Lỗi khi vào phòng cho nhatrang345: A task was canceled.
2025-05-28 13:41:24.140 +07:00 [INF] [2025-05-28 13:41:24.140] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 16582.2361ms
2025-05-28 13:41:24.140 +07:00 [DBG] [2025-05-28 13:41:24.140] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:24.140 +07:00 [DBG] [2025-05-28 13:41:24.140] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:24.153 +07:00 [INF] [2025-05-28 13:41:24.153] [INFO] [nhatrang345]: Đã hủy lấy bàn trống cho nhatrang345
2025-05-28 13:41:24.153 +07:00 [INF] [2025-05-28 13:41:24.153] [INFO] [nhatrang345]: Hoàn thành xử lý cho nhatrang345, thời gian: 16598.0457ms
2025-05-28 13:41:24.153 +07:00 [WRN] [2025-05-28 13:41:24.153] [WARNING]: Đã thử hết user mà không tìm thấy bàn trống
2025-05-28 13:41:24.159 +07:00 [INF] [2025-05-28 13:41:24.159] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:24.160 +07:00 [INF] [2025-05-28 13:41:24.160] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:24.160 +07:00 [INF] [2025-05-28 13:41:24.160] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:24.160 +07:00 [DBG] [2025-05-28 13:41:24.160] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:24.230 +07:00 [INF] [2025-05-28 13:41:24.230] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:24.257 +07:00 [INF] [2025-05-28 13:41:24.257] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.12 giây
2025-05-28 13:41:25.067 +07:00 [DBG] [2025-05-28 13:41:25.067] [DEBUG] [nhatrang345]: ❌ Tắt chế độ Get Empty Table cho nhatrang345
2025-05-28 13:41:25.067 +07:00 [DBG] [2025-05-28 13:41:25.067] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 13:41:25.067 +07:00 [DBG] [2025-05-28 13:41:25.067] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 13:41:25.067 +07:00 [INF] [2025-05-28 13:41:25.067] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 13:41:25.067 +07:00 [DBG] [2025-05-28 13:41:25.067] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 13:41:25.068 +07:00 [DBG] [2025-05-28 13:41:25.068] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:25.068 +07:00 [INF] [2025-05-28 13:41:25.068] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 13:41:25.068 +07:00 [DBG] [2025-05-28 13:41:25.068] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 13:41:25.068 +07:00 [DBG] [2025-05-28 13:41:25.068] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:25.068 +07:00 [INF] [2025-05-28 13:41:25.068] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 13:41:25.068 +07:00 [DBG] [2025-05-28 13:41:25.068] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 13:41:25.068 +07:00 [DBG] [2025-05-28 13:41:25.068] [DEBUG]: Số cột trong hàng: 5
2025-05-28 13:41:25.145 +07:00 [INF] [2025-05-28 13:41:25.145] [INFO]: Tải danh sách user thành công
2025-05-28 13:41:25.174 +07:00 [INF] [2025-05-28 13:41:25.174] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 13:41:28.406 +07:00 [INF] [2025-05-28 13:41:28.406] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 13:41:28.406 +07:00 [INF] [2025-05-28 13:41:28.406] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 13:41:28.416 +07:00 [INF] [2025-05-28 13:41:28.416] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 13:41:33.402 +07:00 [INF] [2025-05-28 13:41:33.402] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 13:41:33.402 +07:00 [INF] [2025-05-28 13:41:33.402] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 13:41:33.406 +07:00 [INF] [2025-05-28 13:41:33.406] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 13:41:38.403 +07:00 [INF] [2025-05-28 13:41:38.403] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 13:41:38.403 +07:00 [INF] [2025-05-28 13:41:38.403] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 13:41:38.415 +07:00 [INF] [2025-05-28 13:41:38.415] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 13:41:43.405 +07:00 [INF] [2025-05-28 13:41:43.405] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 13:41:43.405 +07:00 [INF] [2025-05-28 13:41:43.405] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 13:41:43.409 +07:00 [INF] [2025-05-28 13:41:43.409] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 13:41:48.421 +07:00 [INF] [2025-05-28 13:41:48.421] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 13:41:48.421 +07:00 [INF] [2025-05-28 13:41:48.421] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 13:41:48.422 +07:00 [INF] [2025-05-28 13:41:48.422] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 13:41:53.423 +07:00 [INF] [2025-05-28 13:41:53.423] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 13:41:53.423 +07:00 [INF] [2025-05-28 13:41:53.423] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 13:41:53.426 +07:00 [INF] [2025-05-28 13:41:53.426] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 13:43:47.907 +07:00 [INF] [2025-05-28 13:43:47.907] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 13:43:47.907 +07:00 [INF] [2025-05-28 13:43:47.907] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 13:43:52.640 +07:00 [INF] [2025-05-28 13:43:52.640] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 13:43:52.910 +07:00 [INF] [2025-05-28 13:43:52.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 13:43:52.910 +07:00 [INF] [2025-05-28 13:43:52.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 13:43:57.466 +07:00 [INF] [2025-05-28 13:43:57.466] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 13:43:57.909 +07:00 [INF] [2025-05-28 13:43:57.909] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 13:43:57.910 +07:00 [INF] [2025-05-28 13:43:57.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 13:44:02.908 +07:00 [INF] [2025-05-28 13:44:02.908] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 13:44:02.908 +07:00 [INF] [2025-05-28 13:44:02.908] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 13:44:06.682 +07:00 [INF] [2025-05-28 13:44:06.682] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 13:44:07.461 +07:00 [INF] [2025-05-28 13:44:07.461] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 13:44:07.910 +07:00 [INF] [2025-05-28 13:44:07.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 13:44:07.910 +07:00 [INF] [2025-05-28 13:44:07.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 13:44:12.460 +07:00 [INF] [2025-05-28 13:44:12.460] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 13:44:12.915 +07:00 [INF] [2025-05-28 13:44:12.915] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 13:44:12.915 +07:00 [INF] [2025-05-28 13:44:12.915] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 13:44:17.461 +07:00 [INF] [2025-05-28 13:44:17.461] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 13:44:17.914 +07:00 [INF] [2025-05-28 13:44:17.914] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 13:44:17.914 +07:00 [INF] [2025-05-28 13:44:17.914] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 13:44:22.462 +07:00 [INF] [2025-05-28 13:44:22.462] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 13:44:22.908 +07:00 [INF] [2025-05-28 13:44:22.908] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 13:44:22.908 +07:00 [INF] [2025-05-28 13:44:22.908] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 13:44:27.461 +07:00 [INF] [2025-05-28 13:44:27.461] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 13:44:27.910 +07:00 [INF] [2025-05-28 13:44:27.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 13:44:27.910 +07:00 [INF] [2025-05-28 13:44:27.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 13:44:32.476 +07:00 [INF] [2025-05-28 13:44:32.476] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 13:44:32.914 +07:00 [INF] [2025-05-28 13:44:32.914] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 13:44:32.914 +07:00 [INF] [2025-05-28 13:44:32.914] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 13:44:37.476 +07:00 [INF] [2025-05-28 13:44:37.476] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 13:44:37.910 +07:00 [INF] [2025-05-28 13:44:37.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,21]
2025-05-28 13:44:37.910 +07:00 [INF] [2025-05-28 13:44:37.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,21]
2025-05-28 13:44:42.477 +07:00 [INF] [2025-05-28 13:44:42.477] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 13:44:42.913 +07:00 [INF] [2025-05-28 13:44:42.913] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,22]
2025-05-28 13:44:42.913 +07:00 [INF] [2025-05-28 13:44:42.913] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,22]
2025-05-28 13:44:47.476 +07:00 [INF] [2025-05-28 13:44:47.476] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 13:44:47.913 +07:00 [INF] [2025-05-28 13:44:47.913] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,23]
2025-05-28 13:44:47.913 +07:00 [INF] [2025-05-28 13:44:47.913] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,23]
2025-05-28 13:44:52.482 +07:00 [INF] [2025-05-28 13:44:52.482] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 13:44:52.911 +07:00 [INF] [2025-05-28 13:44:52.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,24]
2025-05-28 13:44:52.911 +07:00 [INF] [2025-05-28 13:44:52.911] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,24]
2025-05-28 13:44:57.495 +07:00 [INF] [2025-05-28 13:44:57.495] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 13:44:57.910 +07:00 [INF] [2025-05-28 13:44:57.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,25]
2025-05-28 13:44:57.910 +07:00 [INF] [2025-05-28 13:44:57.910] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,25]
2025-05-28 13:45:02.514 +07:00 [INF] [2025-05-28 13:45:02.514] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 13:45:02.927 +07:00 [INF] [2025-05-28 13:45:02.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,26]
2025-05-28 13:45:02.927 +07:00 [INF] [2025-05-28 13:45:02.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,26]
2025-05-28 13:45:07.512 +07:00 [INF] [2025-05-28 13:45:07.512] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 13:45:07.928 +07:00 [INF] [2025-05-28 13:45:07.928] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,27]
2025-05-28 13:45:07.928 +07:00 [INF] [2025-05-28 13:45:07.928] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,27]
2025-05-28 13:45:12.527 +07:00 [INF] [2025-05-28 13:45:12.527] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 13:45:12.927 +07:00 [INF] [2025-05-28 13:45:12.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,28]
2025-05-28 13:45:12.927 +07:00 [INF] [2025-05-28 13:45:12.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,28]
2025-05-28 13:45:17.658 +07:00 [INF] [2025-05-28 13:45:17.658] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 13:45:17.929 +07:00 [INF] [2025-05-28 13:45:17.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,29]
2025-05-28 13:45:17.929 +07:00 [INF] [2025-05-28 13:45:17.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,29]
2025-05-28 13:45:22.755 +07:00 [INF] [2025-05-28 13:45:22.755] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 13:45:22.929 +07:00 [INF] [2025-05-28 13:45:22.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,30]
2025-05-28 13:45:22.930 +07:00 [INF] [2025-05-28 13:45:22.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,30]
2025-05-28 13:45:27.543 +07:00 [INF] [2025-05-28 13:45:27.543] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 13:45:27.927 +07:00 [INF] [2025-05-28 13:45:27.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,31]
2025-05-28 13:45:27.927 +07:00 [INF] [2025-05-28 13:45:27.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,31]
2025-05-28 13:45:32.551 +07:00 [INF] [2025-05-28 13:45:32.551] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,21]
2025-05-28 13:45:32.927 +07:00 [INF] [2025-05-28 13:45:32.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,32]
2025-05-28 13:45:32.927 +07:00 [INF] [2025-05-28 13:45:32.927] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,32]
2025-05-28 13:45:37.544 +07:00 [INF] [2025-05-28 13:45:37.544] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,22]
2025-05-28 13:45:37.930 +07:00 [INF] [2025-05-28 13:45:37.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,33]
2025-05-28 13:45:37.930 +07:00 [INF] [2025-05-28 13:45:37.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,33]
2025-05-28 13:45:42.548 +07:00 [INF] [2025-05-28 13:45:42.548] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,23]
2025-05-28 13:45:42.929 +07:00 [INF] [2025-05-28 13:45:42.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,34]
2025-05-28 13:45:42.929 +07:00 [INF] [2025-05-28 13:45:42.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,34]
2025-05-28 13:45:47.545 +07:00 [INF] [2025-05-28 13:45:47.545] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,24]
2025-05-28 13:45:47.929 +07:00 [INF] [2025-05-28 13:45:47.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,35]
2025-05-28 13:45:47.929 +07:00 [INF] [2025-05-28 13:45:47.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,35]
2025-05-28 13:45:52.544 +07:00 [INF] [2025-05-28 13:45:52.544] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,25]
2025-05-28 13:45:52.928 +07:00 [INF] [2025-05-28 13:45:52.928] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,36]
2025-05-28 13:45:52.928 +07:00 [INF] [2025-05-28 13:45:52.928] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,36]
2025-05-28 13:45:57.545 +07:00 [INF] [2025-05-28 13:45:57.545] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,26]
2025-05-28 13:45:57.929 +07:00 [INF] [2025-05-28 13:45:57.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,37]
2025-05-28 13:45:57.929 +07:00 [INF] [2025-05-28 13:45:57.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,37]
2025-05-28 13:46:02.549 +07:00 [INF] [2025-05-28 13:46:02.549] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,27]
2025-05-28 13:46:02.929 +07:00 [INF] [2025-05-28 13:46:02.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,38]
2025-05-28 13:46:02.929 +07:00 [INF] [2025-05-28 13:46:02.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,38]
2025-05-28 13:46:07.547 +07:00 [INF] [2025-05-28 13:46:07.547] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,28]
2025-05-28 13:46:07.929 +07:00 [INF] [2025-05-28 13:46:07.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,39]
2025-05-28 13:46:07.929 +07:00 [INF] [2025-05-28 13:46:07.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,39]
2025-05-28 13:46:12.560 +07:00 [INF] [2025-05-28 13:46:12.560] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,29]
2025-05-28 13:46:12.929 +07:00 [INF] [2025-05-28 13:46:12.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,40]
2025-05-28 13:46:12.929 +07:00 [INF] [2025-05-28 13:46:12.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,40]
2025-05-28 13:46:17.572 +07:00 [INF] [2025-05-28 13:46:17.572] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,30]
2025-05-28 13:46:17.940 +07:00 [INF] [2025-05-28 13:46:17.940] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,41]
2025-05-28 13:46:17.940 +07:00 [INF] [2025-05-28 13:46:17.940] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,41]
2025-05-28 13:46:22.632 +07:00 [INF] [2025-05-28 13:46:22.632] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,31]
2025-05-28 13:46:22.934 +07:00 [INF] [2025-05-28 13:46:22.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,42]
2025-05-28 13:46:22.934 +07:00 [INF] [2025-05-28 13:46:22.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,42]
2025-05-28 13:46:27.565 +07:00 [INF] [2025-05-28 13:46:27.565] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,32]
2025-05-28 13:46:27.929 +07:00 [INF] [2025-05-28 13:46:27.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,43]
2025-05-28 13:46:27.929 +07:00 [INF] [2025-05-28 13:46:27.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,43]
2025-05-28 13:46:32.571 +07:00 [INF] [2025-05-28 13:46:32.571] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,33]
2025-05-28 13:46:32.932 +07:00 [INF] [2025-05-28 13:46:32.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,44]
2025-05-28 13:46:32.932 +07:00 [INF] [2025-05-28 13:46:32.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,44]
2025-05-28 13:46:37.735 +07:00 [INF] [2025-05-28 13:46:37.735] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,34]
2025-05-28 13:46:37.931 +07:00 [INF] [2025-05-28 13:46:37.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,45]
2025-05-28 13:46:37.931 +07:00 [INF] [2025-05-28 13:46:37.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,45]
2025-05-28 13:46:42.562 +07:00 [INF] [2025-05-28 13:46:42.562] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,35]
2025-05-28 13:46:42.930 +07:00 [INF] [2025-05-28 13:46:42.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,46]
2025-05-28 13:46:42.930 +07:00 [INF] [2025-05-28 13:46:42.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,46]
2025-05-28 13:46:47.564 +07:00 [INF] [2025-05-28 13:46:47.564] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,36]
2025-05-28 13:46:47.935 +07:00 [INF] [2025-05-28 13:46:47.935] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,47]
2025-05-28 13:46:47.935 +07:00 [INF] [2025-05-28 13:46:47.935] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,47]
2025-05-28 13:46:52.565 +07:00 [INF] [2025-05-28 13:46:52.565] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,37]
2025-05-28 13:46:52.930 +07:00 [INF] [2025-05-28 13:46:52.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,48]
2025-05-28 13:46:52.930 +07:00 [INF] [2025-05-28 13:46:52.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,48]
2025-05-28 13:46:57.563 +07:00 [INF] [2025-05-28 13:46:57.562] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,38]
2025-05-28 13:46:57.936 +07:00 [INF] [2025-05-28 13:46:57.936] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,49]
2025-05-28 13:46:57.936 +07:00 [INF] [2025-05-28 13:46:57.936] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,49]
2025-05-28 13:47:02.568 +07:00 [INF] [2025-05-28 13:47:02.568] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,39]
2025-05-28 13:47:02.931 +07:00 [INF] [2025-05-28 13:47:02.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,50]
2025-05-28 13:47:02.931 +07:00 [INF] [2025-05-28 13:47:02.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,50]
2025-05-28 13:47:07.573 +07:00 [INF] [2025-05-28 13:47:07.573] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,40]
2025-05-28 13:47:07.943 +07:00 [INF] [2025-05-28 13:47:07.943] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,51]
2025-05-28 13:47:07.943 +07:00 [INF] [2025-05-28 13:47:07.943] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,51]
2025-05-28 13:47:12.568 +07:00 [INF] [2025-05-28 13:47:12.568] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,41]
2025-05-28 13:47:12.929 +07:00 [INF] [2025-05-28 13:47:12.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,52]
2025-05-28 13:47:12.929 +07:00 [INF] [2025-05-28 13:47:12.929] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,52]
2025-05-28 13:47:17.563 +07:00 [INF] [2025-05-28 13:47:17.563] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,42]
2025-05-28 13:47:17.942 +07:00 [INF] [2025-05-28 13:47:17.942] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,53]
2025-05-28 13:47:17.942 +07:00 [INF] [2025-05-28 13:47:17.942] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,53]
2025-05-28 13:47:22.566 +07:00 [INF] [2025-05-28 13:47:22.566] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,43]
2025-05-28 13:47:22.931 +07:00 [INF] [2025-05-28 13:47:22.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,54]
2025-05-28 13:47:22.931 +07:00 [INF] [2025-05-28 13:47:22.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,54]
2025-05-28 13:47:27.585 +07:00 [INF] [2025-05-28 13:47:27.585] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,44]
2025-05-28 13:47:27.931 +07:00 [INF] [2025-05-28 13:47:27.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,55]
2025-05-28 13:47:27.931 +07:00 [INF] [2025-05-28 13:47:27.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,55]
2025-05-28 13:47:32.563 +07:00 [INF] [2025-05-28 13:47:32.563] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,45]
2025-05-28 13:47:32.931 +07:00 [INF] [2025-05-28 13:47:32.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,56]
2025-05-28 13:47:32.931 +07:00 [INF] [2025-05-28 13:47:32.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,56]
2025-05-28 13:47:37.931 +07:00 [INF] [2025-05-28 13:47:37.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,57]
2025-05-28 13:47:37.932 +07:00 [INF] [2025-05-28 13:47:37.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,57]
2025-05-28 13:47:38.187 +07:00 [INF] [2025-05-28 13:47:38.187] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,46]
2025-05-28 13:47:42.597 +07:00 [INF] [2025-05-28 13:47:42.597] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,47]
2025-05-28 13:47:42.931 +07:00 [INF] [2025-05-28 13:47:42.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,58]
2025-05-28 13:47:42.931 +07:00 [INF] [2025-05-28 13:47:42.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,58]
2025-05-28 13:47:47.607 +07:00 [INF] [2025-05-28 13:47:47.607] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,48]
2025-05-28 13:47:47.932 +07:00 [INF] [2025-05-28 13:47:47.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,59]
2025-05-28 13:47:47.932 +07:00 [INF] [2025-05-28 13:47:47.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,59]
2025-05-28 13:47:52.598 +07:00 [INF] [2025-05-28 13:47:52.598] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,49]
2025-05-28 13:47:52.930 +07:00 [INF] [2025-05-28 13:47:52.930] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,60]
2025-05-28 13:47:52.931 +07:00 [INF] [2025-05-28 13:47:52.931] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,60]
2025-05-28 13:47:57.611 +07:00 [INF] [2025-05-28 13:47:57.611] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,50]
2025-05-28 13:47:57.932 +07:00 [INF] [2025-05-28 13:47:57.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,61]
2025-05-28 13:47:57.932 +07:00 [INF] [2025-05-28 13:47:57.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,61]
2025-05-28 13:48:02.598 +07:00 [INF] [2025-05-28 13:48:02.598] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,51]
2025-05-28 13:48:02.934 +07:00 [INF] [2025-05-28 13:48:02.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,62]
2025-05-28 13:48:02.934 +07:00 [INF] [2025-05-28 13:48:02.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,62]
2025-05-28 13:48:07.598 +07:00 [INF] [2025-05-28 13:48:07.598] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,52]
2025-05-28 13:48:07.945 +07:00 [INF] [2025-05-28 13:48:07.945] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,63]
2025-05-28 13:48:07.945 +07:00 [INF] [2025-05-28 13:48:07.945] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,63]
2025-05-28 13:48:12.604 +07:00 [INF] [2025-05-28 13:48:12.604] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,53]
2025-05-28 13:48:12.933 +07:00 [INF] [2025-05-28 13:48:12.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,64]
2025-05-28 13:48:12.933 +07:00 [INF] [2025-05-28 13:48:12.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,64]
2025-05-28 13:48:17.600 +07:00 [INF] [2025-05-28 13:48:17.600] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,54]
2025-05-28 13:48:17.934 +07:00 [INF] [2025-05-28 13:48:17.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,65]
2025-05-28 13:48:17.934 +07:00 [INF] [2025-05-28 13:48:17.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,65]
2025-05-28 13:48:22.599 +07:00 [INF] [2025-05-28 13:48:22.599] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,55]
2025-05-28 13:48:22.934 +07:00 [INF] [2025-05-28 13:48:22.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,66]
2025-05-28 13:48:22.934 +07:00 [INF] [2025-05-28 13:48:22.934] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,66]
2025-05-28 13:48:27.599 +07:00 [INF] [2025-05-28 13:48:27.599] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,56]
2025-05-28 13:48:27.933 +07:00 [INF] [2025-05-28 13:48:27.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,67]
2025-05-28 13:48:27.933 +07:00 [INF] [2025-05-28 13:48:27.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,67]
2025-05-28 13:48:32.604 +07:00 [INF] [2025-05-28 13:48:32.604] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,57]
2025-05-28 13:48:32.932 +07:00 [INF] [2025-05-28 13:48:32.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,68]
2025-05-28 13:48:32.932 +07:00 [INF] [2025-05-28 13:48:32.932] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,68]
2025-05-28 13:48:37.599 +07:00 [INF] [2025-05-28 13:48:37.599] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,58]
2025-05-28 13:48:37.935 +07:00 [INF] [2025-05-28 13:48:37.935] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,69]
2025-05-28 13:48:37.935 +07:00 [INF] [2025-05-28 13:48:37.935] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,69]
2025-05-28 13:48:42.600 +07:00 [INF] [2025-05-28 13:48:42.600] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,59]
2025-05-28 13:48:42.945 +07:00 [INF] [2025-05-28 13:48:42.945] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,70]
2025-05-28 13:48:42.945 +07:00 [INF] [2025-05-28 13:48:42.945] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,70]
2025-05-28 13:48:47.602 +07:00 [INF] [2025-05-28 13:48:47.602] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,60]
2025-05-28 13:48:47.933 +07:00 [INF] [2025-05-28 13:48:47.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,71]
2025-05-28 13:48:47.933 +07:00 [INF] [2025-05-28 13:48:47.933] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,71]
