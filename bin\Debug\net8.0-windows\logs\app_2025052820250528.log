2025-05-28 17:12:56.296 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 17:12:58.229 +07:00 [INF] User cancelled game selection, exiting application.
2025-05-28 17:13:49.312 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 17:13:53.005 +07:00 [INF] User selected: HitClub - Phỏm
2025-05-28 17:13:53.009 +07:00 [INF] Form1 constructor started.
2025-05-28 17:13:53.026 +07:00 [DBG] [2025-05-28 17:13:53.026] [DEBUG]: Gọi InitializeComponent
2025-05-28 17:13:53.037 +07:00 [INF] [2025-05-28 17:13:53.037] [INFO]: Khởi tạo UIManager thành công
2025-05-28 17:13:53.038 +07:00 [DBG] [2025-05-28 17:13:53.038] [DEBUG]: <PERSON><PERSON><PERSON> đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 17:13:53.040 +07:00 [INF] [2025-05-28 17:13:53.040] [INFO]: Đ<PERSON> khởi tạo cột cho dataGridViewUsers
2025-05-28 17:13:53.040 +07:00 [DBG] [2025-05-28 17:13:53.040] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 17:13:53.040 +07:00 [INF] [2025-05-28 17:13:53.040] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 17:13:53.041 +07:00 [INF] [2025-05-28 17:13:53.041] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 17:13:53.053 +07:00 [DBG] [2025-05-28 17:13:53.053] [DEBUG]: Bắt đầu OnLoad
2025-05-28 17:13:53.053 +07:00 [DBG] [2025-05-28 17:13:53.053] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 17:13:53.069 +07:00 [INF] [2025-05-28 17:13:53.068] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 17:13:53.069 +07:00 [INF] [2025-05-28 17:13:53.069] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 17:13:53.085 +07:00 [INF] [2025-05-28 17:13:53.085] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 17:13:53.086 +07:00 [INF] [2025-05-28 17:13:53.086] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 17:13:53.087 +07:00 [INF] [2025-05-28 17:13:53.087] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 17:13:53.087 +07:00 [INF] [2025-05-28 17:13:53.087] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 17:13:53.087 +07:00 [INF] [2025-05-28 17:13:53.087] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 17:13:53.087 +07:00 [INF] [2025-05-28 17:13:53.087] [INFO]: Đã chọn card game: Phỏm
2025-05-28 17:13:53.088 +07:00 [INF] InitializeAsync started.
2025-05-28 17:13:53.088 +07:00 [INF] [2025-05-28 17:13:53.088] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 17:13:53.092 +07:00 [DBG] [2025-05-28 17:13:53.092] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 17:13:53.094 +07:00 [DBG] [2025-05-28 17:13:53.094] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 17:13:53.095 +07:00 [DBG] [2025-05-28 17:13:53.095] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:13:53.095 +07:00 [DBG] [2025-05-28 17:13:53.095] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 17:13:53.103 +07:00 [DBG] [2025-05-28 17:13:53.103] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 17:13:53.103 +07:00 [INF] [2025-05-28 17:13:53.103] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 17:13:53.103 +07:00 [DBG] [2025-05-28 17:13:53.103] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:13:53.105 +07:00 [INF] [2025-05-28 17:13:53.105] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:13:53.105 +07:00 [INF] [2025-05-28 17:13:53.105] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 17:13:53.105 +07:00 [DBG] [2025-05-28 17:13:53.105] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:13:53.160 +07:00 [INF] [2025-05-28 17:13:53.160] [INFO]: Tải danh sách user thành công
2025-05-28 17:13:53.174 +07:00 [INF] [2025-05-28 17:13:53.174] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.08 giây
2025-05-28 17:13:53.175 +07:00 [DBG] [2025-05-28 17:13:53.175] [DEBUG]: OnLoad hoàn tất
2025-05-28 17:13:54.945 +07:00 [DBG] [2025-05-28 17:13:54.945] [DEBUG]: Bắt đầu đóng Form1
2025-05-28 17:13:54.955 +07:00 [INF] [2025-05-28 17:13:54.955] [INFO]: MauBinhCardManager disposed
2025-05-28 17:13:54.955 +07:00 [INF] [2025-05-28 17:13:54.955] [INFO]: Đã đóng Form1 thành công
2025-05-28 17:13:54.964 +07:00 [INF] Application started successfully.
2025-05-28 17:17:54.467 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 17:17:55.889 +07:00 [INF] User cancelled game selection, exiting application.
2025-05-28 17:37:32.446 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 17:37:33.796 +07:00 [INF] User cancelled game selection, exiting application.
2025-05-28 17:47:36.800 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 17:47:40.256 +07:00 [INF] User selected: HitClub - Phỏm
2025-05-28 17:47:40.260 +07:00 [INF] Form1 constructor started.
2025-05-28 17:47:40.278 +07:00 [DBG] [2025-05-28 17:47:40.278] [DEBUG]: Gọi InitializeComponent
2025-05-28 17:47:40.288 +07:00 [INF] [2025-05-28 17:47:40.288] [INFO]: Khởi tạo UIManager thành công
2025-05-28 17:47:40.289 +07:00 [DBG] [2025-05-28 17:47:40.289] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 17:47:40.291 +07:00 [INF] [2025-05-28 17:47:40.291] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 17:47:40.291 +07:00 [DBG] [2025-05-28 17:47:40.291] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 17:47:40.291 +07:00 [INF] [2025-05-28 17:47:40.291] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 17:47:40.292 +07:00 [INF] [2025-05-28 17:47:40.292] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 17:47:40.304 +07:00 [DBG] [2025-05-28 17:47:40.304] [DEBUG]: Bắt đầu OnLoad
2025-05-28 17:47:40.304 +07:00 [DBG] [2025-05-28 17:47:40.304] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 17:47:40.320 +07:00 [INF] [2025-05-28 17:47:40.320] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 17:47:40.320 +07:00 [INF] [2025-05-28 17:47:40.320] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 17:47:40.336 +07:00 [INF] [2025-05-28 17:47:40.336] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 17:47:40.338 +07:00 [INF] [2025-05-28 17:47:40.338] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 17:47:40.339 +07:00 [INF] [2025-05-28 17:47:40.339] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 17:47:40.339 +07:00 [INF] [2025-05-28 17:47:40.339] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 17:47:40.339 +07:00 [INF] [2025-05-28 17:47:40.339] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 17:47:40.339 +07:00 [INF] [2025-05-28 17:47:40.339] [INFO]: Đã chọn card game: Phỏm
2025-05-28 17:47:40.339 +07:00 [INF] InitializeAsync started.
2025-05-28 17:47:40.339 +07:00 [INF] [2025-05-28 17:47:40.339] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 17:47:40.343 +07:00 [DBG] [2025-05-28 17:47:40.343] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 17:47:40.345 +07:00 [DBG] [2025-05-28 17:47:40.345] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 17:47:40.346 +07:00 [DBG] [2025-05-28 17:47:40.346] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:47:40.346 +07:00 [DBG] [2025-05-28 17:47:40.346] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 17:47:40.353 +07:00 [DBG] [2025-05-28 17:47:40.353] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 17:47:40.353 +07:00 [INF] [2025-05-28 17:47:40.353] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 17:47:40.353 +07:00 [DBG] [2025-05-28 17:47:40.353] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 17:47:40.355 +07:00 [DBG] [2025-05-28 17:47:40.355] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:47:40.355 +07:00 [DBG] [2025-05-28 17:47:40.355] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:47:40.355 +07:00 [INF] [2025-05-28 17:47:40.355] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:47:40.355 +07:00 [DBG] [2025-05-28 17:47:40.355] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 17:47:40.355 +07:00 [DBG] [2025-05-28 17:47:40.355] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:47:40.355 +07:00 [DBG] [2025-05-28 17:47:40.355] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:47:40.355 +07:00 [INF] [2025-05-28 17:47:40.355] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:47:40.355 +07:00 [DBG] [2025-05-28 17:47:40.355] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 17:47:40.355 +07:00 [DBG] [2025-05-28 17:47:40.355] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:47:40.425 +07:00 [INF] [2025-05-28 17:47:40.425] [INFO]: Tải danh sách user thành công
2025-05-28 17:47:40.443 +07:00 [INF] [2025-05-28 17:47:40.443] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.10 giây
2025-05-28 17:47:40.444 +07:00 [DBG] [2025-05-28 17:47:40.444] [DEBUG]: OnLoad hoàn tất
2025-05-28 17:47:41.447 +07:00 [DBG] [2025-05-28 17:47:41.447] [DEBUG]: Bắt đầu đóng Form1
2025-05-28 17:47:41.488 +07:00 [INF] [2025-05-28 17:47:41.488] [INFO]: MauBinhCardManager disposed
2025-05-28 17:47:41.488 +07:00 [INF] [2025-05-28 17:47:41.488] [INFO]: Đã đóng Form1 thành công
2025-05-28 17:47:41.498 +07:00 [INF] Application started successfully.
2025-05-28 17:48:19.009 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 17:48:22.460 +07:00 [INF] User cancelled game selection, exiting application.
2025-05-28 17:52:37.288 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 17:52:40.779 +07:00 [INF] User selected: HitClub - Phỏm
2025-05-28 17:52:40.783 +07:00 [INF] Form1 constructor started.
2025-05-28 17:52:40.801 +07:00 [DBG] [2025-05-28 17:52:40.801] [DEBUG]: Gọi InitializeComponent
2025-05-28 17:52:40.812 +07:00 [INF] [2025-05-28 17:52:40.812] [INFO]: Khởi tạo UIManager thành công
2025-05-28 17:52:40.812 +07:00 [DBG] [2025-05-28 17:52:40.812] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 17:52:40.814 +07:00 [INF] [2025-05-28 17:52:40.814] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 17:52:40.814 +07:00 [DBG] [2025-05-28 17:52:40.814] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 17:52:40.814 +07:00 [INF] [2025-05-28 17:52:40.814] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 17:52:40.816 +07:00 [INF] [2025-05-28 17:52:40.816] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 17:52:40.828 +07:00 [DBG] [2025-05-28 17:52:40.828] [DEBUG]: Bắt đầu OnLoad
2025-05-28 17:52:40.829 +07:00 [DBG] [2025-05-28 17:52:40.829] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 17:52:40.845 +07:00 [INF] [2025-05-28 17:52:40.845] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 17:52:40.845 +07:00 [INF] [2025-05-28 17:52:40.845] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 17:52:40.862 +07:00 [INF] [2025-05-28 17:52:40.862] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 17:52:40.864 +07:00 [INF] [2025-05-28 17:52:40.864] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 17:52:40.865 +07:00 [INF] [2025-05-28 17:52:40.865] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 17:52:40.865 +07:00 [INF] [2025-05-28 17:52:40.865] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 17:52:40.865 +07:00 [INF] [2025-05-28 17:52:40.865] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 17:52:40.865 +07:00 [INF] [2025-05-28 17:52:40.865] [INFO]: Đã chọn card game: Phỏm
2025-05-28 17:52:40.865 +07:00 [INF] InitializeAsync started.
2025-05-28 17:52:40.865 +07:00 [INF] [2025-05-28 17:52:40.865] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 17:52:40.872 +07:00 [DBG] [2025-05-28 17:52:40.872] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 17:52:40.875 +07:00 [DBG] [2025-05-28 17:52:40.875] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 17:52:40.875 +07:00 [DBG] [2025-05-28 17:52:40.875] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:52:40.876 +07:00 [DBG] [2025-05-28 17:52:40.876] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 17:52:40.885 +07:00 [DBG] [2025-05-28 17:52:40.885] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 17:52:40.885 +07:00 [INF] [2025-05-28 17:52:40.885] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 17:52:40.885 +07:00 [DBG] [2025-05-28 17:52:40.885] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 17:52:40.887 +07:00 [DBG] [2025-05-28 17:52:40.887] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:52:40.887 +07:00 [DBG] [2025-05-28 17:52:40.887] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:52:40.887 +07:00 [INF] [2025-05-28 17:52:40.887] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:52:40.887 +07:00 [DBG] [2025-05-28 17:52:40.887] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 17:52:40.887 +07:00 [DBG] [2025-05-28 17:52:40.887] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:52:40.887 +07:00 [DBG] [2025-05-28 17:52:40.887] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:52:40.887 +07:00 [INF] [2025-05-28 17:52:40.887] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:52:40.887 +07:00 [DBG] [2025-05-28 17:52:40.887] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 17:52:40.887 +07:00 [DBG] [2025-05-28 17:52:40.887] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:52:40.972 +07:00 [INF] [2025-05-28 17:52:40.972] [INFO]: Tải danh sách user thành công
2025-05-28 17:52:40.991 +07:00 [INF] [2025-05-28 17:52:40.991] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.12 giây
2025-05-28 17:52:40.992 +07:00 [DBG] [2025-05-28 17:52:40.992] [DEBUG]: OnLoad hoàn tất
2025-05-28 17:52:46.352 +07:00 [ERR] [2025-05-28 17:52:46.352] [ERROR]: Lỗi khi kiểm tra GPM-Login tại http://127.0.0.1:11014: One or more errors occurred. (No connection could be made because the target machine actively refused it. (127.0.0.1:11014))
2025-05-28 17:52:56.186 +07:00 [INF] [2025-05-28 17:52:56.186] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11014: Đang chạy
2025-05-28 17:52:56.191 +07:00 [INF] [2025-05-28 17:52:56.191] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 17:52:56.193 +07:00 [INF] [2025-05-28 17:52:56.193] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 17:52:56.196 +07:00 [DBG] [2025-05-28 17:52:56.196] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles: Thành công
2025-05-28 17:52:56.240 +07:00 [INF] [2025-05-28 17:52:56.240] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 45ms
2025-05-28 17:52:56.797 +07:00 [DBG] [2025-05-28 17:52:56.797] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 17:52:56.798 +07:00 [INF] [2025-05-28 17:52:56.798] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:60184. Thời gian: 557ms
2025-05-28 17:52:56.798 +07:00 [DBG] [2025-05-28 17:52:56.798] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:52:56.798 +07:00 [DBG] [2025-05-28 17:52:56.798] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 17:52:56.798 +07:00 [INF] [2025-05-28 17:52:56.798] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 605ms
2025-05-28 17:52:56.798 +07:00 [DBG] [2025-05-28 17:52:56.798] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:52:56.798 +07:00 [DBG] [2025-05-28 17:52:56.798] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 17:52:58.117 +07:00 [INF] [2025-05-28 17:52:58.117] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 17:52:58.134 +07:00 [INF] [2025-05-28 17:52:58.134] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 17:52:58.502 +07:00 [INF] [2025-05-28 17:52:58.502] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 17:52:58.546 +07:00 [DBG] [2025-05-28 17:52:58.546] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 17:52:58.551 +07:00 [INF] [2025-05-28 17:52:58.551] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 17:52:58.551 +07:00 [INF] [2025-05-28 17:52:58.551] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 17:52:58.554 +07:00 [INF] [2025-05-28 17:52:58.554] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 17:52:58.554 +07:00 [INF] [2025-05-28 17:52:58.554] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 17:52:58.554 +07:00 [DBG] [2025-05-28 17:52:58.554] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 17:52:58.555 +07:00 [DBG] [2025-05-28 17:52:58.555] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:52:58.555 +07:00 [INF] [2025-05-28 17:52:58.555] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:52:58.555 +07:00 [DBG] [2025-05-28 17:52:58.555] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 17:52:58.555 +07:00 [DBG] [2025-05-28 17:52:58.555] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:52:58.555 +07:00 [INF] [2025-05-28 17:52:58.555] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:52:58.555 +07:00 [DBG] [2025-05-28 17:52:58.555] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 17:52:58.555 +07:00 [DBG] [2025-05-28 17:52:58.555] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:52:58.582 +07:00 [INF] [2025-05-28 17:52:58.582] [INFO]: Tải danh sách user thành công
2025-05-28 17:52:58.582 +07:00 [INF] [2025-05-28 17:52:58.582] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 17:52:58.582 +07:00 [DBG] [2025-05-28 17:52:58.582] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 17:52:58.582 +07:00 [DBG] [2025-05-28 17:52:58.582] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:52:58.582 +07:00 [INF] [2025-05-28 17:52:58.582] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:52:58.582 +07:00 [DBG] [2025-05-28 17:52:58.582] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 17:52:58.582 +07:00 [DBG] [2025-05-28 17:52:58.582] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:52:58.582 +07:00 [INF] [2025-05-28 17:52:58.582] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:52:58.582 +07:00 [DBG] [2025-05-28 17:52:58.582] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 17:52:58.582 +07:00 [DBG] [2025-05-28 17:52:58.582] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:52:58.608 +07:00 [INF] [2025-05-28 17:52:58.608] [INFO]: Tải danh sách user thành công
2025-05-28 17:52:58.618 +07:00 [INF] [2025-05-28 17:52:58.618] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.82 giây
2025-05-28 17:52:58.625 +07:00 [INF] [2025-05-28 17:52:58.625] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.83 giây
2025-05-28 17:53:00.509 +07:00 [INF] [2025-05-28 17:53:00.509] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 17:53:01.568 +07:00 [INF] [2025-05-28 17:53:01.568] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 17:53:01.569 +07:00 [DBG] [2025-05-28 17:53:01.569] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:53:01.569 +07:00 [DBG] [2025-05-28 17:53:01.569] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 17:53:01.569 +07:00 [INF] [2025-05-28 17:53:01.569] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 17:53:01.569 +07:00 [DBG] [2025-05-28 17:53:01.569] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:53:01.569 +07:00 [DBG] [2025-05-28 17:53:01.569] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 17:53:01.569 +07:00 [INF] [2025-05-28 17:53:01.569] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 17:53:01.569 +07:00 [DBG] [2025-05-28 17:53:01.569] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 17:53:01.569 +07:00 [DBG] [2025-05-28 17:53:01.569] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:53:01.570 +07:00 [INF] [2025-05-28 17:53:01.570] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:53:01.570 +07:00 [DBG] [2025-05-28 17:53:01.570] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 17:53:01.570 +07:00 [DBG] [2025-05-28 17:53:01.570] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:53:01.570 +07:00 [INF] [2025-05-28 17:53:01.570] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:53:01.570 +07:00 [DBG] [2025-05-28 17:53:01.570] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 17:53:01.570 +07:00 [DBG] [2025-05-28 17:53:01.570] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:53:01.639 +07:00 [INF] [2025-05-28 17:53:01.639] [INFO]: Tải danh sách user thành công
2025-05-28 17:53:01.639 +07:00 [INF] [2025-05-28 17:53:01.639] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 17:53:01.639 +07:00 [DBG] [2025-05-28 17:53:01.639] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 17:53:01.639 +07:00 [DBG] [2025-05-28 17:53:01.639] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:53:01.639 +07:00 [INF] [2025-05-28 17:53:01.639] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:53:01.639 +07:00 [DBG] [2025-05-28 17:53:01.639] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 17:53:01.639 +07:00 [DBG] [2025-05-28 17:53:01.639] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:53:01.639 +07:00 [INF] [2025-05-28 17:53:01.639] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:53:01.639 +07:00 [DBG] [2025-05-28 17:53:01.639] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 17:53:01.639 +07:00 [DBG] [2025-05-28 17:53:01.639] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:53:01.691 +07:00 [INF] [2025-05-28 17:53:01.691] [INFO]: Tải danh sách user thành công
2025-05-28 17:53:01.707 +07:00 [INF] [2025-05-28 17:53:01.707] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.14 giây
2025-05-28 17:53:01.723 +07:00 [INF] [2025-05-28 17:53:01.723] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.15 giây
2025-05-28 17:53:04.570 +07:00 [DBG] [2025-05-28 17:53:04.570] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Phỏm
2025-05-28 17:53:04.575 +07:00 [DBG] [2025-05-28 17:53:04.575] [DEBUG]: Bắt đầu khởi tạo PhomSuggestionForm
2025-05-28 17:53:04.655 +07:00 [DBG] [2025-05-28 17:53:04.655] [DEBUG]: Đã gọi InitializeComponent
2025-05-28 17:53:04.685 +07:00 [INF] [2025-05-28 17:53:04.685] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 17:53:04.686 +07:00 [INF] [2025-05-28 17:53:04.686] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 17:53:04.695 +07:00 [INF] [2025-05-28 17:53:04.695] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 17:53:04.730 +07:00 [DBG] [2025-05-28 17:53:04.730] [DEBUG]: Kết thúc khởi tạo PhomSuggestionForm
2025-05-28 17:53:04.741 +07:00 [INF] [2025-05-28 17:53:04.741] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 0 played cards
2025-05-28 17:53:04.742 +07:00 [DBG] [2025-05-28 17:53:04.742] [DEBUG]: Không có lá bài nào đã được đánh - clearing display
2025-05-28 17:53:04.747 +07:00 [DBG] [2025-05-28 17:53:04.747] [DEBUG]: ✅ Đã xóa HOÀN TOÀN tất cả các lá bài đã đánh và giải phóng memory
2025-05-28 17:53:05.590 +07:00 [DBG] [2025-05-28 17:53:05.590] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 17:53:05.592 +07:00 [DBG] [2025-05-28 17:53:05.592] [DEBUG] [nhatrang345]: ℹ️ Không có điều kiện đặc biệt cho nhatrang345, giữ nguyên trong phòng (sit: 0, số người: 4)
2025-05-28 17:53:05.592 +07:00 [WRN] [2025-05-28 17:53:05.592] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 17:53:07.127 +07:00 [INF] [2025-05-28 17:53:07.127] [INFO]: Updated opponent known cards: [K♠]
2025-05-28 17:53:07.128 +07:00 [INF] [2025-05-28 17:53:07.128] [INFO] [nhatrang345]: Next turn: xucha95 (UID: 1_279027341)
2025-05-28 17:53:07.128 +07:00 [DBG] [2025-05-28 17:53:07.128] [DEBUG]: Updated last played card: [K♠]
2025-05-28 17:53:07.128 +07:00 [INF] [2025-05-28 17:53:07.128] [INFO] [nhatrang345]: Player Minhheuu played card 48, next: xucha95
2025-05-28 17:53:07.128 +07:00 [DBG] [2025-05-28 17:53:07.128] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:07.129 +07:00 [ERR] [2025-05-28 17:53:07.129] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:07.129 +07:00 [INF] [2025-05-28 17:53:07.129] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:07.129 +07:00 [DBG] [2025-05-28 17:53:07.129] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:07.129 +07:00 [INF] [2025-05-28 17:53:07.129] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 1 played cards
2025-05-28 17:53:07.130 +07:00 [INF] [2025-05-28 17:53:07.130] [INFO]: 🎴 Displaying played cards: [K♠]
2025-05-28 17:53:07.131 +07:00 [INF] [2025-05-28 17:53:07.131] [INFO]: Hiển thị 1 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:07.132 +07:00 [DBG] [2025-05-28 17:53:07.132] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 0
2025-05-28 17:53:09.071 +07:00 [INF] [2025-05-28 17:53:09.071] [INFO] [nhatrang345]: Player xucha95 drew card -1, new cards: []
2025-05-28 17:53:09.071 +07:00 [DBG] [2025-05-28 17:53:09.071] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:09.071 +07:00 [ERR] [2025-05-28 17:53:09.071] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:09.071 +07:00 [ERR] [2025-05-28 17:53:09.071] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: xucha95, cards: 0
2025-05-28 17:53:09.071 +07:00 [INF] [2025-05-28 17:53:09.071] [INFO]: ✅ Đánh dấu xucha95 đã rút bài
2025-05-28 17:53:09.683 +07:00 [INF] [2025-05-28 17:53:09.683] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 17:53:09.683 +07:00 [INF] [2025-05-28 17:53:09.683] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 17:53:09.693 +07:00 [INF] [2025-05-28 17:53:09.693] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 17:53:10.869 +07:00 [INF] [2025-05-28 17:53:10.869] [INFO]: Updated opponent known cards: [Q♦]
2025-05-28 17:53:10.869 +07:00 [INF] [2025-05-28 17:53:10.869] [INFO] [nhatrang345]: Next turn: HaiDzaiVcl123 (UID: 1_246920792)
2025-05-28 17:53:10.869 +07:00 [DBG] [2025-05-28 17:53:10.869] [DEBUG]: Updated last played card: [Q♦]
2025-05-28 17:53:10.869 +07:00 [INF] [2025-05-28 17:53:10.869] [INFO] [nhatrang345]: Player xucha95 played card 46, next: HaiDzaiVcl123
2025-05-28 17:53:10.869 +07:00 [DBG] [2025-05-28 17:53:10.869] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:10.869 +07:00 [ERR] [2025-05-28 17:53:10.869] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:10.869 +07:00 [INF] [2025-05-28 17:53:10.869] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:10.869 +07:00 [DBG] [2025-05-28 17:53:10.869] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:10.869 +07:00 [INF] [2025-05-28 17:53:10.869] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 2 played cards
2025-05-28 17:53:10.869 +07:00 [INF] [2025-05-28 17:53:10.869] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦]
2025-05-28 17:53:10.869 +07:00 [INF] [2025-05-28 17:53:10.869] [INFO]: Hiển thị 2 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:10.870 +07:00 [DBG] [2025-05-28 17:53:10.870] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 0
2025-05-28 17:53:10.870 +07:00 [DBG] [2025-05-28 17:53:10.870] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 1
2025-05-28 17:53:12.019 +07:00 [INF] [2025-05-28 17:53:12.019] [INFO] [nhatrang345]: Player HaiDzaiVcl123 ate card 46, new cards: []
2025-05-28 17:53:12.019 +07:00 [DBG] [2025-05-28 17:53:12.019] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:12.019 +07:00 [ERR] [2025-05-28 17:53:12.019] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:12.019 +07:00 [ERR] [2025-05-28 17:53:12.019] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: HaiDzaiVcl123, cards: 0
2025-05-28 17:53:12.019 +07:00 [INF] [2025-05-28 17:53:12.019] [INFO]: ✅ Đánh dấu HaiDzaiVcl123 đã ăn bài
2025-05-28 17:53:13.571 +07:00 [INF] [2025-05-28 17:53:13.571] [INFO]: Updated opponent known cards: [K♣]
2025-05-28 17:53:13.571 +07:00 [INF] [2025-05-28 17:53:13.571] [INFO] [nhatrang345]: Next turn: Minhheuu (UID: 22_341334)
2025-05-28 17:53:13.571 +07:00 [DBG] [2025-05-28 17:53:13.571] [DEBUG]: Updated last played card: [K♣]
2025-05-28 17:53:13.571 +07:00 [INF] [2025-05-28 17:53:13.571] [INFO] [nhatrang345]: Player HaiDzaiVcl123 played card 49, next: Minhheuu
2025-05-28 17:53:13.571 +07:00 [DBG] [2025-05-28 17:53:13.571] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:13.571 +07:00 [ERR] [2025-05-28 17:53:13.571] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:13.571 +07:00 [INF] [2025-05-28 17:53:13.571] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:13.571 +07:00 [DBG] [2025-05-28 17:53:13.571] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:13.571 +07:00 [INF] [2025-05-28 17:53:13.571] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 3 played cards
2025-05-28 17:53:13.571 +07:00 [INF] [2025-05-28 17:53:13.571] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣]
2025-05-28 17:53:13.571 +07:00 [INF] [2025-05-28 17:53:13.571] [INFO]: Hiển thị 3 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:13.572 +07:00 [DBG] [2025-05-28 17:53:13.572] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 0
2025-05-28 17:53:13.572 +07:00 [DBG] [2025-05-28 17:53:13.572] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 1
2025-05-28 17:53:13.573 +07:00 [DBG] [2025-05-28 17:53:13.573] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 2
2025-05-28 17:53:14.684 +07:00 [INF] [2025-05-28 17:53:14.684] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 17:53:14.684 +07:00 [INF] [2025-05-28 17:53:14.684] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 17:53:14.684 +07:00 [INF] [2025-05-28 17:53:14.684] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 17:53:14.745 +07:00 [INF] [2025-05-28 17:53:14.745] [INFO] [nhatrang345]: Player Minhheuu drew card -1, new cards: []
2025-05-28 17:53:14.745 +07:00 [DBG] [2025-05-28 17:53:14.745] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:14.745 +07:00 [ERR] [2025-05-28 17:53:14.745] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:14.745 +07:00 [ERR] [2025-05-28 17:53:14.745] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: Minhheuu, cards: 0
2025-05-28 17:53:14.745 +07:00 [INF] [2025-05-28 17:53:14.745] [INFO]: ✅ Đánh dấu Minhheuu đã rút bài
2025-05-28 17:53:18.109 +07:00 [INF] [2025-05-28 17:53:18.109] [INFO]: Updated opponent known cards: [J♦]
2025-05-28 17:53:18.109 +07:00 [INF] [2025-05-28 17:53:18.109] [INFO] [nhatrang345]: Next turn: xucha95 (UID: 1_279027341)
2025-05-28 17:53:18.109 +07:00 [DBG] [2025-05-28 17:53:18.109] [DEBUG]: Updated last played card: [J♦]
2025-05-28 17:53:18.109 +07:00 [INF] [2025-05-28 17:53:18.109] [INFO] [nhatrang345]: Player Minhheuu played card 42, next: xucha95
2025-05-28 17:53:18.109 +07:00 [DBG] [2025-05-28 17:53:18.109] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:18.109 +07:00 [ERR] [2025-05-28 17:53:18.109] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:18.109 +07:00 [INF] [2025-05-28 17:53:18.109] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:18.109 +07:00 [DBG] [2025-05-28 17:53:18.109] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:18.109 +07:00 [INF] [2025-05-28 17:53:18.109] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 4 played cards
2025-05-28 17:53:18.109 +07:00 [INF] [2025-05-28 17:53:18.109] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦]
2025-05-28 17:53:18.109 +07:00 [INF] [2025-05-28 17:53:18.109] [INFO]: Hiển thị 4 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:18.110 +07:00 [DBG] [2025-05-28 17:53:18.110] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 0
2025-05-28 17:53:18.110 +07:00 [DBG] [2025-05-28 17:53:18.110] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 1
2025-05-28 17:53:18.111 +07:00 [DBG] [2025-05-28 17:53:18.111] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 2
2025-05-28 17:53:18.111 +07:00 [DBG] [2025-05-28 17:53:18.111] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 3
2025-05-28 17:53:19.597 +07:00 [INF] [2025-05-28 17:53:19.597] [INFO] [nhatrang345]: Player xucha95 ate card 42, new cards: []
2025-05-28 17:53:19.597 +07:00 [DBG] [2025-05-28 17:53:19.597] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:19.597 +07:00 [ERR] [2025-05-28 17:53:19.597] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:19.597 +07:00 [ERR] [2025-05-28 17:53:19.597] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: xucha95, cards: 0
2025-05-28 17:53:19.597 +07:00 [INF] [2025-05-28 17:53:19.597] [INFO]: ✅ Đánh dấu xucha95 đã ăn bài
2025-05-28 17:53:19.685 +07:00 [INF] [2025-05-28 17:53:19.685] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 17:53:19.685 +07:00 [INF] [2025-05-28 17:53:19.685] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 17:53:19.688 +07:00 [INF] [2025-05-28 17:53:19.688] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 17:53:22.978 +07:00 [INF] [2025-05-28 17:53:22.978] [INFO]: Updated opponent known cards: [9♣]
2025-05-28 17:53:22.978 +07:00 [INF] [2025-05-28 17:53:22.978] [INFO] [nhatrang345]: Next turn: HaiDzaiVcl123 (UID: 1_246920792)
2025-05-28 17:53:22.978 +07:00 [DBG] [2025-05-28 17:53:22.978] [DEBUG]: Updated last played card: [9♣]
2025-05-28 17:53:22.978 +07:00 [INF] [2025-05-28 17:53:22.978] [INFO] [nhatrang345]: Player xucha95 played card 33, next: HaiDzaiVcl123
2025-05-28 17:53:22.978 +07:00 [DBG] [2025-05-28 17:53:22.978] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:22.978 +07:00 [ERR] [2025-05-28 17:53:22.978] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:22.978 +07:00 [INF] [2025-05-28 17:53:22.978] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:22.978 +07:00 [DBG] [2025-05-28 17:53:22.978] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:22.978 +07:00 [INF] [2025-05-28 17:53:22.978] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 5 played cards
2025-05-28 17:53:22.978 +07:00 [INF] [2025-05-28 17:53:22.978] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦],[9♣]
2025-05-28 17:53:22.979 +07:00 [INF] [2025-05-28 17:53:22.979] [INFO]: Hiển thị 5 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:22.979 +07:00 [DBG] [2025-05-28 17:53:22.979] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 0
2025-05-28 17:53:22.980 +07:00 [DBG] [2025-05-28 17:53:22.980] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 1
2025-05-28 17:53:22.980 +07:00 [DBG] [2025-05-28 17:53:22.980] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 2
2025-05-28 17:53:22.981 +07:00 [DBG] [2025-05-28 17:53:22.981] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 3
2025-05-28 17:53:22.981 +07:00 [DBG] [2025-05-28 17:53:22.981] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 4
2025-05-28 17:53:24.266 +07:00 [INF] [2025-05-28 17:53:24.266] [INFO] [nhatrang345]: Player HaiDzaiVcl123 drew card -1, new cards: []
2025-05-28 17:53:24.266 +07:00 [DBG] [2025-05-28 17:53:24.266] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:24.266 +07:00 [ERR] [2025-05-28 17:53:24.266] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:24.266 +07:00 [ERR] [2025-05-28 17:53:24.266] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: HaiDzaiVcl123, cards: 0
2025-05-28 17:53:24.266 +07:00 [INF] [2025-05-28 17:53:24.266] [INFO]: ✅ Đánh dấu HaiDzaiVcl123 đã rút bài
2025-05-28 17:53:24.688 +07:00 [INF] [2025-05-28 17:53:24.688] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 17:53:24.688 +07:00 [INF] [2025-05-28 17:53:24.688] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 17:53:24.690 +07:00 [INF] [2025-05-28 17:53:24.690] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 17:53:29.684 +07:00 [INF] [2025-05-28 17:53:29.684] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 17:53:29.684 +07:00 [INF] [2025-05-28 17:53:29.684] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 17:53:29.685 +07:00 [INF] [2025-05-28 17:53:29.685] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 17:53:29.861 +07:00 [INF] [2025-05-28 17:53:29.861] [INFO]: Updated opponent known cards: [3♠]
2025-05-28 17:53:29.861 +07:00 [INF] [2025-05-28 17:53:29.861] [INFO] [nhatrang345]: Next turn: Minhheuu (UID: 22_341334)
2025-05-28 17:53:29.861 +07:00 [DBG] [2025-05-28 17:53:29.861] [DEBUG]: Updated last played card: [3♠]
2025-05-28 17:53:29.861 +07:00 [INF] [2025-05-28 17:53:29.861] [INFO] [nhatrang345]: Player HaiDzaiVcl123 played card 8, next: Minhheuu
2025-05-28 17:53:29.861 +07:00 [DBG] [2025-05-28 17:53:29.861] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:29.861 +07:00 [ERR] [2025-05-28 17:53:29.861] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:29.861 +07:00 [INF] [2025-05-28 17:53:29.861] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:29.861 +07:00 [DBG] [2025-05-28 17:53:29.861] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:29.861 +07:00 [INF] [2025-05-28 17:53:29.861] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 6 played cards
2025-05-28 17:53:29.861 +07:00 [INF] [2025-05-28 17:53:29.861] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦],[9♣],[3♠]
2025-05-28 17:53:29.861 +07:00 [INF] [2025-05-28 17:53:29.861] [INFO]: Hiển thị 6 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:29.862 +07:00 [DBG] [2025-05-28 17:53:29.862] [DEBUG]: Hiển thị lá bài đã đánh 8 tại vị trí 0
2025-05-28 17:53:29.862 +07:00 [DBG] [2025-05-28 17:53:29.862] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 1
2025-05-28 17:53:29.863 +07:00 [DBG] [2025-05-28 17:53:29.863] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 2
2025-05-28 17:53:29.864 +07:00 [DBG] [2025-05-28 17:53:29.864] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 3
2025-05-28 17:53:29.865 +07:00 [DBG] [2025-05-28 17:53:29.865] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 4
2025-05-28 17:53:29.865 +07:00 [DBG] [2025-05-28 17:53:29.865] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 5
2025-05-28 17:53:31.065 +07:00 [INF] [2025-05-28 17:53:31.065] [INFO] [nhatrang345]: Player Minhheuu drew card -1, new cards: []
2025-05-28 17:53:31.065 +07:00 [DBG] [2025-05-28 17:53:31.065] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:31.065 +07:00 [ERR] [2025-05-28 17:53:31.065] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:31.065 +07:00 [ERR] [2025-05-28 17:53:31.065] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: Minhheuu, cards: 0
2025-05-28 17:53:31.065 +07:00 [INF] [2025-05-28 17:53:31.065] [INFO]: ✅ Đánh dấu Minhheuu đã rút bài
2025-05-28 17:53:33.534 +07:00 [INF] [2025-05-28 17:53:33.534] [INFO]: Updated opponent known cards: [10♦]
2025-05-28 17:53:33.535 +07:00 [INF] [2025-05-28 17:53:33.535] [INFO] [nhatrang345]: Next turn: xucha95 (UID: 1_279027341)
2025-05-28 17:53:33.535 +07:00 [DBG] [2025-05-28 17:53:33.535] [DEBUG]: Updated last played card: [10♦]
2025-05-28 17:53:33.535 +07:00 [INF] [2025-05-28 17:53:33.535] [INFO] [nhatrang345]: Player Minhheuu played card 38, next: xucha95
2025-05-28 17:53:33.535 +07:00 [DBG] [2025-05-28 17:53:33.535] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:33.535 +07:00 [ERR] [2025-05-28 17:53:33.535] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:33.535 +07:00 [INF] [2025-05-28 17:53:33.535] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:33.535 +07:00 [DBG] [2025-05-28 17:53:33.535] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:33.535 +07:00 [INF] [2025-05-28 17:53:33.535] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 7 played cards
2025-05-28 17:53:33.535 +07:00 [INF] [2025-05-28 17:53:33.535] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦],[9♣],[3♠],[10♦]
2025-05-28 17:53:33.535 +07:00 [INF] [2025-05-28 17:53:33.535] [INFO]: Hiển thị 7 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:33.536 +07:00 [DBG] [2025-05-28 17:53:33.536] [DEBUG]: Hiển thị lá bài đã đánh 8 tại vị trí 0
2025-05-28 17:53:33.536 +07:00 [DBG] [2025-05-28 17:53:33.536] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 1
2025-05-28 17:53:33.537 +07:00 [DBG] [2025-05-28 17:53:33.537] [DEBUG]: Hiển thị lá bài đã đánh 38 tại vị trí 2
2025-05-28 17:53:33.538 +07:00 [DBG] [2025-05-28 17:53:33.538] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 3
2025-05-28 17:53:33.538 +07:00 [DBG] [2025-05-28 17:53:33.538] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 4
2025-05-28 17:53:33.539 +07:00 [DBG] [2025-05-28 17:53:33.539] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 5
2025-05-28 17:53:33.539 +07:00 [DBG] [2025-05-28 17:53:33.539] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 6
2025-05-28 17:53:34.684 +07:00 [INF] [2025-05-28 17:53:34.684] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 17:53:34.684 +07:00 [INF] [2025-05-28 17:53:34.684] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 17:53:34.685 +07:00 [INF] [2025-05-28 17:53:34.685] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 17:53:35.677 +07:00 [INF] [2025-05-28 17:53:35.677] [INFO] [nhatrang345]: Player xucha95 drew card -1, new cards: []
2025-05-28 17:53:35.677 +07:00 [DBG] [2025-05-28 17:53:35.677] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:35.677 +07:00 [ERR] [2025-05-28 17:53:35.677] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:35.677 +07:00 [ERR] [2025-05-28 17:53:35.677] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: xucha95, cards: 0
2025-05-28 17:53:35.677 +07:00 [INF] [2025-05-28 17:53:35.677] [INFO]: ✅ Đánh dấu xucha95 đã rút bài
2025-05-28 17:53:39.685 +07:00 [INF] [2025-05-28 17:53:39.685] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 17:53:39.686 +07:00 [INF] [2025-05-28 17:53:39.686] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 17:53:39.687 +07:00 [INF] [2025-05-28 17:53:39.687] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 17:53:40.842 +07:00 [INF] [2025-05-28 17:53:40.842] [INFO]: Updated opponent known cards: [7♥]
2025-05-28 17:53:40.842 +07:00 [INF] [2025-05-28 17:53:40.842] [INFO] [nhatrang345]: Next turn: HaiDzaiVcl123 (UID: 1_246920792)
2025-05-28 17:53:40.842 +07:00 [DBG] [2025-05-28 17:53:40.842] [DEBUG]: Updated last played card: [7♥]
2025-05-28 17:53:40.842 +07:00 [INF] [2025-05-28 17:53:40.842] [INFO] [nhatrang345]: Player xucha95 played card 27, next: HaiDzaiVcl123
2025-05-28 17:53:40.842 +07:00 [DBG] [2025-05-28 17:53:40.842] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:40.842 +07:00 [ERR] [2025-05-28 17:53:40.842] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:40.842 +07:00 [INF] [2025-05-28 17:53:40.842] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:40.842 +07:00 [DBG] [2025-05-28 17:53:40.842] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:40.842 +07:00 [INF] [2025-05-28 17:53:40.842] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 8 played cards
2025-05-28 17:53:40.842 +07:00 [INF] [2025-05-28 17:53:40.842] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦],[9♣],[3♠],[10♦],[7♥]
2025-05-28 17:53:40.843 +07:00 [INF] [2025-05-28 17:53:40.843] [INFO]: Hiển thị 8 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:40.844 +07:00 [DBG] [2025-05-28 17:53:40.844] [DEBUG]: Hiển thị lá bài đã đánh 8 tại vị trí 0
2025-05-28 17:53:40.844 +07:00 [DBG] [2025-05-28 17:53:40.844] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 1
2025-05-28 17:53:40.845 +07:00 [DBG] [2025-05-28 17:53:40.845] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 2
2025-05-28 17:53:40.845 +07:00 [DBG] [2025-05-28 17:53:40.845] [DEBUG]: Hiển thị lá bài đã đánh 38 tại vị trí 3
2025-05-28 17:53:40.846 +07:00 [DBG] [2025-05-28 17:53:40.846] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 4
2025-05-28 17:53:40.846 +07:00 [DBG] [2025-05-28 17:53:40.846] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 5
2025-05-28 17:53:40.847 +07:00 [DBG] [2025-05-28 17:53:40.847] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 6
2025-05-28 17:53:40.847 +07:00 [DBG] [2025-05-28 17:53:40.847] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 7
2025-05-28 17:53:42.175 +07:00 [INF] [2025-05-28 17:53:42.175] [INFO] [nhatrang345]: Player HaiDzaiVcl123 drew card -1, new cards: []
2025-05-28 17:53:42.175 +07:00 [DBG] [2025-05-28 17:53:42.175] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:42.175 +07:00 [ERR] [2025-05-28 17:53:42.175] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:42.176 +07:00 [ERR] [2025-05-28 17:53:42.176] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: HaiDzaiVcl123, cards: 0
2025-05-28 17:53:42.176 +07:00 [INF] [2025-05-28 17:53:42.176] [INFO]: ✅ Đánh dấu HaiDzaiVcl123 đã rút bài
2025-05-28 17:53:44.402 +07:00 [INF] [2025-05-28 17:53:44.402] [INFO]: Updated opponent known cards: [5♠]
2025-05-28 17:53:44.402 +07:00 [INF] [2025-05-28 17:53:44.402] [INFO] [nhatrang345]: Next turn: Minhheuu (UID: 22_341334)
2025-05-28 17:53:44.402 +07:00 [DBG] [2025-05-28 17:53:44.402] [DEBUG]: Updated last played card: [5♠]
2025-05-28 17:53:44.402 +07:00 [INF] [2025-05-28 17:53:44.402] [INFO] [nhatrang345]: Player HaiDzaiVcl123 played card 16, next: Minhheuu
2025-05-28 17:53:44.402 +07:00 [DBG] [2025-05-28 17:53:44.402] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:44.402 +07:00 [ERR] [2025-05-28 17:53:44.402] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:44.402 +07:00 [INF] [2025-05-28 17:53:44.402] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:44.402 +07:00 [DBG] [2025-05-28 17:53:44.402] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:44.402 +07:00 [INF] [2025-05-28 17:53:44.402] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 9 played cards
2025-05-28 17:53:44.402 +07:00 [INF] [2025-05-28 17:53:44.402] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦],[9♣],[3♠],[10♦],[7♥],[5♠]
2025-05-28 17:53:44.403 +07:00 [INF] [2025-05-28 17:53:44.403] [INFO]: Hiển thị 9 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:44.404 +07:00 [DBG] [2025-05-28 17:53:44.404] [DEBUG]: Hiển thị lá bài đã đánh 8 tại vị trí 0
2025-05-28 17:53:44.404 +07:00 [DBG] [2025-05-28 17:53:44.404] [DEBUG]: Hiển thị lá bài đã đánh 16 tại vị trí 1
2025-05-28 17:53:44.405 +07:00 [DBG] [2025-05-28 17:53:44.405] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 2
2025-05-28 17:53:44.405 +07:00 [DBG] [2025-05-28 17:53:44.405] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 3
2025-05-28 17:53:44.406 +07:00 [DBG] [2025-05-28 17:53:44.406] [DEBUG]: Hiển thị lá bài đã đánh 38 tại vị trí 4
2025-05-28 17:53:44.406 +07:00 [DBG] [2025-05-28 17:53:44.406] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 5
2025-05-28 17:53:44.407 +07:00 [DBG] [2025-05-28 17:53:44.407] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 6
2025-05-28 17:53:44.407 +07:00 [DBG] [2025-05-28 17:53:44.407] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 7
2025-05-28 17:53:44.408 +07:00 [DBG] [2025-05-28 17:53:44.408] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 8
2025-05-28 17:53:44.695 +07:00 [INF] [2025-05-28 17:53:44.695] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 17:53:44.695 +07:00 [INF] [2025-05-28 17:53:44.695] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 17:53:44.861 +07:00 [INF] [2025-05-28 17:53:44.861] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 17:53:45.918 +07:00 [INF] [2025-05-28 17:53:45.918] [INFO] [nhatrang345]: Player Minhheuu drew card -1, new cards: []
2025-05-28 17:53:45.918 +07:00 [DBG] [2025-05-28 17:53:45.918] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:45.918 +07:00 [ERR] [2025-05-28 17:53:45.918] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:45.918 +07:00 [ERR] [2025-05-28 17:53:45.918] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: Minhheuu, cards: 0
2025-05-28 17:53:45.918 +07:00 [INF] [2025-05-28 17:53:45.918] [INFO]: ✅ Đánh dấu Minhheuu đã rút bài
2025-05-28 17:53:49.686 +07:00 [INF] [2025-05-28 17:53:49.686] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 17:53:49.686 +07:00 [INF] [2025-05-28 17:53:49.686] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 17:53:49.692 +07:00 [INF] [2025-05-28 17:53:49.692] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 17:53:52.552 +07:00 [INF] [2025-05-28 17:53:52.552] [INFO]: Updated opponent known cards: [8♣]
2025-05-28 17:53:52.552 +07:00 [INF] [2025-05-28 17:53:52.552] [INFO] [nhatrang345]: Next turn: xucha95 (UID: 1_279027341)
2025-05-28 17:53:52.552 +07:00 [DBG] [2025-05-28 17:53:52.552] [DEBUG]: Updated last played card: [8♣]
2025-05-28 17:53:52.552 +07:00 [INF] [2025-05-28 17:53:52.552] [INFO] [nhatrang345]: Player Minhheuu played card 29, next: xucha95
2025-05-28 17:53:52.552 +07:00 [DBG] [2025-05-28 17:53:52.552] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:52.552 +07:00 [ERR] [2025-05-28 17:53:52.552] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:52.552 +07:00 [INF] [2025-05-28 17:53:52.552] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:52.552 +07:00 [DBG] [2025-05-28 17:53:52.552] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:52.552 +07:00 [INF] [2025-05-28 17:53:52.552] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 10 played cards
2025-05-28 17:53:52.552 +07:00 [INF] [2025-05-28 17:53:52.552] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦],[9♣],[3♠],[10♦],[7♥],[5♠],[8♣]
2025-05-28 17:53:52.554 +07:00 [INF] [2025-05-28 17:53:52.554] [INFO]: Hiển thị 10 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:52.554 +07:00 [DBG] [2025-05-28 17:53:52.554] [DEBUG]: Hiển thị lá bài đã đánh 8 tại vị trí 0
2025-05-28 17:53:52.555 +07:00 [DBG] [2025-05-28 17:53:52.555] [DEBUG]: Hiển thị lá bài đã đánh 16 tại vị trí 1
2025-05-28 17:53:52.555 +07:00 [DBG] [2025-05-28 17:53:52.555] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 2
2025-05-28 17:53:52.555 +07:00 [DBG] [2025-05-28 17:53:52.555] [DEBUG]: Hiển thị lá bài đã đánh 29 tại vị trí 3
2025-05-28 17:53:52.556 +07:00 [DBG] [2025-05-28 17:53:52.556] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 4
2025-05-28 17:53:52.556 +07:00 [DBG] [2025-05-28 17:53:52.556] [DEBUG]: Hiển thị lá bài đã đánh 38 tại vị trí 5
2025-05-28 17:53:52.557 +07:00 [DBG] [2025-05-28 17:53:52.557] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 6
2025-05-28 17:53:52.557 +07:00 [DBG] [2025-05-28 17:53:52.557] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 7
2025-05-28 17:53:52.558 +07:00 [DBG] [2025-05-28 17:53:52.558] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 8
2025-05-28 17:53:52.558 +07:00 [DBG] [2025-05-28 17:53:52.558] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 9
2025-05-28 17:53:54.685 +07:00 [INF] [2025-05-28 17:53:54.685] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 17:53:54.685 +07:00 [INF] [2025-05-28 17:53:54.685] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 17:53:54.700 +07:00 [INF] [2025-05-28 17:53:54.700] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 17:53:55.508 +07:00 [INF] [2025-05-28 17:53:55.508] [INFO] [nhatrang345]: Player xucha95 drew card -1, new cards: []
2025-05-28 17:53:55.508 +07:00 [DBG] [2025-05-28 17:53:55.508] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:55.508 +07:00 [ERR] [2025-05-28 17:53:55.508] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:55.508 +07:00 [ERR] [2025-05-28 17:53:55.508] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: xucha95, cards: 0
2025-05-28 17:53:55.508 +07:00 [INF] [2025-05-28 17:53:55.508] [INFO]: ✅ Đánh dấu xucha95 đã rút bài
2025-05-28 17:53:57.354 +07:00 [DBG] [2025-05-28 17:53:57.354] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:57.354 +07:00 [ERR] [2025-05-28 17:53:57.354] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:57.354 +07:00 [INF] [2025-05-28 17:53:57.354] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:57.354 +07:00 [DBG] [2025-05-28 17:53:57.354] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:57.354 +07:00 [INF] [2025-05-28 17:53:57.354] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 10 played cards
2025-05-28 17:53:57.354 +07:00 [INF] [2025-05-28 17:53:57.354] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦],[9♣],[3♠],[10♦],[7♥],[5♠],[8♣]
2025-05-28 17:53:57.355 +07:00 [INF] [2025-05-28 17:53:57.355] [INFO]: Hiển thị 10 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:57.355 +07:00 [DBG] [2025-05-28 17:53:57.355] [DEBUG]: Hiển thị lá bài đã đánh 8 tại vị trí 0
2025-05-28 17:53:57.356 +07:00 [DBG] [2025-05-28 17:53:57.356] [DEBUG]: Hiển thị lá bài đã đánh 16 tại vị trí 1
2025-05-28 17:53:57.356 +07:00 [DBG] [2025-05-28 17:53:57.356] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 2
2025-05-28 17:53:57.357 +07:00 [DBG] [2025-05-28 17:53:57.357] [DEBUG]: Hiển thị lá bài đã đánh 29 tại vị trí 3
2025-05-28 17:53:57.357 +07:00 [DBG] [2025-05-28 17:53:57.357] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 4
2025-05-28 17:53:57.358 +07:00 [DBG] [2025-05-28 17:53:57.358] [DEBUG]: Hiển thị lá bài đã đánh 38 tại vị trí 5
2025-05-28 17:53:57.358 +07:00 [DBG] [2025-05-28 17:53:57.358] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 6
2025-05-28 17:53:57.359 +07:00 [DBG] [2025-05-28 17:53:57.359] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 7
2025-05-28 17:53:57.359 +07:00 [DBG] [2025-05-28 17:53:57.359] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 8
2025-05-28 17:53:57.359 +07:00 [DBG] [2025-05-28 17:53:57.359] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 9
2025-05-28 17:53:59.701 +07:00 [INF] [2025-05-28 17:53:59.701] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 17:53:59.701 +07:00 [INF] [2025-05-28 17:53:59.701] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 17:53:59.703 +07:00 [INF] [2025-05-28 17:53:59.703] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 17:53:59.825 +07:00 [INF] [2025-05-28 17:53:59.825] [INFO]: Updated opponent known cards: [5♦]
2025-05-28 17:53:59.825 +07:00 [INF] [2025-05-28 17:53:59.825] [INFO] [nhatrang345]: Next turn: HaiDzaiVcl123 (UID: 1_246920792)
2025-05-28 17:53:59.825 +07:00 [DBG] [2025-05-28 17:53:59.825] [DEBUG]: Updated last played card: [5♦]
2025-05-28 17:53:59.825 +07:00 [INF] [2025-05-28 17:53:59.825] [INFO] [nhatrang345]: Player xucha95 played card 18, next: HaiDzaiVcl123
2025-05-28 17:53:59.825 +07:00 [DBG] [2025-05-28 17:53:59.825] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:53:59.825 +07:00 [ERR] [2025-05-28 17:53:59.825] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:53:59.825 +07:00 [INF] [2025-05-28 17:53:59.825] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:53:59.825 +07:00 [DBG] [2025-05-28 17:53:59.825] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:53:59.825 +07:00 [INF] [2025-05-28 17:53:59.825] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 11 played cards
2025-05-28 17:53:59.825 +07:00 [INF] [2025-05-28 17:53:59.825] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦],[9♣],[3♠],[10♦],[7♥],[5♠],[8♣],[5♦]
2025-05-28 17:53:59.827 +07:00 [INF] [2025-05-28 17:53:59.827] [INFO]: Hiển thị 11 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:53:59.827 +07:00 [DBG] [2025-05-28 17:53:59.827] [DEBUG]: Hiển thị lá bài đã đánh 8 tại vị trí 0
2025-05-28 17:53:59.828 +07:00 [DBG] [2025-05-28 17:53:59.828] [DEBUG]: Hiển thị lá bài đã đánh 16 tại vị trí 1
2025-05-28 17:53:59.829 +07:00 [DBG] [2025-05-28 17:53:59.829] [DEBUG]: Hiển thị lá bài đã đánh 18 tại vị trí 2
2025-05-28 17:53:59.829 +07:00 [DBG] [2025-05-28 17:53:59.829] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 3
2025-05-28 17:53:59.829 +07:00 [DBG] [2025-05-28 17:53:59.829] [DEBUG]: Hiển thị lá bài đã đánh 29 tại vị trí 4
2025-05-28 17:53:59.830 +07:00 [DBG] [2025-05-28 17:53:59.830] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 5
2025-05-28 17:53:59.830 +07:00 [DBG] [2025-05-28 17:53:59.830] [DEBUG]: Hiển thị lá bài đã đánh 38 tại vị trí 6
2025-05-28 17:53:59.831 +07:00 [DBG] [2025-05-28 17:53:59.831] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 7
2025-05-28 17:53:59.831 +07:00 [DBG] [2025-05-28 17:53:59.831] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 8
2025-05-28 17:53:59.832 +07:00 [DBG] [2025-05-28 17:53:59.832] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 9
2025-05-28 17:53:59.832 +07:00 [DBG] [2025-05-28 17:53:59.832] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 10
2025-05-28 17:54:04.705 +07:00 [INF] [2025-05-28 17:54:04.705] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 17:54:04.705 +07:00 [INF] [2025-05-28 17:54:04.705] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 17:54:06.824 +07:00 [INF] [2025-05-28 17:54:06.824] [INFO] [nhatrang345]: Player HaiDzaiVcl123 drew card -1, new cards: []
2025-05-28 17:54:06.824 +07:00 [DBG] [2025-05-28 17:54:06.824] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:06.824 +07:00 [ERR] [2025-05-28 17:54:06.824] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:54:06.824 +07:00 [ERR] [2025-05-28 17:54:06.824] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: HaiDzaiVcl123, cards: 0
2025-05-28 17:54:06.824 +07:00 [INF] [2025-05-28 17:54:06.824] [INFO]: ✅ Đánh dấu HaiDzaiVcl123 đã rút bài
2025-05-28 17:54:07.262 +07:00 [INF] [2025-05-28 17:54:07.262] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 17:54:09.016 +07:00 [DBG] [2025-05-28 17:54:09.016] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:09.016 +07:00 [ERR] [2025-05-28 17:54:09.016] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:54:09.016 +07:00 [INF] [2025-05-28 17:54:09.016] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:54:09.016 +07:00 [DBG] [2025-05-28 17:54:09.016] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:54:09.016 +07:00 [INF] [2025-05-28 17:54:09.016] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 11 played cards
2025-05-28 17:54:09.016 +07:00 [INF] [2025-05-28 17:54:09.016] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦],[9♣],[3♠],[10♦],[7♥],[5♠],[8♣],[5♦]
2025-05-28 17:54:09.017 +07:00 [INF] [2025-05-28 17:54:09.017] [INFO]: Hiển thị 11 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:54:09.017 +07:00 [DBG] [2025-05-28 17:54:09.017] [DEBUG]: Hiển thị lá bài đã đánh 8 tại vị trí 0
2025-05-28 17:54:09.018 +07:00 [DBG] [2025-05-28 17:54:09.018] [DEBUG]: Hiển thị lá bài đã đánh 16 tại vị trí 1
2025-05-28 17:54:09.018 +07:00 [DBG] [2025-05-28 17:54:09.018] [DEBUG]: Hiển thị lá bài đã đánh 18 tại vị trí 2
2025-05-28 17:54:09.019 +07:00 [DBG] [2025-05-28 17:54:09.019] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 3
2025-05-28 17:54:09.019 +07:00 [DBG] [2025-05-28 17:54:09.019] [DEBUG]: Hiển thị lá bài đã đánh 29 tại vị trí 4
2025-05-28 17:54:09.020 +07:00 [DBG] [2025-05-28 17:54:09.020] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 5
2025-05-28 17:54:09.020 +07:00 [DBG] [2025-05-28 17:54:09.020] [DEBUG]: Hiển thị lá bài đã đánh 38 tại vị trí 6
2025-05-28 17:54:09.021 +07:00 [DBG] [2025-05-28 17:54:09.021] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 7
2025-05-28 17:54:09.021 +07:00 [DBG] [2025-05-28 17:54:09.021] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 8
2025-05-28 17:54:09.022 +07:00 [DBG] [2025-05-28 17:54:09.022] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 9
2025-05-28 17:54:09.022 +07:00 [DBG] [2025-05-28 17:54:09.022] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 10
2025-05-28 17:54:09.705 +07:00 [INF] [2025-05-28 17:54:09.704] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 17:54:09.705 +07:00 [INF] [2025-05-28 17:54:09.705] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 17:54:10.355 +07:00 [INF] [2025-05-28 17:54:10.355] [INFO]: Updated opponent known cards: [10♥]
2025-05-28 17:54:10.355 +07:00 [INF] [2025-05-28 17:54:10.355] [INFO] [nhatrang345]: Next turn: Minhheuu (UID: 22_341334)
2025-05-28 17:54:10.355 +07:00 [DBG] [2025-05-28 17:54:10.355] [DEBUG]: Updated last played card: [10♥]
2025-05-28 17:54:10.355 +07:00 [INF] [2025-05-28 17:54:10.355] [INFO] [nhatrang345]: Player HaiDzaiVcl123 played card 39, next: Minhheuu
2025-05-28 17:54:10.355 +07:00 [DBG] [2025-05-28 17:54:10.355] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:10.355 +07:00 [ERR] [2025-05-28 17:54:10.355] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:54:10.355 +07:00 [INF] [2025-05-28 17:54:10.355] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:54:10.355 +07:00 [DBG] [2025-05-28 17:54:10.355] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:54:10.355 +07:00 [INF] [2025-05-28 17:54:10.355] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 12 played cards
2025-05-28 17:54:10.355 +07:00 [INF] [2025-05-28 17:54:10.355] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦],[9♣],[3♠],[10♦],[7♥],[5♠],[8♣],[5♦],[10♥]
2025-05-28 17:54:10.357 +07:00 [INF] [2025-05-28 17:54:10.357] [INFO]: Hiển thị 12 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:54:10.357 +07:00 [DBG] [2025-05-28 17:54:10.357] [DEBUG]: Hiển thị lá bài đã đánh 8 tại vị trí 0
2025-05-28 17:54:10.358 +07:00 [DBG] [2025-05-28 17:54:10.358] [DEBUG]: Hiển thị lá bài đã đánh 16 tại vị trí 1
2025-05-28 17:54:10.358 +07:00 [DBG] [2025-05-28 17:54:10.358] [DEBUG]: Hiển thị lá bài đã đánh 18 tại vị trí 2
2025-05-28 17:54:10.359 +07:00 [DBG] [2025-05-28 17:54:10.359] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 3
2025-05-28 17:54:10.359 +07:00 [DBG] [2025-05-28 17:54:10.359] [DEBUG]: Hiển thị lá bài đã đánh 29 tại vị trí 4
2025-05-28 17:54:10.359 +07:00 [DBG] [2025-05-28 17:54:10.359] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 5
2025-05-28 17:54:10.360 +07:00 [DBG] [2025-05-28 17:54:10.360] [DEBUG]: Hiển thị lá bài đã đánh 38 tại vị trí 6
2025-05-28 17:54:10.360 +07:00 [DBG] [2025-05-28 17:54:10.360] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 7
2025-05-28 17:54:10.361 +07:00 [DBG] [2025-05-28 17:54:10.361] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 8
2025-05-28 17:54:10.361 +07:00 [DBG] [2025-05-28 17:54:10.361] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 9
2025-05-28 17:54:10.362 +07:00 [DBG] [2025-05-28 17:54:10.362] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 10
2025-05-28 17:54:10.363 +07:00 [DBG] [2025-05-28 17:54:10.363] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 11
2025-05-28 17:54:11.268 +07:00 [INF] [2025-05-28 17:54:11.268] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 17:54:11.848 +07:00 [INF] [2025-05-28 17:54:11.848] [INFO] [nhatrang345]: Player Minhheuu drew card -1, new cards: []
2025-05-28 17:54:11.848 +07:00 [DBG] [2025-05-28 17:54:11.848] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:11.848 +07:00 [ERR] [2025-05-28 17:54:11.848] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:54:11.848 +07:00 [ERR] [2025-05-28 17:54:11.848] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: Minhheuu, cards: 0
2025-05-28 17:54:11.848 +07:00 [INF] [2025-05-28 17:54:11.848] [INFO]: ✅ Đánh dấu Minhheuu đã rút bài
2025-05-28 17:54:13.782 +07:00 [DBG] [2025-05-28 17:54:13.782] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:13.782 +07:00 [ERR] [2025-05-28 17:54:13.782] [ERROR]: userCardsDict rỗng hoặc null
2025-05-28 17:54:13.782 +07:00 [INF] [2025-05-28 17:54:13.782] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:54:13.782 +07:00 [DBG] [2025-05-28 17:54:13.782] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 0 users
2025-05-28 17:54:13.782 +07:00 [INF] [2025-05-28 17:54:13.782] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 12 played cards
2025-05-28 17:54:13.782 +07:00 [INF] [2025-05-28 17:54:13.782] [INFO]: 🎴 Displaying played cards: [K♠],[Q♦],[K♣],[J♦],[9♣],[3♠],[10♦],[7♥],[5♠],[8♣],[5♦],[10♥]
2025-05-28 17:54:13.784 +07:00 [INF] [2025-05-28 17:54:13.784] [INFO]: Hiển thị 12 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:54:13.784 +07:00 [DBG] [2025-05-28 17:54:13.784] [DEBUG]: Hiển thị lá bài đã đánh 8 tại vị trí 0
2025-05-28 17:54:13.785 +07:00 [DBG] [2025-05-28 17:54:13.785] [DEBUG]: Hiển thị lá bài đã đánh 16 tại vị trí 1
2025-05-28 17:54:13.785 +07:00 [DBG] [2025-05-28 17:54:13.785] [DEBUG]: Hiển thị lá bài đã đánh 18 tại vị trí 2
2025-05-28 17:54:13.786 +07:00 [DBG] [2025-05-28 17:54:13.786] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 3
2025-05-28 17:54:13.786 +07:00 [DBG] [2025-05-28 17:54:13.786] [DEBUG]: Hiển thị lá bài đã đánh 29 tại vị trí 4
2025-05-28 17:54:13.787 +07:00 [DBG] [2025-05-28 17:54:13.787] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 5
2025-05-28 17:54:13.787 +07:00 [DBG] [2025-05-28 17:54:13.787] [DEBUG]: Hiển thị lá bài đã đánh 38 tại vị trí 6
2025-05-28 17:54:13.787 +07:00 [DBG] [2025-05-28 17:54:13.787] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 7
2025-05-28 17:54:13.788 +07:00 [DBG] [2025-05-28 17:54:13.788] [DEBUG]: Hiển thị lá bài đã đánh 42 tại vị trí 8
2025-05-28 17:54:13.788 +07:00 [DBG] [2025-05-28 17:54:13.788] [DEBUG]: Hiển thị lá bài đã đánh 46 tại vị trí 9
2025-05-28 17:54:13.789 +07:00 [DBG] [2025-05-28 17:54:13.789] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 10
2025-05-28 17:54:13.789 +07:00 [DBG] [2025-05-28 17:54:13.789] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 11
2025-05-28 17:54:14.702 +07:00 [INF] [2025-05-28 17:54:14.702] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 17:54:14.702 +07:00 [INF] [2025-05-28 17:54:14.702] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 17:54:14.704 +07:00 [INF] [2025-05-28 17:54:14.704] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 17:54:16.485 +07:00 [DBG] [2025-05-28 17:54:16.485] [DEBUG]: ✅ Đã xóa HOÀN TOÀN tất cả các lá bài đã đánh và giải phóng memory
2025-05-28 17:54:16.485 +07:00 [INF] [2025-05-28 17:54:16.485] [INFO]: Cleared opponent played cards after Phom game ended
2025-05-28 17:54:16.485 +07:00 [DBG] [2025-05-28 17:54:16.485] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:54:16.486 +07:00 [DBG] [2025-05-28 17:54:16.486] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:54:16.486 +07:00 [DBG] [2025-05-28 17:54:16.486] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 17:54:16.487 +07:00 [INF] [2025-05-28 17:54:16.487] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 17:54:16.487 +07:00 [DBG] [2025-05-28 17:54:16.487] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 17:54:16.487 +07:00 [DBG] [2025-05-28 17:54:16.487] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:54:16.487 +07:00 [INF] [2025-05-28 17:54:16.487] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:54:16.487 +07:00 [DBG] [2025-05-28 17:54:16.487] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 17:54:16.487 +07:00 [DBG] [2025-05-28 17:54:16.487] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:54:16.487 +07:00 [INF] [2025-05-28 17:54:16.487] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:54:16.487 +07:00 [DBG] [2025-05-28 17:54:16.487] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 17:54:16.487 +07:00 [DBG] [2025-05-28 17:54:16.487] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:54:16.559 +07:00 [INF] [2025-05-28 17:54:16.559] [INFO]: Tải danh sách user thành công
2025-05-28 17:54:16.700 +07:00 [DBG] [2025-05-28 17:54:16.700] [DEBUG]: ✅ Đã xóa HOÀN TOÀN tất cả các lá bài đã đánh và giải phóng memory
2025-05-28 17:54:16.701 +07:00 [INF] [2025-05-28 17:54:16.701] [INFO]: ✅ Đã reset played cards (12 lá), drawn cards (0 lá) và last played card
2025-05-28 17:54:16.712 +07:00 [INF] [2025-05-28 17:54:16.712] [INFO]: ✅ Đã reset hoàn toàn trạng thái PhomSuggestionForm
2025-05-28 17:54:16.712 +07:00 [INF] [2025-05-28 17:54:16.712] [INFO]: ✅ Reset PhomSuggestionForm state completely
2025-05-28 17:54:16.712 +07:00 [INF] [2025-05-28 17:54:16.712] [INFO]: Reset Phom game data
2025-05-28 17:54:16.712 +07:00 [INF] [2025-05-28 17:54:16.712] [INFO]: Nhận được thông báo kết thúc ván
2025-05-28 17:54:16.725 +07:00 [INF] [2025-05-28 17:54:16.725] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.24 giây
2025-05-28 17:54:16.871 +07:00 [DBG] [2025-05-28 17:54:16.871] [DEBUG]: ✅ Đã xóa HOÀN TOÀN tất cả các lá bài đã đánh và giải phóng memory
2025-05-28 17:54:16.871 +07:00 [INF] [2025-05-28 17:54:16.871] [INFO]: ✅ Đã reset played cards (0 lá), drawn cards (0 lá) và last played card
2025-05-28 17:54:16.882 +07:00 [INF] [2025-05-28 17:54:16.882] [INFO]: ✅ Đã reset hoàn toàn trạng thái PhomSuggestionForm
2025-05-28 17:54:19.701 +07:00 [INF] [2025-05-28 17:54:19.701] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 17:54:19.701 +07:00 [INF] [2025-05-28 17:54:19.701] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 17:54:19.705 +07:00 [INF] [2025-05-28 17:54:19.705] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 17:54:23.885 +07:00 [INF] [2025-05-28 17:54:23.885] [INFO] [nhatrang345]: Player order: 1_279027341, 22_341334, 1_246920792, 1_229699688
2025-05-28 17:54:23.885 +07:00 [INF] [2025-05-28 17:54:23.885] [INFO] [nhatrang345]: Next turn: HaiDzaiVcl123 (UID: 1_246920792)
2025-05-28 17:54:23.885 +07:00 [INF] [2025-05-28 17:54:23.885] [INFO] [nhatrang345]: Cmd 850 for nhatrang345: Cards: [2♠, 2♣, 2♥, 5♣, 7♣, 3♦, 4♥, 6♠, K♠]
2025-05-28 17:54:23.885 +07:00 [DBG] [2025-05-28 17:54:23.885] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:23.885 +07:00 [INF] [2025-05-28 17:54:23.885] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:54:24.032 +07:00 [INF] [2025-05-28 17:54:24.032] [INFO]: Chế độ chơi: Team
2025-05-28 17:54:24.032 +07:00 [INF] [2025-05-28 17:54:24.032] [INFO]: Xử lý chế độ Team
2025-05-28 17:54:24.035 +07:00 [DBG] [2025-05-28 17:54:24.035] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:24.037 +07:00 [INF] [2025-05-28 17:54:24.037] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:54:24.038 +07:00 [ERR] [2025-05-28 17:54:24.038] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:54:24.038 +07:00 [INF] [2025-05-28 17:54:24.038] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:54:24.703 +07:00 [INF] [2025-05-28 17:54:24.703] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 17:54:24.703 +07:00 [INF] [2025-05-28 17:54:24.703] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 17:54:24.703 +07:00 [INF] [2025-05-28 17:54:24.703] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,17]
2025-05-28 17:54:29.709 +07:00 [INF] [2025-05-28 17:54:29.709] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 17:54:29.709 +07:00 [INF] [2025-05-28 17:54:29.709] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 17:54:29.710 +07:00 [INF] [2025-05-28 17:54:29.710] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,18]
2025-05-28 17:54:32.448 +07:00 [INF] [2025-05-28 17:54:32.448] [INFO]: Updated opponent known cards: [Q♠]
2025-05-28 17:54:32.448 +07:00 [INF] [2025-05-28 17:54:32.448] [INFO] [nhatrang345]: Next turn: nhatrang345 (UID: 1_229699688)
2025-05-28 17:54:32.448 +07:00 [DBG] [2025-05-28 17:54:32.448] [DEBUG]: Updated last played card: [Q♠]
2025-05-28 17:54:32.448 +07:00 [INF] [2025-05-28 17:54:32.448] [INFO] [nhatrang345]: Player HaiDzaiVcl123 played card 44, next: nhatrang345
2025-05-28 17:54:32.448 +07:00 [DBG] [2025-05-28 17:54:32.448] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:32.448 +07:00 [INF] [2025-05-28 17:54:32.448] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:54:32.586 +07:00 [INF] [2025-05-28 17:54:32.586] [INFO]: Chế độ chơi: Team
2025-05-28 17:54:32.586 +07:00 [INF] [2025-05-28 17:54:32.586] [INFO]: Xử lý chế độ Team
2025-05-28 17:54:32.586 +07:00 [DBG] [2025-05-28 17:54:32.586] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:32.586 +07:00 [INF] [2025-05-28 17:54:32.586] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:54:32.587 +07:00 [ERR] [2025-05-28 17:54:32.587] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:54:32.587 +07:00 [INF] [2025-05-28 17:54:32.587] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:54:32.615 +07:00 [INF] [2025-05-28 17:54:32.615] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:54:32.615 +07:00 [DBG] [2025-05-28 17:54:32.615] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:54:32.615 +07:00 [DBG] [2025-05-28 17:54:32.615] [DEBUG]: ⏸️ SKIP nhatrang345: Không có thay đổi cần update
2025-05-28 17:54:32.615 +07:00 [INF] [2025-05-28 17:54:32.615] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 1 played cards
2025-05-28 17:54:32.615 +07:00 [INF] [2025-05-28 17:54:32.615] [INFO]: 🎴 Displaying played cards: [Q♠]
2025-05-28 17:54:32.615 +07:00 [INF] [2025-05-28 17:54:32.615] [INFO]: Hiển thị 1 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:54:32.616 +07:00 [DBG] [2025-05-28 17:54:32.616] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 0
2025-05-28 17:54:33.592 +07:00 [INF] [2025-05-28 17:54:33.592] [INFO] [nhatrang345]: Updated cards for nhatrang345 after drawing: [2♠, 2♣, 2♥, 5♣, 7♣, 3♦, 4♥, 6♠, 10♥, K♠]
2025-05-28 17:54:33.592 +07:00 [DBG] [2025-05-28 17:54:33.592] [DEBUG]: Updated drawn card: [10♥]
2025-05-28 17:54:33.592 +07:00 [INF] [2025-05-28 17:54:33.592] [INFO] [nhatrang345]: Player nhatrang345 drew card 39, new cards: [2♠, 2♣, 2♥, 5♣, 7♣, 3♦, 4♥, 6♠, 10♥, K♠]
2025-05-28 17:54:33.592 +07:00 [DBG] [2025-05-28 17:54:33.592] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:33.592 +07:00 [INF] [2025-05-28 17:54:33.592] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:54:33.731 +07:00 [INF] [2025-05-28 17:54:33.731] [INFO]: Chế độ chơi: Team
2025-05-28 17:54:33.731 +07:00 [INF] [2025-05-28 17:54:33.731] [INFO]: Xử lý chế độ Team
2025-05-28 17:54:33.731 +07:00 [DBG] [2025-05-28 17:54:33.731] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:33.731 +07:00 [INF] [2025-05-28 17:54:33.731] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:54:33.731 +07:00 [ERR] [2025-05-28 17:54:33.731] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:54:33.731 +07:00 [INF] [2025-05-28 17:54:33.731] [INFO]: nhatrang345 đi trước (10 lá bài)
2025-05-28 17:54:33.732 +07:00 [DBG] [2025-05-28 17:54:33.732] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:54:33.732 +07:00 [INF] [2025-05-28 17:54:33.732] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:54:33.734 +07:00 [INF] [2025-05-28 17:54:33.734] [INFO]: 📊 nhatrang345: 1 phỏm, 0 cạ, 7 rác
2025-05-28 17:54:33.735 +07:00 [INF] [2025-05-28 17:54:33.735] [INFO]: 🔍 Solo analysis: 10 tổng, 3 bảo vệ, 7 có thể đánh, 41 ẩn
2025-05-28 17:54:33.736 +07:00 [INF] [2025-05-28 17:54:33.736] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([K♠]) - Bảo vệ 3 lá - Ẩn: 41
2025-05-28 17:54:33.736 +07:00 [INF] [2025-05-28 17:54:33.736] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [K♠] tại card/48.png
2025-05-28 17:54:33.765 +07:00 [INF] [2025-05-28 17:54:33.765] [INFO]: 🔄 UPDATE PANEL: nhatrang345 tại panel 0 với 10 lá bài (Turn: False, DrawnOrEaten: True)
2025-05-28 17:54:33.765 +07:00 [DBG] [2025-05-28 17:54:33.765] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 10
2025-05-28 17:54:33.792 +07:00 [DBG] [2025-05-28 17:54:33.792] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:33.818 +07:00 [DBG] [2025-05-28 17:54:33.818] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 3♦, 4♥, 6♠, 10♥, K♠]
2025-05-28 17:54:33.818 +07:00 [DBG] [2025-05-28 17:54:33.818] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 7
2025-05-28 17:54:33.821 +07:00 [DBG] [2025-05-28 17:54:33.821] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 7
2025-05-28 17:54:33.852 +07:00 [DBG] [2025-05-28 17:54:33.851] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 7
2025-05-28 17:54:33.852 +07:00 [DBG] [2025-05-28 17:54:33.852] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:54:33.852 +07:00 [INF] [2025-05-28 17:54:33.852] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:54:33.852 +07:00 [INF] [2025-05-28 17:54:33.852] [INFO]: 📊 nhatrang345: 1 phỏm, 0 cạ, 7 rác
2025-05-28 17:54:33.852 +07:00 [INF] [2025-05-28 17:54:33.852] [INFO]: 🔍 Solo analysis: 10 tổng, 3 bảo vệ, 7 có thể đánh, 41 ẩn
2025-05-28 17:54:33.852 +07:00 [INF] [2025-05-28 17:54:33.852] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([K♠]) - Bảo vệ 3 lá - Ẩn: 41
2025-05-28 17:54:33.852 +07:00 [INF] [2025-05-28 17:54:33.852] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [K♠] tại card/48.png
2025-05-28 17:54:33.852 +07:00 [INF] [2025-05-28 17:54:33.852] [INFO]: ✅ Đánh dấu nhatrang345 đã rút bài
2025-05-28 17:54:33.852 +07:00 [INF] [2025-05-28 17:54:33.852] [INFO]: 🎯 Trigger gợi ý cho nhatrang345 sau khi rút/ăn bài
2025-05-28 17:54:33.852 +07:00 [DBG] [2025-05-28 17:54:33.852] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:54:33.852 +07:00 [INF] [2025-05-28 17:54:33.852] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:54:33.852 +07:00 [INF] [2025-05-28 17:54:33.852] [INFO]: 📊 nhatrang345: 1 phỏm, 0 cạ, 7 rác
2025-05-28 17:54:33.852 +07:00 [INF] [2025-05-28 17:54:33.852] [INFO]: 🔍 Solo analysis: 10 tổng, 3 bảo vệ, 7 có thể đánh, 41 ẩn
2025-05-28 17:54:33.852 +07:00 [INF] [2025-05-28 17:54:33.852] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([K♠]) - Bảo vệ 3 lá - Ẩn: 41
2025-05-28 17:54:33.853 +07:00 [INF] [2025-05-28 17:54:33.853] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [K♠] tại card/48.png
2025-05-28 17:54:34.701 +07:00 [INF] [2025-05-28 17:54:34.701] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 17:54:34.701 +07:00 [INF] [2025-05-28 17:54:34.701] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 17:54:34.705 +07:00 [INF] [2025-05-28 17:54:34.705] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,19]
2025-05-28 17:54:39.702 +07:00 [INF] [2025-05-28 17:54:39.702] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 17:54:39.702 +07:00 [INF] [2025-05-28 17:54:39.702] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 17:54:39.703 +07:00 [INF] [2025-05-28 17:54:39.703] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,20]
2025-05-28 17:54:40.766 +07:00 [INF] [2025-05-28 17:54:40.766] [INFO] [nhatrang345]: Updated cards for nhatrang345: [2♠, 2♣, 2♥, 5♣, 7♣, 3♦, 4♥, 6♠, 10♥]
2025-05-28 17:54:40.766 +07:00 [INF] [2025-05-28 17:54:40.766] [INFO]: Updated opponent known cards: [K♠]
2025-05-28 17:54:40.766 +07:00 [INF] [2025-05-28 17:54:40.766] [INFO] [nhatrang345]: Next turn: Minhheuu (UID: 22_341334)
2025-05-28 17:54:40.766 +07:00 [DBG] [2025-05-28 17:54:40.766] [DEBUG]: Updated last played card: [K♠]
2025-05-28 17:54:40.766 +07:00 [INF] [2025-05-28 17:54:40.766] [INFO] [nhatrang345]: Player nhatrang345 played card 48, next: Minhheuu
2025-05-28 17:54:40.766 +07:00 [DBG] [2025-05-28 17:54:40.766] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:40.766 +07:00 [INF] [2025-05-28 17:54:40.766] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:54:40.905 +07:00 [INF] [2025-05-28 17:54:40.905] [INFO]: Chế độ chơi: Team
2025-05-28 17:54:40.905 +07:00 [INF] [2025-05-28 17:54:40.905] [INFO]: Xử lý chế độ Team
2025-05-28 17:54:40.905 +07:00 [DBG] [2025-05-28 17:54:40.905] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:40.905 +07:00 [INF] [2025-05-28 17:54:40.905] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:54:40.906 +07:00 [ERR] [2025-05-28 17:54:40.906] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:54:40.906 +07:00 [INF] [2025-05-28 17:54:40.906] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:54:40.933 +07:00 [INF] [2025-05-28 17:54:40.933] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:54:40.933 +07:00 [DBG] [2025-05-28 17:54:40.933] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:54:40.933 +07:00 [INF] [2025-05-28 17:54:40.933] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:54:40.933 +07:00 [DBG] [2025-05-28 17:54:40.933] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:54:40.959 +07:00 [DBG] [2025-05-28 17:54:40.959] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:40.984 +07:00 [DBG] [2025-05-28 17:54:40.984] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 3♦, 4♥, 6♠, 10♥]
2025-05-28 17:54:40.984 +07:00 [DBG] [2025-05-28 17:54:40.984] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:54:40.987 +07:00 [DBG] [2025-05-28 17:54:40.987] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:54:41.016 +07:00 [DBG] [2025-05-28 17:54:41.016] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:54:41.016 +07:00 [DBG] [2025-05-28 17:54:41.016] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:54:41.018 +07:00 [INF] [2025-05-28 17:54:41.018] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 2 played cards
2025-05-28 17:54:41.018 +07:00 [INF] [2025-05-28 17:54:41.018] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠]
2025-05-28 17:54:41.019 +07:00 [INF] [2025-05-28 17:54:41.019] [INFO]: Hiển thị 2 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:54:41.019 +07:00 [DBG] [2025-05-28 17:54:41.019] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 0
2025-05-28 17:54:41.020 +07:00 [DBG] [2025-05-28 17:54:41.020] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 1
2025-05-28 17:54:41.837 +07:00 [INF] [2025-05-28 17:54:41.837] [INFO] [nhatrang345]: Player Minhheuu drew card -1, new cards: []
2025-05-28 17:54:41.837 +07:00 [DBG] [2025-05-28 17:54:41.837] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:41.837 +07:00 [INF] [2025-05-28 17:54:41.837] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:54:41.975 +07:00 [INF] [2025-05-28 17:54:41.975] [INFO]: Chế độ chơi: Team
2025-05-28 17:54:41.975 +07:00 [INF] [2025-05-28 17:54:41.975] [INFO]: Xử lý chế độ Team
2025-05-28 17:54:41.975 +07:00 [DBG] [2025-05-28 17:54:41.975] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:41.975 +07:00 [INF] [2025-05-28 17:54:41.975] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:54:41.975 +07:00 [ERR] [2025-05-28 17:54:41.975] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:54:41.975 +07:00 [INF] [2025-05-28 17:54:41.975] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:54:42.003 +07:00 [ERR] [2025-05-28 17:54:42.003] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: Minhheuu, cards: 0
2025-05-28 17:54:42.003 +07:00 [INF] [2025-05-28 17:54:42.003] [INFO]: ✅ Đánh dấu Minhheuu đã rút bài
2025-05-28 17:54:44.703 +07:00 [INF] [2025-05-28 17:54:44.703] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,21]
2025-05-28 17:54:44.703 +07:00 [INF] [2025-05-28 17:54:44.703] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,21]
2025-05-28 17:54:44.706 +07:00 [INF] [2025-05-28 17:54:44.706] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,21]
2025-05-28 17:54:46.957 +07:00 [INF] [2025-05-28 17:54:46.957] [INFO]: Updated opponent known cards: [6♥]
2025-05-28 17:54:46.957 +07:00 [INF] [2025-05-28 17:54:46.957] [INFO] [nhatrang345]: Next turn: xucha95 (UID: 1_279027341)
2025-05-28 17:54:46.957 +07:00 [DBG] [2025-05-28 17:54:46.957] [DEBUG]: Updated last played card: [6♥]
2025-05-28 17:54:46.957 +07:00 [INF] [2025-05-28 17:54:46.957] [INFO] [nhatrang345]: Player Minhheuu played card 23, next: xucha95
2025-05-28 17:54:46.957 +07:00 [DBG] [2025-05-28 17:54:46.957] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:46.957 +07:00 [INF] [2025-05-28 17:54:46.957] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:54:47.093 +07:00 [INF] [2025-05-28 17:54:47.093] [INFO]: Chế độ chơi: Team
2025-05-28 17:54:47.093 +07:00 [INF] [2025-05-28 17:54:47.093] [INFO]: Xử lý chế độ Team
2025-05-28 17:54:47.093 +07:00 [DBG] [2025-05-28 17:54:47.093] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:47.093 +07:00 [INF] [2025-05-28 17:54:47.093] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:54:47.093 +07:00 [ERR] [2025-05-28 17:54:47.093] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:54:47.093 +07:00 [INF] [2025-05-28 17:54:47.093] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:54:47.121 +07:00 [INF] [2025-05-28 17:54:47.121] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:54:47.121 +07:00 [DBG] [2025-05-28 17:54:47.121] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:54:47.121 +07:00 [INF] [2025-05-28 17:54:47.121] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:54:47.121 +07:00 [DBG] [2025-05-28 17:54:47.121] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:54:47.146 +07:00 [DBG] [2025-05-28 17:54:47.146] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:47.171 +07:00 [DBG] [2025-05-28 17:54:47.171] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 3♦, 4♥, 6♠, 10♥]
2025-05-28 17:54:47.171 +07:00 [DBG] [2025-05-28 17:54:47.171] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:54:47.174 +07:00 [DBG] [2025-05-28 17:54:47.174] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:54:47.203 +07:00 [DBG] [2025-05-28 17:54:47.203] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:54:47.203 +07:00 [DBG] [2025-05-28 17:54:47.203] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:54:47.205 +07:00 [INF] [2025-05-28 17:54:47.205] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 3 played cards
2025-05-28 17:54:47.205 +07:00 [INF] [2025-05-28 17:54:47.205] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥]
2025-05-28 17:54:47.205 +07:00 [INF] [2025-05-28 17:54:47.205] [INFO]: Hiển thị 3 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:54:47.206 +07:00 [DBG] [2025-05-28 17:54:47.206] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 0
2025-05-28 17:54:47.206 +07:00 [DBG] [2025-05-28 17:54:47.206] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 1
2025-05-28 17:54:47.207 +07:00 [DBG] [2025-05-28 17:54:47.207] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 2
2025-05-28 17:54:48.477 +07:00 [INF] [2025-05-28 17:54:48.477] [INFO] [nhatrang345]: Player xucha95 drew card -1, new cards: []
2025-05-28 17:54:48.477 +07:00 [DBG] [2025-05-28 17:54:48.477] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:48.477 +07:00 [INF] [2025-05-28 17:54:48.477] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:54:48.614 +07:00 [INF] [2025-05-28 17:54:48.614] [INFO]: Chế độ chơi: Team
2025-05-28 17:54:48.614 +07:00 [INF] [2025-05-28 17:54:48.614] [INFO]: Xử lý chế độ Team
2025-05-28 17:54:48.614 +07:00 [DBG] [2025-05-28 17:54:48.614] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:48.614 +07:00 [INF] [2025-05-28 17:54:48.614] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:54:48.614 +07:00 [ERR] [2025-05-28 17:54:48.614] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:54:48.614 +07:00 [INF] [2025-05-28 17:54:48.614] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:54:48.642 +07:00 [ERR] [2025-05-28 17:54:48.642] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: xucha95, cards: 0
2025-05-28 17:54:48.642 +07:00 [INF] [2025-05-28 17:54:48.642] [INFO]: ✅ Đánh dấu xucha95 đã rút bài
2025-05-28 17:54:49.722 +07:00 [INF] [2025-05-28 17:54:49.722] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,22]
2025-05-28 17:54:49.722 +07:00 [INF] [2025-05-28 17:54:49.722] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,22]
2025-05-28 17:54:49.722 +07:00 [INF] [2025-05-28 17:54:49.722] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,22]
2025-05-28 17:54:51.467 +07:00 [INF] [2025-05-28 17:54:51.467] [INFO]: Updated opponent known cards: [K♣]
2025-05-28 17:54:51.467 +07:00 [INF] [2025-05-28 17:54:51.467] [INFO] [nhatrang345]: Next turn: HaiDzaiVcl123 (UID: 1_246920792)
2025-05-28 17:54:51.467 +07:00 [DBG] [2025-05-28 17:54:51.467] [DEBUG]: Updated last played card: [K♣]
2025-05-28 17:54:51.467 +07:00 [INF] [2025-05-28 17:54:51.467] [INFO] [nhatrang345]: Player xucha95 played card 49, next: HaiDzaiVcl123
2025-05-28 17:54:51.467 +07:00 [DBG] [2025-05-28 17:54:51.467] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:51.467 +07:00 [INF] [2025-05-28 17:54:51.467] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:54:51.606 +07:00 [INF] [2025-05-28 17:54:51.606] [INFO]: Chế độ chơi: Team
2025-05-28 17:54:51.606 +07:00 [INF] [2025-05-28 17:54:51.606] [INFO]: Xử lý chế độ Team
2025-05-28 17:54:51.606 +07:00 [DBG] [2025-05-28 17:54:51.606] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:51.606 +07:00 [INF] [2025-05-28 17:54:51.606] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:54:51.606 +07:00 [ERR] [2025-05-28 17:54:51.606] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:54:51.606 +07:00 [INF] [2025-05-28 17:54:51.606] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:54:51.637 +07:00 [INF] [2025-05-28 17:54:51.637] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:54:51.637 +07:00 [DBG] [2025-05-28 17:54:51.637] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:54:51.637 +07:00 [INF] [2025-05-28 17:54:51.637] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:54:51.637 +07:00 [DBG] [2025-05-28 17:54:51.637] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:54:51.663 +07:00 [DBG] [2025-05-28 17:54:51.663] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:51.688 +07:00 [DBG] [2025-05-28 17:54:51.688] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 3♦, 4♥, 6♠, 10♥]
2025-05-28 17:54:51.688 +07:00 [DBG] [2025-05-28 17:54:51.688] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:54:51.691 +07:00 [DBG] [2025-05-28 17:54:51.691] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:54:51.721 +07:00 [DBG] [2025-05-28 17:54:51.721] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:54:51.721 +07:00 [DBG] [2025-05-28 17:54:51.721] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:54:51.723 +07:00 [INF] [2025-05-28 17:54:51.723] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 4 played cards
2025-05-28 17:54:51.723 +07:00 [INF] [2025-05-28 17:54:51.723] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣]
2025-05-28 17:54:51.724 +07:00 [INF] [2025-05-28 17:54:51.724] [INFO]: Hiển thị 4 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:54:51.724 +07:00 [DBG] [2025-05-28 17:54:51.724] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 0
2025-05-28 17:54:51.725 +07:00 [DBG] [2025-05-28 17:54:51.725] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 1
2025-05-28 17:54:51.725 +07:00 [DBG] [2025-05-28 17:54:51.725] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 2
2025-05-28 17:54:51.726 +07:00 [DBG] [2025-05-28 17:54:51.726] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 3
2025-05-28 17:54:52.717 +07:00 [INF] [2025-05-28 17:54:52.717] [INFO] [nhatrang345]: Player HaiDzaiVcl123 drew card -1, new cards: []
2025-05-28 17:54:52.717 +07:00 [DBG] [2025-05-28 17:54:52.717] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:52.717 +07:00 [INF] [2025-05-28 17:54:52.717] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:54:52.857 +07:00 [INF] [2025-05-28 17:54:52.857] [INFO]: Chế độ chơi: Team
2025-05-28 17:54:52.857 +07:00 [INF] [2025-05-28 17:54:52.857] [INFO]: Xử lý chế độ Team
2025-05-28 17:54:52.857 +07:00 [DBG] [2025-05-28 17:54:52.857] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:52.857 +07:00 [INF] [2025-05-28 17:54:52.857] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:54:52.857 +07:00 [ERR] [2025-05-28 17:54:52.857] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:54:52.857 +07:00 [INF] [2025-05-28 17:54:52.857] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:54:52.885 +07:00 [ERR] [2025-05-28 17:54:52.885] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: HaiDzaiVcl123, cards: 0
2025-05-28 17:54:52.885 +07:00 [INF] [2025-05-28 17:54:52.885] [INFO]: ✅ Đánh dấu HaiDzaiVcl123 đã rút bài
2025-05-28 17:54:54.721 +07:00 [INF] [2025-05-28 17:54:54.721] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,23]
2025-05-28 17:54:54.721 +07:00 [INF] [2025-05-28 17:54:54.721] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,23]
2025-05-28 17:54:54.721 +07:00 [INF] [2025-05-28 17:54:54.721] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,23]
2025-05-28 17:54:54.885 +07:00 [INF] [2025-05-28 17:54:54.885] [INFO]: Updated opponent known cards: [J♥]
2025-05-28 17:54:54.885 +07:00 [INF] [2025-05-28 17:54:54.885] [INFO] [nhatrang345]: Next turn: nhatrang345 (UID: 1_229699688)
2025-05-28 17:54:54.885 +07:00 [DBG] [2025-05-28 17:54:54.885] [DEBUG]: Updated last played card: [J♥]
2025-05-28 17:54:54.885 +07:00 [INF] [2025-05-28 17:54:54.885] [INFO] [nhatrang345]: Player HaiDzaiVcl123 played card 43, next: nhatrang345
2025-05-28 17:54:54.885 +07:00 [DBG] [2025-05-28 17:54:54.885] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:54.885 +07:00 [INF] [2025-05-28 17:54:54.885] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:54:55.023 +07:00 [INF] [2025-05-28 17:54:55.023] [INFO]: Chế độ chơi: Team
2025-05-28 17:54:55.023 +07:00 [INF] [2025-05-28 17:54:55.023] [INFO]: Xử lý chế độ Team
2025-05-28 17:54:55.023 +07:00 [DBG] [2025-05-28 17:54:55.023] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:55.023 +07:00 [INF] [2025-05-28 17:54:55.023] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:54:55.023 +07:00 [ERR] [2025-05-28 17:54:55.023] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:54:55.023 +07:00 [INF] [2025-05-28 17:54:55.023] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:54:55.051 +07:00 [INF] [2025-05-28 17:54:55.051] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:54:55.051 +07:00 [DBG] [2025-05-28 17:54:55.051] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:54:55.051 +07:00 [INF] [2025-05-28 17:54:55.051] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:54:55.051 +07:00 [DBG] [2025-05-28 17:54:55.051] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:54:55.077 +07:00 [DBG] [2025-05-28 17:54:55.077] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:55.102 +07:00 [DBG] [2025-05-28 17:54:55.102] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 3♦, 4♥, 6♠, 10♥]
2025-05-28 17:54:55.102 +07:00 [DBG] [2025-05-28 17:54:55.102] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:54:55.105 +07:00 [DBG] [2025-05-28 17:54:55.105] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:54:55.134 +07:00 [DBG] [2025-05-28 17:54:55.134] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:54:55.134 +07:00 [DBG] [2025-05-28 17:54:55.134] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:54:55.137 +07:00 [INF] [2025-05-28 17:54:55.137] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 5 played cards
2025-05-28 17:54:55.137 +07:00 [INF] [2025-05-28 17:54:55.137] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥]
2025-05-28 17:54:55.137 +07:00 [INF] [2025-05-28 17:54:55.137] [INFO]: Hiển thị 5 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:54:55.138 +07:00 [DBG] [2025-05-28 17:54:55.138] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 0
2025-05-28 17:54:55.138 +07:00 [DBG] [2025-05-28 17:54:55.138] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 1
2025-05-28 17:54:55.138 +07:00 [DBG] [2025-05-28 17:54:55.138] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 2
2025-05-28 17:54:55.139 +07:00 [DBG] [2025-05-28 17:54:55.139] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 3
2025-05-28 17:54:55.140 +07:00 [DBG] [2025-05-28 17:54:55.140] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 4
2025-05-28 17:54:56.157 +07:00 [INF] [2025-05-28 17:54:56.157] [INFO] [nhatrang345]: Updated cards for nhatrang345 after drawing: [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, 3♦, 4♥, 6♠, 10♥]
2025-05-28 17:54:56.157 +07:00 [DBG] [2025-05-28 17:54:56.157] [DEBUG]: Updated drawn card: [9♣]
2025-05-28 17:54:56.157 +07:00 [INF] [2025-05-28 17:54:56.157] [INFO] [nhatrang345]: Player nhatrang345 drew card 33, new cards: [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, 3♦, 4♥, 6♠, 10♥]
2025-05-28 17:54:56.157 +07:00 [DBG] [2025-05-28 17:54:56.157] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:54:56.157 +07:00 [INF] [2025-05-28 17:54:56.157] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:54:56.296 +07:00 [INF] [2025-05-28 17:54:56.296] [INFO]: Chế độ chơi: Team
2025-05-28 17:54:56.296 +07:00 [INF] [2025-05-28 17:54:56.296] [INFO]: Xử lý chế độ Team
2025-05-28 17:54:56.296 +07:00 [DBG] [2025-05-28 17:54:56.296] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:56.296 +07:00 [INF] [2025-05-28 17:54:56.296] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:54:56.296 +07:00 [ERR] [2025-05-28 17:54:56.296] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:54:56.296 +07:00 [INF] [2025-05-28 17:54:56.296] [INFO]: nhatrang345 đi trước (10 lá bài)
2025-05-28 17:54:56.296 +07:00 [DBG] [2025-05-28 17:54:56.296] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:54:56.296 +07:00 [INF] [2025-05-28 17:54:56.296] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:54:56.296 +07:00 [INF] [2025-05-28 17:54:56.296] [INFO]: 📊 nhatrang345: 1 phỏm, 0 cạ, 7 rác
2025-05-28 17:54:56.296 +07:00 [INF] [2025-05-28 17:54:56.296] [INFO]: 🔍 Solo analysis: 10 tổng, 3 bảo vệ, 7 có thể đánh, 37 ẩn
2025-05-28 17:54:56.296 +07:00 [INF] [2025-05-28 17:54:56.296] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([10♥]) - Bảo vệ 3 lá - Ẩn: 37
2025-05-28 17:54:56.297 +07:00 [INF] [2025-05-28 17:54:56.297] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [10♥] tại card/39.png
2025-05-28 17:54:56.325 +07:00 [INF] [2025-05-28 17:54:56.325] [INFO]: 🔄 UPDATE PANEL: nhatrang345 tại panel 0 với 10 lá bài (Turn: False, DrawnOrEaten: True)
2025-05-28 17:54:56.325 +07:00 [DBG] [2025-05-28 17:54:56.325] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 10
2025-05-28 17:54:56.351 +07:00 [DBG] [2025-05-28 17:54:56.351] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:54:56.376 +07:00 [DBG] [2025-05-28 17:54:56.376] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, 3♦, 4♥, 6♠, 10♥]
2025-05-28 17:54:56.376 +07:00 [DBG] [2025-05-28 17:54:56.376] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 7
2025-05-28 17:54:56.379 +07:00 [DBG] [2025-05-28 17:54:56.379] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 7
2025-05-28 17:54:56.409 +07:00 [DBG] [2025-05-28 17:54:56.409] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 7
2025-05-28 17:54:56.410 +07:00 [DBG] [2025-05-28 17:54:56.410] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:54:56.410 +07:00 [INF] [2025-05-28 17:54:56.410] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:54:56.410 +07:00 [INF] [2025-05-28 17:54:56.410] [INFO]: 📊 nhatrang345: 1 phỏm, 0 cạ, 7 rác
2025-05-28 17:54:56.410 +07:00 [INF] [2025-05-28 17:54:56.410] [INFO]: 🔍 Solo analysis: 10 tổng, 3 bảo vệ, 7 có thể đánh, 37 ẩn
2025-05-28 17:54:56.410 +07:00 [INF] [2025-05-28 17:54:56.410] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([10♥]) - Bảo vệ 3 lá - Ẩn: 37
2025-05-28 17:54:56.410 +07:00 [INF] [2025-05-28 17:54:56.410] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [10♥] tại card/39.png
2025-05-28 17:54:56.410 +07:00 [INF] [2025-05-28 17:54:56.410] [INFO]: ✅ Đánh dấu nhatrang345 đã rút bài
2025-05-28 17:54:56.410 +07:00 [INF] [2025-05-28 17:54:56.410] [INFO]: 🎯 Trigger gợi ý cho nhatrang345 sau khi rút/ăn bài
2025-05-28 17:54:56.410 +07:00 [DBG] [2025-05-28 17:54:56.410] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:54:56.410 +07:00 [INF] [2025-05-28 17:54:56.410] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:54:56.410 +07:00 [INF] [2025-05-28 17:54:56.410] [INFO]: 📊 nhatrang345: 1 phỏm, 0 cạ, 7 rác
2025-05-28 17:54:56.410 +07:00 [INF] [2025-05-28 17:54:56.410] [INFO]: 🔍 Solo analysis: 10 tổng, 3 bảo vệ, 7 có thể đánh, 37 ẩn
2025-05-28 17:54:56.410 +07:00 [INF] [2025-05-28 17:54:56.410] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([10♥]) - Bảo vệ 3 lá - Ẩn: 37
2025-05-28 17:54:56.411 +07:00 [INF] [2025-05-28 17:54:56.411] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [10♥] tại card/39.png
2025-05-28 17:54:59.740 +07:00 [INF] [2025-05-28 17:54:59.740] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,24]
2025-05-28 17:54:59.740 +07:00 [INF] [2025-05-28 17:54:59.740] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,24]
2025-05-28 17:54:59.744 +07:00 [INF] [2025-05-28 17:54:59.744] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,24]
2025-05-28 17:55:03.339 +07:00 [INF] [2025-05-28 17:55:03.339] [INFO] [nhatrang345]: Updated cards for nhatrang345: [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, 3♦, 4♥, 6♠]
2025-05-28 17:55:03.339 +07:00 [INF] [2025-05-28 17:55:03.339] [INFO]: Updated opponent known cards: [10♥]
2025-05-28 17:55:03.339 +07:00 [INF] [2025-05-28 17:55:03.339] [INFO] [nhatrang345]: Next turn: Minhheuu (UID: 22_341334)
2025-05-28 17:55:03.339 +07:00 [DBG] [2025-05-28 17:55:03.339] [DEBUG]: Updated last played card: [10♥]
2025-05-28 17:55:03.339 +07:00 [INF] [2025-05-28 17:55:03.339] [INFO] [nhatrang345]: Player nhatrang345 played card 39, next: Minhheuu
2025-05-28 17:55:03.339 +07:00 [DBG] [2025-05-28 17:55:03.339] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:03.339 +07:00 [INF] [2025-05-28 17:55:03.339] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:03.478 +07:00 [INF] [2025-05-28 17:55:03.478] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:03.478 +07:00 [INF] [2025-05-28 17:55:03.478] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:03.479 +07:00 [DBG] [2025-05-28 17:55:03.479] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:03.479 +07:00 [INF] [2025-05-28 17:55:03.479] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:03.479 +07:00 [ERR] [2025-05-28 17:55:03.479] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:03.479 +07:00 [INF] [2025-05-28 17:55:03.479] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:03.508 +07:00 [INF] [2025-05-28 17:55:03.508] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:55:03.508 +07:00 [DBG] [2025-05-28 17:55:03.508] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:55:03.508 +07:00 [INF] [2025-05-28 17:55:03.508] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:55:03.508 +07:00 [DBG] [2025-05-28 17:55:03.508] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:55:03.533 +07:00 [DBG] [2025-05-28 17:55:03.533] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:03.559 +07:00 [DBG] [2025-05-28 17:55:03.559] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, 3♦, 4♥, 6♠]
2025-05-28 17:55:03.559 +07:00 [DBG] [2025-05-28 17:55:03.559] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:55:03.562 +07:00 [DBG] [2025-05-28 17:55:03.562] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:55:03.592 +07:00 [DBG] [2025-05-28 17:55:03.592] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:55:03.592 +07:00 [DBG] [2025-05-28 17:55:03.592] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:55:03.594 +07:00 [INF] [2025-05-28 17:55:03.594] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 6 played cards
2025-05-28 17:55:03.594 +07:00 [INF] [2025-05-28 17:55:03.594] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥]
2025-05-28 17:55:03.595 +07:00 [INF] [2025-05-28 17:55:03.595] [INFO]: Hiển thị 6 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:55:03.595 +07:00 [DBG] [2025-05-28 17:55:03.595] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 0
2025-05-28 17:55:03.596 +07:00 [DBG] [2025-05-28 17:55:03.596] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 1
2025-05-28 17:55:03.596 +07:00 [DBG] [2025-05-28 17:55:03.596] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 2
2025-05-28 17:55:03.597 +07:00 [DBG] [2025-05-28 17:55:03.597] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 3
2025-05-28 17:55:03.597 +07:00 [DBG] [2025-05-28 17:55:03.597] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 4
2025-05-28 17:55:03.598 +07:00 [DBG] [2025-05-28 17:55:03.598] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 5
2025-05-28 17:55:04.737 +07:00 [INF] [2025-05-28 17:55:04.737] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,25]
2025-05-28 17:55:04.737 +07:00 [INF] [2025-05-28 17:55:04.737] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,25]
2025-05-28 17:55:04.740 +07:00 [INF] [2025-05-28 17:55:04.740] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,25]
2025-05-28 17:55:04.784 +07:00 [INF] [2025-05-28 17:55:04.784] [INFO] [nhatrang345]: Player Minhheuu ate card 39, new cards: []
2025-05-28 17:55:04.784 +07:00 [DBG] [2025-05-28 17:55:04.784] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:04.784 +07:00 [INF] [2025-05-28 17:55:04.784] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:04.923 +07:00 [INF] [2025-05-28 17:55:04.923] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:04.923 +07:00 [INF] [2025-05-28 17:55:04.923] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:04.923 +07:00 [DBG] [2025-05-28 17:55:04.923] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:04.923 +07:00 [INF] [2025-05-28 17:55:04.923] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:04.923 +07:00 [ERR] [2025-05-28 17:55:04.923] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:04.923 +07:00 [INF] [2025-05-28 17:55:04.923] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:04.951 +07:00 [ERR] [2025-05-28 17:55:04.951] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: Minhheuu, cards: 0
2025-05-28 17:55:04.951 +07:00 [INF] [2025-05-28 17:55:04.951] [INFO]: ✅ Đánh dấu Minhheuu đã ăn bài
2025-05-28 17:55:06.966 +07:00 [INF] [2025-05-28 17:55:06.966] [INFO]: Updated opponent known cards: [J♣]
2025-05-28 17:55:06.966 +07:00 [INF] [2025-05-28 17:55:06.966] [INFO] [nhatrang345]: Next turn: xucha95 (UID: 1_279027341)
2025-05-28 17:55:06.966 +07:00 [DBG] [2025-05-28 17:55:06.966] [DEBUG]: Updated last played card: [J♣]
2025-05-28 17:55:06.966 +07:00 [INF] [2025-05-28 17:55:06.966] [INFO] [nhatrang345]: Player Minhheuu played card 41, next: xucha95
2025-05-28 17:55:06.966 +07:00 [DBG] [2025-05-28 17:55:06.966] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:06.966 +07:00 [INF] [2025-05-28 17:55:06.966] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:07.105 +07:00 [INF] [2025-05-28 17:55:07.105] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:07.105 +07:00 [INF] [2025-05-28 17:55:07.105] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:07.105 +07:00 [DBG] [2025-05-28 17:55:07.105] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:07.105 +07:00 [INF] [2025-05-28 17:55:07.105] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:07.105 +07:00 [ERR] [2025-05-28 17:55:07.105] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:07.105 +07:00 [INF] [2025-05-28 17:55:07.105] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:07.134 +07:00 [INF] [2025-05-28 17:55:07.134] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:55:07.134 +07:00 [DBG] [2025-05-28 17:55:07.134] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:55:07.134 +07:00 [INF] [2025-05-28 17:55:07.134] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:55:07.134 +07:00 [DBG] [2025-05-28 17:55:07.134] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:55:07.159 +07:00 [DBG] [2025-05-28 17:55:07.159] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:07.184 +07:00 [DBG] [2025-05-28 17:55:07.184] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, 3♦, 4♥, 6♠]
2025-05-28 17:55:07.184 +07:00 [DBG] [2025-05-28 17:55:07.184] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:55:07.187 +07:00 [DBG] [2025-05-28 17:55:07.187] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:55:07.216 +07:00 [DBG] [2025-05-28 17:55:07.216] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:55:07.216 +07:00 [DBG] [2025-05-28 17:55:07.216] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:55:07.219 +07:00 [INF] [2025-05-28 17:55:07.219] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 7 played cards
2025-05-28 17:55:07.219 +07:00 [INF] [2025-05-28 17:55:07.219] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣]
2025-05-28 17:55:07.219 +07:00 [INF] [2025-05-28 17:55:07.219] [INFO]: Hiển thị 7 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:55:07.220 +07:00 [DBG] [2025-05-28 17:55:07.220] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 0
2025-05-28 17:55:07.220 +07:00 [DBG] [2025-05-28 17:55:07.220] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 1
2025-05-28 17:55:07.221 +07:00 [DBG] [2025-05-28 17:55:07.221] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 2
2025-05-28 17:55:07.221 +07:00 [DBG] [2025-05-28 17:55:07.221] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 3
2025-05-28 17:55:07.222 +07:00 [DBG] [2025-05-28 17:55:07.222] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 4
2025-05-28 17:55:07.222 +07:00 [DBG] [2025-05-28 17:55:07.222] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 5
2025-05-28 17:55:07.223 +07:00 [DBG] [2025-05-28 17:55:07.223] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 6
2025-05-28 17:55:08.821 +07:00 [INF] [2025-05-28 17:55:08.821] [INFO] [nhatrang345]: Player xucha95 ate card 41, new cards: []
2025-05-28 17:55:08.821 +07:00 [DBG] [2025-05-28 17:55:08.821] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:08.821 +07:00 [INF] [2025-05-28 17:55:08.821] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:08.959 +07:00 [INF] [2025-05-28 17:55:08.959] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:08.959 +07:00 [INF] [2025-05-28 17:55:08.959] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:08.959 +07:00 [DBG] [2025-05-28 17:55:08.959] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:08.959 +07:00 [INF] [2025-05-28 17:55:08.959] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:08.959 +07:00 [ERR] [2025-05-28 17:55:08.959] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:08.959 +07:00 [INF] [2025-05-28 17:55:08.959] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:08.988 +07:00 [ERR] [2025-05-28 17:55:08.988] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: xucha95, cards: 0
2025-05-28 17:55:08.988 +07:00 [INF] [2025-05-28 17:55:08.988] [INFO]: ✅ Đánh dấu xucha95 đã ăn bài
2025-05-28 17:55:09.753 +07:00 [INF] [2025-05-28 17:55:09.753] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,26]
2025-05-28 17:55:09.753 +07:00 [INF] [2025-05-28 17:55:09.753] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,26]
2025-05-28 17:55:09.759 +07:00 [INF] [2025-05-28 17:55:09.759] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,26]
2025-05-28 17:55:13.607 +07:00 [INF] [2025-05-28 17:55:13.607] [INFO]: Updated opponent known cards: [9♥]
2025-05-28 17:55:13.607 +07:00 [INF] [2025-05-28 17:55:13.607] [INFO] [nhatrang345]: Next turn: HaiDzaiVcl123 (UID: 1_246920792)
2025-05-28 17:55:13.607 +07:00 [DBG] [2025-05-28 17:55:13.607] [DEBUG]: Updated last played card: [9♥]
2025-05-28 17:55:13.607 +07:00 [INF] [2025-05-28 17:55:13.607] [INFO] [nhatrang345]: Player xucha95 played card 35, next: HaiDzaiVcl123
2025-05-28 17:55:13.607 +07:00 [DBG] [2025-05-28 17:55:13.607] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:13.607 +07:00 [INF] [2025-05-28 17:55:13.607] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:13.747 +07:00 [INF] [2025-05-28 17:55:13.747] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:13.747 +07:00 [INF] [2025-05-28 17:55:13.747] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:13.747 +07:00 [DBG] [2025-05-28 17:55:13.747] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:13.747 +07:00 [INF] [2025-05-28 17:55:13.747] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:13.747 +07:00 [ERR] [2025-05-28 17:55:13.747] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:13.747 +07:00 [INF] [2025-05-28 17:55:13.747] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:13.777 +07:00 [INF] [2025-05-28 17:55:13.777] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:55:13.777 +07:00 [DBG] [2025-05-28 17:55:13.777] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:55:13.777 +07:00 [INF] [2025-05-28 17:55:13.777] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:55:13.777 +07:00 [DBG] [2025-05-28 17:55:13.777] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:55:13.802 +07:00 [DBG] [2025-05-28 17:55:13.802] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:13.828 +07:00 [DBG] [2025-05-28 17:55:13.828] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, 3♦, 4♥, 6♠]
2025-05-28 17:55:13.828 +07:00 [DBG] [2025-05-28 17:55:13.828] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:55:13.831 +07:00 [DBG] [2025-05-28 17:55:13.831] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:55:13.861 +07:00 [DBG] [2025-05-28 17:55:13.861] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:55:13.861 +07:00 [DBG] [2025-05-28 17:55:13.861] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:55:13.864 +07:00 [INF] [2025-05-28 17:55:13.864] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 8 played cards
2025-05-28 17:55:13.864 +07:00 [INF] [2025-05-28 17:55:13.864] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥]
2025-05-28 17:55:13.864 +07:00 [INF] [2025-05-28 17:55:13.864] [INFO]: Hiển thị 8 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:55:13.865 +07:00 [DBG] [2025-05-28 17:55:13.865] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 0
2025-05-28 17:55:13.866 +07:00 [DBG] [2025-05-28 17:55:13.866] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 1
2025-05-28 17:55:13.866 +07:00 [DBG] [2025-05-28 17:55:13.866] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 2
2025-05-28 17:55:13.867 +07:00 [DBG] [2025-05-28 17:55:13.867] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 3
2025-05-28 17:55:13.867 +07:00 [DBG] [2025-05-28 17:55:13.867] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 4
2025-05-28 17:55:13.868 +07:00 [DBG] [2025-05-28 17:55:13.868] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 5
2025-05-28 17:55:13.868 +07:00 [DBG] [2025-05-28 17:55:13.868] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 6
2025-05-28 17:55:13.869 +07:00 [DBG] [2025-05-28 17:55:13.869] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 7
2025-05-28 17:55:14.770 +07:00 [INF] [2025-05-28 17:55:14.770] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,27]
2025-05-28 17:55:14.770 +07:00 [INF] [2025-05-28 17:55:14.770] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,27]
2025-05-28 17:55:14.770 +07:00 [INF] [2025-05-28 17:55:14.770] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,27]
2025-05-28 17:55:15.020 +07:00 [INF] [2025-05-28 17:55:15.020] [INFO] [nhatrang345]: Player HaiDzaiVcl123 drew card -1, new cards: []
2025-05-28 17:55:15.020 +07:00 [DBG] [2025-05-28 17:55:15.020] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:15.020 +07:00 [INF] [2025-05-28 17:55:15.020] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:15.163 +07:00 [INF] [2025-05-28 17:55:15.163] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:15.163 +07:00 [INF] [2025-05-28 17:55:15.163] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:15.163 +07:00 [DBG] [2025-05-28 17:55:15.163] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:15.163 +07:00 [INF] [2025-05-28 17:55:15.163] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:15.163 +07:00 [ERR] [2025-05-28 17:55:15.163] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:15.163 +07:00 [INF] [2025-05-28 17:55:15.163] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:15.193 +07:00 [ERR] [2025-05-28 17:55:15.193] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: HaiDzaiVcl123, cards: 0
2025-05-28 17:55:15.193 +07:00 [INF] [2025-05-28 17:55:15.193] [INFO]: ✅ Đánh dấu HaiDzaiVcl123 đã rút bài
2025-05-28 17:55:17.195 +07:00 [INF] [2025-05-28 17:55:17.195] [INFO]: Updated opponent known cards: [9♦]
2025-05-28 17:55:17.195 +07:00 [INF] [2025-05-28 17:55:17.195] [INFO] [nhatrang345]: Next turn: nhatrang345 (UID: 1_229699688)
2025-05-28 17:55:17.195 +07:00 [DBG] [2025-05-28 17:55:17.195] [DEBUG]: Updated last played card: [9♦]
2025-05-28 17:55:17.195 +07:00 [INF] [2025-05-28 17:55:17.195] [INFO] [nhatrang345]: Player HaiDzaiVcl123 played card 34, next: nhatrang345
2025-05-28 17:55:17.195 +07:00 [DBG] [2025-05-28 17:55:17.195] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:17.195 +07:00 [INF] [2025-05-28 17:55:17.195] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:17.333 +07:00 [INF] [2025-05-28 17:55:17.333] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:17.333 +07:00 [INF] [2025-05-28 17:55:17.333] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:17.333 +07:00 [DBG] [2025-05-28 17:55:17.333] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:17.333 +07:00 [INF] [2025-05-28 17:55:17.333] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:17.333 +07:00 [ERR] [2025-05-28 17:55:17.333] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:17.333 +07:00 [INF] [2025-05-28 17:55:17.333] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:17.361 +07:00 [INF] [2025-05-28 17:55:17.361] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:55:17.361 +07:00 [DBG] [2025-05-28 17:55:17.361] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:55:17.362 +07:00 [INF] [2025-05-28 17:55:17.362] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:55:17.362 +07:00 [DBG] [2025-05-28 17:55:17.362] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:55:17.387 +07:00 [DBG] [2025-05-28 17:55:17.387] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:17.412 +07:00 [DBG] [2025-05-28 17:55:17.412] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, 3♦, 4♥, 6♠]
2025-05-28 17:55:17.412 +07:00 [DBG] [2025-05-28 17:55:17.412] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:55:17.415 +07:00 [DBG] [2025-05-28 17:55:17.415] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:55:17.445 +07:00 [DBG] [2025-05-28 17:55:17.445] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:55:17.445 +07:00 [DBG] [2025-05-28 17:55:17.445] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:55:17.447 +07:00 [INF] [2025-05-28 17:55:17.447] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 9 played cards
2025-05-28 17:55:17.447 +07:00 [INF] [2025-05-28 17:55:17.447] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦]
2025-05-28 17:55:17.448 +07:00 [INF] [2025-05-28 17:55:17.448] [INFO]: Hiển thị 9 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:55:17.448 +07:00 [DBG] [2025-05-28 17:55:17.448] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 0
2025-05-28 17:55:17.449 +07:00 [DBG] [2025-05-28 17:55:17.449] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 1
2025-05-28 17:55:17.449 +07:00 [DBG] [2025-05-28 17:55:17.449] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 2
2025-05-28 17:55:17.450 +07:00 [DBG] [2025-05-28 17:55:17.450] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 3
2025-05-28 17:55:17.450 +07:00 [DBG] [2025-05-28 17:55:17.450] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 4
2025-05-28 17:55:17.451 +07:00 [DBG] [2025-05-28 17:55:17.451] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 5
2025-05-28 17:55:17.451 +07:00 [DBG] [2025-05-28 17:55:17.451] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 6
2025-05-28 17:55:17.452 +07:00 [DBG] [2025-05-28 17:55:17.452] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 7
2025-05-28 17:55:17.452 +07:00 [DBG] [2025-05-28 17:55:17.452] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 8
2025-05-28 17:55:18.406 +07:00 [INF] [2025-05-28 17:55:18.406] [INFO] [nhatrang345]: Updated cards for nhatrang345 after drawing: [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, A♠, 3♦, 4♥, 6♠]
2025-05-28 17:55:18.406 +07:00 [DBG] [2025-05-28 17:55:18.406] [DEBUG]: Updated drawn card: [A♠]
2025-05-28 17:55:18.406 +07:00 [INF] [2025-05-28 17:55:18.406] [INFO] [nhatrang345]: Player nhatrang345 drew card 0, new cards: [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, A♠, 3♦, 4♥, 6♠]
2025-05-28 17:55:18.406 +07:00 [DBG] [2025-05-28 17:55:18.406] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:18.406 +07:00 [INF] [2025-05-28 17:55:18.406] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:18.546 +07:00 [INF] [2025-05-28 17:55:18.546] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:18.546 +07:00 [INF] [2025-05-28 17:55:18.546] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:18.547 +07:00 [DBG] [2025-05-28 17:55:18.547] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:18.547 +07:00 [INF] [2025-05-28 17:55:18.547] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:18.547 +07:00 [ERR] [2025-05-28 17:55:18.547] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:18.547 +07:00 [INF] [2025-05-28 17:55:18.547] [INFO]: nhatrang345 đi trước (10 lá bài)
2025-05-28 17:55:18.547 +07:00 [DBG] [2025-05-28 17:55:18.547] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:55:18.547 +07:00 [INF] [2025-05-28 17:55:18.547] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:55:18.547 +07:00 [INF] [2025-05-28 17:55:18.547] [INFO]: 📊 nhatrang345: 1 phỏm, 0 cạ, 7 rác
2025-05-28 17:55:18.547 +07:00 [INF] [2025-05-28 17:55:18.547] [INFO]: 🔍 Solo analysis: 10 tổng, 3 bảo vệ, 7 có thể đánh, 33 ẩn
2025-05-28 17:55:18.547 +07:00 [INF] [2025-05-28 17:55:18.547] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([9♣]) - Bảo vệ 3 lá - Ẩn: 33
2025-05-28 17:55:18.547 +07:00 [INF] [2025-05-28 17:55:18.547] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [9♣] tại card/33.png
2025-05-28 17:55:18.578 +07:00 [INF] [2025-05-28 17:55:18.578] [INFO]: 🔄 UPDATE PANEL: nhatrang345 tại panel 0 với 10 lá bài (Turn: False, DrawnOrEaten: True)
2025-05-28 17:55:18.578 +07:00 [DBG] [2025-05-28 17:55:18.578] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 10
2025-05-28 17:55:18.603 +07:00 [DBG] [2025-05-28 17:55:18.603] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:18.629 +07:00 [DBG] [2025-05-28 17:55:18.629] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, A♠, 3♦, 4♥, 6♠]
2025-05-28 17:55:18.629 +07:00 [DBG] [2025-05-28 17:55:18.629] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 7
2025-05-28 17:55:18.632 +07:00 [DBG] [2025-05-28 17:55:18.632] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 7
2025-05-28 17:55:18.661 +07:00 [DBG] [2025-05-28 17:55:18.661] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 7
2025-05-28 17:55:18.661 +07:00 [DBG] [2025-05-28 17:55:18.661] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:55:18.661 +07:00 [INF] [2025-05-28 17:55:18.661] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:55:18.661 +07:00 [INF] [2025-05-28 17:55:18.661] [INFO]: 📊 nhatrang345: 1 phỏm, 0 cạ, 7 rác
2025-05-28 17:55:18.661 +07:00 [INF] [2025-05-28 17:55:18.661] [INFO]: 🔍 Solo analysis: 10 tổng, 3 bảo vệ, 7 có thể đánh, 33 ẩn
2025-05-28 17:55:18.661 +07:00 [INF] [2025-05-28 17:55:18.661] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([9♣]) - Bảo vệ 3 lá - Ẩn: 33
2025-05-28 17:55:18.662 +07:00 [INF] [2025-05-28 17:55:18.662] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [9♣] tại card/33.png
2025-05-28 17:55:18.662 +07:00 [INF] [2025-05-28 17:55:18.662] [INFO]: ✅ Đánh dấu nhatrang345 đã rút bài
2025-05-28 17:55:18.662 +07:00 [INF] [2025-05-28 17:55:18.662] [INFO]: 🎯 Trigger gợi ý cho nhatrang345 sau khi rút/ăn bài
2025-05-28 17:55:18.662 +07:00 [DBG] [2025-05-28 17:55:18.662] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:55:18.662 +07:00 [INF] [2025-05-28 17:55:18.662] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:55:18.662 +07:00 [INF] [2025-05-28 17:55:18.662] [INFO]: 📊 nhatrang345: 1 phỏm, 0 cạ, 7 rác
2025-05-28 17:55:18.662 +07:00 [INF] [2025-05-28 17:55:18.662] [INFO]: 🔍 Solo analysis: 10 tổng, 3 bảo vệ, 7 có thể đánh, 33 ẩn
2025-05-28 17:55:18.662 +07:00 [INF] [2025-05-28 17:55:18.662] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([9♣]) - Bảo vệ 3 lá - Ẩn: 33
2025-05-28 17:55:18.662 +07:00 [INF] [2025-05-28 17:55:18.662] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [9♣] tại card/33.png
2025-05-28 17:55:19.770 +07:00 [INF] [2025-05-28 17:55:19.770] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,28]
2025-05-28 17:55:19.770 +07:00 [INF] [2025-05-28 17:55:19.770] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,28]
2025-05-28 17:55:19.781 +07:00 [INF] [2025-05-28 17:55:19.781] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,28]
2025-05-28 17:55:24.788 +07:00 [INF] [2025-05-28 17:55:24.788] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,29]
2025-05-28 17:55:24.788 +07:00 [INF] [2025-05-28 17:55:24.788] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,29]
2025-05-28 17:55:24.788 +07:00 [INF] [2025-05-28 17:55:24.788] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,29]
2025-05-28 17:55:29.787 +07:00 [INF] [2025-05-28 17:55:29.787] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,30]
2025-05-28 17:55:29.787 +07:00 [INF] [2025-05-28 17:55:29.787] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,30]
2025-05-28 17:55:29.789 +07:00 [INF] [2025-05-28 17:55:29.789] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,30]
2025-05-28 17:55:31.871 +07:00 [INF] [2025-05-28 17:55:31.871] [INFO] [nhatrang345]: Updated cards for nhatrang345: [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, A♠, 3♦, 4♥]
2025-05-28 17:55:31.871 +07:00 [INF] [2025-05-28 17:55:31.871] [INFO]: Updated opponent known cards: [6♠]
2025-05-28 17:55:31.871 +07:00 [INF] [2025-05-28 17:55:31.871] [INFO] [nhatrang345]: Next turn: Minhheuu (UID: 22_341334)
2025-05-28 17:55:31.872 +07:00 [DBG] [2025-05-28 17:55:31.872] [DEBUG]: Updated last played card: [6♠]
2025-05-28 17:55:31.872 +07:00 [INF] [2025-05-28 17:55:31.872] [INFO] [nhatrang345]: Player nhatrang345 played card 20, next: Minhheuu
2025-05-28 17:55:31.872 +07:00 [DBG] [2025-05-28 17:55:31.872] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:31.872 +07:00 [INF] [2025-05-28 17:55:31.872] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:32.014 +07:00 [INF] [2025-05-28 17:55:32.014] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:32.014 +07:00 [INF] [2025-05-28 17:55:32.014] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:32.014 +07:00 [DBG] [2025-05-28 17:55:32.014] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:32.014 +07:00 [INF] [2025-05-28 17:55:32.014] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:32.014 +07:00 [ERR] [2025-05-28 17:55:32.014] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:32.014 +07:00 [INF] [2025-05-28 17:55:32.014] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:32.043 +07:00 [INF] [2025-05-28 17:55:32.043] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:55:32.043 +07:00 [DBG] [2025-05-28 17:55:32.043] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:55:32.043 +07:00 [INF] [2025-05-28 17:55:32.043] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:55:32.043 +07:00 [DBG] [2025-05-28 17:55:32.043] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:55:32.069 +07:00 [DBG] [2025-05-28 17:55:32.069] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:32.094 +07:00 [DBG] [2025-05-28 17:55:32.094] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, A♠, 3♦, 4♥]
2025-05-28 17:55:32.094 +07:00 [DBG] [2025-05-28 17:55:32.094] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:55:32.097 +07:00 [DBG] [2025-05-28 17:55:32.097] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:55:32.127 +07:00 [DBG] [2025-05-28 17:55:32.127] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:55:32.127 +07:00 [DBG] [2025-05-28 17:55:32.127] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:55:32.129 +07:00 [INF] [2025-05-28 17:55:32.129] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 10 played cards
2025-05-28 17:55:32.129 +07:00 [INF] [2025-05-28 17:55:32.129] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠]
2025-05-28 17:55:32.131 +07:00 [INF] [2025-05-28 17:55:32.131] [INFO]: Hiển thị 10 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:55:32.131 +07:00 [DBG] [2025-05-28 17:55:32.131] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 0
2025-05-28 17:55:32.132 +07:00 [DBG] [2025-05-28 17:55:32.132] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 1
2025-05-28 17:55:32.132 +07:00 [DBG] [2025-05-28 17:55:32.132] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 2
2025-05-28 17:55:32.133 +07:00 [DBG] [2025-05-28 17:55:32.133] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 3
2025-05-28 17:55:32.133 +07:00 [DBG] [2025-05-28 17:55:32.133] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 4
2025-05-28 17:55:32.134 +07:00 [DBG] [2025-05-28 17:55:32.134] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 5
2025-05-28 17:55:32.134 +07:00 [DBG] [2025-05-28 17:55:32.134] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 6
2025-05-28 17:55:32.135 +07:00 [DBG] [2025-05-28 17:55:32.135] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 7
2025-05-28 17:55:32.135 +07:00 [DBG] [2025-05-28 17:55:32.135] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 8
2025-05-28 17:55:32.136 +07:00 [DBG] [2025-05-28 17:55:32.136] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 9
2025-05-28 17:55:33.288 +07:00 [INF] [2025-05-28 17:55:33.288] [INFO] [nhatrang345]: Player Minhheuu ate card 20, new cards: []
2025-05-28 17:55:33.288 +07:00 [DBG] [2025-05-28 17:55:33.288] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:33.288 +07:00 [INF] [2025-05-28 17:55:33.288] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:33.429 +07:00 [INF] [2025-05-28 17:55:33.429] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:33.429 +07:00 [INF] [2025-05-28 17:55:33.429] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:33.429 +07:00 [DBG] [2025-05-28 17:55:33.429] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:33.429 +07:00 [INF] [2025-05-28 17:55:33.429] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:33.429 +07:00 [ERR] [2025-05-28 17:55:33.429] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:33.429 +07:00 [INF] [2025-05-28 17:55:33.429] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:33.460 +07:00 [ERR] [2025-05-28 17:55:33.460] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: Minhheuu, cards: 0
2025-05-28 17:55:33.460 +07:00 [INF] [2025-05-28 17:55:33.460] [INFO]: ✅ Đánh dấu Minhheuu đã ăn bài
2025-05-28 17:55:34.803 +07:00 [INF] [2025-05-28 17:55:34.803] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,31]
2025-05-28 17:55:34.803 +07:00 [INF] [2025-05-28 17:55:34.803] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,31]
2025-05-28 17:55:34.805 +07:00 [INF] [2025-05-28 17:55:34.805] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,31]
2025-05-28 17:55:37.466 +07:00 [INF] [2025-05-28 17:55:37.466] [INFO]: Updated opponent known cards: [K♦]
2025-05-28 17:55:37.466 +07:00 [INF] [2025-05-28 17:55:37.466] [INFO] [nhatrang345]: Next turn: xucha95 (UID: 1_279027341)
2025-05-28 17:55:37.466 +07:00 [DBG] [2025-05-28 17:55:37.466] [DEBUG]: Updated last played card: [K♦]
2025-05-28 17:55:37.466 +07:00 [INF] [2025-05-28 17:55:37.466] [INFO] [nhatrang345]: Player Minhheuu played card 50, next: xucha95
2025-05-28 17:55:37.466 +07:00 [DBG] [2025-05-28 17:55:37.466] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:37.466 +07:00 [INF] [2025-05-28 17:55:37.466] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:37.606 +07:00 [INF] [2025-05-28 17:55:37.606] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:37.607 +07:00 [INF] [2025-05-28 17:55:37.607] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:37.607 +07:00 [DBG] [2025-05-28 17:55:37.607] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:37.607 +07:00 [INF] [2025-05-28 17:55:37.607] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:37.607 +07:00 [ERR] [2025-05-28 17:55:37.607] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:37.607 +07:00 [INF] [2025-05-28 17:55:37.607] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:37.636 +07:00 [INF] [2025-05-28 17:55:37.636] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:55:37.636 +07:00 [DBG] [2025-05-28 17:55:37.636] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:55:37.636 +07:00 [INF] [2025-05-28 17:55:37.636] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:55:37.636 +07:00 [DBG] [2025-05-28 17:55:37.636] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:55:37.662 +07:00 [DBG] [2025-05-28 17:55:37.662] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:37.687 +07:00 [DBG] [2025-05-28 17:55:37.687] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, A♠, 3♦, 4♥]
2025-05-28 17:55:37.687 +07:00 [DBG] [2025-05-28 17:55:37.687] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:55:37.690 +07:00 [DBG] [2025-05-28 17:55:37.690] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:55:37.721 +07:00 [DBG] [2025-05-28 17:55:37.721] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:55:37.721 +07:00 [DBG] [2025-05-28 17:55:37.721] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:55:37.723 +07:00 [INF] [2025-05-28 17:55:37.723] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 11 played cards
2025-05-28 17:55:37.723 +07:00 [INF] [2025-05-28 17:55:37.723] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠],[K♦]
2025-05-28 17:55:37.724 +07:00 [INF] [2025-05-28 17:55:37.724] [INFO]: Hiển thị 11 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:55:37.724 +07:00 [DBG] [2025-05-28 17:55:37.724] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 0
2025-05-28 17:55:37.725 +07:00 [DBG] [2025-05-28 17:55:37.725] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 1
2025-05-28 17:55:37.725 +07:00 [DBG] [2025-05-28 17:55:37.725] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 2
2025-05-28 17:55:37.726 +07:00 [DBG] [2025-05-28 17:55:37.726] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 3
2025-05-28 17:55:37.726 +07:00 [DBG] [2025-05-28 17:55:37.726] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 4
2025-05-28 17:55:37.727 +07:00 [DBG] [2025-05-28 17:55:37.727] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 5
2025-05-28 17:55:37.727 +07:00 [DBG] [2025-05-28 17:55:37.727] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 6
2025-05-28 17:55:37.728 +07:00 [DBG] [2025-05-28 17:55:37.728] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 7
2025-05-28 17:55:37.728 +07:00 [DBG] [2025-05-28 17:55:37.728] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 8
2025-05-28 17:55:37.729 +07:00 [DBG] [2025-05-28 17:55:37.729] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 9
2025-05-28 17:55:37.729 +07:00 [DBG] [2025-05-28 17:55:37.729] [DEBUG]: Hiển thị lá bài đã đánh 50 tại vị trí 10
2025-05-28 17:55:39.806 +07:00 [INF] [2025-05-28 17:55:39.806] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,32]
2025-05-28 17:55:39.806 +07:00 [INF] [2025-05-28 17:55:39.806] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,32]
2025-05-28 17:55:39.806 +07:00 [INF] [2025-05-28 17:55:39.806] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,32]
2025-05-28 17:55:40.199 +07:00 [INF] [2025-05-28 17:55:40.198] [INFO] [nhatrang345]: Player xucha95 drew card -1, new cards: []
2025-05-28 17:55:40.199 +07:00 [DBG] [2025-05-28 17:55:40.199] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:40.199 +07:00 [INF] [2025-05-28 17:55:40.199] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:40.340 +07:00 [INF] [2025-05-28 17:55:40.340] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:40.340 +07:00 [INF] [2025-05-28 17:55:40.340] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:40.340 +07:00 [DBG] [2025-05-28 17:55:40.340] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:40.340 +07:00 [INF] [2025-05-28 17:55:40.340] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:40.340 +07:00 [ERR] [2025-05-28 17:55:40.340] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:40.340 +07:00 [INF] [2025-05-28 17:55:40.340] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:40.369 +07:00 [ERR] [2025-05-28 17:55:40.369] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: xucha95, cards: 0
2025-05-28 17:55:40.369 +07:00 [INF] [2025-05-28 17:55:40.369] [INFO]: ✅ Đánh dấu xucha95 đã rút bài
2025-05-28 17:55:43.018 +07:00 [INF] [2025-05-28 17:55:43.018] [INFO]: Updated opponent known cards: [Q♣]
2025-05-28 17:55:43.018 +07:00 [INF] [2025-05-28 17:55:43.018] [INFO] [nhatrang345]: Next turn: HaiDzaiVcl123 (UID: 1_246920792)
2025-05-28 17:55:43.018 +07:00 [DBG] [2025-05-28 17:55:43.018] [DEBUG]: Updated last played card: [Q♣]
2025-05-28 17:55:43.018 +07:00 [INF] [2025-05-28 17:55:43.018] [INFO] [nhatrang345]: Player xucha95 played card 45, next: HaiDzaiVcl123
2025-05-28 17:55:43.018 +07:00 [DBG] [2025-05-28 17:55:43.018] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:43.018 +07:00 [INF] [2025-05-28 17:55:43.018] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:43.159 +07:00 [INF] [2025-05-28 17:55:43.159] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:43.159 +07:00 [INF] [2025-05-28 17:55:43.159] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:43.159 +07:00 [DBG] [2025-05-28 17:55:43.159] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:43.159 +07:00 [INF] [2025-05-28 17:55:43.159] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:43.159 +07:00 [ERR] [2025-05-28 17:55:43.159] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:43.159 +07:00 [INF] [2025-05-28 17:55:43.159] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:43.188 +07:00 [INF] [2025-05-28 17:55:43.188] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:55:43.188 +07:00 [DBG] [2025-05-28 17:55:43.188] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:55:43.188 +07:00 [INF] [2025-05-28 17:55:43.188] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:55:43.188 +07:00 [DBG] [2025-05-28 17:55:43.188] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:55:43.214 +07:00 [DBG] [2025-05-28 17:55:43.214] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:43.240 +07:00 [DBG] [2025-05-28 17:55:43.240] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, A♠, 3♦, 4♥]
2025-05-28 17:55:43.240 +07:00 [DBG] [2025-05-28 17:55:43.240] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:55:43.243 +07:00 [DBG] [2025-05-28 17:55:43.243] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:55:43.273 +07:00 [DBG] [2025-05-28 17:55:43.273] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:55:43.273 +07:00 [DBG] [2025-05-28 17:55:43.273] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:55:43.275 +07:00 [INF] [2025-05-28 17:55:43.275] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 12 played cards
2025-05-28 17:55:43.275 +07:00 [INF] [2025-05-28 17:55:43.275] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠],[K♦],[Q♣]
2025-05-28 17:55:43.277 +07:00 [INF] [2025-05-28 17:55:43.277] [INFO]: Hiển thị 12 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:55:43.277 +07:00 [DBG] [2025-05-28 17:55:43.277] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 0
2025-05-28 17:55:43.277 +07:00 [DBG] [2025-05-28 17:55:43.277] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 1
2025-05-28 17:55:43.278 +07:00 [DBG] [2025-05-28 17:55:43.278] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 2
2025-05-28 17:55:43.278 +07:00 [DBG] [2025-05-28 17:55:43.278] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 3
2025-05-28 17:55:43.279 +07:00 [DBG] [2025-05-28 17:55:43.279] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 4
2025-05-28 17:55:43.279 +07:00 [DBG] [2025-05-28 17:55:43.279] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 5
2025-05-28 17:55:43.280 +07:00 [DBG] [2025-05-28 17:55:43.280] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 6
2025-05-28 17:55:43.280 +07:00 [DBG] [2025-05-28 17:55:43.280] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 7
2025-05-28 17:55:43.281 +07:00 [DBG] [2025-05-28 17:55:43.281] [DEBUG]: Hiển thị lá bài đã đánh 45 tại vị trí 8
2025-05-28 17:55:43.281 +07:00 [DBG] [2025-05-28 17:55:43.281] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 9
2025-05-28 17:55:43.282 +07:00 [DBG] [2025-05-28 17:55:43.282] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 10
2025-05-28 17:55:43.282 +07:00 [DBG] [2025-05-28 17:55:43.282] [DEBUG]: Hiển thị lá bài đã đánh 50 tại vị trí 11
2025-05-28 17:55:44.386 +07:00 [INF] [2025-05-28 17:55:44.386] [INFO] [nhatrang345]: Player HaiDzaiVcl123 drew card -1, new cards: []
2025-05-28 17:55:44.386 +07:00 [DBG] [2025-05-28 17:55:44.386] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:44.386 +07:00 [INF] [2025-05-28 17:55:44.386] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:44.527 +07:00 [INF] [2025-05-28 17:55:44.527] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:44.527 +07:00 [INF] [2025-05-28 17:55:44.527] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:44.527 +07:00 [DBG] [2025-05-28 17:55:44.527] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:44.527 +07:00 [INF] [2025-05-28 17:55:44.527] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:44.527 +07:00 [ERR] [2025-05-28 17:55:44.527] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:44.527 +07:00 [INF] [2025-05-28 17:55:44.527] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:44.556 +07:00 [ERR] [2025-05-28 17:55:44.556] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: HaiDzaiVcl123, cards: 0
2025-05-28 17:55:44.556 +07:00 [INF] [2025-05-28 17:55:44.556] [INFO]: ✅ Đánh dấu HaiDzaiVcl123 đã rút bài
2025-05-28 17:55:44.821 +07:00 [INF] [2025-05-28 17:55:44.821] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,33]
2025-05-28 17:55:44.821 +07:00 [INF] [2025-05-28 17:55:44.821] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,33]
2025-05-28 17:55:44.821 +07:00 [INF] [2025-05-28 17:55:44.821] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,33]
2025-05-28 17:55:46.979 +07:00 [INF] [2025-05-28 17:55:46.979] [INFO]: Updated opponent known cards: [7♥]
2025-05-28 17:55:46.979 +07:00 [INF] [2025-05-28 17:55:46.979] [INFO] [nhatrang345]: Next turn: nhatrang345 (UID: 1_229699688)
2025-05-28 17:55:46.979 +07:00 [DBG] [2025-05-28 17:55:46.979] [DEBUG]: Updated last played card: [7♥]
2025-05-28 17:55:46.979 +07:00 [INF] [2025-05-28 17:55:46.979] [INFO] [nhatrang345]: Player HaiDzaiVcl123 played card 27, next: nhatrang345
2025-05-28 17:55:46.979 +07:00 [DBG] [2025-05-28 17:55:46.979] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:46.979 +07:00 [INF] [2025-05-28 17:55:46.979] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:47.121 +07:00 [INF] [2025-05-28 17:55:47.121] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:47.121 +07:00 [INF] [2025-05-28 17:55:47.121] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:47.121 +07:00 [DBG] [2025-05-28 17:55:47.121] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:47.121 +07:00 [INF] [2025-05-28 17:55:47.121] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:47.121 +07:00 [ERR] [2025-05-28 17:55:47.121] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:47.121 +07:00 [INF] [2025-05-28 17:55:47.121] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:47.151 +07:00 [INF] [2025-05-28 17:55:47.151] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:55:47.151 +07:00 [DBG] [2025-05-28 17:55:47.151] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:55:47.151 +07:00 [INF] [2025-05-28 17:55:47.151] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:55:47.151 +07:00 [DBG] [2025-05-28 17:55:47.151] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:55:47.177 +07:00 [DBG] [2025-05-28 17:55:47.177] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:47.203 +07:00 [DBG] [2025-05-28 17:55:47.203] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, A♠, 3♦, 4♥]
2025-05-28 17:55:47.203 +07:00 [DBG] [2025-05-28 17:55:47.203] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:55:47.206 +07:00 [DBG] [2025-05-28 17:55:47.206] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:55:47.237 +07:00 [DBG] [2025-05-28 17:55:47.237] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:55:47.237 +07:00 [DBG] [2025-05-28 17:55:47.237] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:55:47.240 +07:00 [INF] [2025-05-28 17:55:47.240] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 13 played cards
2025-05-28 17:55:47.240 +07:00 [INF] [2025-05-28 17:55:47.240] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠],[K♦],[Q♣],[7♥]
2025-05-28 17:55:47.241 +07:00 [INF] [2025-05-28 17:55:47.241] [INFO]: Hiển thị 13 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:55:47.241 +07:00 [DBG] [2025-05-28 17:55:47.241] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 0
2025-05-28 17:55:47.242 +07:00 [DBG] [2025-05-28 17:55:47.242] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 1
2025-05-28 17:55:47.242 +07:00 [DBG] [2025-05-28 17:55:47.242] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 2
2025-05-28 17:55:47.243 +07:00 [DBG] [2025-05-28 17:55:47.243] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 3
2025-05-28 17:55:47.243 +07:00 [DBG] [2025-05-28 17:55:47.243] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 4
2025-05-28 17:55:47.244 +07:00 [DBG] [2025-05-28 17:55:47.244] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 5
2025-05-28 17:55:47.244 +07:00 [DBG] [2025-05-28 17:55:47.244] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 6
2025-05-28 17:55:47.245 +07:00 [DBG] [2025-05-28 17:55:47.245] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 7
2025-05-28 17:55:47.245 +07:00 [DBG] [2025-05-28 17:55:47.245] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 8
2025-05-28 17:55:47.246 +07:00 [DBG] [2025-05-28 17:55:47.246] [DEBUG]: Hiển thị lá bài đã đánh 45 tại vị trí 9
2025-05-28 17:55:47.246 +07:00 [DBG] [2025-05-28 17:55:47.246] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 10
2025-05-28 17:55:47.247 +07:00 [DBG] [2025-05-28 17:55:47.247] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 11
2025-05-28 17:55:47.247 +07:00 [DBG] [2025-05-28 17:55:47.247] [DEBUG]: Hiển thị lá bài đã đánh 50 tại vị trí 12
2025-05-28 17:55:48.305 +07:00 [INF] [2025-05-28 17:55:48.305] [INFO] [nhatrang345]: Updated cards for nhatrang345 after drawing: [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, A♠, A♣, 3♦, 4♥]
2025-05-28 17:55:48.305 +07:00 [DBG] [2025-05-28 17:55:48.305] [DEBUG]: Updated drawn card: [A♣]
2025-05-28 17:55:48.305 +07:00 [INF] [2025-05-28 17:55:48.305] [INFO] [nhatrang345]: Player nhatrang345 drew card 1, new cards: [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, A♠, A♣, 3♦, 4♥]
2025-05-28 17:55:48.305 +07:00 [DBG] [2025-05-28 17:55:48.305] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:48.305 +07:00 [INF] [2025-05-28 17:55:48.305] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:48.450 +07:00 [INF] [2025-05-28 17:55:48.450] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:48.450 +07:00 [INF] [2025-05-28 17:55:48.450] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:48.450 +07:00 [DBG] [2025-05-28 17:55:48.450] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:48.450 +07:00 [INF] [2025-05-28 17:55:48.450] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:48.450 +07:00 [ERR] [2025-05-28 17:55:48.450] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:48.450 +07:00 [INF] [2025-05-28 17:55:48.450] [INFO]: nhatrang345 đi trước (10 lá bài)
2025-05-28 17:55:48.450 +07:00 [DBG] [2025-05-28 17:55:48.450] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:55:48.450 +07:00 [INF] [2025-05-28 17:55:48.450] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:55:48.450 +07:00 [INF] [2025-05-28 17:55:48.450] [INFO]: 📊 nhatrang345: 1 phỏm, 1 cạ, 5 rác
2025-05-28 17:55:48.450 +07:00 [INF] [2025-05-28 17:55:48.450] [INFO]: 🔍 Solo analysis: 10 tổng, 5 bảo vệ, 5 có thể đánh, 29 ẩn
2025-05-28 17:55:48.450 +07:00 [INF] [2025-05-28 17:55:48.450] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([9♣]) - Bảo vệ 5 lá - Ẩn: 29
2025-05-28 17:55:48.450 +07:00 [INF] [2025-05-28 17:55:48.450] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [9♣] tại card/33.png
2025-05-28 17:55:48.481 +07:00 [INF] [2025-05-28 17:55:48.481] [INFO]: 🔄 UPDATE PANEL: nhatrang345 tại panel 0 với 10 lá bài (Turn: False, DrawnOrEaten: True)
2025-05-28 17:55:48.481 +07:00 [DBG] [2025-05-28 17:55:48.481] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 10
2025-05-28 17:55:48.507 +07:00 [DBG] [2025-05-28 17:55:48.507] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:48.533 +07:00 [DBG] [2025-05-28 17:55:48.533] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, 9♣, A♠, A♣, 3♦, 4♥]
2025-05-28 17:55:48.533 +07:00 [DBG] [2025-05-28 17:55:48.533] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 7
2025-05-28 17:55:48.536 +07:00 [DBG] [2025-05-28 17:55:48.536] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 7
2025-05-28 17:55:48.567 +07:00 [DBG] [2025-05-28 17:55:48.567] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 7
2025-05-28 17:55:48.567 +07:00 [DBG] [2025-05-28 17:55:48.567] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:55:48.567 +07:00 [INF] [2025-05-28 17:55:48.567] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:55:48.567 +07:00 [INF] [2025-05-28 17:55:48.567] [INFO]: 📊 nhatrang345: 1 phỏm, 1 cạ, 5 rác
2025-05-28 17:55:48.567 +07:00 [INF] [2025-05-28 17:55:48.567] [INFO]: 🔍 Solo analysis: 10 tổng, 5 bảo vệ, 5 có thể đánh, 29 ẩn
2025-05-28 17:55:48.567 +07:00 [INF] [2025-05-28 17:55:48.567] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([9♣]) - Bảo vệ 5 lá - Ẩn: 29
2025-05-28 17:55:48.568 +07:00 [INF] [2025-05-28 17:55:48.568] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [9♣] tại card/33.png
2025-05-28 17:55:48.568 +07:00 [INF] [2025-05-28 17:55:48.568] [INFO]: ✅ Đánh dấu nhatrang345 đã rút bài
2025-05-28 17:55:48.568 +07:00 [INF] [2025-05-28 17:55:48.568] [INFO]: 🎯 Trigger gợi ý cho nhatrang345 sau khi rút/ăn bài
2025-05-28 17:55:48.568 +07:00 [DBG] [2025-05-28 17:55:48.568] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:55:48.568 +07:00 [INF] [2025-05-28 17:55:48.568] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:55:48.568 +07:00 [INF] [2025-05-28 17:55:48.568] [INFO]: 📊 nhatrang345: 1 phỏm, 1 cạ, 5 rác
2025-05-28 17:55:48.568 +07:00 [INF] [2025-05-28 17:55:48.568] [INFO]: 🔍 Solo analysis: 10 tổng, 5 bảo vệ, 5 có thể đánh, 29 ẩn
2025-05-28 17:55:48.568 +07:00 [INF] [2025-05-28 17:55:48.568] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([9♣]) - Bảo vệ 5 lá - Ẩn: 29
2025-05-28 17:55:48.568 +07:00 [INF] [2025-05-28 17:55:48.568] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [9♣] tại card/33.png
2025-05-28 17:55:49.825 +07:00 [INF] [2025-05-28 17:55:49.825] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,34]
2025-05-28 17:55:49.825 +07:00 [INF] [2025-05-28 17:55:49.825] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,34]
2025-05-28 17:55:49.832 +07:00 [INF] [2025-05-28 17:55:49.832] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,34]
2025-05-28 17:55:50.561 +07:00 [INF] [2025-05-28 17:55:50.561] [INFO] [nhatrang345]: Updated cards for nhatrang345: [2♠, 2♣, 2♥, 5♣, 7♣, A♠, A♣, 3♦, 4♥]
2025-05-28 17:55:50.561 +07:00 [INF] [2025-05-28 17:55:50.561] [INFO]: Updated opponent known cards: [9♣]
2025-05-28 17:55:50.561 +07:00 [INF] [2025-05-28 17:55:50.561] [INFO] [nhatrang345]: Next turn: Minhheuu (UID: 22_341334)
2025-05-28 17:55:50.561 +07:00 [DBG] [2025-05-28 17:55:50.561] [DEBUG]: Updated last played card: [9♣]
2025-05-28 17:55:50.561 +07:00 [INF] [2025-05-28 17:55:50.561] [INFO] [nhatrang345]: Player nhatrang345 played card 33, next: Minhheuu
2025-05-28 17:55:50.561 +07:00 [DBG] [2025-05-28 17:55:50.561] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:50.561 +07:00 [INF] [2025-05-28 17:55:50.561] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:50.703 +07:00 [INF] [2025-05-28 17:55:50.703] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:50.703 +07:00 [INF] [2025-05-28 17:55:50.703] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:50.703 +07:00 [DBG] [2025-05-28 17:55:50.703] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:50.703 +07:00 [INF] [2025-05-28 17:55:50.703] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:50.703 +07:00 [ERR] [2025-05-28 17:55:50.703] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:50.703 +07:00 [INF] [2025-05-28 17:55:50.703] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:50.732 +07:00 [INF] [2025-05-28 17:55:50.732] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:55:50.732 +07:00 [DBG] [2025-05-28 17:55:50.732] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:55:50.732 +07:00 [INF] [2025-05-28 17:55:50.732] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:55:50.732 +07:00 [DBG] [2025-05-28 17:55:50.732] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:55:50.759 +07:00 [DBG] [2025-05-28 17:55:50.759] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:50.784 +07:00 [DBG] [2025-05-28 17:55:50.784] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, A♠, A♣, 3♦, 4♥]
2025-05-28 17:55:50.784 +07:00 [DBG] [2025-05-28 17:55:50.784] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:55:50.787 +07:00 [DBG] [2025-05-28 17:55:50.787] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:55:50.821 +07:00 [DBG] [2025-05-28 17:55:50.821] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:55:50.821 +07:00 [DBG] [2025-05-28 17:55:50.821] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:55:50.823 +07:00 [INF] [2025-05-28 17:55:50.823] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 14 played cards
2025-05-28 17:55:50.823 +07:00 [INF] [2025-05-28 17:55:50.823] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠],[K♦],[Q♣],[7♥],[9♣]
2025-05-28 17:55:50.825 +07:00 [INF] [2025-05-28 17:55:50.825] [INFO]: Hiển thị 14 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:55:50.825 +07:00 [DBG] [2025-05-28 17:55:50.825] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 0
2025-05-28 17:55:50.826 +07:00 [DBG] [2025-05-28 17:55:50.826] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 1
2025-05-28 17:55:50.826 +07:00 [DBG] [2025-05-28 17:55:50.826] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 2
2025-05-28 17:55:50.826 +07:00 [DBG] [2025-05-28 17:55:50.826] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 3
2025-05-28 17:55:50.827 +07:00 [DBG] [2025-05-28 17:55:50.827] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 4
2025-05-28 17:55:50.827 +07:00 [DBG] [2025-05-28 17:55:50.827] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 5
2025-05-28 17:55:50.828 +07:00 [DBG] [2025-05-28 17:55:50.828] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 6
2025-05-28 17:55:50.828 +07:00 [DBG] [2025-05-28 17:55:50.828] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 7
2025-05-28 17:55:50.829 +07:00 [DBG] [2025-05-28 17:55:50.829] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 8
2025-05-28 17:55:50.829 +07:00 [DBG] [2025-05-28 17:55:50.829] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 9
2025-05-28 17:55:50.830 +07:00 [DBG] [2025-05-28 17:55:50.830] [DEBUG]: Hiển thị lá bài đã đánh 45 tại vị trí 10
2025-05-28 17:55:50.830 +07:00 [DBG] [2025-05-28 17:55:50.830] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 11
2025-05-28 17:55:50.831 +07:00 [DBG] [2025-05-28 17:55:50.831] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 12
2025-05-28 17:55:50.831 +07:00 [DBG] [2025-05-28 17:55:50.831] [DEBUG]: Hiển thị lá bài đã đánh 50 tại vị trí 13
2025-05-28 17:55:52.167 +07:00 [INF] [2025-05-28 17:55:52.167] [INFO] [nhatrang345]: Player Minhheuu drew card -1, new cards: []
2025-05-28 17:55:52.167 +07:00 [DBG] [2025-05-28 17:55:52.167] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:52.167 +07:00 [INF] [2025-05-28 17:55:52.167] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:52.315 +07:00 [INF] [2025-05-28 17:55:52.315] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:52.315 +07:00 [INF] [2025-05-28 17:55:52.315] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:52.315 +07:00 [DBG] [2025-05-28 17:55:52.315] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:52.315 +07:00 [INF] [2025-05-28 17:55:52.315] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:52.315 +07:00 [ERR] [2025-05-28 17:55:52.315] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:52.316 +07:00 [INF] [2025-05-28 17:55:52.315] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:52.347 +07:00 [ERR] [2025-05-28 17:55:52.347] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: Minhheuu, cards: 0
2025-05-28 17:55:52.347 +07:00 [INF] [2025-05-28 17:55:52.347] [INFO]: ✅ Đánh dấu Minhheuu đã rút bài
2025-05-28 17:55:54.838 +07:00 [INF] [2025-05-28 17:55:54.838] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,35]
2025-05-28 17:55:54.838 +07:00 [INF] [2025-05-28 17:55:54.838] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,35]
2025-05-28 17:55:54.839 +07:00 [INF] [2025-05-28 17:55:54.839] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,35]
2025-05-28 17:55:58.347 +07:00 [INF] [2025-05-28 17:55:58.347] [INFO]: Updated opponent known cards: [3♥]
2025-05-28 17:55:58.347 +07:00 [INF] [2025-05-28 17:55:58.347] [INFO] [nhatrang345]: Next turn: xucha95 (UID: 1_279027341)
2025-05-28 17:55:58.347 +07:00 [DBG] [2025-05-28 17:55:58.347] [DEBUG]: Updated last played card: [3♥]
2025-05-28 17:55:58.347 +07:00 [INF] [2025-05-28 17:55:58.347] [INFO] [nhatrang345]: Player Minhheuu played card 11, next: xucha95
2025-05-28 17:55:58.347 +07:00 [DBG] [2025-05-28 17:55:58.347] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:55:58.347 +07:00 [INF] [2025-05-28 17:55:58.347] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:55:58.490 +07:00 [INF] [2025-05-28 17:55:58.490] [INFO]: Chế độ chơi: Team
2025-05-28 17:55:58.490 +07:00 [INF] [2025-05-28 17:55:58.490] [INFO]: Xử lý chế độ Team
2025-05-28 17:55:58.490 +07:00 [DBG] [2025-05-28 17:55:58.490] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:58.490 +07:00 [INF] [2025-05-28 17:55:58.490] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:55:58.490 +07:00 [ERR] [2025-05-28 17:55:58.490] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:55:58.490 +07:00 [INF] [2025-05-28 17:55:58.490] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:55:58.519 +07:00 [INF] [2025-05-28 17:55:58.519] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:55:58.519 +07:00 [DBG] [2025-05-28 17:55:58.519] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:55:58.519 +07:00 [INF] [2025-05-28 17:55:58.519] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:55:58.519 +07:00 [DBG] [2025-05-28 17:55:58.519] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:55:58.546 +07:00 [DBG] [2025-05-28 17:55:58.546] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:55:58.572 +07:00 [DBG] [2025-05-28 17:55:58.572] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, A♠, A♣, 3♦, 4♥]
2025-05-28 17:55:58.572 +07:00 [DBG] [2025-05-28 17:55:58.572] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:55:58.575 +07:00 [DBG] [2025-05-28 17:55:58.575] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:55:58.608 +07:00 [DBG] [2025-05-28 17:55:58.608] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:55:58.608 +07:00 [DBG] [2025-05-28 17:55:58.608] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:55:58.610 +07:00 [INF] [2025-05-28 17:55:58.610] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 15 played cards
2025-05-28 17:55:58.610 +07:00 [INF] [2025-05-28 17:55:58.610] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠],[K♦],[Q♣],[7♥],[9♣],[3♥]
2025-05-28 17:55:58.613 +07:00 [INF] [2025-05-28 17:55:58.613] [INFO]: Hiển thị 15 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:55:58.613 +07:00 [DBG] [2025-05-28 17:55:58.613] [DEBUG]: Hiển thị lá bài đã đánh 11 tại vị trí 0
2025-05-28 17:55:58.614 +07:00 [DBG] [2025-05-28 17:55:58.614] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 1
2025-05-28 17:55:58.615 +07:00 [DBG] [2025-05-28 17:55:58.615] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 2
2025-05-28 17:55:58.615 +07:00 [DBG] [2025-05-28 17:55:58.615] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 3
2025-05-28 17:55:58.616 +07:00 [DBG] [2025-05-28 17:55:58.616] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 4
2025-05-28 17:55:58.616 +07:00 [DBG] [2025-05-28 17:55:58.616] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 5
2025-05-28 17:55:58.617 +07:00 [DBG] [2025-05-28 17:55:58.617] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 6
2025-05-28 17:55:58.617 +07:00 [DBG] [2025-05-28 17:55:58.617] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 7
2025-05-28 17:55:58.618 +07:00 [DBG] [2025-05-28 17:55:58.618] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 8
2025-05-28 17:55:58.618 +07:00 [DBG] [2025-05-28 17:55:58.618] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 9
2025-05-28 17:55:58.619 +07:00 [DBG] [2025-05-28 17:55:58.619] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 10
2025-05-28 17:55:58.619 +07:00 [DBG] [2025-05-28 17:55:58.619] [DEBUG]: Hiển thị lá bài đã đánh 45 tại vị trí 11
2025-05-28 17:55:58.620 +07:00 [DBG] [2025-05-28 17:55:58.620] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 12
2025-05-28 17:55:58.620 +07:00 [DBG] [2025-05-28 17:55:58.620] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 13
2025-05-28 17:55:58.621 +07:00 [DBG] [2025-05-28 17:55:58.621] [DEBUG]: Hiển thị lá bài đã đánh 50 tại vị trí 14
2025-05-28 17:55:59.855 +07:00 [INF] [2025-05-28 17:55:59.855] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,36]
2025-05-28 17:55:59.855 +07:00 [INF] [2025-05-28 17:55:59.855] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,36]
2025-05-28 17:55:59.865 +07:00 [INF] [2025-05-28 17:55:59.865] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,36]
2025-05-28 17:56:02.357 +07:00 [INF] [2025-05-28 17:56:02.357] [INFO] [nhatrang345]: Player xucha95 drew card -1, new cards: []
2025-05-28 17:56:02.357 +07:00 [DBG] [2025-05-28 17:56:02.357] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:56:02.357 +07:00 [INF] [2025-05-28 17:56:02.357] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:56:02.502 +07:00 [INF] [2025-05-28 17:56:02.502] [INFO]: Chế độ chơi: Team
2025-05-28 17:56:02.502 +07:00 [INF] [2025-05-28 17:56:02.502] [INFO]: Xử lý chế độ Team
2025-05-28 17:56:02.502 +07:00 [DBG] [2025-05-28 17:56:02.502] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:02.502 +07:00 [INF] [2025-05-28 17:56:02.502] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:56:02.502 +07:00 [ERR] [2025-05-28 17:56:02.502] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:56:02.502 +07:00 [INF] [2025-05-28 17:56:02.502] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:56:02.532 +07:00 [ERR] [2025-05-28 17:56:02.532] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: xucha95, cards: 0
2025-05-28 17:56:02.532 +07:00 [INF] [2025-05-28 17:56:02.532] [INFO]: ✅ Đánh dấu xucha95 đã rút bài
2025-05-28 17:56:04.013 +07:00 [DBG] [2025-05-28 17:56:04.013] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:56:04.013 +07:00 [INF] [2025-05-28 17:56:04.013] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:56:04.156 +07:00 [INF] [2025-05-28 17:56:04.156] [INFO]: Chế độ chơi: Team
2025-05-28 17:56:04.156 +07:00 [INF] [2025-05-28 17:56:04.156] [INFO]: Xử lý chế độ Team
2025-05-28 17:56:04.156 +07:00 [DBG] [2025-05-28 17:56:04.156] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:04.156 +07:00 [INF] [2025-05-28 17:56:04.156] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:56:04.156 +07:00 [ERR] [2025-05-28 17:56:04.156] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:56:04.156 +07:00 [INF] [2025-05-28 17:56:04.156] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:56:04.187 +07:00 [INF] [2025-05-28 17:56:04.186] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:56:04.187 +07:00 [DBG] [2025-05-28 17:56:04.187] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:56:04.187 +07:00 [INF] [2025-05-28 17:56:04.187] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:56:04.187 +07:00 [DBG] [2025-05-28 17:56:04.187] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:56:04.213 +07:00 [DBG] [2025-05-28 17:56:04.213] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:04.239 +07:00 [DBG] [2025-05-28 17:56:04.239] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, A♠, A♣, 3♦, 4♥]
2025-05-28 17:56:04.239 +07:00 [DBG] [2025-05-28 17:56:04.239] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:56:04.242 +07:00 [DBG] [2025-05-28 17:56:04.242] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:56:04.273 +07:00 [DBG] [2025-05-28 17:56:04.273] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:56:04.273 +07:00 [DBG] [2025-05-28 17:56:04.273] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:56:04.276 +07:00 [INF] [2025-05-28 17:56:04.276] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 15 played cards
2025-05-28 17:56:04.276 +07:00 [INF] [2025-05-28 17:56:04.276] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠],[K♦],[Q♣],[7♥],[9♣],[3♥]
2025-05-28 17:56:04.278 +07:00 [INF] [2025-05-28 17:56:04.278] [INFO]: Hiển thị 15 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:56:04.278 +07:00 [DBG] [2025-05-28 17:56:04.278] [DEBUG]: Hiển thị lá bài đã đánh 11 tại vị trí 0
2025-05-28 17:56:04.278 +07:00 [DBG] [2025-05-28 17:56:04.278] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 1
2025-05-28 17:56:04.279 +07:00 [DBG] [2025-05-28 17:56:04.279] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 2
2025-05-28 17:56:04.279 +07:00 [DBG] [2025-05-28 17:56:04.279] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 3
2025-05-28 17:56:04.280 +07:00 [DBG] [2025-05-28 17:56:04.280] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 4
2025-05-28 17:56:04.280 +07:00 [DBG] [2025-05-28 17:56:04.280] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 5
2025-05-28 17:56:04.281 +07:00 [DBG] [2025-05-28 17:56:04.281] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 6
2025-05-28 17:56:04.281 +07:00 [DBG] [2025-05-28 17:56:04.281] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 7
2025-05-28 17:56:04.282 +07:00 [DBG] [2025-05-28 17:56:04.282] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 8
2025-05-28 17:56:04.282 +07:00 [DBG] [2025-05-28 17:56:04.282] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 9
2025-05-28 17:56:04.283 +07:00 [DBG] [2025-05-28 17:56:04.283] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 10
2025-05-28 17:56:04.283 +07:00 [DBG] [2025-05-28 17:56:04.283] [DEBUG]: Hiển thị lá bài đã đánh 45 tại vị trí 11
2025-05-28 17:56:04.284 +07:00 [DBG] [2025-05-28 17:56:04.284] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 12
2025-05-28 17:56:04.284 +07:00 [DBG] [2025-05-28 17:56:04.284] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 13
2025-05-28 17:56:04.285 +07:00 [DBG] [2025-05-28 17:56:04.285] [DEBUG]: Hiển thị lá bài đã đánh 50 tại vị trí 14
2025-05-28 17:56:04.855 +07:00 [INF] [2025-05-28 17:56:04.855] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,37]
2025-05-28 17:56:04.855 +07:00 [INF] [2025-05-28 17:56:04.855] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,37]
2025-05-28 17:56:04.865 +07:00 [INF] [2025-05-28 17:56:04.865] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,37]
2025-05-28 17:56:07.864 +07:00 [INF] [2025-05-28 17:56:07.864] [INFO]: Updated opponent known cards: [10♣]
2025-05-28 17:56:07.864 +07:00 [INF] [2025-05-28 17:56:07.864] [INFO] [nhatrang345]: Next turn: HaiDzaiVcl123 (UID: 1_246920792)
2025-05-28 17:56:07.864 +07:00 [DBG] [2025-05-28 17:56:07.864] [DEBUG]: Updated last played card: [10♣]
2025-05-28 17:56:07.864 +07:00 [INF] [2025-05-28 17:56:07.864] [INFO] [nhatrang345]: Player xucha95 played card 37, next: HaiDzaiVcl123
2025-05-28 17:56:07.864 +07:00 [DBG] [2025-05-28 17:56:07.864] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:56:07.864 +07:00 [INF] [2025-05-28 17:56:07.864] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:56:08.014 +07:00 [INF] [2025-05-28 17:56:08.014] [INFO]: Chế độ chơi: Team
2025-05-28 17:56:08.014 +07:00 [INF] [2025-05-28 17:56:08.014] [INFO]: Xử lý chế độ Team
2025-05-28 17:56:08.014 +07:00 [DBG] [2025-05-28 17:56:08.014] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:08.014 +07:00 [INF] [2025-05-28 17:56:08.014] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:56:08.014 +07:00 [ERR] [2025-05-28 17:56:08.014] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:56:08.014 +07:00 [INF] [2025-05-28 17:56:08.014] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:56:08.044 +07:00 [INF] [2025-05-28 17:56:08.044] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:56:08.044 +07:00 [DBG] [2025-05-28 17:56:08.044] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:56:08.044 +07:00 [INF] [2025-05-28 17:56:08.044] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:56:08.044 +07:00 [DBG] [2025-05-28 17:56:08.044] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:56:08.071 +07:00 [DBG] [2025-05-28 17:56:08.071] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:08.097 +07:00 [DBG] [2025-05-28 17:56:08.097] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, A♠, A♣, 3♦, 4♥]
2025-05-28 17:56:08.097 +07:00 [DBG] [2025-05-28 17:56:08.097] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:56:08.100 +07:00 [DBG] [2025-05-28 17:56:08.100] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:56:08.132 +07:00 [DBG] [2025-05-28 17:56:08.132] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:56:08.132 +07:00 [DBG] [2025-05-28 17:56:08.132] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:56:08.135 +07:00 [INF] [2025-05-28 17:56:08.135] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 16 played cards
2025-05-28 17:56:08.135 +07:00 [INF] [2025-05-28 17:56:08.135] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠],[K♦],[Q♣],[7♥],[9♣],[3♥],[10♣]
2025-05-28 17:56:08.136 +07:00 [INF] [2025-05-28 17:56:08.136] [INFO]: Hiển thị 16 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:56:08.137 +07:00 [DBG] [2025-05-28 17:56:08.137] [DEBUG]: Hiển thị lá bài đã đánh 11 tại vị trí 0
2025-05-28 17:56:08.137 +07:00 [DBG] [2025-05-28 17:56:08.137] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 1
2025-05-28 17:56:08.138 +07:00 [DBG] [2025-05-28 17:56:08.138] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 2
2025-05-28 17:56:08.138 +07:00 [DBG] [2025-05-28 17:56:08.138] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 3
2025-05-28 17:56:08.139 +07:00 [DBG] [2025-05-28 17:56:08.139] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 4
2025-05-28 17:56:08.139 +07:00 [DBG] [2025-05-28 17:56:08.139] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 5
2025-05-28 17:56:08.139 +07:00 [DBG] [2025-05-28 17:56:08.139] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 6
2025-05-28 17:56:08.140 +07:00 [DBG] [2025-05-28 17:56:08.140] [DEBUG]: Hiển thị lá bài đã đánh 37 tại vị trí 7
2025-05-28 17:56:08.140 +07:00 [DBG] [2025-05-28 17:56:08.140] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 8
2025-05-28 17:56:08.141 +07:00 [DBG] [2025-05-28 17:56:08.141] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 9
2025-05-28 17:56:08.142 +07:00 [DBG] [2025-05-28 17:56:08.142] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 10
2025-05-28 17:56:08.142 +07:00 [DBG] [2025-05-28 17:56:08.142] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 11
2025-05-28 17:56:08.143 +07:00 [DBG] [2025-05-28 17:56:08.143] [DEBUG]: Hiển thị lá bài đã đánh 45 tại vị trí 12
2025-05-28 17:56:08.143 +07:00 [DBG] [2025-05-28 17:56:08.143] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 13
2025-05-28 17:56:08.144 +07:00 [DBG] [2025-05-28 17:56:08.144] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 14
2025-05-28 17:56:08.144 +07:00 [DBG] [2025-05-28 17:56:08.144] [DEBUG]: Hiển thị lá bài đã đánh 50 tại vị trí 15
2025-05-28 17:56:09.433 +07:00 [INF] [2025-05-28 17:56:09.433] [INFO] [nhatrang345]: Player HaiDzaiVcl123 drew card -1, new cards: []
2025-05-28 17:56:09.433 +07:00 [DBG] [2025-05-28 17:56:09.433] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:56:09.433 +07:00 [INF] [2025-05-28 17:56:09.433] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:56:09.582 +07:00 [INF] [2025-05-28 17:56:09.582] [INFO]: Chế độ chơi: Team
2025-05-28 17:56:09.582 +07:00 [INF] [2025-05-28 17:56:09.582] [INFO]: Xử lý chế độ Team
2025-05-28 17:56:09.582 +07:00 [DBG] [2025-05-28 17:56:09.582] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:09.582 +07:00 [INF] [2025-05-28 17:56:09.582] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:56:09.582 +07:00 [ERR] [2025-05-28 17:56:09.582] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:56:09.582 +07:00 [INF] [2025-05-28 17:56:09.582] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:56:09.611 +07:00 [ERR] [2025-05-28 17:56:09.611] [ERROR]: ❌ UpdateSpecificUser: Invalid parameters - username: HaiDzaiVcl123, cards: 0
2025-05-28 17:56:09.611 +07:00 [INF] [2025-05-28 17:56:09.611] [INFO]: ✅ Đánh dấu HaiDzaiVcl123 đã rút bài
2025-05-28 17:56:09.855 +07:00 [INF] [2025-05-28 17:56:09.855] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,38]
2025-05-28 17:56:09.855 +07:00 [INF] [2025-05-28 17:56:09.855] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,38]
2025-05-28 17:56:09.855 +07:00 [INF] [2025-05-28 17:56:09.855] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,38]
2025-05-28 17:56:11.216 +07:00 [DBG] [2025-05-28 17:56:11.216] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:56:11.216 +07:00 [INF] [2025-05-28 17:56:11.216] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:56:11.360 +07:00 [INF] [2025-05-28 17:56:11.359] [INFO]: Chế độ chơi: Team
2025-05-28 17:56:11.360 +07:00 [INF] [2025-05-28 17:56:11.360] [INFO]: Xử lý chế độ Team
2025-05-28 17:56:11.360 +07:00 [DBG] [2025-05-28 17:56:11.360] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:11.360 +07:00 [INF] [2025-05-28 17:56:11.360] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:56:11.360 +07:00 [ERR] [2025-05-28 17:56:11.360] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:56:11.360 +07:00 [INF] [2025-05-28 17:56:11.360] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:56:11.390 +07:00 [INF] [2025-05-28 17:56:11.390] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:56:11.390 +07:00 [DBG] [2025-05-28 17:56:11.390] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:56:11.390 +07:00 [INF] [2025-05-28 17:56:11.390] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:56:11.390 +07:00 [DBG] [2025-05-28 17:56:11.390] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:56:11.416 +07:00 [DBG] [2025-05-28 17:56:11.416] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:11.442 +07:00 [DBG] [2025-05-28 17:56:11.442] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, A♠, A♣, 3♦, 4♥]
2025-05-28 17:56:11.442 +07:00 [DBG] [2025-05-28 17:56:11.442] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:56:11.445 +07:00 [DBG] [2025-05-28 17:56:11.445] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:56:11.476 +07:00 [DBG] [2025-05-28 17:56:11.476] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:56:11.476 +07:00 [DBG] [2025-05-28 17:56:11.476] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:56:11.479 +07:00 [INF] [2025-05-28 17:56:11.479] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 16 played cards
2025-05-28 17:56:11.479 +07:00 [INF] [2025-05-28 17:56:11.479] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠],[K♦],[Q♣],[7♥],[9♣],[3♥],[10♣]
2025-05-28 17:56:11.480 +07:00 [INF] [2025-05-28 17:56:11.480] [INFO]: Hiển thị 16 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:56:11.481 +07:00 [DBG] [2025-05-28 17:56:11.481] [DEBUG]: Hiển thị lá bài đã đánh 11 tại vị trí 0
2025-05-28 17:56:11.482 +07:00 [DBG] [2025-05-28 17:56:11.482] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 1
2025-05-28 17:56:11.482 +07:00 [DBG] [2025-05-28 17:56:11.482] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 2
2025-05-28 17:56:11.483 +07:00 [DBG] [2025-05-28 17:56:11.483] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 3
2025-05-28 17:56:11.483 +07:00 [DBG] [2025-05-28 17:56:11.483] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 4
2025-05-28 17:56:11.484 +07:00 [DBG] [2025-05-28 17:56:11.484] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 5
2025-05-28 17:56:11.484 +07:00 [DBG] [2025-05-28 17:56:11.484] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 6
2025-05-28 17:56:11.485 +07:00 [DBG] [2025-05-28 17:56:11.485] [DEBUG]: Hiển thị lá bài đã đánh 37 tại vị trí 7
2025-05-28 17:56:11.485 +07:00 [DBG] [2025-05-28 17:56:11.485] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 8
2025-05-28 17:56:11.485 +07:00 [DBG] [2025-05-28 17:56:11.485] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 9
2025-05-28 17:56:11.486 +07:00 [DBG] [2025-05-28 17:56:11.486] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 10
2025-05-28 17:56:11.486 +07:00 [DBG] [2025-05-28 17:56:11.486] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 11
2025-05-28 17:56:11.487 +07:00 [DBG] [2025-05-28 17:56:11.487] [DEBUG]: Hiển thị lá bài đã đánh 45 tại vị trí 12
2025-05-28 17:56:11.487 +07:00 [DBG] [2025-05-28 17:56:11.487] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 13
2025-05-28 17:56:11.488 +07:00 [DBG] [2025-05-28 17:56:11.488] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 14
2025-05-28 17:56:11.488 +07:00 [DBG] [2025-05-28 17:56:11.488] [DEBUG]: Hiển thị lá bài đã đánh 50 tại vị trí 15
2025-05-28 17:56:13.008 +07:00 [INF] [2025-05-28 17:56:13.008] [INFO]: Updated opponent known cards: [Q♥]
2025-05-28 17:56:13.008 +07:00 [INF] [2025-05-28 17:56:13.008] [INFO] [nhatrang345]: Next turn: nhatrang345 (UID: 1_229699688)
2025-05-28 17:56:13.008 +07:00 [DBG] [2025-05-28 17:56:13.008] [DEBUG]: Updated last played card: [Q♥]
2025-05-28 17:56:13.008 +07:00 [INF] [2025-05-28 17:56:13.008] [INFO] [nhatrang345]: Player HaiDzaiVcl123 played card 47, next: nhatrang345
2025-05-28 17:56:13.008 +07:00 [DBG] [2025-05-28 17:56:13.008] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:56:13.008 +07:00 [INF] [2025-05-28 17:56:13.008] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:56:13.156 +07:00 [INF] [2025-05-28 17:56:13.156] [INFO]: Chế độ chơi: Team
2025-05-28 17:56:13.156 +07:00 [INF] [2025-05-28 17:56:13.156] [INFO]: Xử lý chế độ Team
2025-05-28 17:56:13.156 +07:00 [DBG] [2025-05-28 17:56:13.156] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:13.156 +07:00 [INF] [2025-05-28 17:56:13.156] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:56:13.156 +07:00 [ERR] [2025-05-28 17:56:13.156] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:56:13.156 +07:00 [INF] [2025-05-28 17:56:13.156] [INFO]: nhatrang345 không đi trước (9 lá bài)
2025-05-28 17:56:13.186 +07:00 [INF] [2025-05-28 17:56:13.186] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:56:13.186 +07:00 [DBG] [2025-05-28 17:56:13.186] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:56:13.186 +07:00 [INF] [2025-05-28 17:56:13.186] [INFO]: 🔄 UPDATE nhatrang345: thay đổi số bài (12→9)
2025-05-28 17:56:13.186 +07:00 [DBG] [2025-05-28 17:56:13.186] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 9
2025-05-28 17:56:13.214 +07:00 [DBG] [2025-05-28 17:56:13.214] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:13.241 +07:00 [DBG] [2025-05-28 17:56:13.241] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, A♠, A♣, 3♦, 4♥]
2025-05-28 17:56:13.241 +07:00 [DBG] [2025-05-28 17:56:13.241] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 6
2025-05-28 17:56:13.244 +07:00 [DBG] [2025-05-28 17:56:13.244] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 6
2025-05-28 17:56:13.277 +07:00 [DBG] [2025-05-28 17:56:13.277] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 6
2025-05-28 17:56:13.277 +07:00 [DBG] [2025-05-28 17:56:13.277] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=False, hasEatenCard=False, shouldShowSuggestion=False
2025-05-28 17:56:13.279 +07:00 [INF] [2025-05-28 17:56:13.279] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 17 played cards
2025-05-28 17:56:13.280 +07:00 [INF] [2025-05-28 17:56:13.280] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠],[K♦],[Q♣],[7♥],[9♣],[3♥],[10♣],[Q♥]
2025-05-28 17:56:13.282 +07:00 [INF] [2025-05-28 17:56:13.282] [INFO]: Hiển thị 17 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:56:13.282 +07:00 [DBG] [2025-05-28 17:56:13.282] [DEBUG]: Hiển thị lá bài đã đánh 11 tại vị trí 0
2025-05-28 17:56:13.283 +07:00 [DBG] [2025-05-28 17:56:13.283] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 1
2025-05-28 17:56:13.283 +07:00 [DBG] [2025-05-28 17:56:13.283] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 2
2025-05-28 17:56:13.284 +07:00 [DBG] [2025-05-28 17:56:13.284] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 3
2025-05-28 17:56:13.284 +07:00 [DBG] [2025-05-28 17:56:13.284] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 4
2025-05-28 17:56:13.285 +07:00 [DBG] [2025-05-28 17:56:13.285] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 5
2025-05-28 17:56:13.285 +07:00 [DBG] [2025-05-28 17:56:13.285] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 6
2025-05-28 17:56:13.286 +07:00 [DBG] [2025-05-28 17:56:13.286] [DEBUG]: Hiển thị lá bài đã đánh 37 tại vị trí 7
2025-05-28 17:56:13.286 +07:00 [DBG] [2025-05-28 17:56:13.286] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 8
2025-05-28 17:56:13.287 +07:00 [DBG] [2025-05-28 17:56:13.287] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 9
2025-05-28 17:56:13.287 +07:00 [DBG] [2025-05-28 17:56:13.287] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 10
2025-05-28 17:56:13.288 +07:00 [DBG] [2025-05-28 17:56:13.288] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 11
2025-05-28 17:56:13.288 +07:00 [DBG] [2025-05-28 17:56:13.288] [DEBUG]: Hiển thị lá bài đã đánh 45 tại vị trí 12
2025-05-28 17:56:13.289 +07:00 [DBG] [2025-05-28 17:56:13.289] [DEBUG]: Hiển thị lá bài đã đánh 47 tại vị trí 13
2025-05-28 17:56:13.289 +07:00 [DBG] [2025-05-28 17:56:13.289] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 14
2025-05-28 17:56:13.290 +07:00 [DBG] [2025-05-28 17:56:13.290] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 15
2025-05-28 17:56:13.291 +07:00 [DBG] [2025-05-28 17:56:13.291] [DEBUG]: Hiển thị lá bài đã đánh 50 tại vị trí 16
2025-05-28 17:56:14.872 +07:00 [INF] [2025-05-28 17:56:14.872] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,39]
2025-05-28 17:56:14.872 +07:00 [INF] [2025-05-28 17:56:14.872] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,39]
2025-05-28 17:56:14.880 +07:00 [INF] [2025-05-28 17:56:14.880] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,39]
2025-05-28 17:56:18.299 +07:00 [INF] [2025-05-28 17:56:18.299] [INFO] [nhatrang345]: Updated cards for nhatrang345 after drawing: [2♠, 2♣, 2♥, 5♣, 7♣, A♠, A♣, 3♦, 4♥, 8♥]
2025-05-28 17:56:18.299 +07:00 [DBG] [2025-05-28 17:56:18.299] [DEBUG]: Updated drawn card: [8♥]
2025-05-28 17:56:18.299 +07:00 [INF] [2025-05-28 17:56:18.299] [INFO] [nhatrang345]: Player nhatrang345 drew card 31, new cards: [2♠, 2♣, 2♥, 5♣, 7♣, A♠, A♣, 3♦, 4♥, 8♥]
2025-05-28 17:56:18.299 +07:00 [DBG] [2025-05-28 17:56:18.299] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:56:18.299 +07:00 [INF] [2025-05-28 17:56:18.299] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:56:18.446 +07:00 [INF] [2025-05-28 17:56:18.446] [INFO]: Chế độ chơi: Team
2025-05-28 17:56:18.446 +07:00 [INF] [2025-05-28 17:56:18.446] [INFO]: Xử lý chế độ Team
2025-05-28 17:56:18.446 +07:00 [DBG] [2025-05-28 17:56:18.446] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:18.446 +07:00 [INF] [2025-05-28 17:56:18.446] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:56:18.446 +07:00 [ERR] [2025-05-28 17:56:18.446] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:56:18.446 +07:00 [INF] [2025-05-28 17:56:18.446] [INFO]: nhatrang345 đi trước (10 lá bài)
2025-05-28 17:56:18.446 +07:00 [DBG] [2025-05-28 17:56:18.446] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:56:18.446 +07:00 [INF] [2025-05-28 17:56:18.446] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:56:18.446 +07:00 [INF] [2025-05-28 17:56:18.446] [INFO]: 📊 nhatrang345: 1 phỏm, 1 cạ, 5 rác
2025-05-28 17:56:18.446 +07:00 [INF] [2025-05-28 17:56:18.446] [INFO]: 🔍 Solo analysis: 10 tổng, 5 bảo vệ, 5 có thể đánh, 25 ẩn
2025-05-28 17:56:18.446 +07:00 [INF] [2025-05-28 17:56:18.446] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([8♥]) - Bảo vệ 5 lá - Ẩn: 25
2025-05-28 17:56:18.447 +07:00 [INF] [2025-05-28 17:56:18.447] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [8♥] tại card/31.png
2025-05-28 17:56:18.477 +07:00 [INF] [2025-05-28 17:56:18.477] [INFO]: 🔄 UPDATE PANEL: nhatrang345 tại panel 0 với 10 lá bài (Turn: False, DrawnOrEaten: True)
2025-05-28 17:56:18.477 +07:00 [DBG] [2025-05-28 17:56:18.477] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 10
2025-05-28 17:56:18.504 +07:00 [DBG] [2025-05-28 17:56:18.504] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:18.531 +07:00 [DBG] [2025-05-28 17:56:18.531] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, A♠, A♣, 3♦, 4♥, 8♥]
2025-05-28 17:56:18.531 +07:00 [DBG] [2025-05-28 17:56:18.531] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 7
2025-05-28 17:56:18.534 +07:00 [DBG] [2025-05-28 17:56:18.534] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 7
2025-05-28 17:56:18.565 +07:00 [DBG] [2025-05-28 17:56:18.565] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 7
2025-05-28 17:56:18.565 +07:00 [DBG] [2025-05-28 17:56:18.565] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:56:18.565 +07:00 [INF] [2025-05-28 17:56:18.565] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:56:18.566 +07:00 [INF] [2025-05-28 17:56:18.566] [INFO]: 📊 nhatrang345: 1 phỏm, 1 cạ, 5 rác
2025-05-28 17:56:18.566 +07:00 [INF] [2025-05-28 17:56:18.566] [INFO]: 🔍 Solo analysis: 10 tổng, 5 bảo vệ, 5 có thể đánh, 25 ẩn
2025-05-28 17:56:18.566 +07:00 [INF] [2025-05-28 17:56:18.566] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([8♥]) - Bảo vệ 5 lá - Ẩn: 25
2025-05-28 17:56:18.566 +07:00 [INF] [2025-05-28 17:56:18.566] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [8♥] tại card/31.png
2025-05-28 17:56:18.566 +07:00 [INF] [2025-05-28 17:56:18.566] [INFO]: ✅ Đánh dấu nhatrang345 đã rút bài
2025-05-28 17:56:18.566 +07:00 [INF] [2025-05-28 17:56:18.566] [INFO]: 🎯 Trigger gợi ý cho nhatrang345 sau khi rút/ăn bài
2025-05-28 17:56:18.566 +07:00 [DBG] [2025-05-28 17:56:18.566] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:56:18.566 +07:00 [INF] [2025-05-28 17:56:18.566] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:56:18.566 +07:00 [INF] [2025-05-28 17:56:18.566] [INFO]: 📊 nhatrang345: 1 phỏm, 1 cạ, 5 rác
2025-05-28 17:56:18.566 +07:00 [INF] [2025-05-28 17:56:18.566] [INFO]: 🔍 Solo analysis: 10 tổng, 5 bảo vệ, 5 có thể đánh, 25 ẩn
2025-05-28 17:56:18.566 +07:00 [INF] [2025-05-28 17:56:18.566] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([8♥]) - Bảo vệ 5 lá - Ẩn: 25
2025-05-28 17:56:18.566 +07:00 [INF] [2025-05-28 17:56:18.566] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [8♥] tại card/31.png
2025-05-28 17:56:19.873 +07:00 [INF] [2025-05-28 17:56:19.873] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,40]
2025-05-28 17:56:19.873 +07:00 [INF] [2025-05-28 17:56:19.873] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,40]
2025-05-28 17:56:19.877 +07:00 [INF] [2025-05-28 17:56:19.877] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,40]
2025-05-28 17:56:20.377 +07:00 [DBG] [2025-05-28 17:56:20.377] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:56:20.377 +07:00 [INF] [2025-05-28 17:56:20.377] [INFO]: 🎮 Đầu ván - Xử lý CardsUpdated cho tất cả users
2025-05-28 17:56:20.524 +07:00 [INF] [2025-05-28 17:56:20.524] [INFO]: Chế độ chơi: Team
2025-05-28 17:56:20.524 +07:00 [INF] [2025-05-28 17:56:20.524] [INFO]: Xử lý chế độ Team
2025-05-28 17:56:20.524 +07:00 [DBG] [2025-05-28 17:56:20.524] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:20.524 +07:00 [INF] [2025-05-28 17:56:20.524] [INFO]: Tìm thấy phỏm cho nhatrang345: [2♠, 2♣, 2♥]
2025-05-28 17:56:20.524 +07:00 [ERR] [2025-05-28 17:56:20.524] [ERROR]: Không đủ người chơi để tối ưu hóa team: 1 người chơi
2025-05-28 17:56:20.524 +07:00 [INF] [2025-05-28 17:56:20.524] [INFO]: nhatrang345 đi trước (10 lá bài)
2025-05-28 17:56:20.524 +07:00 [DBG] [2025-05-28 17:56:20.524] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:56:20.524 +07:00 [INF] [2025-05-28 17:56:20.524] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:56:20.524 +07:00 [INF] [2025-05-28 17:56:20.524] [INFO]: 📊 nhatrang345: 1 phỏm, 1 cạ, 5 rác
2025-05-28 17:56:20.524 +07:00 [INF] [2025-05-28 17:56:20.524] [INFO]: 🔍 Solo analysis: 10 tổng, 5 bảo vệ, 5 có thể đánh, 25 ẩn
2025-05-28 17:56:20.524 +07:00 [INF] [2025-05-28 17:56:20.524] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([8♥]) - Bảo vệ 5 lá - Ẩn: 25
2025-05-28 17:56:20.525 +07:00 [INF] [2025-05-28 17:56:20.525] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [8♥] tại card/31.png
2025-05-28 17:56:20.556 +07:00 [INF] [2025-05-28 17:56:20.556] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:56:20.556 +07:00 [DBG] [2025-05-28 17:56:20.556] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:56:20.556 +07:00 [INF] [2025-05-28 17:56:20.556] [INFO]: 🔄 UPDATE nhatrang345: đã rút/ăn bài và thay đổi số bài (12→10)
2025-05-28 17:56:20.556 +07:00 [DBG] [2025-05-28 17:56:20.556] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 10
2025-05-28 17:56:20.583 +07:00 [DBG] [2025-05-28 17:56:20.583] [DEBUG]: Tìm thấy bộ: 4, 5, 7
2025-05-28 17:56:20.609 +07:00 [DBG] [2025-05-28 17:56:20.609] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [2♠, 2♣, 2♥, 5♣, 7♣, A♠, A♣, 3♦, 4♥, 8♥]
2025-05-28 17:56:20.609 +07:00 [DBG] [2025-05-28 17:56:20.609] [DEBUG]: Số phỏm tìm được: 1, số lá rác: 7
2025-05-28 17:56:20.612 +07:00 [DBG] [2025-05-28 17:56:20.612] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 3, Rác: 7
2025-05-28 17:56:20.643 +07:00 [DBG] [2025-05-28 17:56:20.643] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 3, Số bài rác: 7
2025-05-28 17:56:20.643 +07:00 [DBG] [2025-05-28 17:56:20.643] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:56:20.643 +07:00 [INF] [2025-05-28 17:56:20.643] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:56:20.643 +07:00 [INF] [2025-05-28 17:56:20.643] [INFO]: 📊 nhatrang345: 1 phỏm, 1 cạ, 5 rác
2025-05-28 17:56:20.643 +07:00 [INF] [2025-05-28 17:56:20.643] [INFO]: 🔍 Solo analysis: 10 tổng, 5 bảo vệ, 5 có thể đánh, 25 ẩn
2025-05-28 17:56:20.643 +07:00 [INF] [2025-05-28 17:56:20.643] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([8♥]) - Bảo vệ 5 lá - Ẩn: 25
2025-05-28 17:56:20.644 +07:00 [INF] [2025-05-28 17:56:20.644] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [8♥] tại card/31.png
2025-05-28 17:56:20.644 +07:00 [INF] [2025-05-28 17:56:20.644] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 17 played cards
2025-05-28 17:56:20.644 +07:00 [INF] [2025-05-28 17:56:20.644] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠],[K♦],[Q♣],[7♥],[9♣],[3♥],[10♣],[Q♥]
2025-05-28 17:56:20.645 +07:00 [INF] [2025-05-28 17:56:20.645] [INFO]: Hiển thị 17 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:56:20.646 +07:00 [DBG] [2025-05-28 17:56:20.646] [DEBUG]: Hiển thị lá bài đã đánh 11 tại vị trí 0
2025-05-28 17:56:20.646 +07:00 [DBG] [2025-05-28 17:56:20.646] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 1
2025-05-28 17:56:20.647 +07:00 [DBG] [2025-05-28 17:56:20.647] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 2
2025-05-28 17:56:20.647 +07:00 [DBG] [2025-05-28 17:56:20.647] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 3
2025-05-28 17:56:20.648 +07:00 [DBG] [2025-05-28 17:56:20.648] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 4
2025-05-28 17:56:20.648 +07:00 [DBG] [2025-05-28 17:56:20.648] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 5
2025-05-28 17:56:20.649 +07:00 [DBG] [2025-05-28 17:56:20.649] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 6
2025-05-28 17:56:20.649 +07:00 [DBG] [2025-05-28 17:56:20.649] [DEBUG]: Hiển thị lá bài đã đánh 37 tại vị trí 7
2025-05-28 17:56:20.650 +07:00 [DBG] [2025-05-28 17:56:20.650] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 8
2025-05-28 17:56:20.650 +07:00 [DBG] [2025-05-28 17:56:20.650] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 9
2025-05-28 17:56:20.651 +07:00 [DBG] [2025-05-28 17:56:20.651] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 10
2025-05-28 17:56:20.651 +07:00 [DBG] [2025-05-28 17:56:20.651] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 11
2025-05-28 17:56:20.652 +07:00 [DBG] [2025-05-28 17:56:20.652] [DEBUG]: Hiển thị lá bài đã đánh 45 tại vị trí 12
2025-05-28 17:56:20.652 +07:00 [DBG] [2025-05-28 17:56:20.652] [DEBUG]: Hiển thị lá bài đã đánh 47 tại vị trí 13
2025-05-28 17:56:20.653 +07:00 [DBG] [2025-05-28 17:56:20.653] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 14
2025-05-28 17:56:20.653 +07:00 [DBG] [2025-05-28 17:56:20.653] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 15
2025-05-28 17:56:20.653 +07:00 [DBG] [2025-05-28 17:56:20.653] [DEBUG]: Hiển thị lá bài đã đánh 50 tại vị trí 16
2025-05-28 17:56:24.872 +07:00 [INF] [2025-05-28 17:56:24.872] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,41]
2025-05-28 17:56:24.872 +07:00 [INF] [2025-05-28 17:56:24.872] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,41]
2025-05-28 17:56:24.872 +07:00 [INF] [2025-05-28 17:56:24.872] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,41]
2025-05-28 17:56:25.139 +07:00 [INF] [2025-05-28 17:56:25.139] [INFO] [nhatrang345]: Updated cards for nhatrang345: [5♣, 7♣, A♠, A♣, 4♥]
2025-05-28 17:56:25.140 +07:00 [INF] [2025-05-28 17:56:25.140] [INFO]: Updated opponent known cards: [8♥]
2025-05-28 17:56:25.140 +07:00 [INF] [2025-05-28 17:56:25.140] [INFO] [nhatrang345]: Next turn: Minhheuu (UID: 22_341334)
2025-05-28 17:56:25.140 +07:00 [DBG] [2025-05-28 17:56:25.140] [DEBUG]: Updated last played card: [8♥]
2025-05-28 17:56:25.140 +07:00 [INF] [2025-05-28 17:56:25.140] [INFO] [nhatrang345]: Player nhatrang345 played card 31, next: Minhheuu
2025-05-28 17:56:25.140 +07:00 [DBG] [2025-05-28 17:56:25.140] [DEBUG]: Nhận được sự kiện CardsUpdated
2025-05-28 17:56:25.140 +07:00 [DBG] [2025-05-28 17:56:25.140] [DEBUG]: ⏭️ Giữa ván - Bỏ qua CardsUpdated (sẽ xử lý qua UpdateSpecificUser)
2025-05-28 17:56:25.140 +07:00 [INF] [2025-05-28 17:56:25.140] [INFO]: Nhận được tin nhắn đánh bài
2025-05-28 17:56:25.140 +07:00 [DBG] [2025-05-28 17:56:25.140] [DEBUG]: 🔄 ProcessPlayCardReceived: Kiểm tra 1 users
2025-05-28 17:56:25.140 +07:00 [INF] [2025-05-28 17:56:25.140] [INFO]: 🔄 UPDATE nhatrang345: đã rút/ăn bài và thay đổi số bài (11→5)
2025-05-28 17:56:25.140 +07:00 [DBG] [2025-05-28 17:56:25.140] [DEBUG]: Cập nhật bài cho nhatrang345 tại panel 0, số lá bài: 5
2025-05-28 17:56:25.166 +07:00 [ERR] [2025-05-28 17:56:25.166] [ERROR]: Danh sách bài đầu vào không hợp lệ cho nhatrang345!
2025-05-28 17:56:25.192 +07:00 [DBG] [2025-05-28 17:56:25.192] [DEBUG]: Hiển thị bài cho nhatrang345 (panel 0): [5♣, 7♣, A♠, A♣, 4♥]
2025-05-28 17:56:25.192 +07:00 [DBG] [2025-05-28 17:56:25.192] [DEBUG]: Số phỏm tìm được: 0, số lá rác: 0
2025-05-28 17:56:25.194 +07:00 [DBG] [2025-05-28 17:56:25.194] [DEBUG]: Tổng số lá bài hiển thị cho nhatrang345: Phỏm: 0, Rác: 0
2025-05-28 17:56:25.220 +07:00 [DBG] [2025-05-28 17:56:25.220] [DEBUG]: Hiển thị bài cho panel 0: Số phỏm: 0, Số bài rác: 0
2025-05-28 17:56:25.220 +07:00 [DBG] [2025-05-28 17:56:25.220] [DEBUG]: 🎯 UpdateTurnStatus cho nhatrang345: isTurn=False, hasDrawnCard=True, hasEatenCard=False, shouldShowSuggestion=True
2025-05-28 17:56:25.220 +07:00 [INF] [2025-05-28 17:56:25.220] [INFO]: 🧠 Smart analysis cho nhatrang345: Solo mode
2025-05-28 17:56:25.220 +07:00 [INF] [2025-05-28 17:56:25.220] [INFO]: 📊 nhatrang345: 0 phỏm, 1 cạ, 3 rác
2025-05-28 17:56:25.220 +07:00 [INF] [2025-05-28 17:56:25.220] [INFO]: 🔍 Solo analysis: 5 tổng, 2 bảo vệ, 3 có thể đánh, 29 ẩn
2025-05-28 17:56:25.220 +07:00 [INF] [2025-05-28 17:56:25.220] [INFO]: 🧠 Gợi ý thông minh cho nhatrang345: ⚔️ Solo: Đánh lá rác cao ([7♣]) - Bảo vệ 2 lá - Ẩn: 29
2025-05-28 17:56:25.221 +07:00 [INF] [2025-05-28 17:56:25.221] [INFO]: ✅ Hiển thị lá bài gợi ý cho nhatrang345: [7♣] tại card/25.png
2025-05-28 17:56:25.221 +07:00 [INF] [2025-05-28 17:56:25.221] [INFO]: 🔄 UpdateOpponentPlayedCards called - Found 18 played cards
2025-05-28 17:56:25.221 +07:00 [INF] [2025-05-28 17:56:25.221] [INFO]: 🎴 Displaying played cards: [Q♠],[K♠],[6♥],[K♣],[J♥],[10♥],[J♣],[9♥],[9♦],[6♠],[K♦],[Q♣],[7♥],[9♣],[3♥],[10♣],[Q♥],[8♥]
2025-05-28 17:56:25.223 +07:00 [INF] [2025-05-28 17:56:25.223] [INFO]: Hiển thị 18 lá bài đã đánh (đã sắp xếp)
2025-05-28 17:56:25.223 +07:00 [DBG] [2025-05-28 17:56:25.223] [DEBUG]: Hiển thị lá bài đã đánh 11 tại vị trí 0
2025-05-28 17:56:25.223 +07:00 [DBG] [2025-05-28 17:56:25.223] [DEBUG]: Hiển thị lá bài đã đánh 20 tại vị trí 1
2025-05-28 17:56:25.224 +07:00 [DBG] [2025-05-28 17:56:25.224] [DEBUG]: Hiển thị lá bài đã đánh 23 tại vị trí 2
2025-05-28 17:56:25.224 +07:00 [DBG] [2025-05-28 17:56:25.224] [DEBUG]: Hiển thị lá bài đã đánh 27 tại vị trí 3
2025-05-28 17:56:25.225 +07:00 [DBG] [2025-05-28 17:56:25.225] [DEBUG]: Hiển thị lá bài đã đánh 31 tại vị trí 4
2025-05-28 17:56:25.225 +07:00 [DBG] [2025-05-28 17:56:25.225] [DEBUG]: Hiển thị lá bài đã đánh 33 tại vị trí 5
2025-05-28 17:56:25.226 +07:00 [DBG] [2025-05-28 17:56:25.226] [DEBUG]: Hiển thị lá bài đã đánh 34 tại vị trí 6
2025-05-28 17:56:25.226 +07:00 [DBG] [2025-05-28 17:56:25.226] [DEBUG]: Hiển thị lá bài đã đánh 35 tại vị trí 7
2025-05-28 17:56:25.227 +07:00 [DBG] [2025-05-28 17:56:25.227] [DEBUG]: Hiển thị lá bài đã đánh 37 tại vị trí 8
2025-05-28 17:56:25.227 +07:00 [DBG] [2025-05-28 17:56:25.227] [DEBUG]: Hiển thị lá bài đã đánh 39 tại vị trí 9
2025-05-28 17:56:25.228 +07:00 [DBG] [2025-05-28 17:56:25.228] [DEBUG]: Hiển thị lá bài đã đánh 41 tại vị trí 10
2025-05-28 17:56:25.228 +07:00 [DBG] [2025-05-28 17:56:25.228] [DEBUG]: Hiển thị lá bài đã đánh 43 tại vị trí 11
2025-05-28 17:56:25.229 +07:00 [DBG] [2025-05-28 17:56:25.229] [DEBUG]: Hiển thị lá bài đã đánh 44 tại vị trí 12
2025-05-28 17:56:25.229 +07:00 [DBG] [2025-05-28 17:56:25.229] [DEBUG]: Hiển thị lá bài đã đánh 45 tại vị trí 13
2025-05-28 17:56:25.230 +07:00 [DBG] [2025-05-28 17:56:25.230] [DEBUG]: Hiển thị lá bài đã đánh 47 tại vị trí 14
2025-05-28 17:56:25.230 +07:00 [DBG] [2025-05-28 17:56:25.230] [DEBUG]: Hiển thị lá bài đã đánh 48 tại vị trí 15
2025-05-28 17:56:25.231 +07:00 [DBG] [2025-05-28 17:56:25.231] [DEBUG]: Hiển thị lá bài đã đánh 49 tại vị trí 16
2025-05-28 17:56:25.231 +07:00 [DBG] [2025-05-28 17:56:25.231] [DEBUG]: Hiển thị lá bài đã đánh 50 tại vị trí 17
2025-05-28 17:56:26.825 +07:00 [INF] [2025-05-28 17:56:26.825] [INFO] [nhatrang345]: Game result for nhatrang345: Lose: -1937 (Points: 0), Cards: [5♣, 7♣, A♠, A♣, 4♥]
2025-05-28 17:56:26.825 +07:00 [INF] [2025-05-28 17:56:26.825] [INFO] [nhatrang345]: Winner of this round: UID 1_229699688
2025-05-28 17:56:26.911 +07:00 [DBG] [2025-05-28 17:56:26.911] [DEBUG]: ✅ Đã xóa HOÀN TOÀN tất cả các lá bài đã đánh và giải phóng memory
2025-05-28 17:56:26.911 +07:00 [INF] [2025-05-28 17:56:26.911] [INFO]: Cleared opponent played cards after Phom game ended
2025-05-28 17:56:26.911 +07:00 [DBG] [2025-05-28 17:56:26.911] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:56:26.911 +07:00 [DBG] [2025-05-28 17:56:26.911] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 17:56:26.912 +07:00 [DBG] [2025-05-28 17:56:26.912] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 17:56:27.057 +07:00 [DBG] [2025-05-28 17:56:27.057] [DEBUG]: ✅ Đã xóa HOÀN TOÀN tất cả các lá bài đã đánh và giải phóng memory
2025-05-28 17:56:27.057 +07:00 [INF] [2025-05-28 17:56:27.057] [INFO]: ✅ Đã reset played cards (18 lá), drawn cards (5 lá) và last played card
2025-05-28 17:56:27.068 +07:00 [INF] [2025-05-28 17:56:27.068] [INFO]: ✅ Đã reset hoàn toàn trạng thái PhomSuggestionForm
2025-05-28 17:56:27.068 +07:00 [INF] [2025-05-28 17:56:27.068] [INFO]: ✅ Reset PhomSuggestionForm state completely
2025-05-28 17:56:27.068 +07:00 [INF] [2025-05-28 17:56:27.068] [INFO]: Reset Phom game data
2025-05-28 17:56:27.068 +07:00 [INF] [2025-05-28 17:56:27.068] [INFO]: Nhận được thông báo kết thúc ván
2025-05-28 17:56:27.068 +07:00 [INF] [2025-05-28 17:56:27.068] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 17:56:27.068 +07:00 [DBG] [2025-05-28 17:56:27.068] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 17:56:27.068 +07:00 [DBG] [2025-05-28 17:56:27.068] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:56:27.068 +07:00 [INF] [2025-05-28 17:56:27.068] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 17:56:27.068 +07:00 [DBG] [2025-05-28 17:56:27.068] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 17:56:27.068 +07:00 [DBG] [2025-05-28 17:56:27.068] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:56:27.068 +07:00 [INF] [2025-05-28 17:56:27.068] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 17:56:27.068 +07:00 [DBG] [2025-05-28 17:56:27.068] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 17:56:27.068 +07:00 [DBG] [2025-05-28 17:56:27.068] [DEBUG]: Số cột trong hàng: 5
2025-05-28 17:56:27.146 +07:00 [INF] [2025-05-28 17:56:27.146] [INFO]: Tải danh sách user thành công
2025-05-28 17:56:27.292 +07:00 [DBG] [2025-05-28 17:56:27.292] [DEBUG]: ✅ Đã xóa HOÀN TOÀN tất cả các lá bài đã đánh và giải phóng memory
2025-05-28 17:56:27.292 +07:00 [INF] [2025-05-28 17:56:27.292] [INFO]: ✅ Đã reset played cards (0 lá), drawn cards (0 lá) và last played card
2025-05-28 17:56:27.303 +07:00 [INF] [2025-05-28 17:56:27.303] [INFO]: ✅ Đã reset hoàn toàn trạng thái PhomSuggestionForm
2025-05-28 17:56:27.323 +07:00 [INF] [2025-05-28 17:56:27.323] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.41 giây
2025-05-28 17:56:29.876 +07:00 [INF] [2025-05-28 17:56:29.876] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,42]
2025-05-28 17:56:29.876 +07:00 [INF] [2025-05-28 17:56:29.876] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,42]
2025-05-28 17:56:29.882 +07:00 [INF] [2025-05-28 17:56:29.882] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,42]
2025-05-28 17:56:34.085 +07:00 [INF] [2025-05-28 17:56:34.085] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 17:56:34.872 +07:00 [INF] [2025-05-28 17:56:34.872] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,43]
2025-05-28 17:56:34.872 +07:00 [INF] [2025-05-28 17:56:34.872] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,43]
2025-05-28 17:56:34.874 +07:00 [INF] [2025-05-28 17:56:34.873] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,43]
2025-05-28 17:56:39.873 +07:00 [INF] [2025-05-28 17:56:39.873] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,44]
2025-05-28 17:56:39.873 +07:00 [INF] [2025-05-28 17:56:39.873] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,44]
2025-05-28 17:56:39.879 +07:00 [INF] [2025-05-28 17:56:39.879] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,44]
2025-05-28 17:56:44.873 +07:00 [INF] [2025-05-28 17:56:44.873] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,45]
2025-05-28 17:56:44.873 +07:00 [INF] [2025-05-28 17:56:44.873] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,45]
2025-05-28 17:56:44.874 +07:00 [INF] [2025-05-28 17:56:44.874] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,45]
2025-05-28 17:56:55.469 +07:00 [DBG] [2025-05-28 17:56:55.469] [DEBUG]: Bắt đầu đóng Form1
2025-05-28 17:56:55.480 +07:00 [INF] [2025-05-28 17:56:55.480] [INFO]: Đã đóng driver cho OpenQA.Selenium.Chrome.ChromeDriver
2025-05-28 17:56:55.486 +07:00 [INF] [2025-05-28 17:56:55.486] [INFO]: MauBinhCardManager disposed
2025-05-28 17:56:55.486 +07:00 [INF] [2025-05-28 17:56:55.486] [INFO]: Đã đóng Form1 thành công
2025-05-28 17:56:55.494 +07:00 [INF] Application started successfully.
2025-05-28 18:12:17.072 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 18:13:13.842 +07:00 [INF] User cancelled game selection, exiting application.
2025-05-28 18:15:51.736 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 18:15:57.617 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 18:15:57.621 +07:00 [INF] Form1 constructor started.
2025-05-28 18:15:57.641 +07:00 [DBG] [2025-05-28 18:15:57.641] [DEBUG]: Gọi InitializeComponent
2025-05-28 18:15:57.651 +07:00 [INF] [2025-05-28 18:15:57.651] [INFO]: Khởi tạo UIManager thành công
2025-05-28 18:15:57.652 +07:00 [DBG] [2025-05-28 18:15:57.652] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 18:15:57.654 +07:00 [INF] [2025-05-28 18:15:57.654] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 18:15:57.654 +07:00 [DBG] [2025-05-28 18:15:57.654] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 18:15:57.654 +07:00 [INF] [2025-05-28 18:15:57.654] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 18:15:57.655 +07:00 [INF] [2025-05-28 18:15:57.655] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 18:15:57.672 +07:00 [DBG] [2025-05-28 18:15:57.672] [DEBUG]: Bắt đầu OnLoad
2025-05-28 18:15:57.673 +07:00 [DBG] [2025-05-28 18:15:57.673] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 18:15:57.689 +07:00 [INF] [2025-05-28 18:15:57.689] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 18:15:57.689 +07:00 [INF] [2025-05-28 18:15:57.689] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 18:15:57.707 +07:00 [INF] [2025-05-28 18:15:57.707] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 18:15:57.708 +07:00 [INF] [2025-05-28 18:15:57.708] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 18:15:57.709 +07:00 [INF] [2025-05-28 18:15:57.709] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 18:15:57.709 +07:00 [INF] [2025-05-28 18:15:57.709] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 18:15:57.709 +07:00 [INF] [2025-05-28 18:15:57.709] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 18:15:57.709 +07:00 [INF] [2025-05-28 18:15:57.709] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 18:15:57.710 +07:00 [INF] InitializeAsync started.
2025-05-28 18:15:57.710 +07:00 [INF] [2025-05-28 18:15:57.710] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 18:15:57.714 +07:00 [DBG] [2025-05-28 18:15:57.714] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 18:15:57.716 +07:00 [DBG] [2025-05-28 18:15:57.716] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 18:15:57.717 +07:00 [DBG] [2025-05-28 18:15:57.717] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 18:15:57.717 +07:00 [DBG] [2025-05-28 18:15:57.717] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 18:15:57.725 +07:00 [DBG] [2025-05-28 18:15:57.725] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 18:15:57.725 +07:00 [INF] [2025-05-28 18:15:57.725] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 18:15:57.725 +07:00 [DBG] [2025-05-28 18:15:57.725] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 18:15:57.727 +07:00 [DBG] [2025-05-28 18:15:57.727] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:15:57.727 +07:00 [DBG] [2025-05-28 18:15:57.727] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:15:57.727 +07:00 [INF] [2025-05-28 18:15:57.727] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:15:57.727 +07:00 [DBG] [2025-05-28 18:15:57.727] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 18:15:57.727 +07:00 [DBG] [2025-05-28 18:15:57.727] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:15:57.727 +07:00 [DBG] [2025-05-28 18:15:57.727] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:15:57.727 +07:00 [INF] [2025-05-28 18:15:57.727] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:15:57.727 +07:00 [DBG] [2025-05-28 18:15:57.727] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 18:15:57.727 +07:00 [DBG] [2025-05-28 18:15:57.727] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:15:57.762 +07:00 [INF] [2025-05-28 18:15:57.762] [INFO]: Tải danh sách user thành công
2025-05-28 18:15:57.770 +07:00 [INF] [2025-05-28 18:15:57.770] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.05 giây
2025-05-28 18:15:57.770 +07:00 [DBG] [2025-05-28 18:15:57.770] [DEBUG]: OnLoad hoàn tất
2025-05-28 18:15:59.847 +07:00 [INF] [2025-05-28 18:15:59.847] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11014: Đang chạy
2025-05-28 18:15:59.853 +07:00 [INF] [2025-05-28 18:15:59.853] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 18:15:59.854 +07:00 [INF] [2025-05-28 18:15:59.854] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 18:15:59.857 +07:00 [DBG] [2025-05-28 18:15:59.857] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles: Thành công
2025-05-28 18:15:59.901 +07:00 [INF] [2025-05-28 18:15:59.901] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 45ms
2025-05-28 18:16:00.286 +07:00 [DBG] [2025-05-28 18:16:00.286] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 18:16:00.287 +07:00 [INF] [2025-05-28 18:16:00.287] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:50092. Thời gian: 385ms
2025-05-28 18:16:00.287 +07:00 [DBG] [2025-05-28 18:16:00.287] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 18:16:00.287 +07:00 [DBG] [2025-05-28 18:16:00.287] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 18:16:00.287 +07:00 [INF] [2025-05-28 18:16:00.287] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 432ms
2025-05-28 18:16:00.287 +07:00 [DBG] [2025-05-28 18:16:00.287] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 18:16:00.287 +07:00 [DBG] [2025-05-28 18:16:00.287] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 18:16:01.514 +07:00 [INF] [2025-05-28 18:16:01.514] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 18:16:01.958 +07:00 [INF] [2025-05-28 18:16:01.958] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 18:16:02.079 +07:00 [INF] [2025-05-28 18:16:02.079] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 18:16:02.084 +07:00 [DBG] [2025-05-28 18:16:02.083] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 18:16:02.087 +07:00 [INF] [2025-05-28 18:16:02.087] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 18:16:02.087 +07:00 [INF] [2025-05-28 18:16:02.087] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 18:16:02.090 +07:00 [INF] [2025-05-28 18:16:02.090] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 18:16:02.090 +07:00 [INF] [2025-05-28 18:16:02.090] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 18:16:02.090 +07:00 [DBG] [2025-05-28 18:16:02.090] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 18:16:02.091 +07:00 [DBG] [2025-05-28 18:16:02.091] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:02.091 +07:00 [INF] [2025-05-28 18:16:02.091] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:16:02.091 +07:00 [DBG] [2025-05-28 18:16:02.091] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 18:16:02.091 +07:00 [DBG] [2025-05-28 18:16:02.091] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:02.091 +07:00 [INF] [2025-05-28 18:16:02.091] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:16:02.091 +07:00 [DBG] [2025-05-28 18:16:02.091] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 18:16:02.091 +07:00 [DBG] [2025-05-28 18:16:02.091] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:02.117 +07:00 [INF] [2025-05-28 18:16:02.117] [INFO]: Tải danh sách user thành công
2025-05-28 18:16:02.118 +07:00 [INF] [2025-05-28 18:16:02.118] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 18:16:02.118 +07:00 [DBG] [2025-05-28 18:16:02.118] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 18:16:02.118 +07:00 [DBG] [2025-05-28 18:16:02.118] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:02.118 +07:00 [INF] [2025-05-28 18:16:02.118] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:16:02.118 +07:00 [DBG] [2025-05-28 18:16:02.118] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 18:16:02.118 +07:00 [DBG] [2025-05-28 18:16:02.118] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:02.118 +07:00 [INF] [2025-05-28 18:16:02.118] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:16:02.118 +07:00 [DBG] [2025-05-28 18:16:02.118] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 18:16:02.118 +07:00 [DBG] [2025-05-28 18:16:02.118] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:02.142 +07:00 [INF] [2025-05-28 18:16:02.142] [INFO]: Tải danh sách user thành công
2025-05-28 18:16:02.150 +07:00 [INF] [2025-05-28 18:16:02.150] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.86 giây
2025-05-28 18:16:02.157 +07:00 [INF] [2025-05-28 18:16:02.157] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.87 giây
2025-05-28 18:16:04.101 +07:00 [INF] [2025-05-28 18:16:04.101] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 18:16:05.097 +07:00 [INF] [2025-05-28 18:16:05.097] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 18:16:05.097 +07:00 [DBG] [2025-05-28 18:16:05.097] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 18:16:05.097 +07:00 [DBG] [2025-05-28 18:16:05.097] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 18:16:05.097 +07:00 [INF] [2025-05-28 18:16:05.097] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 18:16:05.097 +07:00 [DBG] [2025-05-28 18:16:05.097] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 18:16:05.097 +07:00 [DBG] [2025-05-28 18:16:05.097] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 18:16:05.098 +07:00 [INF] [2025-05-28 18:16:05.098] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 18:16:05.098 +07:00 [DBG] [2025-05-28 18:16:05.098] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 18:16:05.098 +07:00 [DBG] [2025-05-28 18:16:05.098] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:05.098 +07:00 [INF] [2025-05-28 18:16:05.098] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:16:05.098 +07:00 [DBG] [2025-05-28 18:16:05.098] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 18:16:05.098 +07:00 [DBG] [2025-05-28 18:16:05.098] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:05.098 +07:00 [INF] [2025-05-28 18:16:05.098] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:16:05.098 +07:00 [DBG] [2025-05-28 18:16:05.098] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 18:16:05.098 +07:00 [DBG] [2025-05-28 18:16:05.098] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:05.160 +07:00 [INF] [2025-05-28 18:16:05.160] [INFO]: Tải danh sách user thành công
2025-05-28 18:16:05.160 +07:00 [INF] [2025-05-28 18:16:05.160] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 18:16:05.160 +07:00 [DBG] [2025-05-28 18:16:05.160] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 18:16:05.161 +07:00 [DBG] [2025-05-28 18:16:05.161] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:05.161 +07:00 [INF] [2025-05-28 18:16:05.161] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:16:05.161 +07:00 [DBG] [2025-05-28 18:16:05.161] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 18:16:05.161 +07:00 [DBG] [2025-05-28 18:16:05.161] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:05.161 +07:00 [INF] [2025-05-28 18:16:05.161] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:16:05.161 +07:00 [DBG] [2025-05-28 18:16:05.161] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 18:16:05.161 +07:00 [DBG] [2025-05-28 18:16:05.161] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:16:05.222 +07:00 [INF] [2025-05-28 18:16:05.222] [INFO]: Tải danh sách user thành công
2025-05-28 18:16:05.238 +07:00 [INF] [2025-05-28 18:16:05.238] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.14 giây
2025-05-28 18:16:05.252 +07:00 [INF] [2025-05-28 18:16:05.252] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.15 giây
2025-05-28 18:16:06.778 +07:00 [DBG] [2025-05-28 18:16:06.778] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 18:16:06.779 +07:00 [DBG] [2025-05-28 18:16:06.779] [DEBUG] [nhatrang345]: ℹ️ Không có điều kiện đặc biệt cho nhatrang345, giữ nguyên trong phòng (sit: 1, số người: 4)
2025-05-28 18:16:06.780 +07:00 [WRN] [2025-05-28 18:16:06.779] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 18:16:08.066 +07:00 [INF] [2025-05-28 18:16:08.066] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 18:16:08.067 +07:00 [INF] [2025-05-28 18:16:08.067] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 18:16:08.068 +07:00 [INF] [2025-05-28 18:16:08.068] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 18:16:08.093 +07:00 [DBG] [2025-05-28 18:16:08.093] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Mậu Binh
2025-05-28 18:16:08.099 +07:00 [INF] [2025-05-28 18:16:08.099] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 18:16:08.105 +07:00 [DBG] [2025-05-28 18:16:08.105] [DEBUG]: Đã khởi tạo giao diện với 4 panel
2025-05-28 18:16:08.105 +07:00 [DBG] [2025-05-28 18:16:08.105] [DEBUG]: Đã khởi tạo MauBinhSuggestionForm
2025-05-28 18:16:10.193 +07:00 [DBG] [2025-05-28 18:16:10.193] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 600 (Mau Binh cards)
2025-05-28 18:16:10.195 +07:00 [INF] [2025-05-28 18:16:10.195] [INFO]: Saved Mau Binh cards for nhatrang345: [Q♣, 2♣, A♥, 10♠, 3♠, J♦, K♣, Q♥, 3♦, 2♥, 9♦, 5♠, 3♣]
2025-05-28 18:16:12.119 +07:00 [DBG] [2025-05-28 18:16:12.119] [DEBUG]: Bắt đầu quét bài, số user: 3
2025-05-28 18:16:12.125 +07:00 [INF] [2025-05-28 18:16:12.125] [INFO]: Updated Mau Binh cards for nhatrang345: [3♣, 5♠, 9♦, 2♥, 3♦, Q♥, K♣, J♦, 3♠, 10♠, A♥, 2♣, Q♣]
2025-05-28 18:16:12.125 +07:00 [INF] [2025-05-28 18:16:12.125] [INFO] [nhatrang345]: Đã quét bài cho nhatrang345 bằng script (MauBinhController): [3♣, 5♠, 9♦, 2♥, 3♦, Q♥, K♣, J♦, 3♠, 10♠, A♥, 2♣, Q♣]
2025-05-28 18:16:12.125 +07:00 [ERR] [2025-05-28 18:16:12.125] [ERROR]: Không tìm thấy driver cho phanthiet989
2025-05-28 18:16:12.125 +07:00 [ERR] [2025-05-28 18:16:12.125] [ERROR]: Không tìm thấy driver cho namdinhx852
2025-05-28 18:16:12.126 +07:00 [WRN] [2025-05-28 18:16:12.126] [WARNING]: Chỉ tìm thấy bài hợp lệ cho 1/3 user
2025-05-28 18:16:12.126 +07:00 [WRN] [2025-05-28 18:16:12.126] [WARNING]: Số user hợp lệ (1) không đủ 3 để tính bài đối thủ
2025-05-28 18:16:12.126 +07:00 [DBG] [2025-05-28 18:16:12.126] [DEBUG]: Bắt đầu UpdateCardDisplay, số user: 1
2025-05-28 18:16:12.126 +07:00 [DBG] [2025-05-28 18:16:12.126] [DEBUG]: Cập nhật bài cho user: nhatrang345, indexKey: user0
2025-05-28 18:16:12.133 +07:00 [INF] [2025-05-28 18:16:12.133] [INFO]: Quét bài thành công, số user: 1, tổng số lá: 13
2025-05-28 18:16:12.765 +07:00 [INF] [2025-05-28 18:16:12.765] [INFO]: 🚀 Tạo gợi ý với thuật toán OPTIMIZED cho 13 lá bài
2025-05-28 18:16:12.766 +07:00 [INF] [2025-05-28 18:16:12.766] [INFO]: 🚀 Bắt đầu thuật toán Mậu Binh mới theo yêu cầu
2025-05-28 18:16:12.766 +07:00 [INF] [2025-05-28 18:16:12.766] [INFO]: 🥇 Tạo gợi ý ưu tiên Chi 1 mạnh nhất
2025-05-28 18:16:12.816 +07:00 [ERR] [2025-05-28 18:16:12.816] [ERROR]: ❌ Lỗi phân tích Chi1: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 18:16:12.828 +07:00 [ERR] [2025-05-28 18:16:12.828] [ERROR]: ❌ Lỗi GenerateStrategy_Chi1First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 18:16:12.829 +07:00 [INF] [2025-05-28 18:16:12.829] [INFO]: ✅ Chi1 ưu tiên: 0/5 gợi ý hợp lệ
2025-05-28 18:16:12.829 +07:00 [INF] [2025-05-28 18:16:12.829] [INFO]: 🥈 Tạo gợi ý ưu tiên Chi 2 mạnh nhất
2025-05-28 18:16:12.839 +07:00 [ERR] [2025-05-28 18:16:12.839] [ERROR]: ❌ Lỗi GenerateStrategy_Chi2First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 18:16:12.839 +07:00 [INF] [2025-05-28 18:16:12.839] [INFO]: ✅ Chi2 ưu tiên: 0/5 gợi ý hợp lệ
2025-05-28 18:16:12.839 +07:00 [INF] [2025-05-28 18:16:12.839] [INFO]: 🥉 Tạo gợi ý ưu tiên Chi 3 mạnh nhất
2025-05-28 18:16:12.854 +07:00 [ERR] [2025-05-28 18:16:12.854] [ERROR]: ❌ Lỗi GenerateStrategy_Chi3First_Enhanced: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 18:16:12.854 +07:00 [INF] [2025-05-28 18:16:12.854] [INFO]: ✅ Chi3 ưu tiên: 0/5 gợi ý hợp lệ
2025-05-28 18:16:12.854 +07:00 [INF] [2025-05-28 18:16:12.854] [INFO]: ⚖️ Tạo gợi ý cân bằng
2025-05-28 18:16:12.863 +07:00 [ERR] [2025-05-28 18:16:12.863] [ERROR]: ❌ Lỗi phân tích Chi1: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 18:16:12.871 +07:00 [ERR] [2025-05-28 18:16:12.871] [ERROR]: ❌ Lỗi thuật toán mới: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 18:16:12.871 +07:00 [WRN] [2025-05-28 18:16:12.871] [WARNING]: 🔄 Fallback sang thuật toán tối ưu cũ
2025-05-28 18:16:12.888 +07:00 [ERR] [2025-05-28 18:16:12.888] [ERROR]: ❌ Lỗi thuật toán cũ: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 18:16:12.888 +07:00 [WRN] [2025-05-28 18:16:12.888] [WARNING]: 🔄 Fallback sang gợi ý đơn giản
2025-05-28 18:16:12.889 +07:00 [INF] [2025-05-28 18:16:12.889] [INFO]: ✅ Đã tạo gợi ý mặc định
2025-05-28 18:16:12.889 +07:00 [INF] [2025-05-28 18:16:12.889] [INFO] [nhatrang345]: Đã tạo 1 gợi ý cho nhatrang345 (indexKey: user0)
2025-05-28 18:16:12.889 +07:00 [INF] [2025-05-28 18:16:12.889] [INFO]: 📝 AddSuggestion for nhatrang345
2025-05-28 18:16:12.889 +07:00 [DBG] [2025-05-28 18:16:12.889] [DEBUG]: Cập nhật ListBox cho user0, số gợi ý: 1
2025-05-28 18:16:12.898 +07:00 [DBG] [2025-05-28 18:16:12.898] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 18:16:12.898 +07:00 [INF] [2025-05-28 18:16:12.898] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 18:16:12.898 +07:00 [DBG] [2025-05-28 18:16:12.898] [DEBUG]: Đã chọn gợi ý đầu tiên cho user0
2025-05-28 18:16:12.898 +07:00 [DBG] [2025-05-28 18:16:12.898] [DEBUG]: Không có gợi ý để hiển thị cho user1
2025-05-28 18:16:12.898 +07:00 [DBG] [2025-05-28 18:16:12.898] [DEBUG]: Không có gợi ý để hiển thị cho user2
2025-05-28 18:16:12.898 +07:00 [DBG] [2025-05-28 18:16:12.898] [DEBUG]: Không có gợi ý để hiển thị cho opponent
2025-05-28 18:16:12.898 +07:00 [DBG] [2025-05-28 18:16:12.898] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 18:16:12.898 +07:00 [INF] [2025-05-28 18:16:12.898] [INFO]: Đã cập nhật danh sách gợi ý
2025-05-28 18:16:13.066 +07:00 [INF] [2025-05-28 18:16:13.066] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 18:16:13.066 +07:00 [INF] [2025-05-28 18:16:13.066] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 18:16:13.066 +07:00 [INF] [2025-05-28 18:16:13.066] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 18:16:13.826 +07:00 [DBG] [2025-05-28 18:16:13.826] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 18:16:13.826 +07:00 [INF] [2025-05-28 18:16:13.826] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 18:16:14.893 +07:00 [INF] [2025-05-28 18:16:14.893] [INFO] [nhatrang345]: Bắt đầu xếp bài cho nhatrang345 theo gợi ý: [A♥, K♣, Q♣, Q♥, J♦, 10♠, 9♦, 5♠, 3♠, 3♣, 3♦, 2♣, 2♥]
2025-05-28 18:16:14.901 +07:00 [INF] [2025-05-28 18:16:14.901] [INFO]: Kết quả xếp bài cho nhatrang345: Đã xếp bài thành công và đang cố gắng cập nhật UI
2025-05-28 18:16:15.904 +07:00 [INF] [2025-05-28 18:16:15.904] [INFO]: Sắp xếp bài thành công cho nhatrang345: 3,49,45,47,42,36,34,16,8,9,10,5,7
2025-05-28 18:16:15.904 +07:00 [INF] [2025-05-28 18:16:15.904] [INFO] [nhatrang345]: Đã xếp bài cho nhatrang345 theo gợi ý: [A♥, K♣, Q♣, Q♥, J♦, 10♠, 9♦, 5♠, 3♠, 3♣, 3♦, 2♣, 2♥]
2025-05-28 18:16:18.067 +07:00 [INF] [2025-05-28 18:16:18.067] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 18:16:18.067 +07:00 [INF] [2025-05-28 18:16:18.067] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 18:16:18.073 +07:00 [INF] [2025-05-28 18:16:18.073] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 18:16:23.070 +07:00 [INF] [2025-05-28 18:16:23.070] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 18:16:23.070 +07:00 [INF] [2025-05-28 18:16:23.070] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 18:16:23.079 +07:00 [INF] [2025-05-28 18:16:23.079] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 18:16:25.852 +07:00 [DBG] [2025-05-28 18:16:25.852] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 18:16:25.852 +07:00 [WRN] [2025-05-28 18:16:25.852] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 18:16:26.498 +07:00 [DBG] [2025-05-28 18:16:26.498] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 18:16:26.498 +07:00 [WRN] [2025-05-28 18:16:26.498] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 18:16:28.936 +07:00 [DBG] [2025-05-28 18:16:28.936] [DEBUG]: Bắt đầu đóng Form1
2025-05-28 18:16:28.948 +07:00 [INF] [2025-05-28 18:16:28.948] [INFO]: Đã đóng driver cho OpenQA.Selenium.Chrome.ChromeDriver
2025-05-28 18:16:28.953 +07:00 [INF] [2025-05-28 18:16:28.953] [INFO]: MauBinhCardManager disposed
2025-05-28 18:16:28.953 +07:00 [INF] [2025-05-28 18:16:28.953] [INFO]: Đã đóng Form1 thành công
2025-05-28 18:16:28.961 +07:00 [INF] Application started successfully.
2025-05-28 18:39:57.687 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 18:40:01.240 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 18:40:01.243 +07:00 [INF] Form1 constructor started.
2025-05-28 18:40:01.264 +07:00 [DBG] [2025-05-28 18:40:01.263] [DEBUG]: Gọi InitializeComponent
2025-05-28 18:40:01.274 +07:00 [INF] [2025-05-28 18:40:01.274] [INFO]: Khởi tạo UIManager thành công
2025-05-28 18:40:01.275 +07:00 [DBG] [2025-05-28 18:40:01.275] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 18:40:01.277 +07:00 [INF] [2025-05-28 18:40:01.277] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 18:40:01.277 +07:00 [DBG] [2025-05-28 18:40:01.277] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 18:40:01.277 +07:00 [INF] [2025-05-28 18:40:01.277] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 18:40:01.278 +07:00 [INF] [2025-05-28 18:40:01.278] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 18:40:01.291 +07:00 [DBG] [2025-05-28 18:40:01.291] [DEBUG]: Bắt đầu OnLoad
2025-05-28 18:40:01.291 +07:00 [DBG] [2025-05-28 18:40:01.291] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 18:40:01.309 +07:00 [INF] [2025-05-28 18:40:01.309] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 18:40:01.309 +07:00 [INF] [2025-05-28 18:40:01.309] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 18:40:01.327 +07:00 [INF] [2025-05-28 18:40:01.327] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 18:40:01.328 +07:00 [INF] [2025-05-28 18:40:01.328] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 18:40:01.329 +07:00 [INF] [2025-05-28 18:40:01.329] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 18:40:01.329 +07:00 [INF] [2025-05-28 18:40:01.329] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 18:40:01.329 +07:00 [INF] [2025-05-28 18:40:01.329] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 18:40:01.329 +07:00 [INF] [2025-05-28 18:40:01.329] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 18:40:01.329 +07:00 [INF] InitializeAsync started.
2025-05-28 18:40:01.330 +07:00 [INF] [2025-05-28 18:40:01.330] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 18:40:01.334 +07:00 [DBG] [2025-05-28 18:40:01.334] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 18:40:01.337 +07:00 [DBG] [2025-05-28 18:40:01.337] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 18:40:01.338 +07:00 [DBG] [2025-05-28 18:40:01.338] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 18:40:01.338 +07:00 [DBG] [2025-05-28 18:40:01.338] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 18:40:01.346 +07:00 [DBG] [2025-05-28 18:40:01.346] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 18:40:01.346 +07:00 [INF] [2025-05-28 18:40:01.346] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 18:40:01.346 +07:00 [DBG] [2025-05-28 18:40:01.346] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 18:40:01.348 +07:00 [DBG] [2025-05-28 18:40:01.348] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:01.348 +07:00 [DBG] [2025-05-28 18:40:01.348] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:40:01.348 +07:00 [INF] [2025-05-28 18:40:01.348] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:40:01.348 +07:00 [DBG] [2025-05-28 18:40:01.348] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 18:40:01.348 +07:00 [DBG] [2025-05-28 18:40:01.348] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:01.348 +07:00 [DBG] [2025-05-28 18:40:01.348] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:40:01.348 +07:00 [INF] [2025-05-28 18:40:01.348] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:40:01.348 +07:00 [DBG] [2025-05-28 18:40:01.348] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 18:40:01.348 +07:00 [DBG] [2025-05-28 18:40:01.348] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:01.405 +07:00 [INF] [2025-05-28 18:40:01.405] [INFO]: Tải danh sách user thành công
2025-05-28 18:40:01.417 +07:00 [INF] [2025-05-28 18:40:01.417] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.08 giây
2025-05-28 18:40:01.418 +07:00 [DBG] [2025-05-28 18:40:01.418] [DEBUG]: OnLoad hoàn tất
2025-05-28 18:40:03.642 +07:00 [INF] [2025-05-28 18:40:03.642] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11014: Đang chạy
2025-05-28 18:40:03.648 +07:00 [INF] [2025-05-28 18:40:03.648] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 18:40:03.649 +07:00 [INF] [2025-05-28 18:40:03.649] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 18:40:03.652 +07:00 [DBG] [2025-05-28 18:40:03.652] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles: Thành công
2025-05-28 18:40:03.695 +07:00 [INF] [2025-05-28 18:40:03.695] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 44ms
2025-05-28 18:40:04.041 +07:00 [DBG] [2025-05-28 18:40:04.041] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 18:40:04.041 +07:00 [INF] [2025-05-28 18:40:04.041] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:56685. Thời gian: 345ms
2025-05-28 18:40:04.041 +07:00 [DBG] [2025-05-28 18:40:04.041] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 18:40:04.041 +07:00 [DBG] [2025-05-28 18:40:04.041] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 18:40:04.042 +07:00 [INF] [2025-05-28 18:40:04.042] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 392ms
2025-05-28 18:40:04.042 +07:00 [DBG] [2025-05-28 18:40:04.042] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 18:40:04.042 +07:00 [DBG] [2025-05-28 18:40:04.042] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 18:40:05.295 +07:00 [INF] [2025-05-28 18:40:05.295] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 18:40:05.617 +07:00 [INF] [2025-05-28 18:40:05.617] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 18:40:05.728 +07:00 [INF] [2025-05-28 18:40:05.728] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 18:40:05.734 +07:00 [DBG] [2025-05-28 18:40:05.734] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 18:40:05.740 +07:00 [INF] [2025-05-28 18:40:05.740] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 18:40:05.740 +07:00 [INF] [2025-05-28 18:40:05.740] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 18:40:05.743 +07:00 [INF] [2025-05-28 18:40:05.743] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 18:40:05.744 +07:00 [INF] [2025-05-28 18:40:05.744] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 18:40:05.744 +07:00 [DBG] [2025-05-28 18:40:05.744] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 18:40:05.744 +07:00 [DBG] [2025-05-28 18:40:05.744] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:05.744 +07:00 [INF] [2025-05-28 18:40:05.744] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:40:05.744 +07:00 [DBG] [2025-05-28 18:40:05.744] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 18:40:05.744 +07:00 [DBG] [2025-05-28 18:40:05.744] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:05.744 +07:00 [INF] [2025-05-28 18:40:05.744] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:40:05.744 +07:00 [DBG] [2025-05-28 18:40:05.744] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 18:40:05.744 +07:00 [DBG] [2025-05-28 18:40:05.744] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:05.772 +07:00 [INF] [2025-05-28 18:40:05.772] [INFO]: Tải danh sách user thành công
2025-05-28 18:40:05.772 +07:00 [INF] [2025-05-28 18:40:05.772] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 18:40:05.772 +07:00 [DBG] [2025-05-28 18:40:05.772] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 18:40:05.773 +07:00 [DBG] [2025-05-28 18:40:05.773] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:05.773 +07:00 [INF] [2025-05-28 18:40:05.773] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:40:05.773 +07:00 [DBG] [2025-05-28 18:40:05.773] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 18:40:05.773 +07:00 [DBG] [2025-05-28 18:40:05.773] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:05.773 +07:00 [INF] [2025-05-28 18:40:05.773] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:40:05.773 +07:00 [DBG] [2025-05-28 18:40:05.773] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 18:40:05.773 +07:00 [DBG] [2025-05-28 18:40:05.773] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:05.802 +07:00 [INF] [2025-05-28 18:40:05.802] [INFO]: Tải danh sách user thành công
2025-05-28 18:40:05.810 +07:00 [INF] [2025-05-28 18:40:05.810] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.77 giây
2025-05-28 18:40:05.818 +07:00 [INF] [2025-05-28 18:40:05.818] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.78 giây
2025-05-28 18:40:07.531 +07:00 [DBG] [2025-05-28 18:40:07.531] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Mậu Binh
2025-05-28 18:40:07.539 +07:00 [INF] [2025-05-28 18:40:07.539] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 18:40:07.546 +07:00 [DBG] [2025-05-28 18:40:07.546] [DEBUG]: Đã khởi tạo giao diện với 4 panel
2025-05-28 18:40:07.546 +07:00 [DBG] [2025-05-28 18:40:07.546] [DEBUG]: Đã khởi tạo MauBinhSuggestionForm
2025-05-28 18:40:07.742 +07:00 [INF] [2025-05-28 18:40:07.742] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 18:40:08.750 +07:00 [INF] [2025-05-28 18:40:08.750] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 18:40:08.750 +07:00 [DBG] [2025-05-28 18:40:08.750] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 18:40:08.750 +07:00 [DBG] [2025-05-28 18:40:08.750] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 18:40:08.750 +07:00 [INF] [2025-05-28 18:40:08.750] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 18:40:08.750 +07:00 [DBG] [2025-05-28 18:40:08.750] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 18:40:08.750 +07:00 [DBG] [2025-05-28 18:40:08.750] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 18:40:08.751 +07:00 [INF] [2025-05-28 18:40:08.751] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 18:40:08.751 +07:00 [DBG] [2025-05-28 18:40:08.751] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 18:40:08.751 +07:00 [DBG] [2025-05-28 18:40:08.751] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:08.751 +07:00 [INF] [2025-05-28 18:40:08.751] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:40:08.751 +07:00 [DBG] [2025-05-28 18:40:08.751] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 18:40:08.751 +07:00 [DBG] [2025-05-28 18:40:08.751] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:08.751 +07:00 [INF] [2025-05-28 18:40:08.751] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:40:08.751 +07:00 [DBG] [2025-05-28 18:40:08.751] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 18:40:08.751 +07:00 [DBG] [2025-05-28 18:40:08.751] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:08.776 +07:00 [INF] [2025-05-28 18:40:08.776] [INFO]: Tải danh sách user thành công
2025-05-28 18:40:08.776 +07:00 [INF] [2025-05-28 18:40:08.776] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 18:40:08.776 +07:00 [DBG] [2025-05-28 18:40:08.776] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 18:40:08.776 +07:00 [DBG] [2025-05-28 18:40:08.776] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:08.776 +07:00 [INF] [2025-05-28 18:40:08.776] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 18:40:08.776 +07:00 [DBG] [2025-05-28 18:40:08.776] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 18:40:08.776 +07:00 [DBG] [2025-05-28 18:40:08.776] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:08.776 +07:00 [INF] [2025-05-28 18:40:08.776] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 18:40:08.776 +07:00 [DBG] [2025-05-28 18:40:08.776] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 18:40:08.776 +07:00 [DBG] [2025-05-28 18:40:08.776] [DEBUG]: Số cột trong hàng: 5
2025-05-28 18:40:08.801 +07:00 [INF] [2025-05-28 18:40:08.801] [INFO]: Tải danh sách user thành công
2025-05-28 18:40:08.808 +07:00 [INF] [2025-05-28 18:40:08.808] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 18:40:08.816 +07:00 [INF] [2025-05-28 18:40:08.816] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 18:40:10.829 +07:00 [DBG] [2025-05-28 18:40:10.829] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 18:40:10.831 +07:00 [DBG] [2025-05-28 18:40:10.831] [DEBUG] [nhatrang345]: ℹ️ Không có điều kiện đặc biệt cho nhatrang345, giữ nguyên trong phòng (sit: 2, số người: 3)
2025-05-28 18:40:10.831 +07:00 [WRN] [2025-05-28 18:40:10.831] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 18:40:11.787 +07:00 [INF] [2025-05-28 18:40:11.787] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 18:40:11.788 +07:00 [INF] [2025-05-28 18:40:11.788] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 18:40:11.794 +07:00 [INF] [2025-05-28 18:40:11.794] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 18:40:16.784 +07:00 [INF] [2025-05-28 18:40:16.784] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 18:40:16.784 +07:00 [INF] [2025-05-28 18:40:16.784] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 18:40:16.790 +07:00 [INF] [2025-05-28 18:40:16.790] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 18:40:17.581 +07:00 [DBG] [2025-05-28 18:40:17.581] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 600 (Mau Binh cards)
2025-05-28 18:40:17.584 +07:00 [INF] [2025-05-28 18:40:17.584] [INFO]: Saved Mau Binh cards for nhatrang345: [A♣, Q♠, 10♠, J♦, 3♠, K♥, 9♥, A♠, Q♥, 2♣, Q♦, 10♦, 5♣]
2025-05-28 18:40:19.782 +07:00 [DBG] [2025-05-28 18:40:19.782] [DEBUG]: Bắt đầu quét bài, số user: 3
2025-05-28 18:40:19.789 +07:00 [INF] [2025-05-28 18:40:19.789] [INFO]: Updated Mau Binh cards for nhatrang345: [5♣, 10♦, Q♦, 2♣, Q♥, A♠, 9♥, K♥, 3♠, J♦, 10♠, Q♠, A♣]
2025-05-28 18:40:19.789 +07:00 [INF] [2025-05-28 18:40:19.789] [INFO] [nhatrang345]: Đã quét bài cho nhatrang345 bằng script (MauBinhController): [5♣, 10♦, Q♦, 2♣, Q♥, A♠, 9♥, K♥, 3♠, J♦, 10♠, Q♠, A♣]
2025-05-28 18:40:19.789 +07:00 [ERR] [2025-05-28 18:40:19.789] [ERROR]: Không tìm thấy driver cho phanthiet989
2025-05-28 18:40:19.789 +07:00 [ERR] [2025-05-28 18:40:19.789] [ERROR]: Không tìm thấy driver cho namdinhx852
2025-05-28 18:40:19.790 +07:00 [WRN] [2025-05-28 18:40:19.790] [WARNING]: Chỉ tìm thấy bài hợp lệ cho 1/3 user
2025-05-28 18:40:19.790 +07:00 [WRN] [2025-05-28 18:40:19.790] [WARNING]: Số user hợp lệ (1) không đủ 3 để tính bài đối thủ
2025-05-28 18:40:19.790 +07:00 [DBG] [2025-05-28 18:40:19.790] [DEBUG]: Bắt đầu UpdateCardDisplay, số user: 1
2025-05-28 18:40:19.790 +07:00 [DBG] [2025-05-28 18:40:19.790] [DEBUG]: Cập nhật bài cho user: nhatrang345, indexKey: user0
2025-05-28 18:40:19.797 +07:00 [INF] [2025-05-28 18:40:19.797] [INFO]: Quét bài thành công, số user: 1, tổng số lá: 13
2025-05-28 18:40:20.712 +07:00 [INF] [2025-05-28 18:40:20.712] [INFO]: 🚀 Tạo gợi ý với thuật toán OPTIMIZED cho 13 lá bài
2025-05-28 18:40:20.712 +07:00 [INF] [2025-05-28 18:40:20.712] [INFO]: 🚀 Bắt đầu thuật toán Mậu Binh theo yêu cầu mới
2025-05-28 18:40:20.712 +07:00 [INF] [2025-05-28 18:40:20.712] [INFO]: 🏆 Kiểm tra đặc biệt tới trắng
2025-05-28 18:40:20.764 +07:00 [ERR] [2025-05-28 18:40:20.764] [ERROR]: ❌ Lỗi thuật toán mới: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 18:40:20.764 +07:00 [WRN] [2025-05-28 18:40:20.764] [WARNING]: 🔄 Fallback sang thuật toán tối ưu cũ
2025-05-28 18:40:20.773 +07:00 [ERR] [2025-05-28 18:40:20.773] [ERROR]: ❌ Lỗi thuật toán cũ: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 18:40:20.773 +07:00 [WRN] [2025-05-28 18:40:20.773] [WARNING]: 🔄 Fallback sang gợi ý đơn giản
2025-05-28 18:40:20.775 +07:00 [INF] [2025-05-28 18:40:20.775] [INFO]: ✅ Đã tạo gợi ý mặc định
2025-05-28 18:40:20.775 +07:00 [INF] [2025-05-28 18:40:20.775] [INFO] [nhatrang345]: Đã tạo 1 gợi ý cho nhatrang345 (indexKey: user0)
2025-05-28 18:40:20.775 +07:00 [INF] [2025-05-28 18:40:20.775] [INFO]: 📝 AddSuggestion for nhatrang345
2025-05-28 18:40:20.775 +07:00 [DBG] [2025-05-28 18:40:20.775] [DEBUG]: Cập nhật ListBox cho user0, số gợi ý: 1
2025-05-28 18:40:20.788 +07:00 [DBG] [2025-05-28 18:40:20.788] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 18:40:20.788 +07:00 [INF] [2025-05-28 18:40:20.788] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 18:40:20.788 +07:00 [DBG] [2025-05-28 18:40:20.788] [DEBUG]: Đã chọn gợi ý đầu tiên cho user0
2025-05-28 18:40:20.788 +07:00 [DBG] [2025-05-28 18:40:20.788] [DEBUG]: Không có gợi ý để hiển thị cho user1
2025-05-28 18:40:20.788 +07:00 [DBG] [2025-05-28 18:40:20.788] [DEBUG]: Không có gợi ý để hiển thị cho user2
2025-05-28 18:40:20.788 +07:00 [DBG] [2025-05-28 18:40:20.788] [DEBUG]: Không có gợi ý để hiển thị cho opponent
2025-05-28 18:40:20.788 +07:00 [DBG] [2025-05-28 18:40:20.788] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 18:40:20.788 +07:00 [INF] [2025-05-28 18:40:20.788] [INFO]: Đã cập nhật danh sách gợi ý
2025-05-28 18:40:21.788 +07:00 [INF] [2025-05-28 18:40:21.788] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 18:40:21.788 +07:00 [INF] [2025-05-28 18:40:21.788] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 18:40:21.794 +07:00 [INF] [2025-05-28 18:40:21.794] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 18:40:22.012 +07:00 [DBG] [2025-05-28 18:40:22.012] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 18:40:22.012 +07:00 [INF] [2025-05-28 18:40:22.012] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 18:40:22.791 +07:00 [DBG] [2025-05-28 18:40:22.791] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 18:40:22.791 +07:00 [INF] [2025-05-28 18:40:22.791] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 18:40:25.887 +07:00 [DBG] [2025-05-28 18:40:25.887] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 18:40:25.887 +07:00 [INF] [2025-05-28 18:40:25.887] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 18:40:26.716 +07:00 [INF] [2025-05-28 18:40:26.716] [INFO] [nhatrang345]: Bắt đầu xếp bài cho nhatrang345 theo gợi ý: [A♠, A♣, K♥, Q♠, Q♦, Q♥, J♦, 10♠, 10♦, 9♥, 5♣, 3♠, 2♣]
2025-05-28 18:40:26.723 +07:00 [INF] [2025-05-28 18:40:26.723] [INFO]: Kết quả xếp bài cho nhatrang345: Đã xếp bài thành công và đang cố gắng cập nhật UI
2025-05-28 18:40:26.787 +07:00 [INF] [2025-05-28 18:40:26.787] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 18:40:26.787 +07:00 [INF] [2025-05-28 18:40:26.787] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 18:40:26.787 +07:00 [INF] [2025-05-28 18:40:26.787] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 18:40:27.736 +07:00 [INF] [2025-05-28 18:40:27.736] [INFO]: Sắp xếp bài thành công cho nhatrang345: 0,1,51,44,46,47,42,36,38,35,17,8,5
2025-05-28 18:40:27.736 +07:00 [INF] [2025-05-28 18:40:27.736] [INFO] [nhatrang345]: Đã xếp bài cho nhatrang345 theo gợi ý: [A♠, A♣, K♥, Q♠, Q♦, Q♥, J♦, 10♠, 10♦, 9♥, 5♣, 3♠, 2♣]
2025-05-28 18:40:29.476 +07:00 [DBG] [2025-05-28 18:40:29.476] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 18:40:29.476 +07:00 [WRN] [2025-05-28 18:40:29.476] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 18:59:58.256 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 19:00:01.134 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 19:00:01.138 +07:00 [INF] Form1 constructor started.
2025-05-28 19:00:01.159 +07:00 [DBG] [2025-05-28 19:00:01.159] [DEBUG]: Gọi InitializeComponent
2025-05-28 19:00:01.172 +07:00 [INF] [2025-05-28 19:00:01.172] [INFO]: Khởi tạo UIManager thành công
2025-05-28 19:00:01.173 +07:00 [DBG] [2025-05-28 19:00:01.173] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 19:00:01.175 +07:00 [INF] [2025-05-28 19:00:01.175] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 19:00:01.175 +07:00 [DBG] [2025-05-28 19:00:01.175] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 19:00:01.175 +07:00 [INF] [2025-05-28 19:00:01.175] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 19:00:01.176 +07:00 [INF] [2025-05-28 19:00:01.176] [INFO]: Form1 constructor hoàn tất trong 0.04 giây
2025-05-28 19:00:01.198 +07:00 [DBG] [2025-05-28 19:00:01.198] [DEBUG]: Bắt đầu OnLoad
2025-05-28 19:00:01.199 +07:00 [DBG] [2025-05-28 19:00:01.199] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 19:00:01.219 +07:00 [INF] [2025-05-28 19:00:01.219] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 19:00:01.219 +07:00 [INF] [2025-05-28 19:00:01.219] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 19:00:01.241 +07:00 [INF] [2025-05-28 19:00:01.241] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:00:01.243 +07:00 [INF] [2025-05-28 19:00:01.243] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 19:00:01.244 +07:00 [INF] [2025-05-28 19:00:01.244] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 19:00:01.244 +07:00 [INF] [2025-05-28 19:00:01.244] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 19:00:01.244 +07:00 [INF] [2025-05-28 19:00:01.244] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 19:00:01.244 +07:00 [INF] [2025-05-28 19:00:01.244] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 19:00:01.245 +07:00 [INF] InitializeAsync started.
2025-05-28 19:00:01.245 +07:00 [INF] [2025-05-28 19:00:01.245] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:00:01.250 +07:00 [DBG] [2025-05-28 19:00:01.250] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 19:00:01.253 +07:00 [DBG] [2025-05-28 19:00:01.253] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 19:00:01.254 +07:00 [DBG] [2025-05-28 19:00:01.254] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:00:01.254 +07:00 [DBG] [2025-05-28 19:00:01.254] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:00:01.262 +07:00 [DBG] [2025-05-28 19:00:01.262] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 19:00:01.262 +07:00 [INF] [2025-05-28 19:00:01.262] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 19:00:01.263 +07:00 [DBG] [2025-05-28 19:00:01.263] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:00:01.264 +07:00 [DBG] [2025-05-28 19:00:01.264] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:01.264 +07:00 [DBG] [2025-05-28 19:00:01.264] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:00:01.264 +07:00 [INF] [2025-05-28 19:00:01.264] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:00:01.264 +07:00 [DBG] [2025-05-28 19:00:01.264] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:00:01.265 +07:00 [DBG] [2025-05-28 19:00:01.265] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:01.265 +07:00 [DBG] [2025-05-28 19:00:01.265] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:00:01.265 +07:00 [INF] [2025-05-28 19:00:01.265] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:00:01.265 +07:00 [DBG] [2025-05-28 19:00:01.265] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:00:01.265 +07:00 [DBG] [2025-05-28 19:00:01.265] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:01.320 +07:00 [INF] [2025-05-28 19:00:01.320] [INFO]: Tải danh sách user thành công
2025-05-28 19:00:01.333 +07:00 [INF] [2025-05-28 19:00:01.333] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.08 giây
2025-05-28 19:00:01.333 +07:00 [DBG] [2025-05-28 19:00:01.333] [DEBUG]: OnLoad hoàn tất
2025-05-28 19:00:02.641 +07:00 [INF] [2025-05-28 19:00:02.641] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11014: Đang chạy
2025-05-28 19:00:02.647 +07:00 [INF] [2025-05-28 19:00:02.646] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 19:00:02.648 +07:00 [INF] [2025-05-28 19:00:02.648] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 19:00:02.671 +07:00 [DBG] [2025-05-28 19:00:02.671] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles: Thành công
2025-05-28 19:00:02.718 +07:00 [INF] [2025-05-28 19:00:02.718] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 69ms
2025-05-28 19:00:03.101 +07:00 [DBG] [2025-05-28 19:00:03.101] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 19:00:03.102 +07:00 [INF] [2025-05-28 19:00:03.102] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:62924. Thời gian: 382ms
2025-05-28 19:00:03.102 +07:00 [DBG] [2025-05-28 19:00:03.102] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:00:03.102 +07:00 [DBG] [2025-05-28 19:00:03.102] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:00:03.102 +07:00 [INF] [2025-05-28 19:00:03.102] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 453ms
2025-05-28 19:00:03.102 +07:00 [DBG] [2025-05-28 19:00:03.102] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:00:03.102 +07:00 [DBG] [2025-05-28 19:00:03.102] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:00:04.495 +07:00 [INF] [2025-05-28 19:00:04.495] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 19:00:04.896 +07:00 [INF] [2025-05-28 19:00:04.896] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 19:00:04.985 +07:00 [INF] [2025-05-28 19:00:04.984] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 19:00:04.999 +07:00 [DBG] [2025-05-28 19:00:04.999] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 19:00:05.004 +07:00 [INF] [2025-05-28 19:00:05.004] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 19:00:05.006 +07:00 [INF] [2025-05-28 19:00:05.005] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 19:00:05.011 +07:00 [INF] [2025-05-28 19:00:05.011] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 19:00:05.011 +07:00 [INF] [2025-05-28 19:00:05.011] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:00:05.011 +07:00 [DBG] [2025-05-28 19:00:05.011] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:00:05.012 +07:00 [DBG] [2025-05-28 19:00:05.012] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:05.012 +07:00 [INF] [2025-05-28 19:00:05.012] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:00:05.012 +07:00 [DBG] [2025-05-28 19:00:05.012] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:00:05.012 +07:00 [DBG] [2025-05-28 19:00:05.012] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:05.012 +07:00 [INF] [2025-05-28 19:00:05.012] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:00:05.012 +07:00 [DBG] [2025-05-28 19:00:05.012] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:00:05.012 +07:00 [DBG] [2025-05-28 19:00:05.012] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:05.040 +07:00 [INF] [2025-05-28 19:00:05.040] [INFO]: Tải danh sách user thành công
2025-05-28 19:00:05.040 +07:00 [INF] [2025-05-28 19:00:05.040] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:00:05.040 +07:00 [DBG] [2025-05-28 19:00:05.040] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:00:05.040 +07:00 [DBG] [2025-05-28 19:00:05.040] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:05.040 +07:00 [INF] [2025-05-28 19:00:05.040] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:00:05.040 +07:00 [DBG] [2025-05-28 19:00:05.040] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:00:05.040 +07:00 [DBG] [2025-05-28 19:00:05.040] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:05.041 +07:00 [INF] [2025-05-28 19:00:05.040] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:00:05.041 +07:00 [DBG] [2025-05-28 19:00:05.041] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:00:05.041 +07:00 [DBG] [2025-05-28 19:00:05.041] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:05.068 +07:00 [INF] [2025-05-28 19:00:05.068] [INFO]: Tải danh sách user thành công
2025-05-28 19:00:05.077 +07:00 [INF] [2025-05-28 19:00:05.077] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.98 giây
2025-05-28 19:00:05.085 +07:00 [INF] [2025-05-28 19:00:05.085] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.98 giây
2025-05-28 19:00:05.566 +07:00 [DBG] [2025-05-28 19:00:05.566] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Mậu Binh
2025-05-28 19:00:05.573 +07:00 [INF] [2025-05-28 19:00:05.573] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:00:05.579 +07:00 [DBG] [2025-05-28 19:00:05.579] [DEBUG]: Đã khởi tạo giao diện với 4 panel
2025-05-28 19:00:05.579 +07:00 [DBG] [2025-05-28 19:00:05.579] [DEBUG]: Đã khởi tạo MauBinhSuggestionForm
2025-05-28 19:00:06.999 +07:00 [INF] [2025-05-28 19:00:06.999] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 19:00:08.015 +07:00 [INF] [2025-05-28 19:00:08.015] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 19:00:08.015 +07:00 [DBG] [2025-05-28 19:00:08.015] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:00:08.015 +07:00 [DBG] [2025-05-28 19:00:08.015] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:00:08.016 +07:00 [INF] [2025-05-28 19:00:08.016] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 19:00:08.016 +07:00 [DBG] [2025-05-28 19:00:08.016] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:00:08.016 +07:00 [DBG] [2025-05-28 19:00:08.016] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:00:08.016 +07:00 [INF] [2025-05-28 19:00:08.016] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:00:08.016 +07:00 [DBG] [2025-05-28 19:00:08.016] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:00:08.016 +07:00 [DBG] [2025-05-28 19:00:08.016] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:08.016 +07:00 [INF] [2025-05-28 19:00:08.016] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:00:08.016 +07:00 [DBG] [2025-05-28 19:00:08.016] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:00:08.016 +07:00 [DBG] [2025-05-28 19:00:08.016] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:08.016 +07:00 [INF] [2025-05-28 19:00:08.016] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:00:08.016 +07:00 [DBG] [2025-05-28 19:00:08.016] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:00:08.016 +07:00 [DBG] [2025-05-28 19:00:08.016] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:08.043 +07:00 [INF] [2025-05-28 19:00:08.043] [INFO]: Tải danh sách user thành công
2025-05-28 19:00:08.043 +07:00 [INF] [2025-05-28 19:00:08.043] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:00:08.043 +07:00 [DBG] [2025-05-28 19:00:08.043] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:00:08.043 +07:00 [DBG] [2025-05-28 19:00:08.043] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:08.043 +07:00 [INF] [2025-05-28 19:00:08.043] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:00:08.043 +07:00 [DBG] [2025-05-28 19:00:08.043] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:00:08.043 +07:00 [DBG] [2025-05-28 19:00:08.043] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:08.043 +07:00 [INF] [2025-05-28 19:00:08.043] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:00:08.043 +07:00 [DBG] [2025-05-28 19:00:08.043] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:00:08.043 +07:00 [DBG] [2025-05-28 19:00:08.043] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:00:08.069 +07:00 [INF] [2025-05-28 19:00:08.069] [INFO]: Tải danh sách user thành công
2025-05-28 19:00:08.077 +07:00 [INF] [2025-05-28 19:00:08.077] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 19:00:08.085 +07:00 [INF] [2025-05-28 19:00:08.085] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 19:00:10.966 +07:00 [INF] [2025-05-28 19:00:10.966] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:00:10.968 +07:00 [INF] [2025-05-28 19:00:10.968] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:00:12.366 +07:00 [DBG] [2025-05-28 19:00:12.366] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:00:12.367 +07:00 [DBG] [2025-05-28 19:00:12.367] [DEBUG] [nhatrang345]: ℹ️ Không có điều kiện đặc biệt cho nhatrang345, giữ nguyên trong phòng (sit: 2, số người: 4)
2025-05-28 19:00:12.367 +07:00 [WRN] [2025-05-28 19:00:12.367] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 19:00:12.424 +07:00 [INF] [2025-05-28 19:00:12.424] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:00:12.734 +07:00 [DBG] [2025-05-28 19:00:12.734] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 600 (Mau Binh cards)
2025-05-28 19:00:12.737 +07:00 [INF] [2025-05-28 19:00:12.737] [INFO]: Saved Mau Binh cards for nhatrang345: [2♣, 4♥, 3♠, 7♥, 10♦, Q♠, 4♦, J♣, 7♠, 2♥, 6♣, Q♦, 4♣]
2025-05-28 19:00:14.533 +07:00 [DBG] [2025-05-28 19:00:14.533] [DEBUG]: Bắt đầu quét bài, số user: 3
2025-05-28 19:00:14.539 +07:00 [INF] [2025-05-28 19:00:14.539] [INFO]: Updated Mau Binh cards for nhatrang345: [4♣, Q♦, 6♣, 2♥, 7♠, J♣, 4♦, Q♠, 10♦, 7♥, 3♠, 4♥, 2♣]
2025-05-28 19:00:14.540 +07:00 [INF] [2025-05-28 19:00:14.540] [INFO] [nhatrang345]: Đã quét bài cho nhatrang345 bằng script (MauBinhController): [4♣, Q♦, 6♣, 2♥, 7♠, J♣, 4♦, Q♠, 10♦, 7♥, 3♠, 4♥, 2♣]
2025-05-28 19:00:14.540 +07:00 [ERR] [2025-05-28 19:00:14.540] [ERROR]: Không tìm thấy driver cho phanthiet989
2025-05-28 19:00:14.540 +07:00 [ERR] [2025-05-28 19:00:14.540] [ERROR]: Không tìm thấy driver cho namdinhx852
2025-05-28 19:00:14.540 +07:00 [WRN] [2025-05-28 19:00:14.540] [WARNING]: Chỉ tìm thấy bài hợp lệ cho 1/3 user
2025-05-28 19:00:14.540 +07:00 [WRN] [2025-05-28 19:00:14.540] [WARNING]: Số user hợp lệ (1) không đủ 3 để tính bài đối thủ
2025-05-28 19:00:14.540 +07:00 [DBG] [2025-05-28 19:00:14.540] [DEBUG]: Bắt đầu UpdateCardDisplay, số user: 1
2025-05-28 19:00:14.540 +07:00 [DBG] [2025-05-28 19:00:14.540] [DEBUG]: Cập nhật bài cho user: nhatrang345, indexKey: user0
2025-05-28 19:00:14.548 +07:00 [INF] [2025-05-28 19:00:14.548] [INFO]: Quét bài thành công, số user: 1, tổng số lá: 13
2025-05-28 19:00:15.342 +07:00 [INF] [2025-05-28 19:00:15.342] [INFO]: 🚀 Tạo gợi ý với thuật toán OPTIMIZED cho 13 lá bài
2025-05-28 19:00:15.343 +07:00 [INF] [2025-05-28 19:00:15.343] [INFO]: 🚀 Bắt đầu thuật toán Mậu Binh theo yêu cầu mới
2025-05-28 19:00:15.343 +07:00 [INF] [2025-05-28 19:00:15.343] [INFO]: 🏆 Kiểm tra đặc biệt tới trắng
2025-05-28 19:00:15.397 +07:00 [ERR] [2025-05-28 19:00:15.397] [ERROR]: ❌ Lỗi thuật toán mới: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 19:00:15.397 +07:00 [WRN] [2025-05-28 19:00:15.397] [WARNING]: 🔄 Fallback sang thuật toán tối ưu cũ
2025-05-28 19:00:15.406 +07:00 [ERR] [2025-05-28 19:00:15.406] [ERROR]: ❌ Lỗi thuật toán cũ: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 19:00:15.406 +07:00 [WRN] [2025-05-28 19:00:15.406] [WARNING]: 🔄 Fallback sang gợi ý đơn giản
2025-05-28 19:00:15.408 +07:00 [INF] [2025-05-28 19:00:15.408] [INFO]: ✅ Đã tạo gợi ý mặc định
2025-05-28 19:00:15.408 +07:00 [INF] [2025-05-28 19:00:15.408] [INFO] [nhatrang345]: Đã tạo 1 gợi ý cho nhatrang345 (indexKey: user0)
2025-05-28 19:00:15.408 +07:00 [INF] [2025-05-28 19:00:15.408] [INFO]: 📝 AddSuggestion for nhatrang345
2025-05-28 19:00:15.408 +07:00 [DBG] [2025-05-28 19:00:15.408] [DEBUG]: Cập nhật ListBox cho user0, số gợi ý: 1
2025-05-28 19:00:15.421 +07:00 [DBG] [2025-05-28 19:00:15.421] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:00:15.421 +07:00 [INF] [2025-05-28 19:00:15.421] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 19:00:15.421 +07:00 [DBG] [2025-05-28 19:00:15.421] [DEBUG]: Đã chọn gợi ý đầu tiên cho user0
2025-05-28 19:00:15.421 +07:00 [DBG] [2025-05-28 19:00:15.421] [DEBUG]: Không có gợi ý để hiển thị cho user1
2025-05-28 19:00:15.421 +07:00 [DBG] [2025-05-28 19:00:15.421] [DEBUG]: Không có gợi ý để hiển thị cho user2
2025-05-28 19:00:15.421 +07:00 [DBG] [2025-05-28 19:00:15.421] [DEBUG]: Không có gợi ý để hiển thị cho opponent
2025-05-28 19:00:15.421 +07:00 [DBG] [2025-05-28 19:00:15.421] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:00:15.421 +07:00 [INF] [2025-05-28 19:00:15.421] [INFO]: Đã cập nhật danh sách gợi ý
2025-05-28 19:00:15.965 +07:00 [INF] [2025-05-28 19:00:15.965] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:00:15.965 +07:00 [INF] [2025-05-28 19:00:15.965] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:00:15.978 +07:00 [INF] [2025-05-28 19:00:15.978] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:00:16.419 +07:00 [DBG] [2025-05-28 19:00:16.419] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:00:16.419 +07:00 [INF] [2025-05-28 19:00:16.419] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 19:00:17.512 +07:00 [DBG] [2025-05-28 19:00:17.512] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:00:17.512 +07:00 [INF] [2025-05-28 19:00:17.512] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 19:00:18.258 +07:00 [INF] [2025-05-28 19:00:18.258] [INFO] [nhatrang345]: Bắt đầu xếp bài cho nhatrang345 theo gợi ý: [Q♠, Q♦, J♣, 10♦, 7♠, 7♥, 6♣, 4♣, 4♦, 4♥, 3♠, 2♣, 2♥]
2025-05-28 19:00:18.266 +07:00 [INF] [2025-05-28 19:00:18.266] [INFO]: Kết quả xếp bài cho nhatrang345: Đã xếp bài thành công và đang cố gắng cập nhật UI
2025-05-28 19:00:19.268 +07:00 [INF] [2025-05-28 19:00:19.268] [INFO]: Sắp xếp bài thành công cho nhatrang345: 44,46,41,38,24,27,21,13,14,15,8,5,7
2025-05-28 19:00:19.268 +07:00 [INF] [2025-05-28 19:00:19.268] [INFO] [nhatrang345]: Đã xếp bài cho nhatrang345 theo gợi ý: [Q♠, Q♦, J♣, 10♦, 7♠, 7♥, 6♣, 4♣, 4♦, 4♥, 3♠, 2♣, 2♥]
2025-05-28 19:00:20.965 +07:00 [INF] [2025-05-28 19:00:20.965] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:00:20.965 +07:00 [INF] [2025-05-28 19:00:20.965] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:00:20.972 +07:00 [INF] [2025-05-28 19:00:20.972] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:00:22.349 +07:00 [DBG] [2025-05-28 19:00:22.349] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 19:00:22.349 +07:00 [WRN] [2025-05-28 19:00:22.349] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 19:00:25.965 +07:00 [INF] [2025-05-28 19:00:25.965] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:00:25.965 +07:00 [INF] [2025-05-28 19:00:25.965] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:00:25.966 +07:00 [INF] [2025-05-28 19:00:25.966] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:00:30.966 +07:00 [INF] [2025-05-28 19:00:30.966] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:00:30.966 +07:00 [INF] [2025-05-28 19:00:30.966] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:00:30.967 +07:00 [INF] [2025-05-28 19:00:30.967] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:00:35.968 +07:00 [INF] [2025-05-28 19:00:35.968] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:00:35.968 +07:00 [INF] [2025-05-28 19:00:35.968] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:00:35.968 +07:00 [INF] [2025-05-28 19:00:35.968] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:00:40.967 +07:00 [INF] [2025-05-28 19:00:40.967] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:00:40.967 +07:00 [INF] [2025-05-28 19:00:40.967] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:00:40.967 +07:00 [INF] [2025-05-28 19:00:40.967] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:00:45.966 +07:00 [INF] [2025-05-28 19:00:45.966] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:00:45.966 +07:00 [INF] [2025-05-28 19:00:45.966] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:00:45.969 +07:00 [INF] [2025-05-28 19:00:45.969] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:00:47.653 +07:00 [DBG] [2025-05-28 19:00:47.653] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 19:00:47.653 +07:00 [WRN] [2025-05-28 19:00:47.653] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 19:00:49.274 +07:00 [DBG] [2025-05-28 19:00:49.274] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 19:00:49.274 +07:00 [WRN] [2025-05-28 19:00:49.274] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 19:00:50.969 +07:00 [INF] [2025-05-28 19:00:50.969] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:00:50.969 +07:00 [INF] [2025-05-28 19:00:50.969] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:00:51.057 +07:00 [INF] [2025-05-28 19:00:51.057] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:11:21.739 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 19:11:24.532 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 19:11:24.535 +07:00 [INF] Form1 constructor started.
2025-05-28 19:11:24.553 +07:00 [DBG] [2025-05-28 19:11:24.553] [DEBUG]: Gọi InitializeComponent
2025-05-28 19:11:24.563 +07:00 [INF] [2025-05-28 19:11:24.563] [INFO]: Khởi tạo UIManager thành công
2025-05-28 19:11:24.564 +07:00 [DBG] [2025-05-28 19:11:24.564] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 19:11:24.566 +07:00 [INF] [2025-05-28 19:11:24.566] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 19:11:24.566 +07:00 [DBG] [2025-05-28 19:11:24.566] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 19:11:24.566 +07:00 [INF] [2025-05-28 19:11:24.566] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 19:11:24.567 +07:00 [INF] [2025-05-28 19:11:24.567] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 19:11:24.580 +07:00 [DBG] [2025-05-28 19:11:24.580] [DEBUG]: Bắt đầu OnLoad
2025-05-28 19:11:24.580 +07:00 [DBG] [2025-05-28 19:11:24.580] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 19:11:24.597 +07:00 [INF] [2025-05-28 19:11:24.596] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 19:11:24.597 +07:00 [INF] [2025-05-28 19:11:24.597] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 19:11:24.613 +07:00 [INF] [2025-05-28 19:11:24.613] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:11:24.615 +07:00 [INF] [2025-05-28 19:11:24.615] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 19:11:24.616 +07:00 [INF] [2025-05-28 19:11:24.616] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 19:11:24.616 +07:00 [INF] [2025-05-28 19:11:24.616] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 19:11:24.616 +07:00 [INF] [2025-05-28 19:11:24.616] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 19:11:24.616 +07:00 [INF] [2025-05-28 19:11:24.616] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 19:11:24.616 +07:00 [INF] InitializeAsync started.
2025-05-28 19:11:24.616 +07:00 [INF] [2025-05-28 19:11:24.616] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:11:24.621 +07:00 [DBG] [2025-05-28 19:11:24.621] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 19:11:24.624 +07:00 [DBG] [2025-05-28 19:11:24.624] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 19:11:24.624 +07:00 [DBG] [2025-05-28 19:11:24.624] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:11:24.625 +07:00 [DBG] [2025-05-28 19:11:24.625] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:11:24.632 +07:00 [DBG] [2025-05-28 19:11:24.632] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 19:11:24.632 +07:00 [INF] [2025-05-28 19:11:24.632] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 19:11:24.632 +07:00 [DBG] [2025-05-28 19:11:24.632] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:11:24.633 +07:00 [DBG] [2025-05-28 19:11:24.633] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:24.633 +07:00 [DBG] [2025-05-28 19:11:24.633] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:11:24.633 +07:00 [INF] [2025-05-28 19:11:24.633] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:11:24.633 +07:00 [DBG] [2025-05-28 19:11:24.633] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:11:24.634 +07:00 [DBG] [2025-05-28 19:11:24.634] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:24.634 +07:00 [DBG] [2025-05-28 19:11:24.634] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:11:24.634 +07:00 [INF] [2025-05-28 19:11:24.634] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:11:24.634 +07:00 [DBG] [2025-05-28 19:11:24.634] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:11:24.634 +07:00 [DBG] [2025-05-28 19:11:24.634] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:24.678 +07:00 [INF] [2025-05-28 19:11:24.678] [INFO]: Tải danh sách user thành công
2025-05-28 19:11:24.687 +07:00 [INF] [2025-05-28 19:11:24.687] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 19:11:24.687 +07:00 [DBG] [2025-05-28 19:11:24.687] [DEBUG]: OnLoad hoàn tất
2025-05-28 19:11:26.006 +07:00 [INF] [2025-05-28 19:11:26.006] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11014: Đang chạy
2025-05-28 19:11:26.012 +07:00 [INF] [2025-05-28 19:11:26.012] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 19:11:26.013 +07:00 [INF] [2025-05-28 19:11:26.013] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 19:11:26.016 +07:00 [DBG] [2025-05-28 19:11:26.016] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles: Thành công
2025-05-28 19:11:26.060 +07:00 [INF] [2025-05-28 19:11:26.060] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 45ms
2025-05-28 19:11:26.444 +07:00 [DBG] [2025-05-28 19:11:26.444] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 19:11:26.445 +07:00 [INF] [2025-05-28 19:11:26.445] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:49771. Thời gian: 383ms
2025-05-28 19:11:26.445 +07:00 [DBG] [2025-05-28 19:11:26.445] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:11:26.445 +07:00 [DBG] [2025-05-28 19:11:26.445] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:11:26.445 +07:00 [INF] [2025-05-28 19:11:26.445] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 431ms
2025-05-28 19:11:26.445 +07:00 [DBG] [2025-05-28 19:11:26.445] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:11:26.445 +07:00 [DBG] [2025-05-28 19:11:26.445] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:11:27.971 +07:00 [INF] [2025-05-28 19:11:27.971] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 19:11:28.347 +07:00 [INF] [2025-05-28 19:11:28.347] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 19:11:28.421 +07:00 [INF] [2025-05-28 19:11:28.420] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 19:11:28.459 +07:00 [DBG] [2025-05-28 19:11:28.459] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 19:11:28.464 +07:00 [INF] [2025-05-28 19:11:28.464] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 19:11:28.466 +07:00 [INF] [2025-05-28 19:11:28.466] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 19:11:28.470 +07:00 [INF] [2025-05-28 19:11:28.470] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 19:11:28.471 +07:00 [INF] [2025-05-28 19:11:28.471] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:11:28.471 +07:00 [DBG] [2025-05-28 19:11:28.471] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:11:28.471 +07:00 [DBG] [2025-05-28 19:11:28.471] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:28.471 +07:00 [INF] [2025-05-28 19:11:28.471] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:11:28.471 +07:00 [DBG] [2025-05-28 19:11:28.471] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:11:28.471 +07:00 [DBG] [2025-05-28 19:11:28.471] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:28.471 +07:00 [INF] [2025-05-28 19:11:28.471] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:11:28.471 +07:00 [DBG] [2025-05-28 19:11:28.471] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:11:28.471 +07:00 [DBG] [2025-05-28 19:11:28.471] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:28.498 +07:00 [INF] [2025-05-28 19:11:28.498] [INFO]: Tải danh sách user thành công
2025-05-28 19:11:28.498 +07:00 [INF] [2025-05-28 19:11:28.498] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:11:28.498 +07:00 [DBG] [2025-05-28 19:11:28.498] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:11:28.499 +07:00 [DBG] [2025-05-28 19:11:28.499] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:28.499 +07:00 [INF] [2025-05-28 19:11:28.499] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:11:28.499 +07:00 [DBG] [2025-05-28 19:11:28.499] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:11:28.499 +07:00 [DBG] [2025-05-28 19:11:28.499] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:28.499 +07:00 [INF] [2025-05-28 19:11:28.499] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:11:28.499 +07:00 [DBG] [2025-05-28 19:11:28.499] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:11:28.499 +07:00 [DBG] [2025-05-28 19:11:28.499] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:28.524 +07:00 [INF] [2025-05-28 19:11:28.524] [INFO]: Tải danh sách user thành công
2025-05-28 19:11:28.534 +07:00 [INF] [2025-05-28 19:11:28.534] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.09 giây
2025-05-28 19:11:28.541 +07:00 [INF] [2025-05-28 19:11:28.541] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.10 giây
2025-05-28 19:11:28.545 +07:00 [DBG] [2025-05-28 19:11:28.545] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Mậu Binh
2025-05-28 19:11:28.551 +07:00 [INF] [2025-05-28 19:11:28.551] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:11:28.557 +07:00 [DBG] [2025-05-28 19:11:28.557] [DEBUG]: Đã khởi tạo giao diện với 4 panel
2025-05-28 19:11:28.557 +07:00 [DBG] [2025-05-28 19:11:28.557] [DEBUG]: Đã khởi tạo MauBinhSuggestionForm
2025-05-28 19:11:30.436 +07:00 [INF] [2025-05-28 19:11:30.436] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 19:11:31.474 +07:00 [INF] [2025-05-28 19:11:31.474] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 19:11:31.474 +07:00 [DBG] [2025-05-28 19:11:31.474] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:11:31.474 +07:00 [DBG] [2025-05-28 19:11:31.474] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:11:31.474 +07:00 [INF] [2025-05-28 19:11:31.474] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 19:11:31.474 +07:00 [DBG] [2025-05-28 19:11:31.474] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:11:31.474 +07:00 [DBG] [2025-05-28 19:11:31.474] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:11:31.475 +07:00 [INF] [2025-05-28 19:11:31.475] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:11:31.475 +07:00 [DBG] [2025-05-28 19:11:31.475] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:11:31.475 +07:00 [DBG] [2025-05-28 19:11:31.475] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:31.475 +07:00 [INF] [2025-05-28 19:11:31.475] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:11:31.475 +07:00 [DBG] [2025-05-28 19:11:31.475] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:11:31.475 +07:00 [DBG] [2025-05-28 19:11:31.475] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:31.475 +07:00 [INF] [2025-05-28 19:11:31.475] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:11:31.475 +07:00 [DBG] [2025-05-28 19:11:31.475] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:11:31.475 +07:00 [DBG] [2025-05-28 19:11:31.475] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:31.543 +07:00 [INF] [2025-05-28 19:11:31.543] [INFO]: Tải danh sách user thành công
2025-05-28 19:11:31.544 +07:00 [INF] [2025-05-28 19:11:31.544] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:11:31.544 +07:00 [DBG] [2025-05-28 19:11:31.544] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:11:31.544 +07:00 [DBG] [2025-05-28 19:11:31.544] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:31.544 +07:00 [INF] [2025-05-28 19:11:31.544] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:11:31.544 +07:00 [DBG] [2025-05-28 19:11:31.544] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:11:31.544 +07:00 [DBG] [2025-05-28 19:11:31.544] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:31.544 +07:00 [INF] [2025-05-28 19:11:31.544] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:11:31.544 +07:00 [DBG] [2025-05-28 19:11:31.544] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:11:31.544 +07:00 [DBG] [2025-05-28 19:11:31.544] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:11:31.646 +07:00 [INF] [2025-05-28 19:11:31.646] [INFO]: Tải danh sách user thành công
2025-05-28 19:11:31.656 +07:00 [INF] [2025-05-28 19:11:31.656] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.18 giây
2025-05-28 19:11:31.671 +07:00 [INF] [2025-05-28 19:11:31.671] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.20 giây
2025-05-28 19:11:33.638 +07:00 [DBG] [2025-05-28 19:11:33.638] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:11:33.640 +07:00 [DBG] [2025-05-28 19:11:33.640] [DEBUG] [nhatrang345]: ℹ️ Không có điều kiện đặc biệt cho nhatrang345, giữ nguyên trong phòng (sit: 2, số người: 4)
2025-05-28 19:11:33.640 +07:00 [WRN] [2025-05-28 19:11:33.640] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 19:11:34.467 +07:00 [INF] [2025-05-28 19:11:34.467] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:11:34.468 +07:00 [INF] [2025-05-28 19:11:34.468] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:11:34.475 +07:00 [INF] [2025-05-28 19:11:34.475] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:11:39.472 +07:00 [INF] [2025-05-28 19:11:39.472] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:11:39.472 +07:00 [INF] [2025-05-28 19:11:39.472] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:11:39.542 +07:00 [INF] [2025-05-28 19:11:39.542] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:11:44.468 +07:00 [INF] [2025-05-28 19:11:44.468] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:11:44.468 +07:00 [INF] [2025-05-28 19:11:44.468] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:11:44.469 +07:00 [INF] [2025-05-28 19:11:44.469] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:11:49.470 +07:00 [INF] [2025-05-28 19:11:49.470] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:11:49.470 +07:00 [INF] [2025-05-28 19:11:49.470] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:11:49.471 +07:00 [INF] [2025-05-28 19:11:49.471] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:11:54.470 +07:00 [INF] [2025-05-28 19:11:54.470] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:11:54.470 +07:00 [INF] [2025-05-28 19:11:54.470] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:11:54.471 +07:00 [INF] [2025-05-28 19:11:54.471] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:11:55.116 +07:00 [DBG] [2025-05-28 19:11:55.116] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 600 (Mau Binh cards)
2025-05-28 19:11:55.119 +07:00 [INF] [2025-05-28 19:11:55.119] [INFO]: Saved Mau Binh cards for nhatrang345: [5♣, Q♦, 8♦, A♦, A♣, 2♥, J♥, Q♠, 4♠, A♠, K♠, K♥, 4♣]
2025-05-28 19:11:57.566 +07:00 [DBG] [2025-05-28 19:11:57.566] [DEBUG]: Bắt đầu quét bài, số user: 3
2025-05-28 19:11:57.573 +07:00 [INF] [2025-05-28 19:11:57.573] [INFO]: Updated Mau Binh cards for nhatrang345: [4♣, K♥, K♠, A♠, 4♠, Q♠, J♥, 2♥, A♣, A♦, 8♦, Q♦, 5♣]
2025-05-28 19:11:57.573 +07:00 [INF] [2025-05-28 19:11:57.573] [INFO] [nhatrang345]: Đã quét bài cho nhatrang345 bằng script (MauBinhController): [4♣, K♥, K♠, A♠, 4♠, Q♠, J♥, 2♥, A♣, A♦, 8♦, Q♦, 5♣]
2025-05-28 19:11:57.573 +07:00 [ERR] [2025-05-28 19:11:57.573] [ERROR]: Không tìm thấy driver cho phanthiet989
2025-05-28 19:11:57.573 +07:00 [ERR] [2025-05-28 19:11:57.573] [ERROR]: Không tìm thấy driver cho namdinhx852
2025-05-28 19:11:57.573 +07:00 [WRN] [2025-05-28 19:11:57.573] [WARNING]: Chỉ tìm thấy bài hợp lệ cho 1/3 user
2025-05-28 19:11:57.573 +07:00 [WRN] [2025-05-28 19:11:57.573] [WARNING]: Số user hợp lệ (1) không đủ 3 để tính bài đối thủ
2025-05-28 19:11:57.573 +07:00 [DBG] [2025-05-28 19:11:57.573] [DEBUG]: Bắt đầu UpdateCardDisplay, số user: 1
2025-05-28 19:11:57.574 +07:00 [DBG] [2025-05-28 19:11:57.574] [DEBUG]: Cập nhật bài cho user: nhatrang345, indexKey: user0
2025-05-28 19:11:57.580 +07:00 [INF] [2025-05-28 19:11:57.580] [INFO]: Quét bài thành công, số user: 1, tổng số lá: 13
2025-05-28 19:11:58.181 +07:00 [INF] [2025-05-28 19:11:58.181] [INFO]: 🚀 Tạo gợi ý với thuật toán OPTIMIZED cho 13 lá bài
2025-05-28 19:11:58.181 +07:00 [INF] [2025-05-28 19:11:58.181] [INFO]: 🚀 Bắt đầu thuật toán Mậu Binh theo yêu cầu mới
2025-05-28 19:11:58.181 +07:00 [INF] [2025-05-28 19:11:58.181] [INFO]: 🏆 Kiểm tra đặc biệt tới trắng
2025-05-28 19:11:58.233 +07:00 [ERR] [2025-05-28 19:11:58.233] [ERROR]: ❌ Lỗi thuật toán mới: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 19:11:58.233 +07:00 [WRN] [2025-05-28 19:11:58.233] [WARNING]: 🔄 Fallback sang thuật toán tối ưu cũ
2025-05-28 19:11:58.242 +07:00 [ERR] [2025-05-28 19:11:58.242] [ERROR]: ❌ Lỗi thuật toán cũ: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 19:11:58.242 +07:00 [WRN] [2025-05-28 19:11:58.242] [WARNING]: 🔄 Fallback sang gợi ý đơn giản
2025-05-28 19:11:58.243 +07:00 [INF] [2025-05-28 19:11:58.243] [INFO]: ✅ Đã tạo gợi ý mặc định
2025-05-28 19:11:58.244 +07:00 [INF] [2025-05-28 19:11:58.244] [INFO] [nhatrang345]: Đã tạo 1 gợi ý cho nhatrang345 (indexKey: user0)
2025-05-28 19:11:58.244 +07:00 [INF] [2025-05-28 19:11:58.244] [INFO]: 📝 AddSuggestion for nhatrang345
2025-05-28 19:11:58.244 +07:00 [DBG] [2025-05-28 19:11:58.244] [DEBUG]: Cập nhật ListBox cho user0, số gợi ý: 1
2025-05-28 19:11:58.257 +07:00 [DBG] [2025-05-28 19:11:58.257] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:11:58.257 +07:00 [INF] [2025-05-28 19:11:58.257] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 19:11:58.257 +07:00 [DBG] [2025-05-28 19:11:58.257] [DEBUG]: Đã chọn gợi ý đầu tiên cho user0
2025-05-28 19:11:58.257 +07:00 [DBG] [2025-05-28 19:11:58.257] [DEBUG]: Không có gợi ý để hiển thị cho user1
2025-05-28 19:11:58.257 +07:00 [DBG] [2025-05-28 19:11:58.257] [DEBUG]: Không có gợi ý để hiển thị cho user2
2025-05-28 19:11:58.257 +07:00 [DBG] [2025-05-28 19:11:58.257] [DEBUG]: Không có gợi ý để hiển thị cho opponent
2025-05-28 19:11:58.257 +07:00 [DBG] [2025-05-28 19:11:58.257] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:11:58.257 +07:00 [INF] [2025-05-28 19:11:58.257] [INFO]: Đã cập nhật danh sách gợi ý
2025-05-28 19:11:59.470 +07:00 [INF] [2025-05-28 19:11:59.470] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:11:59.470 +07:00 [INF] [2025-05-28 19:11:59.470] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:11:59.479 +07:00 [INF] [2025-05-28 19:11:59.479] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:11:59.484 +07:00 [DBG] [2025-05-28 19:11:59.484] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:11:59.484 +07:00 [INF] [2025-05-28 19:11:59.484] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 19:12:04.468 +07:00 [INF] [2025-05-28 19:12:04.468] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:12:04.468 +07:00 [INF] [2025-05-28 19:12:04.468] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:12:04.469 +07:00 [INF] [2025-05-28 19:12:04.469] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:12:09.470 +07:00 [INF] [2025-05-28 19:12:09.470] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:12:09.470 +07:00 [INF] [2025-05-28 19:12:09.470] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:12:09.471 +07:00 [INF] [2025-05-28 19:12:09.471] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:12:09.770 +07:00 [INF] [2025-05-28 19:12:09.770] [INFO] [nhatrang345]: Bắt đầu xếp bài cho nhatrang345 theo gợi ý: [A♠, A♣, A♦, K♠, K♥, Q♠, Q♦, J♥, 8♦, 5♣, 4♠, 4♣, 2♥]
2025-05-28 19:12:09.779 +07:00 [INF] [2025-05-28 19:12:09.779] [INFO]: Kết quả xếp bài cho nhatrang345: Đã xếp bài thành công và đang cố gắng cập nhật UI
2025-05-28 19:12:10.778 +07:00 [INF] [2025-05-28 19:12:10.778] [INFO]: Sắp xếp bài thành công cho nhatrang345: 0,1,2,48,51,44,46,43,30,17,12,13,7
2025-05-28 19:12:10.778 +07:00 [INF] [2025-05-28 19:12:10.778] [INFO] [nhatrang345]: Đã xếp bài cho nhatrang345 theo gợi ý: [A♠, A♣, A♦, K♠, K♥, Q♠, Q♦, J♥, 8♦, 5♣, 4♠, 4♣, 2♥]
2025-05-28 19:12:11.671 +07:00 [DBG] [2025-05-28 19:12:11.671] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 19:12:11.671 +07:00 [WRN] [2025-05-28 19:12:11.671] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 19:12:12.500 +07:00 [DBG] [2025-05-28 19:12:12.500] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 19:12:12.500 +07:00 [WRN] [2025-05-28 19:12:12.500] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 19:12:14.470 +07:00 [INF] [2025-05-28 19:12:14.470] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:12:14.470 +07:00 [INF] [2025-05-28 19:12:14.470] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:12:14.486 +07:00 [INF] [2025-05-28 19:12:14.486] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:12:19.482 +07:00 [INF] [2025-05-28 19:12:19.482] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 19:12:19.482 +07:00 [INF] [2025-05-28 19:12:19.482] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 19:12:19.483 +07:00 [INF] [2025-05-28 19:12:19.483] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 19:12:26.637 +07:00 [INF] [2025-05-28 19:12:26.637] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 19:12:26.638 +07:00 [INF] [2025-05-28 19:12:26.638] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 19:12:26.640 +07:00 [INF] [2025-05-28 19:12:26.640] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 19:12:31.639 +07:00 [INF] [2025-05-28 19:12:31.639] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 19:12:31.639 +07:00 [INF] [2025-05-28 19:12:31.639] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 19:12:31.639 +07:00 [INF] [2025-05-28 19:12:31.639] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 19:12:36.640 +07:00 [INF] [2025-05-28 19:12:36.640] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 19:12:36.640 +07:00 [INF] [2025-05-28 19:12:36.640] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 19:12:36.751 +07:00 [INF] [2025-05-28 19:12:36.751] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 19:12:41.638 +07:00 [INF] [2025-05-28 19:12:41.638] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 19:12:41.638 +07:00 [INF] [2025-05-28 19:12:41.638] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 19:12:41.641 +07:00 [INF] [2025-05-28 19:12:41.641] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 19:12:42.583 +07:00 [DBG] [2025-05-28 19:12:42.583] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 19:12:42.583 +07:00 [WRN] [2025-05-28 19:12:42.583] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 19:12:45.114 +07:00 [INF] [2025-05-28 19:12:45.114] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 602 (Mau Binh game end)
2025-05-28 19:12:45.115 +07:00 [INF] [2025-05-28 19:12:45.115] [INFO] [nhatrang345]: Game result for nhatrang345: Win: +98 (Chi: 3)
2025-05-28 19:12:45.115 +07:00 [DBG] [2025-05-28 19:12:45.115] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:12:45.116 +07:00 [INF] [2025-05-28 19:12:45.116] [INFO]: Waiting 8 seconds before clearing Mau Binh data...
2025-05-28 19:12:45.116 +07:00 [DBG] [2025-05-28 19:12:45.116] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:12:45.116 +07:00 [DBG] [2025-05-28 19:12:45.116] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:12:45.116 +07:00 [INF] [2025-05-28 19:12:45.116] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:12:45.116 +07:00 [DBG] [2025-05-28 19:12:45.116] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:12:45.117 +07:00 [DBG] [2025-05-28 19:12:45.117] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:12:45.117 +07:00 [INF] [2025-05-28 19:12:45.117] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:12:45.117 +07:00 [DBG] [2025-05-28 19:12:45.117] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:12:45.117 +07:00 [DBG] [2025-05-28 19:12:45.117] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:12:45.117 +07:00 [INF] [2025-05-28 19:12:45.117] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:12:45.117 +07:00 [DBG] [2025-05-28 19:12:45.117] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:12:45.117 +07:00 [DBG] [2025-05-28 19:12:45.117] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:12:45.188 +07:00 [INF] [2025-05-28 19:12:45.188] [INFO]: Tải danh sách user thành công
2025-05-28 19:12:45.205 +07:00 [INF] [2025-05-28 19:12:45.205] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.09 giây
2025-05-28 19:12:46.636 +07:00 [INF] [2025-05-28 19:12:46.636] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 19:12:46.636 +07:00 [INF] [2025-05-28 19:12:46.636] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 19:12:46.638 +07:00 [INF] [2025-05-28 19:12:46.638] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,15]
2025-05-28 19:12:51.637 +07:00 [INF] [2025-05-28 19:12:51.637] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 19:12:51.637 +07:00 [INF] [2025-05-28 19:12:51.637] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 19:12:51.638 +07:00 [INF] [2025-05-28 19:12:51.638] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,16]
2025-05-28 19:12:53.119 +07:00 [INF] [2025-05-28 19:12:53.119] [INFO]: 🗑️ ClearSuggestions
2025-05-28 19:12:53.129 +07:00 [DBG] [2025-05-28 19:12:53.129] [DEBUG]: Đã đặt hình ảnh mặc định cho PictureBox, xóa ListBox và ẩn label Sập Làng cho user0
2025-05-28 19:12:53.136 +07:00 [DBG] [2025-05-28 19:12:53.136] [DEBUG]: Đã đặt hình ảnh mặc định cho PictureBox, xóa ListBox và ẩn label Sập Làng cho user1
2025-05-28 19:12:53.143 +07:00 [DBG] [2025-05-28 19:12:53.143] [DEBUG]: Đã đặt hình ảnh mặc định cho PictureBox, xóa ListBox và ẩn label Sập Làng cho user2
2025-05-28 19:12:53.150 +07:00 [DBG] [2025-05-28 19:12:53.150] [DEBUG]: Đã đặt hình ảnh mặc định cho PictureBox, xóa ListBox và ẩn label Sập Làng cho opponent
2025-05-28 19:12:53.150 +07:00 [INF] [2025-05-28 19:12:53.150] [INFO]: Đã xóa hiển thị bài, gợi ý và label Sập Làng, đặt hình ảnh mặc định
2025-05-28 19:12:53.150 +07:00 [INF] [2025-05-28 19:12:53.150] [INFO]: Cleared card display in MauBinhSuggestionForm
2025-05-28 19:34:59.555 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 19:35:02.108 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 19:35:02.111 +07:00 [INF] Form1 constructor started.
2025-05-28 19:35:02.129 +07:00 [DBG] [2025-05-28 19:35:02.129] [DEBUG]: Gọi InitializeComponent
2025-05-28 19:35:02.140 +07:00 [INF] [2025-05-28 19:35:02.140] [INFO]: Khởi tạo UIManager thành công
2025-05-28 19:35:02.141 +07:00 [DBG] [2025-05-28 19:35:02.141] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 19:35:02.143 +07:00 [INF] [2025-05-28 19:35:02.143] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 19:35:02.143 +07:00 [DBG] [2025-05-28 19:35:02.143] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 19:35:02.143 +07:00 [INF] [2025-05-28 19:35:02.143] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 19:35:02.144 +07:00 [INF] [2025-05-28 19:35:02.144] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 19:35:02.156 +07:00 [DBG] [2025-05-28 19:35:02.156] [DEBUG]: Bắt đầu OnLoad
2025-05-28 19:35:02.157 +07:00 [DBG] [2025-05-28 19:35:02.157] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 19:35:02.173 +07:00 [INF] [2025-05-28 19:35:02.173] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 19:35:02.174 +07:00 [INF] [2025-05-28 19:35:02.174] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 19:35:02.199 +07:00 [INF] [2025-05-28 19:35:02.199] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:35:02.200 +07:00 [INF] [2025-05-28 19:35:02.200] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 19:35:02.201 +07:00 [INF] [2025-05-28 19:35:02.201] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 19:35:02.201 +07:00 [INF] [2025-05-28 19:35:02.201] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 19:35:02.201 +07:00 [INF] [2025-05-28 19:35:02.201] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 19:35:02.201 +07:00 [INF] [2025-05-28 19:35:02.201] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 19:35:02.202 +07:00 [INF] InitializeAsync started.
2025-05-28 19:35:02.202 +07:00 [INF] [2025-05-28 19:35:02.202] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:35:02.207 +07:00 [DBG] [2025-05-28 19:35:02.207] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 19:35:02.209 +07:00 [DBG] [2025-05-28 19:35:02.209] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 19:35:02.210 +07:00 [DBG] [2025-05-28 19:35:02.210] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:02.210 +07:00 [DBG] [2025-05-28 19:35:02.210] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:02.220 +07:00 [DBG] [2025-05-28 19:35:02.220] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 19:35:02.220 +07:00 [INF] [2025-05-28 19:35:02.220] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 19:35:02.221 +07:00 [DBG] [2025-05-28 19:35:02.221] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:02.222 +07:00 [DBG] [2025-05-28 19:35:02.222] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:02.223 +07:00 [DBG] [2025-05-28 19:35:02.223] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:02.223 +07:00 [INF] [2025-05-28 19:35:02.223] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:02.223 +07:00 [DBG] [2025-05-28 19:35:02.223] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:02.223 +07:00 [DBG] [2025-05-28 19:35:02.223] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:02.223 +07:00 [DBG] [2025-05-28 19:35:02.223] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:02.223 +07:00 [INF] [2025-05-28 19:35:02.223] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:02.223 +07:00 [DBG] [2025-05-28 19:35:02.223] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:02.223 +07:00 [DBG] [2025-05-28 19:35:02.223] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:02.270 +07:00 [INF] [2025-05-28 19:35:02.270] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:02.282 +07:00 [INF] [2025-05-28 19:35:02.282] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 19:35:02.282 +07:00 [DBG] [2025-05-28 19:35:02.282] [DEBUG]: OnLoad hoàn tất
2025-05-28 19:35:03.591 +07:00 [INF] [2025-05-28 19:35:03.591] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11014: Đang chạy
2025-05-28 19:35:03.597 +07:00 [INF] [2025-05-28 19:35:03.597] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 19:35:03.599 +07:00 [INF] [2025-05-28 19:35:03.599] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 19:35:03.601 +07:00 [DBG] [2025-05-28 19:35:03.601] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles: Thành công
2025-05-28 19:35:03.644 +07:00 [INF] [2025-05-28 19:35:03.644] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 44ms
2025-05-28 19:35:04.024 +07:00 [DBG] [2025-05-28 19:35:04.024] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 19:35:04.024 +07:00 [INF] [2025-05-28 19:35:04.024] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:57463. Thời gian: 379ms
2025-05-28 19:35:04.024 +07:00 [DBG] [2025-05-28 19:35:04.024] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:04.025 +07:00 [DBG] [2025-05-28 19:35:04.025] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:04.025 +07:00 [INF] [2025-05-28 19:35:04.025] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 426ms
2025-05-28 19:35:04.025 +07:00 [DBG] [2025-05-28 19:35:04.025] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:04.025 +07:00 [DBG] [2025-05-28 19:35:04.025] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:06.066 +07:00 [INF] [2025-05-28 19:35:06.066] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 19:35:06.154 +07:00 [INF] [2025-05-28 19:35:06.154] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 19:35:06.494 +07:00 [INF] [2025-05-28 19:35:06.494] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 19:35:06.508 +07:00 [DBG] [2025-05-28 19:35:06.508] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 19:35:06.513 +07:00 [INF] [2025-05-28 19:35:06.513] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 19:35:06.516 +07:00 [INF] [2025-05-28 19:35:06.516] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 19:35:06.519 +07:00 [INF] [2025-05-28 19:35:06.519] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 19:35:06.520 +07:00 [INF] [2025-05-28 19:35:06.520] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:06.520 +07:00 [DBG] [2025-05-28 19:35:06.520] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:06.520 +07:00 [DBG] [2025-05-28 19:35:06.520] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:06.520 +07:00 [INF] [2025-05-28 19:35:06.520] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:06.520 +07:00 [DBG] [2025-05-28 19:35:06.520] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:06.520 +07:00 [DBG] [2025-05-28 19:35:06.520] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:06.520 +07:00 [INF] [2025-05-28 19:35:06.520] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:06.520 +07:00 [DBG] [2025-05-28 19:35:06.520] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:06.520 +07:00 [DBG] [2025-05-28 19:35:06.520] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:06.550 +07:00 [INF] [2025-05-28 19:35:06.550] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:06.550 +07:00 [INF] [2025-05-28 19:35:06.550] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:06.550 +07:00 [DBG] [2025-05-28 19:35:06.550] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:06.550 +07:00 [DBG] [2025-05-28 19:35:06.550] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:06.550 +07:00 [INF] [2025-05-28 19:35:06.550] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:06.550 +07:00 [DBG] [2025-05-28 19:35:06.550] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:06.550 +07:00 [DBG] [2025-05-28 19:35:06.550] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:06.550 +07:00 [INF] [2025-05-28 19:35:06.550] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:06.550 +07:00 [DBG] [2025-05-28 19:35:06.550] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:06.550 +07:00 [DBG] [2025-05-28 19:35:06.550] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:06.576 +07:00 [INF] [2025-05-28 19:35:06.576] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:06.584 +07:00 [INF] [2025-05-28 19:35:06.584] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.56 giây
2025-05-28 19:35:06.592 +07:00 [INF] [2025-05-28 19:35:06.592] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.57 giây
2025-05-28 19:35:08.518 +07:00 [INF] [2025-05-28 19:35:08.518] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 19:35:09.530 +07:00 [INF] [2025-05-28 19:35:09.530] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 19:35:09.530 +07:00 [DBG] [2025-05-28 19:35:09.530] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:09.530 +07:00 [DBG] [2025-05-28 19:35:09.530] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:09.531 +07:00 [INF] [2025-05-28 19:35:09.531] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 19:35:09.531 +07:00 [DBG] [2025-05-28 19:35:09.531] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:09.531 +07:00 [DBG] [2025-05-28 19:35:09.531] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:09.531 +07:00 [INF] [2025-05-28 19:35:09.531] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:09.531 +07:00 [DBG] [2025-05-28 19:35:09.531] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:09.531 +07:00 [DBG] [2025-05-28 19:35:09.531] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:09.531 +07:00 [INF] [2025-05-28 19:35:09.531] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:09.531 +07:00 [DBG] [2025-05-28 19:35:09.531] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:09.531 +07:00 [DBG] [2025-05-28 19:35:09.531] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:09.531 +07:00 [INF] [2025-05-28 19:35:09.531] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:09.531 +07:00 [DBG] [2025-05-28 19:35:09.531] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:09.532 +07:00 [DBG] [2025-05-28 19:35:09.532] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:09.557 +07:00 [INF] [2025-05-28 19:35:09.557] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:09.557 +07:00 [INF] [2025-05-28 19:35:09.557] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:09.557 +07:00 [DBG] [2025-05-28 19:35:09.557] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:09.557 +07:00 [DBG] [2025-05-28 19:35:09.557] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:09.557 +07:00 [INF] [2025-05-28 19:35:09.557] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:09.557 +07:00 [DBG] [2025-05-28 19:35:09.557] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:09.557 +07:00 [DBG] [2025-05-28 19:35:09.557] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:09.557 +07:00 [INF] [2025-05-28 19:35:09.557] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:09.557 +07:00 [DBG] [2025-05-28 19:35:09.557] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:09.557 +07:00 [DBG] [2025-05-28 19:35:09.557] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:09.583 +07:00 [INF] [2025-05-28 19:35:09.583] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:09.590 +07:00 [INF] [2025-05-28 19:35:09.590] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 19:35:09.598 +07:00 [INF] [2025-05-28 19:35:09.598] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 19:35:12.245 +07:00 [DBG] [2025-05-28 19:35:12.245] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Mậu Binh
2025-05-28 19:35:12.251 +07:00 [INF] [2025-05-28 19:35:12.251] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:35:12.257 +07:00 [DBG] [2025-05-28 19:35:12.257] [DEBUG]: Đã khởi tạo giao diện với 4 panel
2025-05-28 19:35:12.258 +07:00 [DBG] [2025-05-28 19:35:12.258] [DEBUG]: Đã khởi tạo MauBinhSuggestionForm
2025-05-28 19:35:12.686 +07:00 [INF] [2025-05-28 19:35:12.686] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:35:12.687 +07:00 [INF] [2025-05-28 19:35:12.687] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:35:12.787 +07:00 [DBG] [2025-05-28 19:35:12.787] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:35:12.789 +07:00 [DBG] [2025-05-28 19:35:12.789] [DEBUG] [nhatrang345]: ℹ️ Không có điều kiện đặc biệt cho nhatrang345, giữ nguyên trong phòng (sit: 0, số người: 4)
2025-05-28 19:35:12.789 +07:00 [WRN] [2025-05-28 19:35:12.789] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 19:35:13.472 +07:00 [INF] [2025-05-28 19:35:13.472] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:35:15.542 +07:00 [INF] [2025-05-28 19:35:15.542] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 19:35:16.109 +07:00 [DBG] [2025-05-28 19:35:16.109] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 19:35:16.382 +07:00 [INF] [2025-05-28 19:35:16.382] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 19:35:16.383 +07:00 [INF] [2025-05-28 19:35:16.383] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 19:35:16.383 +07:00 [INF] [2025-05-28 19:35:16.383] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 19:35:16.383 +07:00 [DBG] [2025-05-28 19:35:16.383] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 19:35:16.386 +07:00 [INF] [2025-05-28 19:35:16.386] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 19:35:16.387 +07:00 [INF] [2025-05-28 19:35:16.387] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 19:35:16.390 +07:00 [INF] [2025-05-28 19:35:16.390] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 19:35:16.401 +07:00 [INF] [2025-05-28 19:35:16.401] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 19:35:16.407 +07:00 [INF] [2025-05-28 19:35:16.407] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 19:35:16.413 +07:00 [INF] [2025-05-28 19:35:16.413] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 19:35:16.416 +07:00 [DBG] [2025-05-28 19:35:16.416] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 19:35:16.416
2025-05-28 19:35:16.416 +07:00 [INF] [2025-05-28 19:35:16.416] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:16.420 +07:00 [INF] [2025-05-28 19:35:16.420] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:16.479 +07:00 [DBG] [2025-05-28 19:35:16.479] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:35:16.479 +07:00 [INF] [2025-05-28 19:35:16.479] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 19:35:16.480 +07:00 [INF] [2025-05-28 19:35:16.480] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 19:35:16.480 +07:00 [DBG] [2025-05-28 19:35:16.480] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 19:35:16.480 +07:00 [INF] [2025-05-28 19:35:16.480] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:16.480 +07:00 [INF] [2025-05-28 19:35:16.480] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:16.480 +07:00 [INF] [2025-05-28 19:35:16.480] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 19:35:16.484 +07:00 [INF] [2025-05-28 19:35:16.484] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:16.484 +07:00 [INF] [2025-05-28 19:35:16.484] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:16.484 +07:00 [ERR] [2025-05-28 19:35:16.484] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 19:35:16.484 +07:00 [INF] [2025-05-28 19:35:16.484] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 89.8521ms
2025-05-28 19:35:16.484 +07:00 [WRN] [2025-05-28 19:35:16.484] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 19:35:16.489 +07:00 [INF] [2025-05-28 19:35:16.489] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 19:35:16.493 +07:00 [WRN] [2025-05-28 19:35:16.493] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 19:35:16.493 +07:00 [INF] [2025-05-28 19:35:16.493] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 6.7862ms
2025-05-28 19:35:16.493 +07:00 [INF] [2025-05-28 19:35:16.493] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 11.7432ms
2025-05-28 19:35:16.493 +07:00 [DBG] [2025-05-28 19:35:16.493] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:16.494 +07:00 [INF] [2025-05-28 19:35:16.494] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 19:35:16.495 +07:00 [DBG] [2025-05-28 19:35:16.495] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:16.495 +07:00 [DBG] [2025-05-28 19:35:16.495] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:16.496 +07:00 [INF] [2025-05-28 19:35:16.496] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:16.496 +07:00 [DBG] [2025-05-28 19:35:16.496] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:16.496 +07:00 [DBG] [2025-05-28 19:35:16.496] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:16.496 +07:00 [INF] [2025-05-28 19:35:16.496] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:16.496 +07:00 [DBG] [2025-05-28 19:35:16.496] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:16.496 +07:00 [DBG] [2025-05-28 19:35:16.496] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:16.496 +07:00 [INF] [2025-05-28 19:35:16.496] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:16.496 +07:00 [DBG] [2025-05-28 19:35:16.496] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:16.496 +07:00 [DBG] [2025-05-28 19:35:16.496] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:16.548 +07:00 [INF] [2025-05-28 19:35:16.548] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 19:35:16.562 +07:00 [INF] [2025-05-28 19:35:16.562] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:16.588 +07:00 [INF] [2025-05-28 19:35:16.588] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.09 giây
2025-05-28 19:35:16.689 +07:00 [INF] [2025-05-28 19:35:16.689] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 19:35:16.689 +07:00 [INF] [2025-05-28 19:35:16.689] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 19:35:17.193 +07:00 [INF] [2025-05-28 19:35:17.193] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 19:35:17.201 +07:00 [INF] [2025-05-28 19:35:17.201] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 19:35:17.205 +07:00 [INF] [2025-05-28 19:35:17.205] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 19:35:17.210 +07:00 [INF] [2025-05-28 19:35:17.210] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 19:35:17.213 +07:00 [DBG] [2025-05-28 19:35:17.213] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 19:35:17.213
2025-05-28 19:35:17.213 +07:00 [INF] [2025-05-28 19:35:17.213] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:17.217 +07:00 [INF] [2025-05-28 19:35:17.217] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:17.379 +07:00 [DBG] [2025-05-28 19:35:17.379] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:35:17.379 +07:00 [INF] [2025-05-28 19:35:17.379] [INFO] [nhatrang345]: ✅ Tìm thấy bàn ít người hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 19:35:17.379 +07:00 [DBG] [2025-05-28 19:35:17.379] [DEBUG] [nhatrang345]: ✅ Báo TaskCompletionSource thành công cho nhatrang345 (shouldAutoLeave=false)
2025-05-28 19:35:17.380 +07:00 [INF] [2025-05-28 19:35:17.380] [INFO] [nhatrang345]: Nhận được cmd: 202 cho nhatrang345 (thời gian: 183.0354ms)
2025-05-28 19:35:17.380 +07:00 [INF] [2025-05-28 19:35:17.380] [INFO] [nhatrang345]: 🔄 Bắt đầu sync data từ WebSocketManager cho nhatrang345
2025-05-28 19:35:17.380 +07:00 [INF] [2025-05-28 19:35:17.380] [INFO] [nhatrang345]: ✅ Synced room players for nhatrang345: 1 players
2025-05-28 19:35:17.380 +07:00 [INF] [2025-05-28 19:35:17.380] [INFO] [nhatrang345]: ✅ Synced user seat for nhatrang345: 0
2025-05-28 19:35:17.380 +07:00 [INF] [2025-05-28 19:35:17.380] [INFO] [nhatrang345]: ✅ Synced room time for nhatrang345: 0
2025-05-28 19:35:17.380 +07:00 [INF] [2025-05-28 19:35:17.380] [INFO] [nhatrang345]: 🔄 Hoàn thành sync data cho nhatrang345
2025-05-28 19:35:17.380 +07:00 [INF] [2025-05-28 19:35:17.380] [INFO] [nhatrang345]: ✅ Xác nhận trạng thái phòng cho nhatrang345: sit=0, số người=1
2025-05-28 19:35:17.380 +07:00 [INF] [2025-05-28 19:35:17.380] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 183.6551ms
2025-05-28 19:35:17.380 +07:00 [INF] [2025-05-28 19:35:17.380] [INFO] [nhatrang345]: Vào phòng thành công cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 19:35:17.380 +07:00 [INF] [2025-05-28 19:35:17.380] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 990.3837ms
2025-05-28 19:35:17.380 +07:00 [DBG] [2025-05-28 19:35:17.380] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:17.380 +07:00 [DBG] [2025-05-28 19:35:17.380] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:17.381 +07:00 [INF] [2025-05-28 19:35:17.381] [INFO]: Đã đặt nhatrang345 làm MainUser với seat 0
2025-05-28 19:35:17.382 +07:00 [INF] [2025-05-28 19:35:17.382] [INFO]: Đã cập nhật token.txt
2025-05-28 19:35:17.382 +07:00 [INF] [2025-05-28 19:35:17.382] [INFO]: Đã cập nhật trạng thái IsMainUser cho nhatrang345
2025-05-28 19:35:17.382 +07:00 [INF] [2025-05-28 19:35:17.382] [INFO] [nhatrang345]: ✅ Tìm thấy bàn trống hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 19:35:17.388 +07:00 [INF] [2025-05-28 19:35:17.388] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:17.388 +07:00 [DBG] [2025-05-28 19:35:17.388] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:17.388 +07:00 [DBG] [2025-05-28 19:35:17.388] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:17.388 +07:00 [INF] [2025-05-28 19:35:17.388] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:17.388 +07:00 [DBG] [2025-05-28 19:35:17.388] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:17.388 +07:00 [DBG] [2025-05-28 19:35:17.388] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:17.388 +07:00 [INF] [2025-05-28 19:35:17.388] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:17.388 +07:00 [DBG] [2025-05-28 19:35:17.388] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:17.388 +07:00 [DBG] [2025-05-28 19:35:17.388] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:17.475 +07:00 [INF] [2025-05-28 19:35:17.475] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:17.511 +07:00 [INF] [2025-05-28 19:35:17.511] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.13 giây
2025-05-28 19:35:17.685 +07:00 [INF] [2025-05-28 19:35:17.685] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:35:17.685 +07:00 [INF] [2025-05-28 19:35:17.685] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:35:18.381 +07:00 [INF] [2025-05-28 19:35:18.381] [INFO] [nhatrang345]: Đã xóa trạng thái phòng cho nhatrang345
2025-05-28 19:35:18.381 +07:00 [INF] [2025-05-28 19:35:18.381] [INFO]: Đã hủy lấy bàn trống
2025-05-28 19:35:18.381 +07:00 [INF] [2025-05-28 19:35:18.381] [INFO] [nhatrang345]: Hoàn thành xử lý cho nhatrang345, thời gian: 1994.2492ms
2025-05-28 19:35:18.381 +07:00 [DBG] [2025-05-28 19:35:18.381] [DEBUG] [nhatrang345]: ❌ Tắt chế độ Get Empty Table cho nhatrang345
2025-05-28 19:35:18.381 +07:00 [DBG] [2025-05-28 19:35:18.381] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:18.381 +07:00 [DBG] [2025-05-28 19:35:18.381] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:18.381 +07:00 [INF] [2025-05-28 19:35:18.381] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:18.381 +07:00 [DBG] [2025-05-28 19:35:18.381] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:18.381 +07:00 [DBG] [2025-05-28 19:35:18.381] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:18.381 +07:00 [INF] [2025-05-28 19:35:18.381] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:18.382 +07:00 [DBG] [2025-05-28 19:35:18.382] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:18.382 +07:00 [DBG] [2025-05-28 19:35:18.382] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:18.382 +07:00 [INF] [2025-05-28 19:35:18.382] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:18.382 +07:00 [DBG] [2025-05-28 19:35:18.382] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:18.382 +07:00 [DBG] [2025-05-28 19:35:18.382] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:18.469 +07:00 [INF] [2025-05-28 19:35:18.469] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:35:18.470 +07:00 [INF] [2025-05-28 19:35:18.470] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:18.495 +07:00 [INF] [2025-05-28 19:35:18.495] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 19:35:20.234 +07:00 [INF] [2025-05-28 19:35:20.234] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 19:35:21.422 +07:00 [DBG] [2025-05-28 19:35:21.422] [DEBUG] [nhatrang345]: Làm mới trạng thái phòng cho nhatrang345
2025-05-28 19:35:21.619 +07:00 [INF] [2025-05-28 19:35:21.619] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 19:35:21.619 +07:00 [INF] [2025-05-28 19:35:21.619] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 19:35:21.620 +07:00 [INF] [2025-05-28 19:35:21.620] [INFO]: Bắt đầu lấy bàn trống với maxAttempts: 10, roomId: 100, delayJoinRoom: 0, delaySwitchUser: 1000, attemptDelay: 700
2025-05-28 19:35:21.620 +07:00 [DBG] [2025-05-28 19:35:21.620] [DEBUG] [nhatrang345]: ✅ Đặt nhatrang345 vào chế độ Get Empty Table
2025-05-28 19:35:21.623 +07:00 [INF] [2025-05-28 19:35:21.623] [INFO] [nhatrang345]: Đang thử lấy bàn trống với nhatrang345
2025-05-28 19:35:21.623 +07:00 [INF] [2025-05-28 19:35:21.623] [INFO] [nhatrang345]: Thử lần 1/10 cho nhatrang345
2025-05-28 19:35:21.625 +07:00 [INF] [2025-05-28 19:35:21.625] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 19:35:21.633 +07:00 [INF] [2025-05-28 19:35:21.633] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 19:35:21.637 +07:00 [INF] [2025-05-28 19:35:21.637] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 19:35:21.642 +07:00 [INF] [2025-05-28 19:35:21.642] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 19:35:21.644 +07:00 [DBG] [2025-05-28 19:35:21.644] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 19:35:21.644
2025-05-28 19:35:21.644 +07:00 [INF] [2025-05-28 19:35:21.644] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:21.649 +07:00 [INF] [2025-05-28 19:35:21.649] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:21.706 +07:00 [DBG] [2025-05-28 19:35:21.706] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:35:21.706 +07:00 [INF] [2025-05-28 19:35:21.706] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 19:35:21.706 +07:00 [INF] [2025-05-28 19:35:21.706] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 19:35:21.706 +07:00 [DBG] [2025-05-28 19:35:21.706] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 19:35:21.706 +07:00 [INF] [2025-05-28 19:35:21.706] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 19:35:21.706 +07:00 [INF] [2025-05-28 19:35:21.706] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:21.706 +07:00 [INF] [2025-05-28 19:35:21.706] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:21.711 +07:00 [INF] [2025-05-28 19:35:21.711] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:21.711 +07:00 [INF] [2025-05-28 19:35:21.711] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:21.711 +07:00 [ERR] [2025-05-28 19:35:21.711] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 19:35:21.711 +07:00 [INF] [2025-05-28 19:35:21.711] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 83.596ms
2025-05-28 19:35:21.711 +07:00 [WRN] [2025-05-28 19:35:21.711] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 19:35:21.715 +07:00 [INF] [2025-05-28 19:35:21.715] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 19:35:21.718 +07:00 [WRN] [2025-05-28 19:35:21.718] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 19:35:21.718 +07:00 [INF] [2025-05-28 19:35:21.718] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 9.234ms
2025-05-28 19:35:21.718 +07:00 [INF] [2025-05-28 19:35:21.718] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 12.2837ms
2025-05-28 19:35:21.718 +07:00 [DBG] [2025-05-28 19:35:21.718] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:21.718 +07:00 [INF] [2025-05-28 19:35:21.718] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 19:35:21.718 +07:00 [DBG] [2025-05-28 19:35:21.718] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:21.718 +07:00 [DBG] [2025-05-28 19:35:21.718] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:21.720 +07:00 [INF] [2025-05-28 19:35:21.720] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:21.720 +07:00 [DBG] [2025-05-28 19:35:21.720] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:21.720 +07:00 [DBG] [2025-05-28 19:35:21.720] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:21.720 +07:00 [INF] [2025-05-28 19:35:21.720] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:21.720 +07:00 [DBG] [2025-05-28 19:35:21.720] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:21.720 +07:00 [DBG] [2025-05-28 19:35:21.720] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:21.720 +07:00 [INF] [2025-05-28 19:35:21.720] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:21.720 +07:00 [DBG] [2025-05-28 19:35:21.720] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:21.720 +07:00 [DBG] [2025-05-28 19:35:21.720] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:21.759 +07:00 [INF] [2025-05-28 19:35:21.759] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 19:35:21.789 +07:00 [INF] [2025-05-28 19:35:21.789] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:21.812 +07:00 [INF] [2025-05-28 19:35:21.812] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.09 giây
2025-05-28 19:35:21.884 +07:00 [INF] [2025-05-28 19:35:21.884] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 19:35:21.884 +07:00 [INF] [2025-05-28 19:35:21.884] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 19:35:22.414 +07:00 [INF] [2025-05-28 19:35:22.414] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 19:35:22.422 +07:00 [INF] [2025-05-28 19:35:22.422] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 19:35:22.426 +07:00 [INF] [2025-05-28 19:35:22.426] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 19:35:22.431 +07:00 [INF] [2025-05-28 19:35:22.431] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 19:35:22.434 +07:00 [DBG] [2025-05-28 19:35:22.434] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 19:35:22.434
2025-05-28 19:35:22.434 +07:00 [INF] [2025-05-28 19:35:22.434] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:22.437 +07:00 [INF] [2025-05-28 19:35:22.437] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:22.504 +07:00 [DBG] [2025-05-28 19:35:22.504] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:35:22.504 +07:00 [INF] [2025-05-28 19:35:22.504] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 3, số người: 4), tự động thoát phòng
2025-05-28 19:35:22.504 +07:00 [INF] [2025-05-28 19:35:22.504] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 19:35:22.504 +07:00 [DBG] [2025-05-28 19:35:22.504] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 19:35:22.504 +07:00 [INF] [2025-05-28 19:35:22.504] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 19:35:22.504 +07:00 [INF] [2025-05-28 19:35:22.504] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:22.504 +07:00 [INF] [2025-05-28 19:35:22.504] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:22.509 +07:00 [INF] [2025-05-28 19:35:22.509] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:22.509 +07:00 [INF] [2025-05-28 19:35:22.509] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:22.509 +07:00 [ERR] [2025-05-28 19:35:22.509] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 19:35:22.509 +07:00 [INF] [2025-05-28 19:35:22.509] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 91.929ms
2025-05-28 19:35:22.509 +07:00 [WRN] [2025-05-28 19:35:22.509] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 19:35:22.511 +07:00 [INF] [2025-05-28 19:35:22.511] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 19:35:22.515 +07:00 [WRN] [2025-05-28 19:35:22.515] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 19:35:22.515 +07:00 [INF] [2025-05-28 19:35:22.515] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 8.7314ms
2025-05-28 19:35:22.515 +07:00 [INF] [2025-05-28 19:35:22.515] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 11.4177ms
2025-05-28 19:35:22.515 +07:00 [DBG] [2025-05-28 19:35:22.515] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:22.515 +07:00 [INF] [2025-05-28 19:35:22.515] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 19:35:22.515 +07:00 [DBG] [2025-05-28 19:35:22.515] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:22.515 +07:00 [DBG] [2025-05-28 19:35:22.515] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:22.517 +07:00 [INF] [2025-05-28 19:35:22.517] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:22.517 +07:00 [DBG] [2025-05-28 19:35:22.517] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:22.517 +07:00 [DBG] [2025-05-28 19:35:22.517] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:22.517 +07:00 [INF] [2025-05-28 19:35:22.517] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:22.517 +07:00 [DBG] [2025-05-28 19:35:22.517] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:22.517 +07:00 [DBG] [2025-05-28 19:35:22.517] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:22.517 +07:00 [INF] [2025-05-28 19:35:22.517] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:22.517 +07:00 [DBG] [2025-05-28 19:35:22.517] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:22.517 +07:00 [DBG] [2025-05-28 19:35:22.517] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:22.560 +07:00 [INF] [2025-05-28 19:35:22.560] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:22.568 +07:00 [INF] [2025-05-28 19:35:22.568] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 19:35:22.575 +07:00 [INF] [2025-05-28 19:35:22.575] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 19:35:22.692 +07:00 [INF] [2025-05-28 19:35:22.692] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:35:22.692 +07:00 [INF] [2025-05-28 19:35:22.692] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:35:22.701 +07:00 [INF] [2025-05-28 19:35:22.701] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 19:35:22.701 +07:00 [INF] [2025-05-28 19:35:22.701] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 19:35:23.225 +07:00 [INF] [2025-05-28 19:35:23.225] [INFO] [nhatrang345]: Thử vào phòng lần 3/3 cho nhatrang345
2025-05-28 19:35:23.233 +07:00 [INF] [2025-05-28 19:35:23.233] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 19:35:23.237 +07:00 [INF] [2025-05-28 19:35:23.237] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 19:35:23.242 +07:00 [INF] [2025-05-28 19:35:23.242] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 19:35:23.244 +07:00 [DBG] [2025-05-28 19:35:23.244] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 19:35:23.244
2025-05-28 19:35:23.244 +07:00 [INF] [2025-05-28 19:35:23.244] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:23.248 +07:00 [INF] [2025-05-28 19:35:23.248] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:23.321 +07:00 [DBG] [2025-05-28 19:35:23.321] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:35:23.321 +07:00 [INF] [2025-05-28 19:35:23.321] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 19:35:23.321 +07:00 [INF] [2025-05-28 19:35:23.321] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 19:35:23.321 +07:00 [DBG] [2025-05-28 19:35:23.321] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 19:35:23.321 +07:00 [INF] [2025-05-28 19:35:23.321] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 19:35:23.321 +07:00 [INF] [2025-05-28 19:35:23.321] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:23.321 +07:00 [INF] [2025-05-28 19:35:23.321] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:23.327 +07:00 [INF] [2025-05-28 19:35:23.327] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:23.327 +07:00 [INF] [2025-05-28 19:35:23.327] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:23.327 +07:00 [ERR] [2025-05-28 19:35:23.327] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 19:35:23.327 +07:00 [INF] [2025-05-28 19:35:23.327] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 98.3858ms
2025-05-28 19:35:23.327 +07:00 [WRN] [2025-05-28 19:35:23.327] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 19:35:23.327 +07:00 [WRN] [2025-05-28 19:35:23.327] [WARNING] [nhatrang345]: Không thể vào phòng sau 3 lần thử cho nhatrang345
2025-05-28 19:35:23.327 +07:00 [INF] [2025-05-28 19:35:23.327] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 1701.1351ms
2025-05-28 19:35:23.327 +07:00 [DBG] [2025-05-28 19:35:23.327] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:23.327 +07:00 [DBG] [2025-05-28 19:35:23.327] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:23.327 +07:00 [WRN] [2025-05-28 19:35:23.327] [WARNING] [nhatrang345]: ❌ Vào phòng thất bại cho nhatrang345, thử lại sau 700ms
2025-05-28 19:35:23.327 +07:00 [INF] [2025-05-28 19:35:23.327] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:23.327 +07:00 [DBG] [2025-05-28 19:35:23.327] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:23.327 +07:00 [DBG] [2025-05-28 19:35:23.327] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:23.327 +07:00 [INF] [2025-05-28 19:35:23.327] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:23.327 +07:00 [DBG] [2025-05-28 19:35:23.327] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:23.327 +07:00 [DBG] [2025-05-28 19:35:23.327] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:23.327 +07:00 [INF] [2025-05-28 19:35:23.327] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:23.327 +07:00 [DBG] [2025-05-28 19:35:23.327] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:23.327 +07:00 [DBG] [2025-05-28 19:35:23.327] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:23.329 +07:00 [INF] [2025-05-28 19:35:23.329] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 19:35:23.333 +07:00 [WRN] [2025-05-28 19:35:23.333] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 19:35:23.333 +07:00 [INF] [2025-05-28 19:35:23.333] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 9.0009ms
2025-05-28 19:35:23.333 +07:00 [INF] [2025-05-28 19:35:23.333] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 11.7535ms
2025-05-28 19:35:23.333 +07:00 [DBG] [2025-05-28 19:35:23.333] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:23.333 +07:00 [INF] [2025-05-28 19:35:23.333] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 19:35:23.373 +07:00 [INF] [2025-05-28 19:35:23.373] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 19:35:23.419 +07:00 [INF] [2025-05-28 19:35:23.419] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:23.419 +07:00 [DBG] [2025-05-28 19:35:23.419] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:23.419 +07:00 [DBG] [2025-05-28 19:35:23.419] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:23.441 +07:00 [INF] [2025-05-28 19:35:23.441] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 19:35:23.441 +07:00 [INF] [2025-05-28 19:35:23.441] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:23.441 +07:00 [DBG] [2025-05-28 19:35:23.441] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:23.441 +07:00 [DBG] [2025-05-28 19:35:23.441] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:23.441 +07:00 [INF] [2025-05-28 19:35:23.441] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:23.441 +07:00 [DBG] [2025-05-28 19:35:23.441] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:23.441 +07:00 [DBG] [2025-05-28 19:35:23.441] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:23.441 +07:00 [INF] [2025-05-28 19:35:23.441] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:23.441 +07:00 [DBG] [2025-05-28 19:35:23.441] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:23.442 +07:00 [DBG] [2025-05-28 19:35:23.442] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:23.474 +07:00 [INF] [2025-05-28 19:35:23.474] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:35:23.522 +07:00 [INF] [2025-05-28 19:35:23.522] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 19:35:23.522 +07:00 [INF] [2025-05-28 19:35:23.522] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 19:35:23.530 +07:00 [INF] [2025-05-28 19:35:23.530] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:23.553 +07:00 [INF] [2025-05-28 19:35:23.553] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.13 giây
2025-05-28 19:35:24.047 +07:00 [INF] [2025-05-28 19:35:24.047] [INFO] [nhatrang345]: Thử lần 2/10 cho nhatrang345
2025-05-28 19:35:24.050 +07:00 [INF] [2025-05-28 19:35:24.050] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 19:35:24.057 +07:00 [INF] [2025-05-28 19:35:24.057] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 19:35:24.061 +07:00 [INF] [2025-05-28 19:35:24.061] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 19:35:24.065 +07:00 [INF] [2025-05-28 19:35:24.065] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 19:35:24.068 +07:00 [DBG] [2025-05-28 19:35:24.068] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 19:35:24.068
2025-05-28 19:35:24.068 +07:00 [INF] [2025-05-28 19:35:24.068] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:24.071 +07:00 [INF] [2025-05-28 19:35:24.071] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:24.387 +07:00 [DBG] [2025-05-28 19:35:24.387] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:35:24.387 +07:00 [INF] [2025-05-28 19:35:24.387] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 19:35:24.387 +07:00 [INF] [2025-05-28 19:35:24.387] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 19:35:24.387 +07:00 [DBG] [2025-05-28 19:35:24.387] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 19:35:24.387 +07:00 [INF] [2025-05-28 19:35:24.387] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 19:35:24.387 +07:00 [INF] [2025-05-28 19:35:24.387] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:24.387 +07:00 [INF] [2025-05-28 19:35:24.387] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:24.393 +07:00 [INF] [2025-05-28 19:35:24.393] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:24.393 +07:00 [INF] [2025-05-28 19:35:24.393] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:24.393 +07:00 [ERR] [2025-05-28 19:35:24.393] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 19:35:24.393 +07:00 [INF] [2025-05-28 19:35:24.393] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 340.7412ms
2025-05-28 19:35:24.393 +07:00 [WRN] [2025-05-28 19:35:24.393] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 19:35:24.395 +07:00 [INF] [2025-05-28 19:35:24.395] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 19:35:24.400 +07:00 [WRN] [2025-05-28 19:35:24.400] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 19:35:24.400 +07:00 [INF] [2025-05-28 19:35:24.400] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 10.2923ms
2025-05-28 19:35:24.400 +07:00 [INF] [2025-05-28 19:35:24.400] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 13.022ms
2025-05-28 19:35:24.400 +07:00 [DBG] [2025-05-28 19:35:24.400] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:24.401 +07:00 [INF] [2025-05-28 19:35:24.401] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 19:35:24.401 +07:00 [DBG] [2025-05-28 19:35:24.401] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:24.401 +07:00 [DBG] [2025-05-28 19:35:24.401] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:24.402 +07:00 [INF] [2025-05-28 19:35:24.402] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:24.402 +07:00 [DBG] [2025-05-28 19:35:24.402] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:24.402 +07:00 [DBG] [2025-05-28 19:35:24.402] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:24.402 +07:00 [INF] [2025-05-28 19:35:24.402] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:24.402 +07:00 [DBG] [2025-05-28 19:35:24.402] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:24.403 +07:00 [DBG] [2025-05-28 19:35:24.403] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:24.403 +07:00 [INF] [2025-05-28 19:35:24.403] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:24.403 +07:00 [DBG] [2025-05-28 19:35:24.403] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:24.403 +07:00 [DBG] [2025-05-28 19:35:24.403] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:24.442 +07:00 [INF] [2025-05-28 19:35:24.442] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 19:35:24.487 +07:00 [INF] [2025-05-28 19:35:24.487] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:24.510 +07:00 [INF] [2025-05-28 19:35:24.510] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 19:35:24.602 +07:00 [INF] [2025-05-28 19:35:24.602] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 19:35:24.602 +07:00 [INF] [2025-05-28 19:35:24.602] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 19:35:25.098 +07:00 [INF] [2025-05-28 19:35:25.098] [INFO] [nhatrang345]: Thử vào phòng lần 2/3 cho nhatrang345
2025-05-28 19:35:25.106 +07:00 [INF] [2025-05-28 19:35:25.106] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 19:35:25.110 +07:00 [INF] [2025-05-28 19:35:25.110] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 19:35:25.115 +07:00 [INF] [2025-05-28 19:35:25.115] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 19:35:25.117 +07:00 [DBG] [2025-05-28 19:35:25.117] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 19:35:25.117
2025-05-28 19:35:25.117 +07:00 [INF] [2025-05-28 19:35:25.117] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:25.120 +07:00 [INF] [2025-05-28 19:35:25.120] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:25.265 +07:00 [DBG] [2025-05-28 19:35:25.265] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:35:25.265 +07:00 [INF] [2025-05-28 19:35:25.265] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 0, số người: 4), tự động thoát phòng
2025-05-28 19:35:25.265 +07:00 [INF] [2025-05-28 19:35:25.265] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 19:35:25.265 +07:00 [DBG] [2025-05-28 19:35:25.265] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 19:35:25.265 +07:00 [INF] [2025-05-28 19:35:25.265] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 19:35:25.265 +07:00 [INF] [2025-05-28 19:35:25.265] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:25.265 +07:00 [INF] [2025-05-28 19:35:25.265] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:25.270 +07:00 [INF] [2025-05-28 19:35:25.270] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:25.270 +07:00 [INF] [2025-05-28 19:35:25.270] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:25.270 +07:00 [ERR] [2025-05-28 19:35:25.270] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 19:35:25.270 +07:00 [INF] [2025-05-28 19:35:25.270] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 169.5077ms
2025-05-28 19:35:25.270 +07:00 [WRN] [2025-05-28 19:35:25.270] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 19:35:25.272 +07:00 [INF] [2025-05-28 19:35:25.272] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 19:35:25.276 +07:00 [WRN] [2025-05-28 19:35:25.276] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 19:35:25.276 +07:00 [INF] [2025-05-28 19:35:25.276] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 7.7597ms
2025-05-28 19:35:25.276 +07:00 [INF] [2025-05-28 19:35:25.276] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 10.548ms
2025-05-28 19:35:25.276 +07:00 [DBG] [2025-05-28 19:35:25.276] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:25.276 +07:00 [INF] [2025-05-28 19:35:25.276] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 19:35:25.276 +07:00 [DBG] [2025-05-28 19:35:25.276] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:25.276 +07:00 [DBG] [2025-05-28 19:35:25.276] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:25.277 +07:00 [INF] [2025-05-28 19:35:25.277] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:25.277 +07:00 [DBG] [2025-05-28 19:35:25.277] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:25.277 +07:00 [DBG] [2025-05-28 19:35:25.277] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:25.277 +07:00 [INF] [2025-05-28 19:35:25.277] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:25.277 +07:00 [DBG] [2025-05-28 19:35:25.277] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:25.277 +07:00 [DBG] [2025-05-28 19:35:25.277] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:25.277 +07:00 [INF] [2025-05-28 19:35:25.277] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:25.277 +07:00 [DBG] [2025-05-28 19:35:25.277] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:25.277 +07:00 [DBG] [2025-05-28 19:35:25.277] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:25.330 +07:00 [INF] [2025-05-28 19:35:25.330] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 19:35:25.360 +07:00 [INF] [2025-05-28 19:35:25.360] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:25.385 +07:00 [INF] [2025-05-28 19:35:25.385] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 19:35:25.451 +07:00 [INF] [2025-05-28 19:35:25.451] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 19:35:25.451 +07:00 [INF] [2025-05-28 19:35:25.451] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 19:35:25.978 +07:00 [INF] [2025-05-28 19:35:25.978] [INFO] [nhatrang345]: Thử vào phòng lần 3/3 cho nhatrang345
2025-05-28 19:35:25.986 +07:00 [INF] [2025-05-28 19:35:25.986] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 19:35:25.990 +07:00 [INF] [2025-05-28 19:35:25.990] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 19:35:25.994 +07:00 [INF] [2025-05-28 19:35:25.994] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 19:35:25.997 +07:00 [DBG] [2025-05-28 19:35:25.997] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 19:35:25.997
2025-05-28 19:35:25.997 +07:00 [INF] [2025-05-28 19:35:25.997] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:26.001 +07:00 [INF] [2025-05-28 19:35:26.001] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:26.061 +07:00 [DBG] [2025-05-28 19:35:26.061] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:35:26.061 +07:00 [INF] [2025-05-28 19:35:26.061] [INFO] [nhatrang345]: ❌ Bàn không hợp lệ cho Get Empty Table nhatrang345 (sit: 3, số người: 4), tự động thoát phòng
2025-05-28 19:35:26.061 +07:00 [INF] [2025-05-28 19:35:26.061] [INFO] [nhatrang345]: 🚪 Bắt đầu tự động thoát phòng cho nhatrang345 - Lý do: Bàn không ít người
2025-05-28 19:35:26.061 +07:00 [DBG] [2025-05-28 19:35:26.061] [DEBUG] [nhatrang345]: 🔄 Báo TaskCompletionSource thất bại cho nhatrang345 (shouldAutoLeave=true)
2025-05-28 19:35:26.061 +07:00 [INF] [2025-05-28 19:35:26.061] [INFO] [nhatrang345]: 🔄 Đang thực hiện thoát phòng cho nhatrang345...
2025-05-28 19:35:26.061 +07:00 [INF] [2025-05-28 19:35:26.061] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:26.061 +07:00 [INF] [2025-05-28 19:35:26.061] [INFO] [nhatrang345]: Thử lần 2/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:26.066 +07:00 [INF] [2025-05-28 19:35:26.066] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:26.066 +07:00 [INF] [2025-05-28 19:35:26.066] [INFO] [nhatrang345]: ⚡ BaseWebSocketHandler đã tự động thoát phòng cho nhatrang345, thử lại ngay
2025-05-28 19:35:26.066 +07:00 [ERR] [2025-05-28 19:35:26.066] [ERROR] [nhatrang345]: Không thể vào phòng 100 sau 2 lần thử cho nhatrang345
2025-05-28 19:35:26.066 +07:00 [INF] [2025-05-28 19:35:26.066] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 84.9898ms
2025-05-28 19:35:26.066 +07:00 [WRN] [2025-05-28 19:35:26.066] [WARNING] [nhatrang345]: Vào phòng thất bại cho nhatrang345, thử lại
2025-05-28 19:35:26.066 +07:00 [WRN] [2025-05-28 19:35:26.066] [WARNING] [nhatrang345]: Không thể vào phòng sau 3 lần thử cho nhatrang345
2025-05-28 19:35:26.066 +07:00 [INF] [2025-05-28 19:35:26.066] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 2016.5029ms
2025-05-28 19:35:26.066 +07:00 [DBG] [2025-05-28 19:35:26.066] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:26.066 +07:00 [DBG] [2025-05-28 19:35:26.066] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:26.066 +07:00 [WRN] [2025-05-28 19:35:26.066] [WARNING] [nhatrang345]: ❌ Vào phòng thất bại cho nhatrang345, thử lại sau 700ms
2025-05-28 19:35:26.067 +07:00 [INF] [2025-05-28 19:35:26.067] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:26.067 +07:00 [DBG] [2025-05-28 19:35:26.067] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:26.067 +07:00 [DBG] [2025-05-28 19:35:26.067] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:26.067 +07:00 [INF] [2025-05-28 19:35:26.067] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:26.067 +07:00 [DBG] [2025-05-28 19:35:26.067] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:26.067 +07:00 [DBG] [2025-05-28 19:35:26.067] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:26.067 +07:00 [INF] [2025-05-28 19:35:26.067] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:26.067 +07:00 [DBG] [2025-05-28 19:35:26.067] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:26.067 +07:00 [DBG] [2025-05-28 19:35:26.067] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:26.068 +07:00 [INF] [2025-05-28 19:35:26.068] [INFO] [nhatrang345]: Đang thử rời phòng bằng JavaScript cho nhatrang345
2025-05-28 19:35:26.072 +07:00 [WRN] [2025-05-28 19:35:26.072] [WARNING] [nhatrang345]: JavaScript không thành công, thử phương pháp click thông thường cho nhatrang345
2025-05-28 19:35:26.072 +07:00 [INF] [2025-05-28 19:35:26.072] [INFO] [nhatrang345]: Hoàn thành LeaveRoomWithJavaScript cho nhatrang345, thời gian: 7.9726ms
2025-05-28 19:35:26.072 +07:00 [INF] [2025-05-28 19:35:26.072] [INFO] [nhatrang345]: Hoàn thành LeaveRoomAsync cho nhatrang345, thời gian: 10.8658ms
2025-05-28 19:35:26.072 +07:00 [DBG] [2025-05-28 19:35:26.072] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:26.072 +07:00 [INF] [2025-05-28 19:35:26.072] [INFO] [nhatrang345]: ✅ Tự động thoát phòng thành công cho nhatrang345
2025-05-28 19:35:26.115 +07:00 [INF] [2025-05-28 19:35:26.115] [INFO] [nhatrang345]: Confirmed room leave for nhatrang345
2025-05-28 19:35:26.116 +07:00 [INF] [2025-05-28 19:35:26.116] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:26.116 +07:00 [DBG] [2025-05-28 19:35:26.116] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:26.116 +07:00 [DBG] [2025-05-28 19:35:26.116] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:26.126 +07:00 [INF] [2025-05-28 19:35:26.126] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 19:35:26.127 +07:00 [INF] [2025-05-28 19:35:26.127] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:26.127 +07:00 [DBG] [2025-05-28 19:35:26.127] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:26.127 +07:00 [DBG] [2025-05-28 19:35:26.127] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:26.127 +07:00 [INF] [2025-05-28 19:35:26.127] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:26.127 +07:00 [DBG] [2025-05-28 19:35:26.127] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:26.127 +07:00 [DBG] [2025-05-28 19:35:26.127] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:26.127 +07:00 [INF] [2025-05-28 19:35:26.127] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:26.127 +07:00 [DBG] [2025-05-28 19:35:26.127] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:26.127 +07:00 [DBG] [2025-05-28 19:35:26.127] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:26.170 +07:00 [INF] [2025-05-28 19:35:26.170] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:26.179 +07:00 [INF] [2025-05-28 19:35:26.179] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 19:35:26.252 +07:00 [INF] [2025-05-28 19:35:26.252] [INFO]: Kích thước ảnh chụp: 684x405, Kích thước img/phongcho_hitclub.png: 46x24
2025-05-28 19:35:26.252 +07:00 [INF] [2025-05-28 19:35:26.252] [INFO]: Tìm thấy img/phongcho_hitclub.png trong ảnh chụp: True, Giá trị tương đồng: 0.5722825527191162, Vị trí: (613, 367)
2025-05-28 19:35:26.781 +07:00 [INF] [2025-05-28 19:35:26.781] [INFO] [nhatrang345]: Thử lần 3/10 cho nhatrang345
2025-05-28 19:35:26.784 +07:00 [INF] [2025-05-28 19:35:26.784] [INFO] [nhatrang345]: Thử vào phòng lần 1/3 cho nhatrang345
2025-05-28 19:35:26.791 +07:00 [INF] [2025-05-28 19:35:26.791] [INFO] [nhatrang345]: Đang tìm canvas bằng XPath: //canvas[@id='GameCanvas'] cho nhatrang345
2025-05-28 19:35:26.795 +07:00 [INF] [2025-05-28 19:35:26.795] [INFO] [nhatrang345]: Đã tìm thấy canvas cho nhatrang345
2025-05-28 19:35:26.800 +07:00 [INF] [2025-05-28 19:35:26.800] [INFO] [nhatrang345]: Kích thước canvas cho nhatrang345: 684x405
2025-05-28 19:35:26.802 +07:00 [DBG] [2025-05-28 19:35:26.802] [DEBUG] [nhatrang345]: Created TaskCompletionSource for nhatrang345 in JoinRoom at 19:35:26.802
2025-05-28 19:35:26.802 +07:00 [INF] [2025-05-28 19:35:26.802] [INFO] [nhatrang345]: Thử lần 1/2 click vào phòng 100 cho nhatrang345
2025-05-28 19:35:26.806 +07:00 [INF] [2025-05-28 19:35:26.806] [INFO] [nhatrang345]: Click lần 1 thành công vào phòng 100 tại tọa độ (200, 139) cho nhatrang345
2025-05-28 19:35:26.877 +07:00 [DBG] [2025-05-28 19:35:26.877] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO] [nhatrang345]: ✅ Tìm thấy bàn ít người hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 19:35:26.877 +07:00 [DBG] [2025-05-28 19:35:26.877] [DEBUG] [nhatrang345]: ✅ Báo TaskCompletionSource thành công cho nhatrang345 (shouldAutoLeave=false)
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO] [nhatrang345]: Nhận được cmd: 202 cho nhatrang345 (thời gian: 90.8583ms)
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO] [nhatrang345]: 🔄 Bắt đầu sync data từ WebSocketManager cho nhatrang345
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO] [nhatrang345]: ✅ Synced room players for nhatrang345: 1 players
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO] [nhatrang345]: ✅ Synced user seat for nhatrang345: 0
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO] [nhatrang345]: ✅ Synced room time for nhatrang345: 0
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO] [nhatrang345]: 🔄 Hoàn thành sync data cho nhatrang345
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO] [nhatrang345]: ✅ Xác nhận trạng thái phòng cho nhatrang345: sit=0, số người=1
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO] [nhatrang345]: Hoàn thành JoinRoom cho nhatrang345, thời gian: 90.9238ms
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO] [nhatrang345]: Vào phòng thành công cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO] [nhatrang345]: Hoàn thành JoinRoomAsync cho nhatrang345, thời gian: 93.367ms
2025-05-28 19:35:26.877 +07:00 [DBG] [2025-05-28 19:35:26.877] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:26.877 +07:00 [DBG] [2025-05-28 19:35:26.877] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:26.877 +07:00 [INF] [2025-05-28 19:35:26.877] [INFO]: Đã đặt nhatrang345 làm MainUser với seat 0
2025-05-28 19:35:26.878 +07:00 [INF] [2025-05-28 19:35:26.878] [INFO]: Đã cập nhật token.txt
2025-05-28 19:35:26.878 +07:00 [INF] [2025-05-28 19:35:26.878] [INFO]: Đã cập nhật trạng thái IsMainUser cho nhatrang345
2025-05-28 19:35:26.878 +07:00 [INF] [2025-05-28 19:35:26.878] [INFO] [nhatrang345]: ✅ Tìm thấy bàn trống hợp lệ cho nhatrang345 (sit: 0, số người: 1)
2025-05-28 19:35:26.883 +07:00 [INF] [2025-05-28 19:35:26.883] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:26.883 +07:00 [DBG] [2025-05-28 19:35:26.883] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:26.883 +07:00 [DBG] [2025-05-28 19:35:26.883] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:26.883 +07:00 [INF] [2025-05-28 19:35:26.883] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:26.883 +07:00 [DBG] [2025-05-28 19:35:26.883] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:26.883 +07:00 [DBG] [2025-05-28 19:35:26.883] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:26.883 +07:00 [INF] [2025-05-28 19:35:26.883] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:26.883 +07:00 [DBG] [2025-05-28 19:35:26.883] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:26.883 +07:00 [DBG] [2025-05-28 19:35:26.883] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:26.962 +07:00 [INF] [2025-05-28 19:35:26.962] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:26.995 +07:00 [INF] [2025-05-28 19:35:26.995] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.12 giây
2025-05-28 19:35:27.706 +07:00 [INF] [2025-05-28 19:35:27.706] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:35:27.706 +07:00 [INF] [2025-05-28 19:35:27.706] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:35:27.960 +07:00 [INF] [2025-05-28 19:35:27.960] [INFO] [nhatrang345]: Đã xóa trạng thái phòng cho nhatrang345
2025-05-28 19:35:27.960 +07:00 [INF] [2025-05-28 19:35:27.960] [INFO]: Đã hủy lấy bàn trống
2025-05-28 19:35:27.960 +07:00 [INF] [2025-05-28 19:35:27.960] [INFO] [nhatrang345]: Hoàn thành xử lý cho nhatrang345, thời gian: 6337.1976ms
2025-05-28 19:35:27.960 +07:00 [DBG] [2025-05-28 19:35:27.960] [DEBUG] [nhatrang345]: ❌ Tắt chế độ Get Empty Table cho nhatrang345
2025-05-28 19:35:27.960 +07:00 [DBG] [2025-05-28 19:35:27.960] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:35:27.960 +07:00 [DBG] [2025-05-28 19:35:27.960] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:35:27.961 +07:00 [INF] [2025-05-28 19:35:27.961] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:35:27.961 +07:00 [DBG] [2025-05-28 19:35:27.961] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:35:27.961 +07:00 [DBG] [2025-05-28 19:35:27.961] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:27.961 +07:00 [INF] [2025-05-28 19:35:27.961] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:35:27.961 +07:00 [DBG] [2025-05-28 19:35:27.961] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:35:27.961 +07:00 [DBG] [2025-05-28 19:35:27.961] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:27.961 +07:00 [INF] [2025-05-28 19:35:27.961] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:35:27.961 +07:00 [DBG] [2025-05-28 19:35:27.961] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:35:27.961 +07:00 [DBG] [2025-05-28 19:35:27.961] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:35:28.045 +07:00 [INF] [2025-05-28 19:35:28.045] [INFO]: Tải danh sách user thành công
2025-05-28 19:35:28.073 +07:00 [INF] [2025-05-28 19:35:28.073] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.11 giây
2025-05-28 19:35:28.471 +07:00 [INF] [2025-05-28 19:35:28.471] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:35:28.708 +07:00 [DBG] [2025-05-28 19:35:28.708] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 600 (Mau Binh cards)
2025-05-28 19:35:28.711 +07:00 [INF] [2025-05-28 19:35:28.711] [INFO]: Saved Mau Binh cards for nhatrang345: [2♣, A♣, 5♦, K♥, 9♥, Q♣, 3♠, 6♠, 3♦, A♠, 2♦, 3♥, 10♣]
2025-05-28 19:35:30.149 +07:00 [DBG] [2025-05-28 19:35:30.149] [DEBUG]: Bắt đầu quét bài, số user: 3
2025-05-28 19:35:30.155 +07:00 [INF] [2025-05-28 19:35:30.155] [INFO]: Updated Mau Binh cards for nhatrang345: [10♣, 3♥, 2♦, A♠, 3♦, 6♠, 3♠, Q♣, 9♥, K♥, 5♦, A♣, 2♣]
2025-05-28 19:35:30.155 +07:00 [INF] [2025-05-28 19:35:30.155] [INFO] [nhatrang345]: Đã quét bài cho nhatrang345 bằng script (MauBinhController): [10♣, 3♥, 2♦, A♠, 3♦, 6♠, 3♠, Q♣, 9♥, K♥, 5♦, A♣, 2♣]
2025-05-28 19:35:30.155 +07:00 [ERR] [2025-05-28 19:35:30.155] [ERROR]: Không tìm thấy driver cho phanthiet989
2025-05-28 19:35:30.155 +07:00 [ERR] [2025-05-28 19:35:30.155] [ERROR]: Không tìm thấy driver cho namdinhx852
2025-05-28 19:35:30.155 +07:00 [WRN] [2025-05-28 19:35:30.155] [WARNING]: Chỉ tìm thấy bài hợp lệ cho 1/3 user
2025-05-28 19:35:30.155 +07:00 [WRN] [2025-05-28 19:35:30.155] [WARNING]: Số user hợp lệ (1) không đủ 3 để tính bài đối thủ
2025-05-28 19:35:30.155 +07:00 [DBG] [2025-05-28 19:35:30.155] [DEBUG]: Bắt đầu UpdateCardDisplay, số user: 1
2025-05-28 19:35:30.156 +07:00 [DBG] [2025-05-28 19:35:30.156] [DEBUG]: Cập nhật bài cho user: nhatrang345, indexKey: user0
2025-05-28 19:35:30.163 +07:00 [INF] [2025-05-28 19:35:30.163] [INFO]: Quét bài thành công, số user: 1, tổng số lá: 13
2025-05-28 19:35:30.809 +07:00 [INF] [2025-05-28 19:35:30.809] [INFO]: 🚀 Tạo gợi ý với thuật toán OPTIMIZED cho 13 lá bài
2025-05-28 19:35:30.810 +07:00 [INF] [2025-05-28 19:35:30.810] [INFO]: 🚀 Bắt đầu thuật toán Mậu Binh theo yêu cầu mới
2025-05-28 19:35:30.810 +07:00 [INF] [2025-05-28 19:35:30.810] [INFO]: 🏆 Kiểm tra đặc biệt tới trắng
2025-05-28 19:35:30.864 +07:00 [ERR] [2025-05-28 19:35:30.864] [ERROR]: ❌ Lỗi thuật toán mới: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 19:35:30.864 +07:00 [WRN] [2025-05-28 19:35:30.864] [WARNING]: 🔄 Fallback sang gợi ý đơn giản (thuật toán cũ bị disable)
2025-05-28 19:35:30.871 +07:00 [INF] [2025-05-28 19:35:30.871] [INFO]: ✅ Tạo được 3 gợi ý fallback đơn giản
2025-05-28 19:35:30.871 +07:00 [INF] [2025-05-28 19:35:30.871] [INFO]: 📊 BÁO CÁO GỢI Ý TỐI ƯU MAU BINH:

🎯 Gợi ý 1: 🎯 Sắp xếp theo rank cao
   Tổng điểm: 1000.0
   Chi 1: [A♠, A♣, K♥, Q♣, 10♣] - 💫 Đôi
   Chi 2: [9♥, 6♠, 5♦, 3♥, 3♦] - 💫 Đôi
   Chi 3: [3♠, 2♦, 2♣] - 💫 Đôi

🎯 Gợi ý 2: 💎 Ưu tiên đôi/xám
   Tổng điểm: 1100.0
   Chi 1: [3♥, 3♦, 2♦, 2♣, A♠] - 👥 Thú
   Chi 2: [A♣, K♥, Q♣, 10♣, 9♥] - 🃏 Mậu Thầu
   Chi 3: [6♠, 5♦, 3♠] - 🃏 Mậu Thầu

🎯 Gợi ý 3: ⚖️ Cân bằng
   Tổng điểm: 1200.0
   Chi 1: [A♠, 6♠, 3♠, A♣, Q♣] - 💫 Đôi
   Chi 2: [10♣, 2♣, 5♦, 3♦, 2♦] - 💫 Đôi
   Chi 3: [K♥, 9♥, 3♥] - 🌊 Thùng


2025-05-28 19:35:30.872 +07:00 [INF] [2025-05-28 19:35:30.872] [INFO] [nhatrang345]: Đã tạo 3 gợi ý cho nhatrang345 (indexKey: user0)
2025-05-28 19:35:30.872 +07:00 [INF] [2025-05-28 19:35:30.872] [INFO]: 📝 AddSuggestion for nhatrang345
2025-05-28 19:35:30.872 +07:00 [INF] [2025-05-28 19:35:30.872] [INFO]: 📝 AddSuggestion for nhatrang345
2025-05-28 19:35:30.872 +07:00 [INF] [2025-05-28 19:35:30.872] [INFO]: 📝 AddSuggestion for nhatrang345
2025-05-28 19:35:30.873 +07:00 [DBG] [2025-05-28 19:35:30.873] [DEBUG]: Cập nhật ListBox cho user0, số gợi ý: 3
2025-05-28 19:35:30.882 +07:00 [DBG] [2025-05-28 19:35:30.882] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:35:30.882 +07:00 [INF] [2025-05-28 19:35:30.882] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 19:35:30.882 +07:00 [DBG] [2025-05-28 19:35:30.882] [DEBUG]: Đã chọn gợi ý đầu tiên cho user0
2025-05-28 19:35:30.882 +07:00 [DBG] [2025-05-28 19:35:30.882] [DEBUG]: Không có gợi ý để hiển thị cho user1
2025-05-28 19:35:30.882 +07:00 [DBG] [2025-05-28 19:35:30.882] [DEBUG]: Không có gợi ý để hiển thị cho user2
2025-05-28 19:35:30.882 +07:00 [DBG] [2025-05-28 19:35:30.882] [DEBUG]: Không có gợi ý để hiển thị cho opponent
2025-05-28 19:35:30.882 +07:00 [DBG] [2025-05-28 19:35:30.882] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:35:30.882 +07:00 [INF] [2025-05-28 19:35:30.882] [INFO]: Đã cập nhật danh sách gợi ý
2025-05-28 19:35:31.945 +07:00 [DBG] [2025-05-28 19:35:31.945] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:35:31.945 +07:00 [INF] [2025-05-28 19:35:31.945] [INFO]: Đã chọn gợi ý 2 cho user0
2025-05-28 19:35:32.409 +07:00 [DBG] [2025-05-28 19:35:32.409] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:35:32.409 +07:00 [INF] [2025-05-28 19:35:32.409] [INFO]: Đã chọn gợi ý 3 cho user0
2025-05-28 19:35:32.693 +07:00 [INF] [2025-05-28 19:35:32.693] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:35:32.693 +07:00 [INF] [2025-05-28 19:35:32.693] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:35:33.017 +07:00 [DBG] [2025-05-28 19:35:33.017] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:35:33.018 +07:00 [INF] [2025-05-28 19:35:33.018] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 19:35:33.468 +07:00 [INF] [2025-05-28 19:35:33.468] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:35:33.932 +07:00 [DBG] [2025-05-28 19:35:33.932] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:35:33.932 +07:00 [INF] [2025-05-28 19:35:33.932] [INFO]: Đã chọn gợi ý 3 cho user0
2025-05-28 19:35:34.373 +07:00 [DBG] [2025-05-28 19:35:34.373] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:35:34.373 +07:00 [INF] [2025-05-28 19:35:34.373] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 19:35:36.654 +07:00 [DBG] [2025-05-28 19:35:36.654] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:35:36.654 +07:00 [INF] [2025-05-28 19:35:36.654] [INFO]: Đã chọn gợi ý 2 cho user0
2025-05-28 19:35:37.583 +07:00 [DBG] [2025-05-28 19:35:37.583] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:35:37.583 +07:00 [INF] [2025-05-28 19:35:37.583] [INFO]: Đã chọn gợi ý 3 cho user0
2025-05-28 19:35:37.696 +07:00 [INF] [2025-05-28 19:35:37.696] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:35:37.696 +07:00 [INF] [2025-05-28 19:35:37.696] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:35:38.107 +07:00 [DBG] [2025-05-28 19:35:38.107] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:35:38.107 +07:00 [INF] [2025-05-28 19:35:38.107] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 19:35:38.476 +07:00 [INF] [2025-05-28 19:35:38.476] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:35:38.836 +07:00 [DBG] [2025-05-28 19:35:38.836] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:35:38.836 +07:00 [INF] [2025-05-28 19:35:38.836] [INFO]: Đã chọn gợi ý 3 cho user0
2025-05-28 19:35:39.999 +07:00 [INF] [2025-05-28 19:35:39.999] [INFO] [nhatrang345]: Bắt đầu xếp bài cho nhatrang345 theo gợi ý: [A♠, 6♠, 3♠, A♣, Q♣, 10♣, 2♣, 5♦, 3♦, 2♦, K♥, 9♥, 3♥]
2025-05-28 19:35:40.005 +07:00 [INF] [2025-05-28 19:35:40.005] [INFO]: Kết quả xếp bài cho nhatrang345: Đã xếp bài thành công và đang cố gắng cập nhật UI
2025-05-28 19:35:41.012 +07:00 [INF] [2025-05-28 19:35:41.012] [INFO]: Sắp xếp bài thành công cho nhatrang345: 0,20,8,1,45,37,5,18,10,6,51,35,11
2025-05-28 19:35:41.013 +07:00 [INF] [2025-05-28 19:35:41.013] [INFO] [nhatrang345]: Đã xếp bài cho nhatrang345 theo gợi ý: [A♠, 6♠, 3♠, A♣, Q♣, 10♣, 2♣, 5♦, 3♦, 2♦, K♥, 9♥, 3♥]
2025-05-28 19:35:42.689 +07:00 [INF] [2025-05-28 19:35:42.689] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:35:42.689 +07:00 [INF] [2025-05-28 19:35:42.689] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:35:43.156 +07:00 [INF] [2025-05-28 19:35:43.156] [INFO] [nhatrang345]: Bắt đầu xếp bài cho nhatrang345 theo gợi ý: [A♠, 6♠, 3♠, A♣, Q♣, 10♣, 2♣, 5♦, 3♦, 2♦, K♥, 9♥, 3♥]
2025-05-28 19:35:43.161 +07:00 [INF] [2025-05-28 19:35:43.161] [INFO]: Kết quả xếp bài cho nhatrang345: Đã xếp bài thành công và đang cố gắng cập nhật UI
2025-05-28 19:35:43.473 +07:00 [INF] [2025-05-28 19:35:43.473] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:35:44.176 +07:00 [INF] [2025-05-28 19:35:44.176] [INFO]: Sắp xếp bài thành công cho nhatrang345: 0,20,8,1,45,37,5,18,10,6,51,35,11
2025-05-28 19:35:44.176 +07:00 [INF] [2025-05-28 19:35:44.176] [INFO] [nhatrang345]: Đã xếp bài cho nhatrang345 theo gợi ý: [A♠, 6♠, 3♠, A♣, Q♣, 10♣, 2♣, 5♦, 3♦, 2♦, K♥, 9♥, 3♥]
2025-05-28 19:35:47.692 +07:00 [INF] [2025-05-28 19:35:47.692] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:35:47.692 +07:00 [INF] [2025-05-28 19:35:47.692] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:35:48.488 +07:00 [INF] [2025-05-28 19:35:48.488] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:35:52.691 +07:00 [INF] [2025-05-28 19:35:52.691] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:35:52.691 +07:00 [INF] [2025-05-28 19:35:52.691] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:35:53.486 +07:00 [INF] [2025-05-28 19:35:53.486] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:35:57.688 +07:00 [INF] [2025-05-28 19:35:57.688] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 19:35:57.688 +07:00 [INF] [2025-05-28 19:35:57.688] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 19:35:58.486 +07:00 [INF] [2025-05-28 19:35:58.486] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 19:36:02.690 +07:00 [INF] [2025-05-28 19:36:02.690] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 19:36:02.690 +07:00 [INF] [2025-05-28 19:36:02.690] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 19:36:03.488 +07:00 [INF] [2025-05-28 19:36:03.488] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 19:36:05.116 +07:00 [DBG] [2025-05-28 19:36:05.116] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 19:36:05.116 +07:00 [WRN] [2025-05-28 19:36:05.116] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 19:36:07.689 +07:00 [INF] [2025-05-28 19:36:07.689] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 19:36:07.689 +07:00 [INF] [2025-05-28 19:36:07.689] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 19:36:35.854 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 19:36:37.224 +07:00 [INF] User cancelled game selection, exiting application.
2025-05-28 19:37:35.782 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 19:37:39.206 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 19:37:39.210 +07:00 [INF] Form1 constructor started.
2025-05-28 19:37:39.225 +07:00 [DBG] [2025-05-28 19:37:39.225] [DEBUG]: Gọi InitializeComponent
2025-05-28 19:37:39.235 +07:00 [INF] [2025-05-28 19:37:39.235] [INFO]: Khởi tạo UIManager thành công
2025-05-28 19:37:39.236 +07:00 [DBG] [2025-05-28 19:37:39.236] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 19:37:39.238 +07:00 [INF] [2025-05-28 19:37:39.238] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 19:37:39.238 +07:00 [DBG] [2025-05-28 19:37:39.238] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 19:37:39.238 +07:00 [INF] [2025-05-28 19:37:39.238] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 19:37:39.239 +07:00 [INF] [2025-05-28 19:37:39.239] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 19:37:39.251 +07:00 [DBG] [2025-05-28 19:37:39.251] [DEBUG]: Bắt đầu OnLoad
2025-05-28 19:37:39.251 +07:00 [DBG] [2025-05-28 19:37:39.251] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 19:37:39.267 +07:00 [INF] [2025-05-28 19:37:39.267] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 19:37:39.267 +07:00 [INF] [2025-05-28 19:37:39.267] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 19:37:39.284 +07:00 [INF] [2025-05-28 19:37:39.284] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:37:39.286 +07:00 [INF] [2025-05-28 19:37:39.286] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 19:37:39.286 +07:00 [INF] [2025-05-28 19:37:39.286] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 19:37:39.286 +07:00 [INF] [2025-05-28 19:37:39.286] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 19:37:39.287 +07:00 [INF] [2025-05-28 19:37:39.287] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 19:37:39.287 +07:00 [INF] [2025-05-28 19:37:39.287] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 19:37:39.287 +07:00 [INF] InitializeAsync started.
2025-05-28 19:37:39.287 +07:00 [INF] [2025-05-28 19:37:39.287] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:37:39.291 +07:00 [DBG] [2025-05-28 19:37:39.291] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 19:37:39.294 +07:00 [DBG] [2025-05-28 19:37:39.294] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 19:37:39.295 +07:00 [DBG] [2025-05-28 19:37:39.295] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:37:39.295 +07:00 [DBG] [2025-05-28 19:37:39.295] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:37:39.302 +07:00 [DBG] [2025-05-28 19:37:39.302] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 19:37:39.302 +07:00 [INF] [2025-05-28 19:37:39.302] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 19:37:39.302 +07:00 [DBG] [2025-05-28 19:37:39.302] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:37:39.304 +07:00 [DBG] [2025-05-28 19:37:39.304] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:39.304 +07:00 [DBG] [2025-05-28 19:37:39.304] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:37:39.304 +07:00 [INF] [2025-05-28 19:37:39.304] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:37:39.304 +07:00 [DBG] [2025-05-28 19:37:39.304] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:37:39.304 +07:00 [DBG] [2025-05-28 19:37:39.304] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:39.304 +07:00 [DBG] [2025-05-28 19:37:39.304] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:37:39.304 +07:00 [INF] [2025-05-28 19:37:39.304] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:37:39.304 +07:00 [DBG] [2025-05-28 19:37:39.304] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:37:39.304 +07:00 [DBG] [2025-05-28 19:37:39.304] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:39.357 +07:00 [INF] [2025-05-28 19:37:39.357] [INFO]: Tải danh sách user thành công
2025-05-28 19:37:39.369 +07:00 [INF] [2025-05-28 19:37:39.369] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 19:37:39.369 +07:00 [DBG] [2025-05-28 19:37:39.369] [DEBUG]: OnLoad hoàn tất
2025-05-28 19:37:40.896 +07:00 [INF] [2025-05-28 19:37:40.896] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11014: Đang chạy
2025-05-28 19:37:40.901 +07:00 [INF] [2025-05-28 19:37:40.901] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 19:37:40.903 +07:00 [INF] [2025-05-28 19:37:40.903] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 19:37:40.906 +07:00 [DBG] [2025-05-28 19:37:40.906] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles: Thành công
2025-05-28 19:37:40.949 +07:00 [INF] [2025-05-28 19:37:40.949] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 45ms
2025-05-28 19:37:41.335 +07:00 [DBG] [2025-05-28 19:37:41.335] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 19:37:41.336 +07:00 [INF] [2025-05-28 19:37:41.336] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:58248. Thời gian: 386ms
2025-05-28 19:37:41.336 +07:00 [DBG] [2025-05-28 19:37:41.336] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:37:41.336 +07:00 [DBG] [2025-05-28 19:37:41.336] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:37:41.336 +07:00 [INF] [2025-05-28 19:37:41.336] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 433ms
2025-05-28 19:37:41.336 +07:00 [DBG] [2025-05-28 19:37:41.336] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:37:41.336 +07:00 [DBG] [2025-05-28 19:37:41.336] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:37:42.808 +07:00 [INF] [2025-05-28 19:37:42.808] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 19:37:43.152 +07:00 [INF] [2025-05-28 19:37:43.152] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 19:37:43.242 +07:00 [INF] [2025-05-28 19:37:43.242] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 19:37:43.255 +07:00 [DBG] [2025-05-28 19:37:43.255] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 19:37:43.263 +07:00 [INF] [2025-05-28 19:37:43.263] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 19:37:43.264 +07:00 [INF] [2025-05-28 19:37:43.263] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 19:37:43.269 +07:00 [INF] [2025-05-28 19:37:43.269] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 19:37:43.270 +07:00 [INF] [2025-05-28 19:37:43.270] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:37:43.270 +07:00 [DBG] [2025-05-28 19:37:43.270] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:37:43.270 +07:00 [DBG] [2025-05-28 19:37:43.270] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:43.270 +07:00 [INF] [2025-05-28 19:37:43.270] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:37:43.270 +07:00 [DBG] [2025-05-28 19:37:43.270] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:37:43.270 +07:00 [DBG] [2025-05-28 19:37:43.270] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:43.270 +07:00 [INF] [2025-05-28 19:37:43.270] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:37:43.270 +07:00 [DBG] [2025-05-28 19:37:43.270] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:37:43.270 +07:00 [DBG] [2025-05-28 19:37:43.270] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:43.300 +07:00 [INF] [2025-05-28 19:37:43.300] [INFO]: Tải danh sách user thành công
2025-05-28 19:37:43.300 +07:00 [INF] [2025-05-28 19:37:43.300] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:37:43.300 +07:00 [DBG] [2025-05-28 19:37:43.300] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:37:43.300 +07:00 [DBG] [2025-05-28 19:37:43.300] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:43.300 +07:00 [INF] [2025-05-28 19:37:43.300] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:37:43.300 +07:00 [DBG] [2025-05-28 19:37:43.300] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:37:43.300 +07:00 [DBG] [2025-05-28 19:37:43.300] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:43.300 +07:00 [INF] [2025-05-28 19:37:43.300] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:37:43.300 +07:00 [DBG] [2025-05-28 19:37:43.300] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:37:43.300 +07:00 [DBG] [2025-05-28 19:37:43.300] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:43.327 +07:00 [INF] [2025-05-28 19:37:43.327] [INFO]: Tải danh sách user thành công
2025-05-28 19:37:43.335 +07:00 [INF] [2025-05-28 19:37:43.335] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.00 giây
2025-05-28 19:37:43.342 +07:00 [INF] [2025-05-28 19:37:43.342] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 2.01 giây
2025-05-28 19:37:43.346 +07:00 [DBG] [2025-05-28 19:37:43.346] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Mậu Binh
2025-05-28 19:37:43.353 +07:00 [INF] [2025-05-28 19:37:43.353] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:37:43.360 +07:00 [DBG] [2025-05-28 19:37:43.360] [DEBUG]: Đã khởi tạo giao diện với 4 panel
2025-05-28 19:37:43.360 +07:00 [DBG] [2025-05-28 19:37:43.360] [DEBUG]: Đã khởi tạo MauBinhSuggestionForm
2025-05-28 19:37:45.257 +07:00 [INF] [2025-05-28 19:37:45.257] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 19:37:46.288 +07:00 [INF] [2025-05-28 19:37:46.288] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 19:37:46.288 +07:00 [DBG] [2025-05-28 19:37:46.288] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:37:46.288 +07:00 [DBG] [2025-05-28 19:37:46.288] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:37:46.289 +07:00 [INF] [2025-05-28 19:37:46.289] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 19:37:46.289 +07:00 [DBG] [2025-05-28 19:37:46.289] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:37:46.289 +07:00 [DBG] [2025-05-28 19:37:46.289] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:37:46.289 +07:00 [INF] [2025-05-28 19:37:46.289] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:37:46.289 +07:00 [DBG] [2025-05-28 19:37:46.289] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:37:46.289 +07:00 [DBG] [2025-05-28 19:37:46.289] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:46.289 +07:00 [INF] [2025-05-28 19:37:46.289] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:37:46.289 +07:00 [DBG] [2025-05-28 19:37:46.289] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:37:46.289 +07:00 [DBG] [2025-05-28 19:37:46.289] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:46.289 +07:00 [INF] [2025-05-28 19:37:46.289] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:37:46.289 +07:00 [DBG] [2025-05-28 19:37:46.289] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:37:46.289 +07:00 [DBG] [2025-05-28 19:37:46.289] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:46.355 +07:00 [INF] [2025-05-28 19:37:46.355] [INFO]: Tải danh sách user thành công
2025-05-28 19:37:46.355 +07:00 [INF] [2025-05-28 19:37:46.355] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:37:46.355 +07:00 [DBG] [2025-05-28 19:37:46.355] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:37:46.355 +07:00 [DBG] [2025-05-28 19:37:46.355] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:46.355 +07:00 [INF] [2025-05-28 19:37:46.355] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:37:46.355 +07:00 [DBG] [2025-05-28 19:37:46.355] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:37:46.355 +07:00 [DBG] [2025-05-28 19:37:46.355] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:46.355 +07:00 [INF] [2025-05-28 19:37:46.355] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:37:46.355 +07:00 [DBG] [2025-05-28 19:37:46.355] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:37:46.355 +07:00 [DBG] [2025-05-28 19:37:46.355] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:37:46.432 +07:00 [INF] [2025-05-28 19:37:46.432] [INFO]: Tải danh sách user thành công
2025-05-28 19:37:46.457 +07:00 [INF] [2025-05-28 19:37:46.457] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.17 giây
2025-05-28 19:37:46.478 +07:00 [INF] [2025-05-28 19:37:46.478] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.19 giây
2025-05-28 19:37:47.794 +07:00 [DBG] [2025-05-28 19:37:47.794] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:37:47.795 +07:00 [DBG] [2025-05-28 19:37:47.795] [DEBUG] [nhatrang345]: ℹ️ Không có điều kiện đặc biệt cho nhatrang345, giữ nguyên trong phòng (sit: 3, số người: 4)
2025-05-28 19:37:47.795 +07:00 [WRN] [2025-05-28 19:37:47.795] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 19:37:49.175 +07:00 [INF] [2025-05-28 19:37:49.175] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:37:49.176 +07:00 [INF] [2025-05-28 19:37:49.176] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:37:49.177 +07:00 [INF] [2025-05-28 19:37:49.177] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:37:54.207 +07:00 [INF] [2025-05-28 19:37:54.207] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:37:54.207 +07:00 [INF] [2025-05-28 19:37:54.207] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:37:54.272 +07:00 [INF] [2025-05-28 19:37:54.272] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:37:59.191 +07:00 [INF] [2025-05-28 19:37:59.191] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:37:59.191 +07:00 [INF] [2025-05-28 19:37:59.191] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:37:59.194 +07:00 [INF] [2025-05-28 19:37:59.194] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:38:00.867 +07:00 [DBG] [2025-05-28 19:38:00.867] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 600 (Mau Binh cards)
2025-05-28 19:38:00.870 +07:00 [INF] [2025-05-28 19:38:00.870] [INFO]: Saved Mau Binh cards for nhatrang345: [9♠, 7♣, A♠, Q♦, A♥, 3♠, Q♥, 7♦, K♥, 5♠, 10♣, 3♣, K♣]
2025-05-28 19:38:02.974 +07:00 [DBG] [2025-05-28 19:38:02.973] [DEBUG]: Bắt đầu quét bài, số user: 3
2025-05-28 19:38:02.980 +07:00 [INF] [2025-05-28 19:38:02.980] [INFO]: Updated Mau Binh cards for nhatrang345: [K♣, 3♣, 10♣, 5♠, K♥, 7♦, Q♥, 3♠, A♥, Q♦, A♠, 7♣, 9♠]
2025-05-28 19:38:02.980 +07:00 [INF] [2025-05-28 19:38:02.980] [INFO] [nhatrang345]: Đã quét bài cho nhatrang345 bằng script (MauBinhController): [K♣, 3♣, 10♣, 5♠, K♥, 7♦, Q♥, 3♠, A♥, Q♦, A♠, 7♣, 9♠]
2025-05-28 19:38:02.980 +07:00 [ERR] [2025-05-28 19:38:02.980] [ERROR]: Không tìm thấy driver cho phanthiet989
2025-05-28 19:38:02.980 +07:00 [ERR] [2025-05-28 19:38:02.980] [ERROR]: Không tìm thấy driver cho namdinhx852
2025-05-28 19:38:02.980 +07:00 [WRN] [2025-05-28 19:38:02.980] [WARNING]: Chỉ tìm thấy bài hợp lệ cho 1/3 user
2025-05-28 19:38:02.980 +07:00 [WRN] [2025-05-28 19:38:02.980] [WARNING]: Số user hợp lệ (1) không đủ 3 để tính bài đối thủ
2025-05-28 19:38:02.980 +07:00 [DBG] [2025-05-28 19:38:02.980] [DEBUG]: Bắt đầu UpdateCardDisplay, số user: 1
2025-05-28 19:38:02.980 +07:00 [DBG] [2025-05-28 19:38:02.980] [DEBUG]: Cập nhật bài cho user: nhatrang345, indexKey: user0
2025-05-28 19:38:02.987 +07:00 [INF] [2025-05-28 19:38:02.987] [INFO]: Quét bài thành công, số user: 1, tổng số lá: 13
2025-05-28 19:38:03.740 +07:00 [INF] [2025-05-28 19:38:03.740] [INFO]: 🚀 Tạo gợi ý với thuật toán OPTIMIZED cho 13 lá bài
2025-05-28 19:38:03.740 +07:00 [INF] [2025-05-28 19:38:03.740] [INFO]: 🚀 Bắt đầu thuật toán Mậu Binh theo yêu cầu mới
2025-05-28 19:38:03.740 +07:00 [INF] [2025-05-28 19:38:03.740] [INFO]: 🏆 Kiểm tra đặc biệt tới trắng
2025-05-28 19:38:03.791 +07:00 [ERR] [2025-05-28 19:38:03.791] [ERROR]: ❌ Lỗi thuật toán mới: 'System.Collections.Generic.List<<>f__AnonymousType2<int,int>>' does not contain a definition for 'ToList'
2025-05-28 19:38:03.791 +07:00 [WRN] [2025-05-28 19:38:03.791] [WARNING]: 🔄 Fallback sang gợi ý đơn giản (thuật toán cũ bị disable)
2025-05-28 19:38:03.797 +07:00 [INF] [2025-05-28 19:38:03.797] [INFO]: ✅ Tạo được 3 gợi ý fallback đơn giản
2025-05-28 19:38:03.797 +07:00 [INF] [2025-05-28 19:38:03.797] [INFO]: 📊 BÁO CÁO GỢI Ý TỐI ƯU MAU BINH:

🎯 Gợi ý 1: 🎯 Sắp xếp theo rank cao
   Tổng điểm: 1000.0
   Chi 1: [A♥, A♠, K♣, K♥, Q♥] - 👥 Thú
   Chi 2: [Q♦, 10♣, 9♠, 7♦, 7♣] - 💫 Đôi
   Chi 3: [5♠, 3♣, 3♠] - 💫 Đôi

🎯 Gợi ý 2: 💎 Ưu tiên đôi/xám
   Tổng điểm: 1100.0
   Chi 1: [K♣, K♥, 3♣, 3♠, 7♦] - 👥 Thú
   Chi 2: [7♣, Q♥, Q♦, A♥, A♠] - 👥 Thú
   Chi 3: [10♣, 9♠, 5♠] - 🃏 Mậu Thầu

🎯 Gợi ý 3: ⚖️ Cân bằng
   Tổng điểm: 1200.0
   Chi 1: [A♠, 9♠, 5♠, 3♠, K♣] - 🃏 Mậu Thầu
   Chi 2: [10♣, 7♣, 3♣, Q♦, 7♦] - 💫 Đôi
   Chi 3: [A♥, K♥, Q♥] - 🔥 Thùng Phá Sảnh


2025-05-28 19:38:03.799 +07:00 [INF] [2025-05-28 19:38:03.799] [INFO] [nhatrang345]: Đã tạo 3 gợi ý cho nhatrang345 (indexKey: user0)
2025-05-28 19:38:03.799 +07:00 [INF] [2025-05-28 19:38:03.799] [INFO]: 📝 AddSuggestion for nhatrang345
2025-05-28 19:38:03.799 +07:00 [INF] [2025-05-28 19:38:03.799] [INFO]: 📝 AddSuggestion for nhatrang345
2025-05-28 19:38:03.799 +07:00 [INF] [2025-05-28 19:38:03.799] [INFO]: 📝 AddSuggestion for nhatrang345
2025-05-28 19:38:03.799 +07:00 [DBG] [2025-05-28 19:38:03.799] [DEBUG]: Cập nhật ListBox cho user0, số gợi ý: 3
2025-05-28 19:38:03.808 +07:00 [DBG] [2025-05-28 19:38:03.808] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:38:03.808 +07:00 [INF] [2025-05-28 19:38:03.808] [INFO]: Đã chọn gợi ý 1 cho user0
2025-05-28 19:38:03.808 +07:00 [DBG] [2025-05-28 19:38:03.808] [DEBUG]: Đã chọn gợi ý đầu tiên cho user0
2025-05-28 19:38:03.808 +07:00 [DBG] [2025-05-28 19:38:03.808] [DEBUG]: Không có gợi ý để hiển thị cho user1
2025-05-28 19:38:03.808 +07:00 [DBG] [2025-05-28 19:38:03.808] [DEBUG]: Không có gợi ý để hiển thị cho user2
2025-05-28 19:38:03.808 +07:00 [DBG] [2025-05-28 19:38:03.808] [DEBUG]: Không có gợi ý để hiển thị cho opponent
2025-05-28 19:38:03.808 +07:00 [DBG] [2025-05-28 19:38:03.808] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:38:03.808 +07:00 [INF] [2025-05-28 19:38:03.808] [INFO]: Đã cập nhật danh sách gợi ý
2025-05-28 19:38:04.208 +07:00 [INF] [2025-05-28 19:38:04.208] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:38:04.208 +07:00 [INF] [2025-05-28 19:38:04.208] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:38:04.300 +07:00 [INF] [2025-05-28 19:38:04.300] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:38:05.182 +07:00 [DBG] [2025-05-28 19:38:05.182] [DEBUG]: Không đủ dữ liệu để tính tỷ lệ thắng
2025-05-28 19:38:05.182 +07:00 [INF] [2025-05-28 19:38:05.182] [INFO]: Đã chọn gợi ý 3 cho user0
2025-05-28 19:38:09.210 +07:00 [INF] [2025-05-28 19:38:09.210] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:38:09.210 +07:00 [INF] [2025-05-28 19:38:09.210] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:38:09.211 +07:00 [INF] [2025-05-28 19:38:09.211] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:38:09.347 +07:00 [INF] [2025-05-28 19:38:09.347] [INFO] [nhatrang345]: Bắt đầu xếp bài cho nhatrang345 theo gợi ý: [A♠, 9♠, 5♠, 3♠, K♣, 10♣, 7♣, 3♣, Q♦, 7♦, A♥, K♥, Q♥]
2025-05-28 19:38:09.355 +07:00 [INF] [2025-05-28 19:38:09.355] [INFO]: Kết quả xếp bài cho nhatrang345: Đã xếp bài thành công và đang cố gắng cập nhật UI
2025-05-28 19:38:10.362 +07:00 [INF] [2025-05-28 19:38:10.362] [INFO]: Sắp xếp bài thành công cho nhatrang345: 0,32,16,8,49,37,25,9,46,26,3,51,47
2025-05-28 19:38:10.362 +07:00 [INF] [2025-05-28 19:38:10.362] [INFO] [nhatrang345]: Đã xếp bài cho nhatrang345 theo gợi ý: [A♠, 9♠, 5♠, 3♠, K♣, 10♣, 7♣, 3♣, Q♦, 7♦, A♥, K♥, Q♥]
2025-05-28 19:38:14.208 +07:00 [INF] [2025-05-28 19:38:14.208] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:38:14.208 +07:00 [INF] [2025-05-28 19:38:14.208] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:38:14.208 +07:00 [INF] [2025-05-28 19:38:14.208] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:38:19.207 +07:00 [INF] [2025-05-28 19:38:19.207] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:38:19.207 +07:00 [INF] [2025-05-28 19:38:19.207] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:38:19.218 +07:00 [INF] [2025-05-28 19:38:19.218] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:38:24.209 +07:00 [INF] [2025-05-28 19:38:24.209] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:38:24.209 +07:00 [INF] [2025-05-28 19:38:24.209] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:38:24.209 +07:00 [INF] [2025-05-28 19:38:24.209] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:38:28.929 +07:00 [DBG] [2025-05-28 19:38:28.929] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 19:38:28.929 +07:00 [WRN] [2025-05-28 19:38:28.929] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 19:38:29.209 +07:00 [INF] [2025-05-28 19:38:29.209] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:38:29.209 +07:00 [INF] [2025-05-28 19:38:29.209] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:38:29.214 +07:00 [INF] [2025-05-28 19:38:29.214] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:38:33.592 +07:00 [DBG] [2025-05-28 19:38:33.592] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 19:38:33.592 +07:00 [WRN] [2025-05-28 19:38:33.592] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 19:38:34.224 +07:00 [INF] [2025-05-28 19:38:34.223] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 19:38:34.224 +07:00 [INF] [2025-05-28 19:38:34.224] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 19:38:34.226 +07:00 [INF] [2025-05-28 19:38:34.226] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,10]
2025-05-28 19:38:39.216 +07:00 [DBG] [2025-05-28 19:38:39.216] [DEBUG]: [WebSocket] nhatrang345: Processing cmd 603 (Mau Binh card update)
2025-05-28 19:38:39.216 +07:00 [WRN] [2025-05-28 19:38:39.216] [WARNING]: Cmd 603 for nhatrang345 lacks 'cs' field
2025-05-28 19:38:39.244 +07:00 [INF] [2025-05-28 19:38:39.244] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 19:38:39.244 +07:00 [INF] [2025-05-28 19:38:39.244] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 19:38:39.250 +07:00 [INF] [2025-05-28 19:38:39.250] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,11]
2025-05-28 19:38:44.243 +07:00 [INF] [2025-05-28 19:38:44.243] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 19:38:44.243 +07:00 [INF] [2025-05-28 19:38:44.243] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 19:38:44.410 +07:00 [INF] [2025-05-28 19:38:44.410] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,12]
2025-05-28 19:38:49.240 +07:00 [INF] [2025-05-28 19:38:49.240] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 19:38:49.240 +07:00 [INF] [2025-05-28 19:38:49.240] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 19:38:49.244 +07:00 [INF] [2025-05-28 19:38:49.244] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,13]
2025-05-28 19:38:54.242 +07:00 [INF] [2025-05-28 19:38:54.242] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 19:38:54.243 +07:00 [INF] [2025-05-28 19:38:54.243] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 19:38:54.243 +07:00 [INF] [2025-05-28 19:38:54.243] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,14]
2025-05-28 19:48:07.840 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 19:48:10.621 +07:00 [INF] User selected: HitClub - Tiến Lên
2025-05-28 19:48:10.624 +07:00 [INF] Form1 constructor started.
2025-05-28 19:48:10.641 +07:00 [DBG] [2025-05-28 19:48:10.641] [DEBUG]: Gọi InitializeComponent
2025-05-28 19:48:10.651 +07:00 [INF] [2025-05-28 19:48:10.651] [INFO]: Khởi tạo UIManager thành công
2025-05-28 19:48:10.652 +07:00 [DBG] [2025-05-28 19:48:10.652] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 19:48:10.654 +07:00 [INF] [2025-05-28 19:48:10.654] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 19:48:10.654 +07:00 [DBG] [2025-05-28 19:48:10.654] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 19:48:10.654 +07:00 [INF] [2025-05-28 19:48:10.654] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 19:48:10.655 +07:00 [INF] [2025-05-28 19:48:10.655] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 19:48:10.667 +07:00 [DBG] [2025-05-28 19:48:10.667] [DEBUG]: Bắt đầu OnLoad
2025-05-28 19:48:10.667 +07:00 [DBG] [2025-05-28 19:48:10.667] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 19:48:10.683 +07:00 [INF] [2025-05-28 19:48:10.683] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 19:48:10.683 +07:00 [INF] [2025-05-28 19:48:10.683] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 19:48:10.699 +07:00 [INF] [2025-05-28 19:48:10.699] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:48:10.700 +07:00 [INF] [2025-05-28 19:48:10.700] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 19:48:10.701 +07:00 [INF] [2025-05-28 19:48:10.701] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 19:48:10.701 +07:00 [INF] [2025-05-28 19:48:10.701] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 19:48:10.701 +07:00 [INF] [2025-05-28 19:48:10.701] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 19:48:10.701 +07:00 [INF] [2025-05-28 19:48:10.701] [INFO]: Đã chọn card game: Tiến Lên
2025-05-28 19:48:10.702 +07:00 [INF] InitializeAsync started.
2025-05-28 19:48:10.702 +07:00 [INF] [2025-05-28 19:48:10.702] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 19:48:10.707 +07:00 [DBG] [2025-05-28 19:48:10.707] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 19:48:10.709 +07:00 [DBG] [2025-05-28 19:48:10.709] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 19:48:10.710 +07:00 [DBG] [2025-05-28 19:48:10.710] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:48:10.710 +07:00 [DBG] [2025-05-28 19:48:10.710] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:48:10.717 +07:00 [DBG] [2025-05-28 19:48:10.717] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 19:48:10.717 +07:00 [INF] [2025-05-28 19:48:10.717] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 19:48:10.718 +07:00 [DBG] [2025-05-28 19:48:10.718] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:48:10.719 +07:00 [DBG] [2025-05-28 19:48:10.719] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:10.719 +07:00 [DBG] [2025-05-28 19:48:10.719] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:48:10.719 +07:00 [INF] [2025-05-28 19:48:10.719] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:48:10.719 +07:00 [DBG] [2025-05-28 19:48:10.719] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:48:10.719 +07:00 [DBG] [2025-05-28 19:48:10.719] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:10.719 +07:00 [DBG] [2025-05-28 19:48:10.719] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:48:10.719 +07:00 [INF] [2025-05-28 19:48:10.719] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:48:10.719 +07:00 [DBG] [2025-05-28 19:48:10.719] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:48:10.719 +07:00 [DBG] [2025-05-28 19:48:10.719] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:10.767 +07:00 [INF] [2025-05-28 19:48:10.767] [INFO]: Tải danh sách user thành công
2025-05-28 19:48:10.776 +07:00 [INF] [2025-05-28 19:48:10.776] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 19:48:10.776 +07:00 [DBG] [2025-05-28 19:48:10.776] [DEBUG]: OnLoad hoàn tất
2025-05-28 19:48:11.854 +07:00 [INF] [2025-05-28 19:48:11.854] [INFO]: Kiểm tra GPM-Login tại http://127.0.0.1:11014: Đang chạy
2025-05-28 19:48:11.860 +07:00 [INF] [2025-05-28 19:48:11.860] [INFO] [nhatrang345]: Đang mở profile cho nhatrang345...
2025-05-28 19:48:11.861 +07:00 [INF] [2025-05-28 19:48:11.861] [INFO] [nhatrang345]: Bắt đầu mở profile cho nhatrang345...
2025-05-28 19:48:11.864 +07:00 [DBG] [2025-05-28 19:48:11.864] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles: Thành công
2025-05-28 19:48:11.907 +07:00 [INF] [2025-05-28 19:48:11.907] [INFO] [nhatrang345]: Tìm thấy profile cho nhatrang345 với ID: 49bc7e28-84c2-4541-8ae7-424e94e54ae5. Thời gian: 44ms
2025-05-28 19:48:12.349 +07:00 [DBG] [2025-05-28 19:48:12.349] [DEBUG] [nhatrang345]: Gửi GET yêu cầu đến http://127.0.0.1:11014/api/v3/profiles/start/49bc7e28-84c2-4541-8ae7-424e94e54ae5: Thành công
2025-05-28 19:48:12.350 +07:00 [INF] [2025-05-28 19:48:12.350] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345 với remote debugging: 127.0.0.1:61045. Thời gian: 442ms
2025-05-28 19:48:12.350 +07:00 [DBG] [2025-05-28 19:48:12.350] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:48:12.350 +07:00 [DBG] [2025-05-28 19:48:12.350] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:48:12.350 +07:00 [INF] [2025-05-28 19:48:12.350] [INFO] [nhatrang345]: Mở profile cho nhatrang345 thành công. Thời gian: 488ms
2025-05-28 19:48:12.350 +07:00 [DBG] [2025-05-28 19:48:12.350] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:48:12.350 +07:00 [DBG] [2025-05-28 19:48:12.350] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:48:13.612 +07:00 [INF] [2025-05-28 19:48:13.612] [INFO] [nhatrang345]: Đã khởi tạo ChromeDriver tại C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\chromedriver.exe cho nhatrang345 và mở URL https://web.hit.club/
2025-05-28 19:48:13.985 +07:00 [INF] [2025-05-28 19:48:13.985] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 19:48:14.068 +07:00 [INF] [2025-05-28 19:48:14.068] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 19:48:14.081 +07:00 [DBG] [2025-05-28 19:48:14.081] [DEBUG] [nhatrang345]: Phiên bản Chrome: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36
2025-05-28 19:48:14.085 +07:00 [INF] [2025-05-28 19:48:14.085] [INFO] [nhatrang345]: ✅ Đã setup WebView external handler cho nhatrang345
2025-05-28 19:48:14.087 +07:00 [INF] [2025-05-28 19:48:14.087] [INFO] [nhatrang345]: ✅ Đã setup console log listener cho nhatrang345
2025-05-28 19:48:14.090 +07:00 [INF] [2025-05-28 19:48:14.090] [INFO] [nhatrang345]: Tìm thấy token (token) cho nhatrang345: 1-dcf02ed2d227a0efec7a0cfeaa76dfdd
2025-05-28 19:48:14.091 +07:00 [INF] [2025-05-28 19:48:14.091] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:48:14.091 +07:00 [DBG] [2025-05-28 19:48:14.091] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:48:14.091 +07:00 [DBG] [2025-05-28 19:48:14.091] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:14.091 +07:00 [INF] [2025-05-28 19:48:14.091] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:48:14.091 +07:00 [DBG] [2025-05-28 19:48:14.091] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:48:14.091 +07:00 [DBG] [2025-05-28 19:48:14.091] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:14.091 +07:00 [INF] [2025-05-28 19:48:14.091] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:48:14.091 +07:00 [DBG] [2025-05-28 19:48:14.091] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:48:14.091 +07:00 [DBG] [2025-05-28 19:48:14.091] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:14.119 +07:00 [INF] [2025-05-28 19:48:14.119] [INFO]: Tải danh sách user thành công
2025-05-28 19:48:14.119 +07:00 [INF] [2025-05-28 19:48:14.119] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:48:14.119 +07:00 [DBG] [2025-05-28 19:48:14.119] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:48:14.119 +07:00 [DBG] [2025-05-28 19:48:14.119] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:14.119 +07:00 [INF] [2025-05-28 19:48:14.119] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:48:14.119 +07:00 [DBG] [2025-05-28 19:48:14.119] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:48:14.119 +07:00 [DBG] [2025-05-28 19:48:14.119] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:14.119 +07:00 [INF] [2025-05-28 19:48:14.119] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:48:14.119 +07:00 [DBG] [2025-05-28 19:48:14.119] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:48:14.119 +07:00 [DBG] [2025-05-28 19:48:14.119] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:14.144 +07:00 [INF] [2025-05-28 19:48:14.144] [INFO]: Tải danh sách user thành công
2025-05-28 19:48:14.152 +07:00 [INF] [2025-05-28 19:48:14.152] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.80 giây
2025-05-28 19:48:14.159 +07:00 [INF] [2025-05-28 19:48:14.159] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 1.81 giây
2025-05-28 19:48:14.163 +07:00 [DBG] [2025-05-28 19:48:14.163] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Tiến Lên
2025-05-28 19:48:14.178 +07:00 [INF] [2025-05-28 19:48:14.178] [INFO]: 🎮 Đã khởi tạo TienLenSuggestionForm với Strategy Engine
2025-05-28 19:48:16.087 +07:00 [INF] [2025-05-28 19:48:16.087] [INFO] [nhatrang345]: Đã set lại kích thước profile nhatrang345 về 700x500 sau khi load
2025-05-28 19:48:17.108 +07:00 [INF] [2025-05-28 19:48:17.108] [INFO] [nhatrang345]: WebSocket initialized for nhatrang345
2025-05-28 19:48:17.108 +07:00 [DBG] [2025-05-28 19:48:17.108] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:48:17.108 +07:00 [DBG] [2025-05-28 19:48:17.108] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:48:17.108 +07:00 [INF] [2025-05-28 19:48:17.108] [INFO] [nhatrang345]: Đã mở profile cho nhatrang345
2025-05-28 19:48:17.108 +07:00 [DBG] [2025-05-28 19:48:17.108] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 19:48:17.108 +07:00 [DBG] [2025-05-28 19:48:17.108] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 19:48:17.109 +07:00 [INF] [2025-05-28 19:48:17.109] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:48:17.109 +07:00 [DBG] [2025-05-28 19:48:17.109] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:48:17.109 +07:00 [DBG] [2025-05-28 19:48:17.109] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:17.109 +07:00 [INF] [2025-05-28 19:48:17.109] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:48:17.109 +07:00 [DBG] [2025-05-28 19:48:17.109] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:48:17.109 +07:00 [DBG] [2025-05-28 19:48:17.109] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:17.109 +07:00 [INF] [2025-05-28 19:48:17.109] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:48:17.109 +07:00 [DBG] [2025-05-28 19:48:17.109] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:48:17.109 +07:00 [DBG] [2025-05-28 19:48:17.109] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:17.135 +07:00 [INF] [2025-05-28 19:48:17.135] [INFO]: Tải danh sách user thành công
2025-05-28 19:48:17.135 +07:00 [INF] [2025-05-28 19:48:17.135] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Mở
2025-05-28 19:48:17.135 +07:00 [DBG] [2025-05-28 19:48:17.135] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 19:48:17.136 +07:00 [DBG] [2025-05-28 19:48:17.136] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:17.136 +07:00 [INF] [2025-05-28 19:48:17.136] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 19:48:17.136 +07:00 [DBG] [2025-05-28 19:48:17.136] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 19:48:17.136 +07:00 [DBG] [2025-05-28 19:48:17.136] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:17.136 +07:00 [INF] [2025-05-28 19:48:17.136] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 19:48:17.136 +07:00 [DBG] [2025-05-28 19:48:17.136] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 19:48:17.136 +07:00 [DBG] [2025-05-28 19:48:17.136] [DEBUG]: Số cột trong hàng: 5
2025-05-28 19:48:17.163 +07:00 [INF] [2025-05-28 19:48:17.163] [INFO]: Tải danh sách user thành công
2025-05-28 19:48:17.172 +07:00 [INF] [2025-05-28 19:48:17.172] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.06 giây
2025-05-28 19:48:17.180 +07:00 [INF] [2025-05-28 19:48:17.180] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.07 giây
2025-05-28 19:48:20.001 +07:00 [DBG] [2025-05-28 19:48:20.001] [DEBUG] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 202 (Join Room)
2025-05-28 19:48:20.003 +07:00 [DBG] [2025-05-28 19:48:20.003] [DEBUG] [nhatrang345]: ℹ️ Không có điều kiện đặc biệt cho nhatrang345, giữ nguyên trong phòng (sit: 1, số người: 2)
2025-05-28 19:48:20.003 +07:00 [WRN] [2025-05-28 19:48:20.003] [WARNING] [nhatrang345]: ⚠️ Không tìm thấy TaskCompletionSource cho nhatrang345
2025-05-28 19:48:20.024 +07:00 [INF] [2025-05-28 19:48:20.024] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:48:20.025 +07:00 [INF] [2025-05-28 19:48:20.025] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:48:20.026 +07:00 [INF] [2025-05-28 19:48:20.026] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,1]
2025-05-28 19:48:22.307 +07:00 [INF] [2025-05-28 19:48:22.307] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 250 (Tien Len game info)
2025-05-28 19:48:22.307 +07:00 [INF] [2025-05-28 19:48:22.307] [INFO]: 🔄 UpdateGameInfo: currentPlayer=, myUsername=nhatrang345, playerCount=0
2025-05-28 19:48:22.309 +07:00 [INF] [2025-05-28 19:48:22.309] [INFO]: 📊 Player card counts: 
2025-05-28 19:48:22.309 +07:00 [INF] [2025-05-28 19:48:22.309] [INFO]: 📊 UpdateRemainingCardsDisplay: 0 players
2025-05-28 19:48:22.310 +07:00 [INF] [2025-05-28 19:48:22.310] [INFO]: 📊 Hiển thị: Số lá Đối Thủ: --
2025-05-28 19:48:22.311 +07:00 [INF] [2025-05-28 19:48:22.311] [INFO]: ⏳ Chờ  đánh bài
2025-05-28 19:48:25.027 +07:00 [INF] [2025-05-28 19:48:25.027] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:48:25.027 +07:00 [INF] [2025-05-28 19:48:25.027] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:48:25.028 +07:00 [INF] [2025-05-28 19:48:25.028] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,2]
2025-05-28 19:48:27.616 +07:00 [INF] [2025-05-28 19:48:27.616] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 251 (Tien Len player info)
2025-05-28 19:48:28.733 +07:00 [INF] [2025-05-28 19:48:28.733] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 251 (Tien Len player info)
2025-05-28 19:48:30.042 +07:00 [INF] [2025-05-28 19:48:30.042] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:48:30.042 +07:00 [INF] [2025-05-28 19:48:30.042] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:48:30.086 +07:00 [INF] [2025-05-28 19:48:30.086] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,3]
2025-05-28 19:48:35.042 +07:00 [INF] [2025-05-28 19:48:35.042] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:48:35.042 +07:00 [INF] [2025-05-28 19:48:35.042] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:48:35.043 +07:00 [INF] [2025-05-28 19:48:35.043] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,4]
2025-05-28 19:48:38.113 +07:00 [INF] [2025-05-28 19:48:38.113] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 251 (Tien Len player info)
2025-05-28 19:48:40.042 +07:00 [INF] [2025-05-28 19:48:40.042] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:48:40.042 +07:00 [INF] [2025-05-28 19:48:40.042] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:48:40.059 +07:00 [INF] [2025-05-28 19:48:40.059] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,5]
2025-05-28 19:48:40.488 +07:00 [INF] [2025-05-28 19:48:40.488] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 251 (Tien Len player info)
2025-05-28 19:48:45.050 +07:00 [INF] [2025-05-28 19:48:45.050] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:48:45.050 +07:00 [INF] [2025-05-28 19:48:45.050] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:48:45.061 +07:00 [INF] [2025-05-28 19:48:45.061] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,6]
2025-05-28 19:48:48.875 +07:00 [INF] [2025-05-28 19:48:48.875] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 251 (Tien Len player info)
2025-05-28 19:48:50.048 +07:00 [INF] [2025-05-28 19:48:50.048] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:48:50.048 +07:00 [INF] [2025-05-28 19:48:50.048] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:48:50.062 +07:00 [INF] [2025-05-28 19:48:50.062] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,7]
2025-05-28 19:48:51.330 +07:00 [INF] [2025-05-28 19:48:51.330] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 251 (Tien Len player info)
2025-05-28 19:48:53.320 +07:00 [INF] [2025-05-28 19:48:53.320] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 251 (Tien Len player info)
2025-05-28 19:48:54.428 +07:00 [INF] [2025-05-28 19:48:54.428] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 251 (Tien Len player info)
2025-05-28 19:48:55.041 +07:00 [INF] [2025-05-28 19:48:55.041] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:48:55.041 +07:00 [INF] [2025-05-28 19:48:55.041] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:48:55.044 +07:00 [INF] [2025-05-28 19:48:55.044] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,8]
2025-05-28 19:48:58.257 +07:00 [INF] [2025-05-28 19:48:58.257] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 251 (Tien Len player info)
2025-05-28 19:49:00.040 +07:00 [INF] [2025-05-28 19:49:00.040] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:49:00.040 +07:00 [INF] [2025-05-28 19:49:00.040] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:49:00.041 +07:00 [INF] [2025-05-28 19:49:00.041] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Skipping control message (type 6): [6,1,9]
2025-05-28 19:49:00.437 +07:00 [INF] [2025-05-28 19:49:00.437] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 251 (Tien Len player info)
2025-05-28 19:49:01.214 +07:00 [INF] [2025-05-28 19:49:01.214] [INFO] [nhatrang345]: [WebSocket] nhatrang345: Processing cmd 252 (Tien Len game end)
2025-05-28 19:49:01.215 +07:00 [INF] [2025-05-28 19:49:01.215] [INFO]: 🔄 Reset phân tích đối thủ
2025-05-28 19:49:01.228 +07:00 [ERR] [2025-05-28 19:49:01.228] [ERROR]: ❌ Lỗi ResetGame: Cross-thread operation not valid: Control '' accessed from a thread other than the thread it was created on.
2025-05-28 19:49:01.229 +07:00 [INF] [2025-05-28 19:49:01.229] [INFO]: Reset Tien Len game data
2025-05-28 19:49:01.229 +07:00 [INF] [2025-05-28 19:49:01.229] [INFO] [nhatrang345]: 🎮 Tien Len game ended!
2025-05-28 20:27:06.593 +07:00 [INF] Starting AutoGameBai application...
2025-05-28 20:27:09.386 +07:00 [INF] User selected: HitClub - Mậu Binh
2025-05-28 20:27:09.389 +07:00 [INF] Form1 constructor started.
2025-05-28 20:27:09.407 +07:00 [DBG] [2025-05-28 20:27:09.407] [DEBUG]: Gọi InitializeComponent
2025-05-28 20:27:09.418 +07:00 [INF] [2025-05-28 20:27:09.418] [INFO]: Khởi tạo UIManager thành công
2025-05-28 20:27:09.419 +07:00 [DBG] [2025-05-28 20:27:09.419] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewUsers
2025-05-28 20:27:09.421 +07:00 [INF] [2025-05-28 20:27:09.421] [INFO]: Đã khởi tạo cột cho dataGridViewUsers
2025-05-28 20:27:09.421 +07:00 [DBG] [2025-05-28 20:27:09.421] [DEBUG]: Bắt đầu khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 20:27:09.421 +07:00 [INF] [2025-05-28 20:27:09.421] [INFO]: Đã khởi tạo cột cho dataGridViewSunWinUsers
2025-05-28 20:27:09.422 +07:00 [INF] [2025-05-28 20:27:09.422] [INFO]: Form1 constructor hoàn tất trong 0.03 giây
2025-05-28 20:27:09.434 +07:00 [DBG] [2025-05-28 20:27:09.434] [DEBUG]: Bắt đầu OnLoad
2025-05-28 20:27:09.435 +07:00 [DBG] [2025-05-28 20:27:09.435] [DEBUG]: Bắt đầu LoadConfigAsync
2025-05-28 20:27:09.451 +07:00 [INF] [2025-05-28 20:27:09.451] [INFO]: Đã tải cấu hình từ C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\config.txt, API URL: http://127.0.0.1:11014
2025-05-28 20:27:09.451 +07:00 [INF] [2025-05-28 20:27:09.451] [INFO]: LoadConfigAsync hoàn tất trong 0.02 giây
2025-05-28 20:27:09.468 +07:00 [INF] [2025-05-28 20:27:09.468] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 20:27:09.470 +07:00 [INF] [2025-05-28 20:27:09.470] [INFO]: WebSocketManager initialized with all game handlers
2025-05-28 20:27:09.471 +07:00 [INF] [2025-05-28 20:27:09.471] [INFO]: Đã tải 3 user từ hitclub_token.txt
2025-05-28 20:27:09.471 +07:00 [INF] [2025-05-28 20:27:09.471] [INFO]: Đã tải 1 user từ sunwin_token.txt
2025-05-28 20:27:09.471 +07:00 [INF] [2025-05-28 20:27:09.471] [INFO]: Khởi tạo GameClientManager thành công
2025-05-28 20:27:09.471 +07:00 [INF] [2025-05-28 20:27:09.471] [INFO]: Đã chọn card game: Mậu Binh
2025-05-28 20:27:09.472 +07:00 [INF] InitializeAsync started.
2025-05-28 20:27:09.472 +07:00 [INF] [2025-05-28 20:27:09.472] [INFO]: 🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu
2025-05-28 20:27:09.478 +07:00 [DBG] [2025-05-28 20:27:09.478] [DEBUG]: Bắt đầu UpdateRoomList
2025-05-28 20:27:09.480 +07:00 [DBG] [2025-05-28 20:27:09.480] [DEBUG]: UpdateRoomList hoàn tất trong 0.00 giây
2025-05-28 20:27:09.481 +07:00 [DBG] [2025-05-28 20:27:09.481] [DEBUG]: Bắt đầu LoadUsersToDataGridViewAsync
2025-05-28 20:27:09.481 +07:00 [DBG] [2025-05-28 20:27:09.481] [DEBUG]: Tải 3 người dùng vào DataGridView
2025-05-28 20:27:09.488 +07:00 [DBG] [2025-05-28 20:27:09.488] [DEBUG] [nhatrang345]: Cập nhật trạng thái profile cho nhatrang345: Đóng
2025-05-28 20:27:09.488 +07:00 [INF] [2025-05-28 20:27:09.488] [INFO] [nhatrang345]: Kiểm tra trạng thái profile cho nhatrang345: Đóng
2025-05-28 20:27:09.488 +07:00 [DBG] [2025-05-28 20:27:09.488] [DEBUG]: Row values: ID=1, Username=nhatrang345
2025-05-28 20:27:09.490 +07:00 [DBG] [2025-05-28 20:27:09.490] [DEBUG]: Số cột trong hàng: 5
2025-05-28 20:27:09.490 +07:00 [DBG] [2025-05-28 20:27:09.490] [DEBUG] [phanthiet989]: Cập nhật trạng thái profile cho phanthiet989: Đóng
2025-05-28 20:27:09.490 +07:00 [INF] [2025-05-28 20:27:09.490] [INFO] [phanthiet989]: Kiểm tra trạng thái profile cho phanthiet989: Đóng
2025-05-28 20:27:09.490 +07:00 [DBG] [2025-05-28 20:27:09.490] [DEBUG]: Row values: ID=2, Username=phanthiet989
2025-05-28 20:27:09.490 +07:00 [DBG] [2025-05-28 20:27:09.490] [DEBUG]: Số cột trong hàng: 5
2025-05-28 20:27:09.490 +07:00 [DBG] [2025-05-28 20:27:09.490] [DEBUG] [namdinhx852]: Cập nhật trạng thái profile cho namdinhx852: Đóng
2025-05-28 20:27:09.490 +07:00 [INF] [2025-05-28 20:27:09.490] [INFO] [namdinhx852]: Kiểm tra trạng thái profile cho namdinhx852: Đóng
2025-05-28 20:27:09.490 +07:00 [DBG] [2025-05-28 20:27:09.490] [DEBUG]: Row values: ID=3, Username=namdinhx852
2025-05-28 20:27:09.490 +07:00 [DBG] [2025-05-28 20:27:09.490] [DEBUG]: Số cột trong hàng: 5
2025-05-28 20:27:09.549 +07:00 [INF] [2025-05-28 20:27:09.549] [INFO]: Tải danh sách user thành công
2025-05-28 20:27:09.562 +07:00 [INF] [2025-05-28 20:27:09.562] [INFO]: LoadUsersToDataGridViewAsync hoàn tất trong 0.08 giây
2025-05-28 20:27:09.562 +07:00 [DBG] [2025-05-28 20:27:09.562] [DEBUG]: OnLoad hoàn tất
2025-05-28 20:27:10.423 +07:00 [DBG] [2025-05-28 20:27:10.423] [DEBUG]: BtnShowSuggestions_Click: Hiển thị form gợi ý cho Mậu Binh
2025-05-28 20:27:10.428 +07:00 [INF] [2025-05-28 20:27:10.428] [INFO]: Mậu Binh suggestion form temporarily disabled
2025-05-28 20:27:15.285 +07:00 [DBG] [2025-05-28 20:27:15.285] [DEBUG]: Bắt đầu đóng Form1
2025-05-28 20:27:15.334 +07:00 [INF] [2025-05-28 20:27:15.334] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 22344)
2025-05-28 20:27:15.334 +07:00 [INF] [2025-05-28 20:27:15.334] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 31528)
2025-05-28 20:27:15.334 +07:00 [INF] [2025-05-28 20:27:15.334] [INFO]: Đã dừng tiến trình chromedriver.exe (PID: 11312)
2025-05-28 20:27:15.334 +07:00 [INF] [2025-05-28 20:27:15.334] [INFO]: MauBinhCardManager disposed
2025-05-28 20:27:15.334 +07:00 [INF] [2025-05-28 20:27:15.334] [INFO]: Đã đóng Form1 thành công
2025-05-28 20:27:15.344 +07:00 [INF] Application started successfully.
