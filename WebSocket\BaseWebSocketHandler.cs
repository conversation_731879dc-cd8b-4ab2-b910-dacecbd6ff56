using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.DevTools;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoGameBai.Models;
using AutoGameBai.UI;

namespace AutoGameBai.WebSocket
{
    /// <summary>
    /// Base WebSocket handler chứa common logic cho tất cả games
    /// </summary>
    public class BaseWebSocketHandler
    {
        protected readonly GameClientManager _gameClient;
        protected readonly UIManager _uiManager;
        protected readonly Dictionary<string, IDevToolsSession> _devToolsSessions;
        protected readonly Dictionary<string, TaskCompletionSource<bool>> _roomJoinedTcs;
        protected readonly Dictionary<string, List<User>> _roomPlayers;
        protected readonly Dictionary<string, int> _roomTimes;
        protected readonly Dictionary<string, int> _userSeats;
        protected readonly Dictionary<string, int[]> _userCards;
        protected readonly Dictionary<string, string> _userRooms;
        protected readonly object _lock = new object();

        // Game-specific handlers
        protected readonly Dictionary<int, IGameWebSocketHandler> _gameHandlers;

        public BaseWebSocketHandler(GameClientManager gameClient, UIManager uiManager)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));

            _devToolsSessions = new Dictionary<string, IDevToolsSession>();
            _roomJoinedTcs = new Dictionary<string, TaskCompletionSource<bool>>();
            _roomPlayers = new Dictionary<string, List<User>>();
            _roomTimes = new Dictionary<string, int>();
            _userSeats = new Dictionary<string, int>();
            _userCards = new Dictionary<string, int[]>();
            _userRooms = new Dictionary<string, string>();
            _gameHandlers = new Dictionary<int, IGameWebSocketHandler>();

            InitializeGameHandlers();
        }

        /// <summary>
        /// Khởi tạo các game-specific handlers
        /// </summary>
        protected virtual void InitializeGameHandlers()
        {
            // Sẽ được implement trong derived classes hoặc dependency injection
        }

        /// <summary>
        /// Đăng ký game handler
        /// </summary>
        public void RegisterGameHandler(IGameWebSocketHandler handler)
        {
            foreach (var cmd in handler.SupportedCommands)
            {
                _gameHandlers[cmd] = handler;
            }
        }

        /// <summary>
        /// Xử lý WebSocket message
        /// </summary>
        public virtual void ProcessMessage(string payloadData, string username)
        {
            try
            {
                if (string.IsNullOrEmpty(payloadData) || !payloadData.StartsWith("[")) return;

                var jsonArray = JArray.Parse(payloadData);
                if (jsonArray.Count < 1) return;

                string messageType = jsonArray[0].ToString();
                if (string.IsNullOrEmpty(messageType)) return;

                // Skip MiniGame messages
                if (messageType == "7" && jsonArray.Count > 1 && jsonArray[1]?.ToString() == "MiniGame") return;

                if (messageType == "5" && jsonArray.Count >= 2)
                {
                    JObject messageData = jsonArray[1].Type == JTokenType.String && jsonArray[3] != null
                        ? jsonArray[3] as JObject
                        : jsonArray[1] as JObject;

                    if (messageData == null) return;

                    int cmd = messageData["cmd"]?.Value<int>() ?? -1;

                    // Skip certain system commands
                    if (cmd == 300 || cmd == 10000 || cmd == 10001 || cmd == 10003 || cmd == 1015) return;

                    // Route to appropriate handler
                    if (_gameHandlers.ContainsKey(cmd))
                    {
                        _gameHandlers[cmd].HandleCommand(messageData, username, cmd);
                    }
                    else if (IsCommonCommand(cmd))
                    {
                        HandleCommonCommand(messageData, username, cmd);
                    }
                }
                else if (messageType == "4")
                {
                    HandleLeaveRoomMessage(jsonArray, username);
                }
                else if (messageType == "6")
                {
                    HandleControlMessage(payloadData, username);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing WebSocket message for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Kiểm tra xem có phải common command không
        /// </summary>
        protected virtual bool IsCommonCommand(int cmd)
        {
            return cmd == 202 || cmd == 100 || cmd == 2006 || cmd == 2007;
        }

        /// <summary>
        /// Xử lý common commands
        /// </summary>
        protected virtual void HandleCommonCommand(JObject messageData, string username, int cmd)
        {
            switch (cmd)
            {
                case 202:
                    HandleCmd202(messageData, username);
                    break;
                case 100:
                case 2006:
                case 2007:
                    HandleSystemMessage(messageData, username);
                    break;
            }
        }

        /// <summary>
        /// Xử lý cmd 202 - Join Room
        /// </summary>
        protected virtual void HandleCmd202(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 202 (Join Room)", UIManager.LogLevel.Debug, username);

                int currentUserSeat = -1;
                int playerCount = 0;
                var players = new List<User>();

                if (messageData.ContainsKey("ps"))
                {
                    var playersArray = messageData["ps"] as JArray;

                    foreach (var player in playersArray ?? new JArray())
                    {
                        var playerUsername = player["dn"]?.ToString();
                        if (string.IsNullOrEmpty(playerUsername)) continue;

                        var user = new User
                        {
                            Username = playerUsername,
                            DisplayName = playerUsername,
                            Seat = player["sit"]?.Value<int>() ?? -1,
                            Gold = player["m"]?.Value<long>() ?? 0,
                            Uid = player["uid"]?.ToString()
                        };

                        if (_gameClient.SelectedGame == "SunWin")
                        {
                            user.Chip = player["As"]?["chip"]?.Value<long>() ?? 0;
                            user.Vip = player["As"]?["vip"]?.Value<int>() ?? 0;
                            user.Exp = player["As"]?["exp"]?.Value<long>() ?? 0;
                        }

                        players.Add(user);

                        if (_gameClient.GetUsers().ContainsKey(user.Username) && !string.IsNullOrEmpty(user.Uid))
                        {
                            var existingUser = _gameClient.GetUsers()[user.Username];
                            existingUser.Uid = user.Uid;
                            existingUser.Gold = user.Gold;
                            if (_gameClient.SelectedGame == "SunWin")
                            {
                                existingUser.Chip = user.Chip;
                                existingUser.Vip = user.Vip;
                                existingUser.Exp = user.Exp;
                            }
                        }

                        if (user.Username == username)
                        {
                            currentUserSeat = user.Seat;
                        }
                    }

                    playerCount = players.Count;

                    lock (_lock)
                    {
                        _roomPlayers[username] = players;
                        _userSeats[username] = currentUserSeat;
                    }
                }

                if (messageData.ContainsKey("rmT"))
                {
                    int rmT = messageData["rmT"]?.Value<int?>() ?? -1;
                    lock (_lock)
                    {
                        _roomTimes[username] = rmT;
                    }
                }

                // ✅ KIỂM TRA ĐIỀU KIỆN PHÒNG VÀ TỰ ĐỘNG THOÁT NẾU CẦN
                bool shouldAutoLeave = CheckRoomConditionsAndLeaveIfNeeded(username, currentUserSeat, playerCount, players);

                lock (_lock)
                {
                    if (_roomJoinedTcs.ContainsKey(username))
                    {
                        if (shouldAutoLeave)
                        {
                            // Nếu tự động thoát phòng, báo thất bại để RoomManager thử lại ngay
                            _uiManager.AppendLog($"🔄 Báo TaskCompletionSource thất bại cho {username} (shouldAutoLeave=true)", UIManager.LogLevel.Debug, username);
                            _roomJoinedTcs[username].TrySetResult(false);
                            _roomJoinedTcs.Remove(username);
                        }
                        else
                        {
                            // Vào phòng thành công, báo thành công
                            _uiManager.AppendLog($"✅ Báo TaskCompletionSource thành công cho {username} (shouldAutoLeave=false)", UIManager.LogLevel.Debug, username);
                            _roomJoinedTcs[username].TrySetResult(true);
                            _roomJoinedTcs.Remove(username);
                        }
                    }
                    else
                    {
                        _uiManager.AppendLog($"⚠️ Không tìm thấy TaskCompletionSource cho {username}", UIManager.LogLevel.Warning, username);
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 202 for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Kiểm tra điều kiện phòng và tự động thoát nếu cần thiết
        /// </summary>
        /// <returns>True nếu tự động thoát phòng, False nếu ở lại phòng</returns>
        protected virtual bool CheckRoomConditionsAndLeaveIfNeeded(string username, int currentUserSeat, int playerCount, List<User> players)
        {
            try
            {
                // Lấy thông tin user hiện tại
                if (!_gameClient.GetUsers().ContainsKey(username))
                {
                    _uiManager.AppendLog($"❌ Không tìm thấy user {username} trong danh sách", UIManager.LogLevel.Warning, username);
                    return false; // Không thoát phòng
                }

                var currentUser = _gameClient.GetUsers()[username];

                // Kiểm tra điều kiện cho Get Key Room (lấy bàn trống)
                if (IsGetKeyRoomMode(username))
                {
                    // Điều kiện: sit = 0 và chỉ có 1 người (bàn trống)
                    if (currentUserSeat == 0 && playerCount == 1)
                    {
                        _uiManager.AppendLog($"✅ Tìm thấy bàn trống hợp lệ cho {username} (sit: {currentUserSeat}, số người: {playerCount})", UIManager.LogLevel.Info, username);
                        return false; // Không thoát, bàn hợp lệ
                    }
                    else
                    {
                        _uiManager.AppendLog($"❌ Bàn không hợp lệ cho Get Key Room {username} (sit: {currentUserSeat}, số người: {playerCount}), tự động thoát phòng", UIManager.LogLevel.Info, username);
                        AutoLeaveRoom(username, "Bàn không trống");
                        return true; // Tự động thoát phòng
                    }
                }

                // Kiểm tra điều kiện cho Join Main User (tìm main user)
                string mainUser = GetMainUserForJoin(username);
                if (!string.IsNullOrEmpty(mainUser))
                {
                    // Tìm main user trong danh sách players
                    bool foundMainUser = players.Any(p => p.Username == mainUser);

                    if (foundMainUser && playerCount <= GetMaxPlayersForGame())
                    {
                        _uiManager.AppendLog($"✅ Tìm thấy mainUser {mainUser} trong phòng cho {username} (số người: {playerCount})", UIManager.LogLevel.Info, username);
                        return false; // Không thoát, tìm thấy main user
                    }
                    else
                    {
                        string reason = !foundMainUser ? $"Không tìm thấy mainUser {mainUser}" : $"Phòng đầy ({playerCount} người)";
                        _uiManager.AppendLog($"❌ {reason} cho {username}, tự động thoát phòng", UIManager.LogLevel.Info, username);
                        AutoLeaveRoom(username, reason);
                        return true; // Tự động thoát phòng
                    }
                }

                // Kiểm tra điều kiện chung cho Get Empty Table
                if (IsGetEmptyTableMode(username))
                {
                    // Điều kiện: sit = 0 và số người <= 2 (bàn ít người)
                    if (currentUserSeat == 0 && playerCount <= 2)
                    {
                        _uiManager.AppendLog($"✅ Tìm thấy bàn ít người hợp lệ cho {username} (sit: {currentUserSeat}, số người: {playerCount})", UIManager.LogLevel.Info, username);
                        return false; // Không thoát, bàn hợp lệ
                    }
                    else
                    {
                        _uiManager.AppendLog($"❌ Bàn không hợp lệ cho Get Empty Table {username} (sit: {currentUserSeat}, số người: {playerCount}), tự động thoát phòng", UIManager.LogLevel.Info, username);
                        AutoLeaveRoom(username, "Bàn không ít người");
                        return true; // Tự động thoát phòng
                    }
                }

                _uiManager.AppendLog($"ℹ️ Không có điều kiện đặc biệt cho {username}, giữ nguyên trong phòng (sit: {currentUserSeat}, số người: {playerCount})", UIManager.LogLevel.Debug, username);
                return false; // Không thoát phòng
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi kiểm tra điều kiện phòng cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                return false; // Không thoát phòng khi có lỗi
            }
        }

        /// <summary>
        /// Tự động thoát phòng
        /// </summary>
        protected virtual void AutoLeaveRoom(string username, string reason)
        {
            try
            {
                _uiManager.AppendLog($"🚪 Bắt đầu tự động thoát phòng cho {username} - Lý do: {reason}", UIManager.LogLevel.Info, username);

                bool hasCards = false;
                lock (_lock)
                {
                    hasCards = _userCards.ContainsKey(username);
                }

                if (hasCards)
                {
                    _uiManager.AppendLog($"⚠️ User {username} đã nhận bài (cmd 600), không thể tự động thoát phòng để tránh crash", UIManager.LogLevel.Warning, username);
                    return;
                }

                // Gọi async để không block WebSocket thread
                Task.Run(async () =>
                {
                    try
                    {
                        _uiManager.AppendLog($"🔄 Đang thực hiện thoát phòng cho {username}...", UIManager.LogLevel.Info, username);
                        await _gameClient.LeaveRoomAsync(username, 200);
                        _uiManager.AppendLog($"✅ Tự động thoát phòng thành công cho {username}", UIManager.LogLevel.Info, username);

                        // Đợi user về lobby (nếu cần)
                        var driver = _gameClient.GetDrivers().GetValueOrDefault(username);
                        if (driver != null)
                        {
                            int waited = 0;
                            while (!_gameClient.IsInLobby(username, driver) && waited < 5000)
                            {
                                await Task.Delay(200);
                                waited += 200;
                            }
                        }

                        // Báo hiệu cho TaskCompletionSource khi đã thoát xong
                        lock (_lock)
                        {
                            if (_roomJoinedTcs.ContainsKey(username))
                            {
                                _uiManager.AppendLog($"🔄 Báo TaskCompletionSource thất bại cho {username} (sau khi đã thoát phòng)", UIManager.LogLevel.Debug, username);
                                _roomJoinedTcs[username].TrySetResult(false);
                                _roomJoinedTcs.Remove(username);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"❌ Lỗi tự động thoát phòng cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                    }
                });
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khởi tạo tự động thoát phòng cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Kiểm tra xem có đang ở chế độ Get Key Room không
        /// </summary>
        protected virtual bool IsGetKeyRoomMode(string username)
        {
            // Logic để xác định user đang ở chế độ Get Key Room
            // Có thể dựa vào trạng thái button hoặc flag trong GameClientManager
            return _gameClient.IsUserInGetKeyRoomMode(username);
        }

        /// <summary>
        /// Kiểm tra xem có đang ở chế độ Get Empty Table không
        /// </summary>
        protected virtual bool IsGetEmptyTableMode(string username)
        {
            // Logic để xác định user đang ở chế độ Get Empty Table
            return _gameClient.IsUserInGetEmptyTableMode(username);
        }

        /// <summary>
        /// Lấy main user để join (nếu có)
        /// </summary>
        protected virtual string GetMainUserForJoin(string username)
        {
            // Logic để lấy main user mà user này cần tìm
            return _gameClient.GetMainUserForJoin(username);
        }

        /// <summary>
        /// Lấy số người tối đa cho game hiện tại
        /// </summary>
        protected virtual int GetMaxPlayersForGame()
        {
            // Mau Binh: 4 người, Phom: 4 người, Tien Len: 4 người
            return 4;
        }

        /// <summary>
        /// Xử lý system messages
        /// </summary>
        protected virtual void HandleSystemMessage(JObject messageData, string username)
        {
            // Handle system messages if needed
        }

        /// <summary>
        /// Xử lý leave room message
        /// </summary>
        protected virtual void HandleLeaveRoomMessage(JArray jsonArray, string username)
        {
            if (jsonArray.Count >= 2 && jsonArray[1]?.Type == JTokenType.Boolean && jsonArray[1].Value<bool>())
            {
                bool isLeaveMessage = _gameClient.SelectedGame == "HitClub"
                    ? jsonArray.Count == 6 && jsonArray[1].Value<bool>() == true && jsonArray[2]?.Value<int>() == 1 && jsonArray[3]?.Value<int>() == -1 && jsonArray[4]?.Value<int>() == 0 && jsonArray[5]?.ToString() == ""
                    : jsonArray.Count >= 4 && jsonArray[1].Value<bool>() == true && jsonArray[2]?.Value<int>() == 1 && jsonArray[4]?.Value<int>() == 0 && jsonArray[5]?.ToString() == "";

                if (isLeaveMessage)
                {
                    _uiManager.AppendLog($"Confirmed room leave for {username}", UIManager.LogLevel.Info, username);
                    lock (_lock)
                    {
                        if (_roomJoinedTcs.ContainsKey(username))
                        {
                            _roomJoinedTcs[username].TrySetResult(true);
                        }
                        _userCards.Remove(username);
                        _userRooms.Remove(username);
                        _roomPlayers.Remove(username);
                        _roomTimes.Remove(username);
                        _userSeats.Remove(username);
                    }
                }
            }
        }

        /// <summary>
        /// Xử lý control messages
        /// </summary>
        protected virtual async void HandleControlMessage(string payloadData, string username)
        {
            _uiManager.AppendLog($"[WebSocket] {username}: Skipping control message (type 6): {payloadData}", UIManager.LogLevel.Info, username);
            await MessageHandler.HandleMessage(_gameClient, username, payloadData);
        }

        // Getters
        public Dictionary<string, IDevToolsSession> GetDevToolsSessions() => _devToolsSessions;
        public object GetLock() => _lock;
        public Dictionary<string, int[]> GetUserCardsDict() => _userCards;
        public Dictionary<string, List<User>> GetRoomPlayers() => _roomPlayers;
        public Dictionary<string, int> GetRoomTimes() => _roomTimes;
        public Dictionary<string, int> GetUserSeats() => _userSeats;
    }
}
