﻿using AutoGameBai.Gamemaubinh;
using System;
using System.Collections.Generic;
using System.Linq;

namespace AutoGameBai.Gamemaubinh
{
    public class MauBinhCardManager : IDisposable
    {
        private readonly UIManager _uiManager;
        private readonly GameClientManager _gameClient;
        private readonly MauBinhEngine _engine;
        private bool _disposed;

        public MauBinhCardManager(UIManager uiManager, GameClientManager gameClient = null)
        {
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _gameClient = gameClient;
            _engine = new MauBinhEngine(uiManager);
            _uiManager.AppendLog("🚀 Đã khởi tạo MauBinhCardManager với thuật toán tối ưu", UIManager.LogLevel.Info);
        }

        public int[] SortCards(int[] cards, string username)
        {
            if (cards == null || cards.Length != 13)
            {
                _uiManager.AppendLog($"Dữ liệu bài không hợp lệ cho {username}: {cards?.Length ?? 0} lá (yêu cầu 13)", UIManager.LogLevel.Error, username);
                throw new ArgumentException("Yêu cầu chính xác 13 lá bài");
            }

            try
            {
                var cardInfos = cards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();
                var sortedCards = cardInfos
                    .OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank))
                    .ThenBy(c => c.Suit)
                    .Select(c => c.Id)
                    .ToArray();

                _uiManager.AppendLog($"Đã sắp xếp bài cho {username}: {CardUtilityMaubinh.ConvertCardsToString(sortedCards)}", UIManager.LogLevel.Info, username);
                return sortedCards;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi sắp xếp bài cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                throw;
            }
        }

        public List<(int[] Chi1, int[] Chi2, int[] Chi3)> GenerateSuggestions(int[] cards, bool isOpponent = false, int[] opponentCards = null)
        {
            try
            {
                if (cards == null || cards.Length != 13)
                {
                    _uiManager.AppendLog($"Dữ liệu bài không hợp lệ: {cards?.Length ?? 0} lá (yêu cầu 13)", UIManager.LogLevel.Error);
                    return new List<(int[] Chi1, int[] Chi2, int[] Chi3)>();
                }

                _uiManager.AppendLog($"🚀 Tạo gợi ý với thuật toán OPTIMIZED cho {cards.Length} lá bài", UIManager.LogLevel.Info);

                // Sử dụng thuật toán mới
                var suggestions = _engine.GenerateSuggestions(cards);
                if (suggestions.Any())
                {
                    LogSuggestions(suggestions);
                    return ConvertToLegacyFormat(suggestions);
                }

                // Fallback 2: Tạo gợi ý mặc định
                _uiManager.AppendLog("🔄 Fallback sang gợi ý đơn giản", UIManager.LogLevel.Warning);
                return CreateDefaultSuggestion(cards);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi tạo gợi ý: {ex.Message}", UIManager.LogLevel.Error);
                return CreateDefaultSuggestion(cards);
            }
        }

        /// <summary>
        /// Tạo gợi ý mặc định khi thuật toán tối ưu thất bại
        /// </summary>
        private List<(int[] Chi1, int[] Chi2, int[] Chi3)> CreateDefaultSuggestion(int[] cards)
        {
            try
            {
                if (cards == null || cards.Length != 13)
                {
                    return new List<(int[] Chi1, int[] Chi2, int[] Chi3)>();
                }

                var cardInfos = cards.Select(id => new CardUtilityMaubinh.CardInfo(id))
                                    .OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank))
                                    .ThenBy(c => c.Suit)
                                    .ToList();

                // Gợi ý mặc định đơn giản: 5-5-3 theo thứ tự giảm dần
                var chi1 = cardInfos.Take(5).Select(c => c.Id).ToArray();
                var chi2 = cardInfos.Skip(5).Take(5).Select(c => c.Id).ToArray();
                var chi3 = cardInfos.Skip(10).Take(3).Select(c => c.Id).ToArray();

                _uiManager.AppendLog("✅ Đã tạo gợi ý mặc định", UIManager.LogLevel.Info);
                return new List<(int[] Chi1, int[] Chi2, int[] Chi3)> { (chi1, chi2, chi3) };
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tạo gợi ý mặc định: {ex.Message}", UIManager.LogLevel.Error);
                return new List<(int[] Chi1, int[] Chi2, int[] Chi3)>();
            }
        }

        public List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)> OptimizeTeamSuggestions(Dictionary<string, int[]> userCards, int[] opponentCards)
        {
            var teamSuggestions = new List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)>();
            var userSuggestions = new Dictionary<string, List<(int[] Chi1, int[] Chi2, int[] Chi3)>>();

            foreach (var user in userCards.Keys)
            {
                var suggestions = GenerateSuggestions(userCards[user]);
                userSuggestions[user] = suggestions.Take(10).ToList();
                _uiManager.AppendLog($"Đã tạo {userSuggestions[user].Count} gợi ý cho {user}", UIManager.LogLevel.Info, user);
            }

            var opponentSuggestions = GenerateSuggestions(opponentCards, isOpponent: true);
            _uiManager.AppendLog($"Đã tạo {opponentSuggestions.Count} gợi ý cho đối thủ", UIManager.LogLevel.Info);

            double bestTeamScore = double.MinValue;
            int bestNumLosingChis = 0;
            int bestNumAllLosingChis = 0;
            int bestOpponentPoints = int.MaxValue;
            var bestTeamCombination = new List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)>();

            foreach (var user in userCards.Keys)
            {
                foreach (var suggestion in userSuggestions[user])
                {
                    var cards = suggestion.Chi1.Concat(suggestion.Chi2).Concat(suggestion.Chi3).ToList();
                    var cardInfos = cards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();
                    int specialPoints = CalculateSpecialPoints(cardInfos);
                    if (specialPoints > 0)
                    {
                        bestTeamCombination.Clear();
                        bestTeamCombination.Add((user, suggestion.Chi1, suggestion.Chi2, suggestion.Chi3));
                        bestTeamScore = specialPoints;
                        bestOpponentPoints = -specialPoints;
                        bestNumLosingChis = 3;
                        bestNumAllLosingChis = 3;

                        foreach (var otherUser in userCards.Keys.Where(u => u != user))
                        {
                            var defaultSuggestion = userSuggestions[otherUser].FirstOrDefault();
                            bestTeamCombination.Add((otherUser, defaultSuggestion.Chi1, defaultSuggestion.Chi2, defaultSuggestion.Chi3));
                        }

                        _uiManager.AppendLog($"Phát hiện bài đặc biệt cho {user} (điểm: {specialPoints}), ưu tiên tổ hợp này", UIManager.LogLevel.Info);
                        goto FinalizeSuggestions;
                    }
                }
            }

            foreach (var suggestion1 in userSuggestions[userCards.Keys.ElementAt(0)].Take(10))
            {
                foreach (var suggestion2 in userSuggestions[userCards.Keys.ElementAt(1)].Take(10))
                {
                    foreach (var suggestion3 in userSuggestions[userCards.Keys.ElementAt(2)].Take(10))
                    {
                        var teamCombination = new List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)>
                        {
                            (userCards.Keys.ElementAt(0), suggestion1.Chi1, suggestion1.Chi2, suggestion1.Chi3),
                            (userCards.Keys.ElementAt(1), suggestion2.Chi1, suggestion2.Chi2, suggestion2.Chi3),
                            (userCards.Keys.ElementAt(2), suggestion3.Chi1, suggestion3.Chi2, suggestion3.Chi3)
                        };

                        double teamScore = 0;
                        int numLosingChis = 0;
                        int numAllLosingChis = 0;
                        int opponentPoints = 0;
                        int count = 0;

                        foreach (var opponentSuggestion in opponentSuggestions.Take(3))
                        {
                            int chi1Wins = 0, chi2Wins = 0, chi3Wins = 0;
                            double combinationScore = 0;
                            int opponentPointsForSuggestion = 0;

                            foreach (var userSuggestion in teamCombination)
                            {
                                var userCardsList = userSuggestion.Chi1.Concat(userSuggestion.Chi2).Concat(userSuggestion.Chi3).Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();
                                var (chi1WinRate, chi2WinRate, chi3WinRate) = CalculateWinRates(
                                    (userSuggestion.Chi1, userSuggestion.Chi2, userSuggestion.Chi3),
                                    opponentSuggestion);
                                combinationScore += (chi1WinRate + chi2WinRate * 2 + chi3WinRate * 0.5) / 3.5;

                                if (chi1WinRate > 50) chi1Wins++;
                                if (chi2WinRate > 50) chi2Wins++;
                                if (chi3WinRate > 50) chi3Wins++;

                                int specialPoints = CalculateSpecialPoints(userCardsList);
                                if (specialPoints > 0)
                                {
                                    opponentPointsForSuggestion -= specialPoints;
                                    combinationScore += specialPoints / 3;
                                }
                                else
                                {
                                    opponentPointsForSuggestion += CalculateChiPoints(userSuggestion.Chi1, opponentSuggestion.Chi1, 0, specialPoints > 0);
                                    opponentPointsForSuggestion += CalculateChiPoints(userSuggestion.Chi2, opponentSuggestion.Chi2, 1, specialPoints > 0);
                                    opponentPointsForSuggestion += CalculateChiPoints(userSuggestion.Chi3, opponentSuggestion.Chi3, 2, specialPoints > 0);
                                }
                            }

                            if (chi1Wins >= 2) numLosingChis++;
                            if (chi2Wins >= 2) numLosingChis++;
                            if (chi3Wins >= 2) numLosingChis++;

                            if (chi1Wins == 3 && teamCombination.All(u => CalculateWinRates((u.Chi1, u.Chi2, u.Chi3), opponentSuggestion).Chi1WinRate > 80))
                                numAllLosingChis++;
                            if (chi2Wins == 3 && teamCombination.All(u => CalculateWinRates((u.Chi1, u.Chi2, u.Chi3), opponentSuggestion).Chi2WinRate > 80))
                                numAllLosingChis++;
                            if (chi3Wins == 3 && teamCombination.All(u => CalculateWinRates((u.Chi1, u.Chi2, u.Chi3), opponentSuggestion).Chi3WinRate > 80))
                                numAllLosingChis++;

                            teamScore += combinationScore / teamCombination.Count;
                            opponentPoints += opponentPointsForSuggestion;
                            count++;
                        }

                        if (count > 0)
                        {
                            teamScore /= count;
                            opponentPoints /= count;
                            _uiManager.AppendLog($"Evaluated combination: opponentPoints={opponentPoints}, numLosingChis={numLosingChis}, numAllLosingChis={numAllLosingChis}, teamScore={teamScore:F2}", UIManager.LogLevel.Debug);

                            bool meetsTwoThirds = numLosingChis >= 3;

                            if (opponentPoints < bestOpponentPoints ||
                                (opponentPoints == bestOpponentPoints && meetsTwoThirds && numLosingChis > bestNumLosingChis) ||
                                (opponentPoints == bestOpponentPoints && meetsTwoThirds && numLosingChis == bestNumLosingChis && numAllLosingChis > bestNumAllLosingChis) ||
                                (opponentPoints == bestOpponentPoints && meetsTwoThirds && numLosingChis == bestNumLosingChis && numAllLosingChis == bestNumAllLosingChis && teamScore > bestTeamScore))
                            {
                                bestNumLosingChis = numLosingChis;
                                bestNumAllLosingChis = numAllLosingChis;
                                bestTeamScore = teamScore;
                                bestOpponentPoints = opponentPoints;
                                bestTeamCombination = teamCombination;
                                _uiManager.AppendLog($"Updated best combination: opponentPoints={bestOpponentPoints}, numLosingChis={bestNumLosingChis}, numAllLosingChis={bestNumAllLosingChis}, teamScore={bestTeamScore:F2}", UIManager.LogLevel.Debug);
                            }
                        }
                    }
                }
            }

        FinalizeSuggestions:
            if (bestTeamCombination.Any())
            {
                teamSuggestions.AddRange(bestTeamCombination);
                _uiManager.AppendLog($"Selected best team combination: opponentPoints={bestOpponentPoints}, numLosingChis={bestNumLosingChis}, numAllLosingChis={bestNumAllLosingChis}, teamScore={bestTeamScore:F2}", UIManager.LogLevel.Info);
            }
            else
            {
                foreach (var user in userCards.Keys)
                {
                    var suggestion = userSuggestions[user].FirstOrDefault();
                    if (suggestion.Chi1 != null)
                    {
                        teamSuggestions.Add((user, suggestion.Chi1, suggestion.Chi2, suggestion.Chi3));
                    }
                }
                _uiManager.AppendLog("No optimal team combination found, using default suggestions", UIManager.LogLevel.Warning);
            }

            return teamSuggestions;
        }

        public (double Chi1WinRate, double Chi2WinRate, double Chi3WinRate) CalculateWinRates(
            (int[] Chi1, int[] Chi2, int[] Chi3) userSuggestion,
            (int[] Chi1, int[] Chi2, int[] Chi3) opponentSuggestion)
        {
            double chi1WinRate = CalculateChiWinRate(userSuggestion.Chi1, opponentSuggestion.Chi1);
            double chi2WinRate = CalculateChiWinRate(userSuggestion.Chi2, opponentSuggestion.Chi2);
            double chi3WinRate = CalculateChiWinRate(userSuggestion.Chi3, opponentSuggestion.Chi3);

            return (chi1WinRate, chi2WinRate, chi3WinRate);
        }

        private double CalculateChiWinRate(int[] userChi, int[] opponentChi)
        {
            var userCards = userChi.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();
            var opponentCards = opponentChi.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();

            var userStrength = CardUtilityMaubinh.GetChiStrength(userCards);
            var opponentStrength = CardUtilityMaubinh.GetChiStrength(opponentCards);

            var userHandType = CardUtilityMaubinh.EvaluateHand(userChi);
            var opponentHandType = CardUtilityMaubinh.EvaluateHand(opponentChi);

            if (userHandType != opponentHandType)
            {
                if ((int)userHandType > (int)opponentHandType)
                    return Math.Min(85 + ((int)userHandType - (int)opponentHandType) * 3, 99.9);
                else
                    return Math.Max(15 - ((int)opponentHandType - (int)userHandType) * 3, 0.1);
            }

            if (userStrength > opponentStrength)
            {
                double difference = userStrength - opponentStrength;
                return Math.Min(75 + difference * 5, 99.9);
            }
            else if (userStrength < opponentStrength)
            {
                double difference = opponentStrength - userStrength;
                return Math.Max(25 - difference * 5, 0.1);
            }

            var userHighCard = userCards.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).First();
            var opponentHighCard = opponentCards.OrderByDescending(c => CardUtilityMaubinh.GetCardValue(c.Rank)).First();

            if (CardUtilityMaubinh.GetCardValue(userHighCard.Rank) > CardUtilityMaubinh.GetCardValue(opponentHighCard.Rank))
                return 60;
            else if (CardUtilityMaubinh.GetCardValue(userHighCard.Rank) < CardUtilityMaubinh.GetCardValue(opponentHighCard.Rank))
                return 40;

            if (userHighCard.Suit < opponentHighCard.Suit)
                return 55;
            else
                return 45;
        }

        private int CalculateChiPoints(int[] userChi, int[] opponentChi, int chiIndex, bool hasSpecialCase)
        {
            if (hasSpecialCase)
            {
                return 0;
            }

            var userHandType = CardUtilityMaubinh.EvaluateHand(userChi);
            var opponentHandType = CardUtilityMaubinh.EvaluateHand(opponentChi);
            var userStrength = CardUtilityMaubinh.GetChiStrength(userChi.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());
            var opponentStrength = CardUtilityMaubinh.GetChiStrength(opponentChi.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());

            int points = 0;

            if (userHandType != opponentHandType)
            {
                if ((int)userHandType > (int)opponentHandType)
                {
                    points = chiIndex switch
                    {
                        0 => userHandType switch
                        {
                            CardUtilityMaubinh.HandType.StraightFlush => -10,
                            CardUtilityMaubinh.HandType.FourOfAKind => -8,
                            _ => -1
                        },
                        1 => userHandType switch
                        {
                            CardUtilityMaubinh.HandType.StraightFlush => -20,
                            CardUtilityMaubinh.HandType.FourOfAKind => -16,
                            CardUtilityMaubinh.HandType.FullHouse => -4,
                            _ => -1
                        },
                        2 => userHandType switch
                        {
                            CardUtilityMaubinh.HandType.ThreeOfAKind => -6,
                            _ => -1
                        },
                        _ => -1
                    };
                }
                else
                {
                    points = chiIndex switch
                    {
                        0 => opponentHandType switch
                        {
                            CardUtilityMaubinh.HandType.StraightFlush => 10,
                            CardUtilityMaubinh.HandType.FourOfAKind => 8,
                            _ => 1
                        },
                        1 => opponentHandType switch
                        {
                            CardUtilityMaubinh.HandType.StraightFlush => 20,
                            CardUtilityMaubinh.HandType.FourOfAKind => 16,
                            CardUtilityMaubinh.HandType.FullHouse => 4,
                            _ => 1
                        },
                        2 => opponentHandType switch
                        {
                            CardUtilityMaubinh.HandType.ThreeOfAKind => 6,
                            _ => 1
                        },
                        _ => 1
                    };
                }
            }
            else if (userStrength > opponentStrength)
            {
                points = chiIndex switch
                {
                    0 => userHandType switch
                    {
                        CardUtilityMaubinh.HandType.StraightFlush => -10,
                        CardUtilityMaubinh.HandType.FourOfAKind => -8,
                        _ => -1
                    },
                    1 => userHandType switch
                    {
                        CardUtilityMaubinh.HandType.StraightFlush => -20,
                        CardUtilityMaubinh.HandType.FourOfAKind => -16,
                        CardUtilityMaubinh.HandType.FullHouse => -4,
                        _ => -1
                    },
                    2 => userHandType switch
                    {
                        CardUtilityMaubinh.HandType.ThreeOfAKind => -6,
                        _ => -1
                    },
                    _ => -1
                };
            }
            else if (userStrength < opponentStrength)
            {
                points = chiIndex switch
                {
                    0 => opponentHandType switch
                    {
                        CardUtilityMaubinh.HandType.StraightFlush => 10,
                        CardUtilityMaubinh.HandType.FourOfAKind => 8,
                        _ => 1
                    },
                    1 => opponentHandType switch
                    {
                        CardUtilityMaubinh.HandType.StraightFlush => 20,
                        CardUtilityMaubinh.HandType.FourOfAKind => 16,
                        CardUtilityMaubinh.HandType.FullHouse => 4,
                        _ => 1
                    },
                    2 => opponentHandType switch
                    {
                        CardUtilityMaubinh.HandType.ThreeOfAKind => 6,
                        _ => 1
                    },
                    _ => 1
                };
            }

            return points;
        }

        public int CalculateSpecialPoints(List<CardUtilityMaubinh.CardInfo> cardInfos)
        {
            if (HasDragonStraightFlush(cardInfos)) return 100;
            if (HasDragonStraight(cardInfos)) return 50;
            if (HasSameSuit(cardInfos)) return 30;
            if (HasFivePairsOneThree(cardInfos)) return 10;
            if (HasSixPairs(cardInfos)) return 8;
            if (HasThreeFlushes(cardInfos)) return 8;
            if (HasThreeStraights(cardInfos)) return 8;
            return 0;
        }

        // Delegated methods - Sử dụng CardUtilityMaubinh
        public bool HasSixPairs(List<CardUtilityMaubinh.CardInfo> cards)
        {
            return CardUtilityMaubinh.HasSixPairs(cards);
        }

        public bool HasThreeFlushes(List<CardUtilityMaubinh.CardInfo> cards)
        {
            return CardUtilityMaubinh.HasThreeFlushes(cards);
        }

        public bool HasThreeStraights(List<CardUtilityMaubinh.CardInfo> cards)
        {
            // Simplified implementation - có thể cải thiện sau
            return false; // Tạm thời return false
        }

        public bool HasFivePairsOneThree(List<CardUtilityMaubinh.CardInfo> cards)
        {
            // Simplified implementation - có thể cải thiện sau
            var rankGroups = cards.GroupBy(c => c.Rank).ToList();
            int pairCount = rankGroups.Count(g => g.Count() == 2);
            int threeOfAKindCount = rankGroups.Count(g => g.Count() == 3);
            return threeOfAKindCount >= 1 && pairCount >= 5;
        }

        public bool HasSameSuit(List<CardUtilityMaubinh.CardInfo> cards)
        {
            return CardUtilityMaubinh.HasSameSuit(cards);
        }

        public bool HasDragonStraight(List<CardUtilityMaubinh.CardInfo> cards)
        {
            return CardUtilityMaubinh.HasDragonStraight(cards);
        }

        public bool HasDragonStraightFlush(List<CardUtilityMaubinh.CardInfo> cards)
        {
            return CardUtilityMaubinh.HasDragonStraightFlush(cards);
        }

        public (int[] Chi1, int[] Chi2, int[] Chi3) CreateSixPairsSuggestion(List<CardUtilityMaubinh.CardInfo> cards)
        {
            // Simplified implementation - tạo gợi ý mặc định
            var cardIds = cards.Select(c => c.Id).ToArray();
            var defaultSuggestions = CreateDefaultSuggestion(cardIds);
            return defaultSuggestions.FirstOrDefault();
        }

        public (int[] Chi1, int[] Chi2, int[] Chi3) CreateThreeFlushesSuggestion(List<CardUtilityMaubinh.CardInfo> cards)
        {
            // Simplified implementation - tạo gợi ý mặc định
            var cardIds = cards.Select(c => c.Id).ToArray();
            var defaultSuggestions = CreateDefaultSuggestion(cardIds);
            return defaultSuggestions.FirstOrDefault();
        }

        /// <summary>
        /// Phân tích bài và đưa ra khuyến nghị chiến lược
        /// </summary>
        public string AnalyzeCardsAndRecommendStrategy(int[] cards)
        {
            try
            {
                if (cards == null || cards.Length != 13)
                {
                    return "Dữ liệu bài không hợp lệ.";
                }

                var cardInfos = cards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();
                var rankGroups = cardInfos.GroupBy(c => c.Rank).ToList();

                var analysis = "🔍 PHÂN TÍCH BÀI:\n";

                // Phân tích pairs và three of a kinds
                var threeGroups = rankGroups.Where(g => g.Count() >= 3).ToList();
                var pairGroups = rankGroups.Where(g => g.Count() == 2).ToList();
                var singleCards = rankGroups.Where(g => g.Count() == 1).ToList();

                analysis += $"   Xám (3 lá): {threeGroups.Count}\n";
                analysis += $"   Đôi (2 lá): {pairGroups.Count}\n";
                analysis += $"   Lá lẻ: {singleCards.Count}\n";

                // Khuyến nghị chiến lược
                analysis += "\n💡 KHUYẾN NGHỊ CHIẾN LƯỢC:\n";

                if (threeGroups.Count >= 2)
                {
                    analysis += "   🔥 Có nhiều xám - Nên dùng chiến lược Chi3Focus\n";
                }
                else if (threeGroups.Count == 1)
                {
                    analysis += "   💎 Có 1 xám - Nên đặt xám ở Chi 3 (Chi3Focus)\n";
                }
                else if (pairGroups.Count >= 3)
                {
                    analysis += "   ⚖️ Có nhiều đôi - Nên dùng chiến lược Balanced\n";
                }
                else if (pairGroups.Count >= 1)
                {
                    analysis += "   🛡️ Có ít đôi - Nên dùng chiến lược Defensive\n";
                }
                else
                {
                    analysis += "   ⚔️ Toàn lá lẻ - Nên dùng chiến lược Aggressive\n";
                }

                // Kiểm tra trường hợp đặc biệt
                if (HasSixPairs(cardInfos))
                {
                    analysis += "   🎰 PHÁT HIỆN 6 ĐÔI - Gợi ý đặc biệt!\n";
                }

                return analysis;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi phân tích bài: {ex.Message}", UIManager.LogLevel.Error);
                return "Không thể phân tích bài.";
            }
        }

        /// <summary>
        /// Lấy gợi ý chi tiết mới
        /// </summary>
        public List<MauBinhEngine.MauBinhSuggestion> GetAdvancedSuggestions(int[] cards, bool isOpponent = false, int[] opponentCards = null)
        {
            try
            {
                if (cards == null || cards.Length != 13)
                {
                    _uiManager.AppendLog($"Dữ liệu bài không hợp lệ: {cards?.Length ?? 0} lá (yêu cầu 13)", UIManager.LogLevel.Error);
                    return new List<MauBinhEngine.MauBinhSuggestion>();
                }

                return _engine.GenerateSuggestions(cards);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi tạo gợi ý chi tiết: {ex.Message}", UIManager.LogLevel.Error);
                return new List<MauBinhEngine.MauBinhSuggestion>();
            }
        }

        /// <summary>
        /// Lấy gợi ý tối ưu mới
        /// </summary>
        public List<MauBinhEngine.MauBinhSuggestion> GetOptimizedSuggestions(int[] cards)
        {
            try
            {
                if (cards == null || cards.Length != 13)
                {
                    _uiManager.AppendLog($"Dữ liệu bài không hợp lệ: {cards?.Length ?? 0} lá (yêu cầu 13)", UIManager.LogLevel.Error);
                    return new List<MauBinhEngine.MauBinhSuggestion>();
                }

                return _engine.GenerateSuggestions(cards);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi tạo gợi ý tối ưu: {ex.Message}", UIManager.LogLevel.Error);
                return new List<MauBinhEngine.MauBinhSuggestion>();
            }
        }

        /// <summary>
        /// Log báo cáo chi tiết cho gợi ý
        /// </summary>
        private void LogOptimizedSuggestions(List<MauBinhEngine.MauBinhSuggestion> suggestions)
        {
            if (!suggestions.Any()) return;

            var report = "📊 BÁO CÁO GỢI Ý MAU BINH:\n\n";

            for (int i = 0; i < suggestions.Count; i++)
            {
                var suggestion = suggestions[i];
                report += $"🎯 Gợi ý {i + 1}: {suggestion.Description}\n";
                report += $"   Điểm: {suggestion.Score:F1}\n";
                report += $"   Chi 1: {CardUtilityMaubinh.ConvertCardsToString(suggestion.Chi1)}\n";
                report += $"   Chi 2: {CardUtilityMaubinh.ConvertCardsToString(suggestion.Chi2)}\n";
                report += $"   Chi 3: {CardUtilityMaubinh.ConvertCardsToString(suggestion.Chi3)}\n";

                if (suggestion.IsSpecial)
                {
                    report += $"   🏆 TRƯỜNG HỢP ĐẶC BIỆT!\n";
                }

                report += "\n";
            }

            _uiManager.AppendLog(report, UIManager.LogLevel.Info);
        }

        /// <summary>
        /// Log gợi ý từ MauBinhEngine
        /// </summary>
        private void LogSuggestions(List<MauBinhEngine.MauBinhSuggestion> suggestions)
        {
            try
            {
                _uiManager.AppendLog($"✅ Tạo được {suggestions.Count} gợi ý:", UIManager.LogLevel.Info);
                for (int i = 0; i < Math.Min(suggestions.Count, 3); i++)
                {
                    var suggestion = suggestions[i];
                    var chi1Str = string.Join(",", suggestion.Chi1.Select(CardUtilityMaubinh.GetCardDisplayName));
                    var chi2Str = string.Join(",", suggestion.Chi2.Select(CardUtilityMaubinh.GetCardDisplayName));
                    var chi3Str = string.Join(",", suggestion.Chi3.Select(CardUtilityMaubinh.GetCardDisplayName));
                    _uiManager.AppendLog($"  {i + 1}. {chi1Str} | {chi2Str} | {chi3Str} - {suggestion.Description}", UIManager.LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi log gợi ý: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Convert MauBinhEngine suggestions to legacy format
        /// </summary>
        private List<(int[] Chi1, int[] Chi2, int[] Chi3)> ConvertToLegacyFormat(List<MauBinhEngine.MauBinhSuggestion> suggestions)
        {
            return suggestions.Select(s => (s.Chi1, s.Chi2, s.Chi3)).ToList();
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                _uiManager.AppendLog("MauBinhCardManager disposed", UIManager.LogLevel.Info);
            }
        }
    }
}