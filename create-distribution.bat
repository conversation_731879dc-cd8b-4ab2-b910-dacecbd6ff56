@echo off
echo ========================================
echo   AutoGameBai Distribution Creator
echo ========================================

echo.
echo Step 1: Building production version...
call production-mode.bat

echo.
echo Step 2: Creating distribution folder...
if exist "Distribution" rmdir /s /q "Distribution"
mkdir "Distribution"
mkdir "Distribution\AutoGameBai"

echo.
echo Step 3: Copying production files...
copy "bin\Release\net8.0-windows\AutoGameBai.exe" "Distribution\AutoGameBai\"
copy "bin\Release\net8.0-windows\AutoGameBai.dll" "Distribution\AutoGameBai\"
copy "bin\Release\net8.0-windows\AutoGameBai.runtimeconfig.json" "Distribution\AutoGameBai\"

echo.
echo Step 4: Copying required dependencies...
xcopy "bin\Release\net8.0-windows\*.dll" "Distribution\AutoGameBai\" /Y /Q
xcopy "bin\Release\net8.0-windows\runtimes" "Distribution\AutoGameBai\runtimes\" /E /I /Y /Q

echo.
echo Step 5: Copying configuration files...
if exist "*.txt" copy "*.txt" "Distribution\AutoGameBai\"
if exist "config.json" copy "config.json" "Distribution\AutoGameBai\"

echo.
echo Step 6: Creating README for users...
echo AutoGameBai - Game Assistant Tool > "Distribution\AutoGameBai\README.txt"
echo. >> "Distribution\AutoGameBai\README.txt"
echo INSTALLATION: >> "Distribution\AutoGameBai\README.txt"
echo 1. Extract all files to a folder >> "Distribution\AutoGameBai\README.txt"
echo 2. Run AutoGameBai.exe >> "Distribution\AutoGameBai\README.txt"
echo 3. Follow the setup instructions >> "Distribution\AutoGameBai\README.txt"
echo. >> "Distribution\AutoGameBai\README.txt"
echo REQUIREMENTS: >> "Distribution\AutoGameBai\README.txt"
echo - Windows 10/11 >> "Distribution\AutoGameBai\README.txt"
echo - .NET 8.0 Runtime (will auto-install if needed) >> "Distribution\AutoGameBai\README.txt"
echo - Chrome browser >> "Distribution\AutoGameBai\README.txt"
echo. >> "Distribution\AutoGameBai\README.txt"
echo FEATURES: >> "Distribution\AutoGameBai\README.txt"
echo - Phom game assistance >> "Distribution\AutoGameBai\README.txt"
echo - Mau Binh game assistance >> "Distribution\AutoGameBai\README.txt"
echo - Tien Len game assistance >> "Distribution\AutoGameBai\README.txt"
echo - Smart card suggestions >> "Distribution\AutoGameBai\README.txt"
echo - Team coordination >> "Distribution\AutoGameBai\README.txt"

echo.
echo Step 7: Creating ZIP package...
powershell -command "Compress-Archive -Path 'Distribution\AutoGameBai\*' -DestinationPath 'Distribution\AutoGameBai-v1.0.zip' -Force"

echo.
echo ✅ Distribution package created successfully!
echo.
echo Files created:
echo - Distribution\AutoGameBai\ (folder with all files)
echo - Distribution\AutoGameBai-v1.0.zip (ready to send)
echo.
echo Package contents:
dir "Distribution\AutoGameBai" /B

echo.
echo 📦 Ready to distribute: Distribution\AutoGameBai-v1.0.zip
echo 🔒 Protection level: MAXIMUM (cannot be debugged)
echo 📁 Package size: 
powershell -command "(Get-Item 'Distribution\AutoGameBai-v1.0.zip').Length / 1MB | ForEach-Object { '{0:N2} MB' -f $_ }"

echo.
pause
