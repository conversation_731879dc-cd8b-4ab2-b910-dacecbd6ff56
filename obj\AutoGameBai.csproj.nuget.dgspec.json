{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\WebSocketGameClient\\WebSocketGameClient\\AutoGameBai.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\WebSocketGameClient\\WebSocketGameClient\\AutoGameBai.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\WebSocketGameClient\\WebSocketGameClient\\AutoGameBai.csproj", "projectName": "AutoGameBai", "projectPath": "C:\\Users\\<USER>\\source\\repos\\WebSocketGameClient\\WebSocketGameClient\\AutoGameBai.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\WebSocketGameClient\\WebSocketGameClient\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"HIC.System.Windows.Forms.DataVisualization": {"target": "Package", "version": "[1.0.1, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.4, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenCVSharp4": {"target": "Package", "version": "[4.11.0.20250507, )"}, "OpenCVSharp4.runtime.win": {"target": "Package", "version": "[4.11.0.20250507, )"}, "OpenCvSharp4.Extensions": {"target": "Package", "version": "[4.11.0.20250507, )"}, "Selenium.WebDriver": {"target": "Package", "version": "[4.25.0, )"}, "Selenium.WebDriver.ChromeDriver": {"target": "Package", "version": "[129.0.6668.7000, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}, "SocketIOClient": {"target": "Package", "version": "[3.1.2, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.119, )"}, "System.Management": {"target": "Package", "version": "[9.0.4, )"}, "System.Net.Http.Json": {"target": "Package", "version": "[9.0.4, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.4, )"}, "WebDriverManager": {"target": "Package", "version": "[2.17.5, )"}, "WebSocketSharp-netstandard": {"target": "Package", "version": "[1.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}