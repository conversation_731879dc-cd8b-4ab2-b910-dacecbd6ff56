using Newtonsoft.Json.Linq;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using AutoGameBai.Gamephom;

namespace AutoGameBai.WebSocket
{
    /// <summary>
    /// WebSocket handler cho game Phỏm
    /// <PERSON><PERSON> lý các cmd: 850, 851, 852, 853, 854, 855, 857
    /// </summary>
    public class PhomWebSocketHandler : IGameWebSocketHandler
    {
        private readonly GameClientManager _gameClient;
        private readonly UIManager _uiManager;
        private readonly Dictionary<string, int[]> _userCards;
        private readonly ConcurrentDictionary<string, int> _cardCounts;
        private readonly ConcurrentDictionary<string, bool> _userTurnStatus;
        private readonly ConcurrentDictionary<string, List<int>> _knownCards;
        private readonly ConcurrentDictionary<string, int[]> _haphomStatus;
        private readonly List<string> _playerOrder;
        private string _firstPlayerUid;
        private string _lastWinnerUid;
        private readonly object _lock = new object();

        public HashSet<int> SupportedCommands { get; } = new HashSet<int> { 850, 851, 852, 853, 854, 855, 857 };

        public event Action? CardsUpdated;
        public event Action? PlayCardReceived;
        public event Action? GameEnded;
        public event Action? NewGameStarted;

        public PhomWebSocketHandler(GameClientManager gameClient, UIManager uiManager)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));

            _userCards = new Dictionary<string, int[]>();
            _cardCounts = new ConcurrentDictionary<string, int>();
            _userTurnStatus = new ConcurrentDictionary<string, bool>();
            _knownCards = new ConcurrentDictionary<string, List<int>>();
            _haphomStatus = new ConcurrentDictionary<string, int[]>();
            _playerOrder = new List<string>();
            _firstPlayerUid = "";
            _lastWinnerUid = "";
        }

        public void HandleCommand(JObject messageData, string username, int cmd)
        {
            switch (cmd)
            {
                case 850:
                    HandleCmd850(messageData, username);
                    break;
                case 851:
                    HandleCmd851(messageData, username);
                    break;
                case 852:
                    HandleCmd852(messageData, username);
                    break;
                case 853:
                    HandleCmd853(messageData, username);
                    break;
                case 854:
                    HandleCmd854(messageData, username);
                    break;
                case 855:
                    HandleCmd855(messageData, username);
                    break;
                case 857:
                    HandleCmd857(messageData, username);
                    break;
                default:
                    _uiManager.AppendLog($"Unsupported Phom command: {cmd}", UIManager.LogLevel.Warning, username);
                    break;
            }
        }

        /// <summary>
        /// Xử lý cmd 850 - Bắt đầu game Phỏm
        /// </summary>
        private void HandleCmd850(JObject messageData, string username)
        {
            try
            {
                var cardIds = messageData["cs"]?.ToObject<int[]>();
                var lpi = messageData["lpi"]?.ToObject<string[]>();
                var tP = messageData["tP"] as JObject;
                var nextPlayerUid = tP?["uid"]?.ToString();
                var nextPlayerName = tP?["dn"]?.ToString();

                if (cardIds != null)
                {
                    _userCards[username] = cardIds;
                    _knownCards[username] = cardIds.ToList();
                    _cardCounts[username] = cardIds.Length;

                    if (cardIds.Length == 10)
                    {
                        _firstPlayerUid = username;
                        _userTurnStatus[username] = true;
                        _uiManager.AppendLog($"First player: {username} (10 cards)", UIManager.LogLevel.Info, username);
                    }
                }

                if (lpi != null)
                {
                    lock (_playerOrder)
                    {
                        _playerOrder.Clear();
                        _playerOrder.AddRange(lpi);
                        foreach (var uid in lpi)
                        {
                            _userTurnStatus.TryAdd(uid, false);
                        }
                        _uiManager.AppendLog($"Player order: {string.Join(", ", _playerOrder)}", UIManager.LogLevel.Info, username);
                    }
                }

                if (_cardCounts.Count == 3 && _cardCounts.Values.All(count => count == 9))
                {
                    _firstPlayerUid = "opponent";
                    _uiManager.AppendLog("Opponent is the first player (all team members have 9 cards)", UIManager.LogLevel.Info, username);
                    _userTurnStatus[_playerOrder.First()] = true;
                }

                if (nextPlayerUid != null && nextPlayerName != null)
                {
                    foreach (var user in _userTurnStatus.Keys.ToList())
                    {
                        _userTurnStatus[user] = user == nextPlayerName;
                    }
                    _uiManager.AppendLog($"Next turn: {nextPlayerName} (UID: {nextPlayerUid})", UIManager.LogLevel.Info, username);
                }

                string cardsString = CardUtilityPhom.ConvertCardsToString(cardIds);
                _uiManager.AppendLog($"Cmd 850 for {username}: Cards: {cardsString}", UIManager.LogLevel.Info, username);

                CardsUpdated?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 850 for {username}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Xử lý cmd 851 - Đánh bài Phỏm
        /// </summary>
        private void HandleCmd851(JObject messageData, string username)
        {
            try
            {
                var fP = messageData["fP"] as JObject;
                var tP = messageData["tP"] as JObject;
                var sAC = messageData["sAC"]?.ToObject<int[]>();
                var playedCard = fP?["dCs"]?.Value<int>() ?? -1;
                var fromPlayerName = fP?["dn"]?.ToString();
                var fromPlayerUid = fP?["uid"]?.ToString();
                var nextPlayerUid = tP?["uid"]?.ToString();
                var nextPlayerName = tP?["dn"]?.ToString();

                if (sAC != null && fromPlayerName != null && _userCards.ContainsKey(fromPlayerName))
                {
                    _userCards[fromPlayerName] = sAC;
                    _knownCards[fromPlayerName] = sAC.ToList();
                    _uiManager.AppendLog($"Updated cards for {fromPlayerName}: {CardUtilityPhom.ConvertCardsToString(sAC)}", UIManager.LogLevel.Info, fromPlayerName);
                }

                if (playedCard != -1)
                {
                    _knownCards.GetOrAdd("opponent", _ => new List<int>()).Add(playedCard);
                    _uiManager.AppendLog($"Updated opponent known cards: {CardUtilityPhom.ConvertCardsToString(new[] { playedCard })}", UIManager.LogLevel.Info);
                }

                if (nextPlayerUid != null && nextPlayerName != null)
                {
                    foreach (var user in _userTurnStatus.Keys.ToList())
                    {
                        _userTurnStatus[user] = user == nextPlayerName;
                    }
                    _uiManager.AppendLog($"Next turn: {nextPlayerName} (UID: {nextPlayerUid})", UIManager.LogLevel.Info, username);
                }

                _gameClient.UpdateLastPlayedCard(playedCard);
                _uiManager.AppendLog($"Player {fromPlayerName} played card {playedCard}, next: {nextPlayerName}", UIManager.LogLevel.Info, username);

                CardsUpdated?.Invoke();
                PlayCardReceived?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 851 for {username}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Xử lý cmd 852 - Rút bài Phỏm
        /// </summary>
        private void HandleCmd852(JObject messageData, string username)
        {
            try
            {
                var sAC = messageData["sAC"]?.ToObject<int[]>();
                var drawnCard = messageData["cs"]?.Value<int>() ?? -1;
                var playerName = messageData["dn"]?.ToString();

                if (sAC != null && playerName != null && _userCards.ContainsKey(playerName))
                {
                    _userCards[playerName] = sAC;
                    _knownCards[playerName] = sAC.ToList();
                    _uiManager.AppendLog($"Updated cards for {playerName} after drawing: {CardUtilityPhom.ConvertCardsToString(sAC)}", UIManager.LogLevel.Info, playerName);
                }

                _gameClient.UpdateDrawnCard(drawnCard);
                _uiManager.AppendLog($"Player {playerName} drew card {drawnCard}, new cards: {CardUtilityPhom.ConvertCardsToString(sAC)}", UIManager.LogLevel.Info, username);

                CardsUpdated?.Invoke();

                if (_gameClient.GetPhomSuggestionForm() != null && !_gameClient.GetPhomSuggestionForm().IsDisposed)
                {
                    _gameClient.GetPhomSuggestionForm().Invoke(new Action(() =>
                    {
                        _gameClient.GetPhomSuggestionForm().UpdateSpecificUser(playerName, sAC);
                        _gameClient.GetPhomSuggestionForm().MarkUserDrawnCard(playerName);
                    }));
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 852 for {username}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Xử lý cmd 853 - Ăn bài Phỏm
        /// </summary>
        private void HandleCmd853(JObject messageData, string username)
        {
            try
            {
                var sAC = messageData["sAC"]?.ToObject<int[]>();
                var eatenCard = messageData["cs"]?.Value<int>() ?? -1;
                var fP = messageData["fP"] as JObject;
                var playerName = fP?["dn"]?.ToString();

                if (sAC != null && playerName != null && _userCards.ContainsKey(playerName))
                {
                    _userCards[playerName] = sAC;
                    _knownCards[playerName] = sAC.ToList();
                    _uiManager.AppendLog($"Updated cards for {playerName} after eating: {CardUtilityPhom.ConvertCardsToString(sAC)}", UIManager.LogLevel.Info, playerName);
                    _gameClient.UpdateLastPlayedCard(eatenCard);
                }

                _uiManager.AppendLog($"Player {playerName} ate card {eatenCard}, new cards: {CardUtilityPhom.ConvertCardsToString(sAC)}", UIManager.LogLevel.Info, username);

                CardsUpdated?.Invoke();

                if (_gameClient.GetPhomSuggestionForm() != null && !_gameClient.GetPhomSuggestionForm().IsDisposed)
                {
                    _gameClient.GetPhomSuggestionForm().Invoke(new Action(() =>
                    {
                        _gameClient.GetPhomSuggestionForm().UpdateSpecificUser(playerName, sAC);
                        _gameClient.GetPhomSuggestionForm().MarkUserEatenCard(playerName);
                    }));
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 853 for {username}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Xử lý cmd 854 - Hạ phỏm
        /// </summary>
        private void HandleCmd854(JObject messageData, string username)
        {
            try
            {
                var phomCards = messageData["cs"]?.ToObject<int[]>();
                if (phomCards != null)
                {
                    _uiManager.AppendLog($"Player {username} played phom: {CardUtilityPhom.ConvertCardsToString(phomCards)}", UIManager.LogLevel.Info, username);

                    // Add phom cards to played cards
                    foreach (var card in phomCards)
                    {
                        _gameClient.UpdateLastPlayedCard(card);
                    }

                    _uiManager.AppendLog($"Added {phomCards.Length} phom cards to played cards: {CardUtilityPhom.ConvertCardsToString(phomCards)}", UIManager.LogLevel.Info, username);

                    // Update PhomSuggestionForm
                    if (_gameClient.GetPhomSuggestionForm() != null && !_gameClient.GetPhomSuggestionForm().IsDisposed)
                    {
                        _gameClient.GetPhomSuggestionForm().Invoke(new Action(() =>
                        {
                            _gameClient.GetPhomSuggestionForm().UpdateOpponentPlayedCards();
                            _uiManager.AppendLog($"Updated phom cards display in PhomSuggestionForm", UIManager.LogLevel.Info, username);
                        }));
                    }
                }

                CardsUpdated?.Invoke();
                PlayCardReceived?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 854 for {username}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Xử lý cmd 855 - Kết thúc ván Phỏm
        /// </summary>
        private void HandleCmd855(JObject messageData, string username)
        {
            try
            {
                var playersArray = messageData["ps"] as JArray;
                if (playersArray != null)
                {
                    string winnerUid = null;
                    long maxMoneyChange = long.MinValue;

                    foreach (var player in playersArray)
                    {
                        var playerUsername = player["dn"]?.ToString();
                        var moneyChange = player["mX"]?.Value<long>() ?? 0;
                        var points = player["pt"]?.Value<int>() ?? 0;
                        var finalCards = player["cs"]?.ToObject<int[]>();
                        var playerUid = player["uid"]?.ToString();

                        if (_gameClient.GetUsers().ContainsKey(playerUsername))
                        {
                            string result = moneyChange > 0 ? $"Win: +{moneyChange} (Points: {points})"
                                : moneyChange < 0 ? $"Lose: {moneyChange} (Points: {points})"
                                : $"Tie (Points: {points})";
                            _gameClient.GetUsers()[playerUsername].GameResult = result;
                            _uiManager.AppendLog($"Game result for {playerUsername}: {result}, Cards: {CardUtilityPhom.ConvertCardsToString(finalCards)}", UIManager.LogLevel.Info, playerUsername);

                            if (moneyChange > maxMoneyChange)
                            {
                                maxMoneyChange = moneyChange;
                                winnerUid = playerUid;
                            }
                        }
                    }

                    if (winnerUid != null)
                    {
                        _lastWinnerUid = winnerUid;
                        _uiManager.AppendLog($"Winner of this round: UID {winnerUid}", UIManager.LogLevel.Info, username);
                    }

                    // Clear played cards when game ends
                    try
                    {
                        if (_gameClient.GetPhomSuggestionForm() != null && !_gameClient.GetPhomSuggestionForm().IsDisposed)
                        {
                            _gameClient.GetPhomSuggestionForm().ClearOpponentPlayedCards();
                            _uiManager.AppendLog("Cleared opponent played cards after Phom game ended", UIManager.LogLevel.Info);
                        }
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Error clearing opponent played cards: {ex.Message}", UIManager.LogLevel.Error);
                    }

                    _gameClient.RefreshUserList?.Invoke();
                }

                ResetGameData();
                GameEnded?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 855 for {username}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Xử lý cmd 857 - Đánh bài đơn
        /// </summary>
        private void HandleCmd857(JObject messageData, string username)
        {
            try
            {
                var playedCard = messageData["cs"]?.Value<int>() ?? -1;
                _gameClient.UpdateLastPlayedCard(playedCard);
                _uiManager.AppendLog($"Player {username} played card {playedCard}", UIManager.LogLevel.Info, username);

                PlayCardReceived?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 857 for {username}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        public void ResetGameData()
        {
            lock (_lock)
            {
                _userCards.Clear();
                _knownCards.Clear();
                _cardCounts.Clear();
                _haphomStatus.Clear();
                _playerOrder.Clear();
                _firstPlayerUid = "";
                _lastWinnerUid = "";

                foreach (var user in _userTurnStatus.Keys.ToList())
                {
                    _userTurnStatus[user] = false;
                }
            }

            // Reset PhomSuggestionForm hoàn toàn
            try
            {
                if (_gameClient.GetPhomSuggestionForm() != null && !_gameClient.GetPhomSuggestionForm().IsDisposed)
                {
                    _gameClient.GetPhomSuggestionForm().ResetGameState();
                    _uiManager.AppendLog("✅ Reset PhomSuggestionForm state completely", UIManager.LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error resetting Phom game data: {ex.Message}", UIManager.LogLevel.Error);
            }

            _uiManager.AppendLog("Reset Phom game data", UIManager.LogLevel.Info);
        }

        public bool IsTeamMode()
        {
            return _userCards.Count >= 3 || _playerOrder.Count >= 3;
        }

        public void UpdateUserCards(string username, int[] cards)
        {
            if (string.IsNullOrEmpty(username) || cards == null)
            {
                _uiManager.AppendLog($"Cannot update Phom cards for {username}: Invalid data", UIManager.LogLevel.Error);
                return;
            }

            lock (_lock)
            {
                _userCards[username] = cards;
                _knownCards[username] = cards.ToList();
                string cardsString = CardUtilityPhom.ConvertCardsToString(cards);
                _uiManager.AppendLog($"Updated Phom cards for {username}: {cardsString}", UIManager.LogLevel.Info);
            }

            CardsUpdated?.Invoke();
        }

        // Getters for Phom specific data
        public string GetFirstPlayerUid() => _firstPlayerUid;
        public string GetLastWinnerUid() => _lastWinnerUid;
        public bool IsUserTurn(string username) => _userTurnStatus.GetValueOrDefault(username, false);
        public List<string> GetPlayerOrder() => _playerOrder.ToList();
        public Dictionary<string, bool> GetUserTurnStatus() => _userTurnStatus.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        public Dictionary<string, List<int>> GetKnownCards() => _knownCards.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        public Dictionary<string, int[]> GetHaphomStatus() => _haphomStatus.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        public Dictionary<string, int[]> GetUserCards() => _userCards.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

        public void UpdateHaphomStatus(string username, int[] phomCards)
        {
            _haphomStatus[username] = phomCards;
        }

        public void ClearHaphomStatus()
        {
            _haphomStatus.Clear();
        }
    }
}
