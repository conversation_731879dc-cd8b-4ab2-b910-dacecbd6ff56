@echo off
echo ========================================
echo   AutoGameBai Protected Build Script
echo ========================================

REM Set variables
set PROJECT_NAME=AutoGameBai
set BUILD_CONFIG=Release
set TARGET_FRAMEWORK=net8.0-windows
set OUTPUT_DIR=bin\%BUILD_CONFIG%\%TARGET_FRAMEWORK%
set OBFUSCATED_DIR=bin\Obfuscated
set TOOLS_DIR=tools

echo.
echo [1/6] Cleaning previous builds...
dotnet clean --configuration %BUILD_CONFIG% --verbosity quiet
if exist "%OBFUSCATED_DIR%" rmdir /s /q "%OBFUSCATED_DIR%"

echo [2/6] Building in Release mode...
dotnet build --configuration %BUILD_CONFIG% --no-restore ^
    -p:DebugType=None ^
    -p:DebugSymbols=false ^
    -p:Optimize=true ^
    -p:DefineConstants=RELEASE ^
    --verbosity quiet

if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo [3/6] Publishing single file...
dotnet publish --configuration %BUILD_CONFIG% --no-build ^
    --self-contained true ^
    --runtime win-x64 ^
    -p:PublishSingleFile=true ^
    -p:IncludeNativeLibrariesForSelfExtract=true ^
    -p:PublishTrimmed=false ^
    --verbosity quiet

if %ERRORLEVEL% neq 0 (
    echo ERROR: Publish failed!
    pause
    exit /b 1
)

echo [4/6] Checking ConfuserEx...
if not exist "%TOOLS_DIR%\ConfuserEx\Confuser.CLI.exe" (
    echo ERROR: ConfuserEx not found! Please run setup-confuserex.bat first.
    pause
    exit /b 1
)

echo [5/6] Running obfuscation...
mkdir "%OBFUSCATED_DIR%" 2>nul

REM Copy files to obfuscation directory
copy "%OUTPUT_DIR%\win-x64\publish\%PROJECT_NAME%.exe" "%OUTPUT_DIR%\" >nul
copy "%OUTPUT_DIR%\win-x64\publish\*.dll" "%OUTPUT_DIR%\" >nul 2>nul

REM Run ConfuserEx
"%TOOLS_DIR%\ConfuserEx\Confuser.CLI.exe" "%PROJECT_NAME%.crproj"

if %ERRORLEVEL% neq 0 (
    echo ERROR: Obfuscation failed!
    pause
    exit /b 1
)

echo [6/6] Finalizing protected build...

REM Copy additional files
if exist "img" xcopy "img" "%OBFUSCATED_DIR%\img\" /E /I /Q >nul
if exist "logs" mkdir "%OBFUSCATED_DIR%\logs" >nul
if exist "config.txt" copy "config.txt" "%OBFUSCATED_DIR%\" >nul
if exist "*.txt" copy "*.txt" "%OBFUSCATED_DIR%\" >nul

REM Calculate and update hash
echo Calculating integrity hash...
powershell -Command "$hash = Get-FileHash '%OBFUSCATED_DIR%\%PROJECT_NAME%.exe' -Algorithm SHA256; $hash.Hash" > temp_hash.txt
set /p FILE_HASH=<temp_hash.txt
del temp_hash.txt

echo.
echo ========================================
echo   BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Protected executable: %OBFUSCATED_DIR%\%PROJECT_NAME%.exe
echo File hash: %FILE_HASH%
echo.
echo Protection features applied:
echo  ✓ Name obfuscation (Unicode mode)
echo  ✓ Control flow obfuscation (60%% intensity)
echo  ✓ String encryption (Dynamic mode)
echo  ✓ Anti-debug protection
echo  ✓ Anti-dump protection
echo  ✓ Anti-tamper protection
echo  ✓ Resource encryption
echo  ✓ Reference proxy obfuscation
echo.
echo IMPORTANT: Update AntiReverseEngineering.cs with the new hash:
echo Replace "PLACEHOLDER_HASH" with: %FILE_HASH%
echo.

pause
