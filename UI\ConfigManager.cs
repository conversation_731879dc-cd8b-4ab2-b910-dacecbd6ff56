﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace AutoGameBai.UI
{
    /// <summary>
    /// Lớp quản lý cấu hình <PERSON>ng dụng
    /// </summary>
    public class ConfigManager
    {
        private readonly UIManager _uiManager;
        private readonly string _configFilePath;

        public ConfigManager(UIManager uiManager)
        {
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.txt");
        }

        /// <summary>
        /// Tải cấu hình từ file
        /// </summary>
        /// <returns>Dictionary chứa các cặp key-value của cấu hình</returns>
        public async Task<Dictionary<string, string>> LoadConfigAsync()
        {
            var startTime = DateTime.Now;
            _uiManager.AppendLog("Bắt đầu LoadConfigAsync", UIManager.LogLevel.Debug);

            var config = new Dictionary<string, string>();

            try
            {
                if (File.Exists(_configFilePath))
                {
                    string[] lines = await File.ReadAllLinesAsync(_configFilePath);
                    foreach (string line in lines)
                    {
                        if (string.IsNullOrWhiteSpace(line) || !line.Contains('='))
                            continue;

                        string[] parts = line.Split(new[] { '=' }, 2);
                        if (parts.Length == 2)
                        {
                            string key = parts[0].Trim();
                            string value = parts[1].Trim();
                            config[key] = value;
                        }
                    }

                    if (config.TryGetValue("ApiUrl", out string apiUrl))
                    {
                        _uiManager.AppendLog($"Đã tải cấu hình từ {_configFilePath}, API URL: {apiUrl}", UIManager.LogLevel.Info);
                    }
                    if (config.TryGetValue("VerChrome", out string verChrome))
                    {
                        _uiManager.AppendLog($"Đã tải VerChrome: {verChrome}", UIManager.LogLevel.Info);
                    }
                }
                else
                {
                    _uiManager.AppendLog($"File cấu hình không tồn tại: {_configFilePath}, tạo mới", UIManager.LogLevel.Warning);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi tải cấu hình: {ex.Message}", UIManager.LogLevel.Error);
            }

            var endTime = DateTime.Now;
            _uiManager.AppendLog($"LoadConfigAsync hoàn tất trong {(endTime - startTime).TotalSeconds:F2} giây", UIManager.LogLevel.Info);

            return config;
        }

        /// <summary>
        /// Lưu cấu hình vào file
        /// </summary>
        /// <param name="config">Dictionary chứa các cặp key-value của cấu hình</param>
        public async Task SaveConfigAsync(Dictionary<string, string> config)
        {
            var startTime = DateTime.Now;
            _uiManager.AppendLog("Bắt đầu SaveConfigAsync", UIManager.LogLevel.Debug);

            try
            {
                // Kiểm tra giá trị VerChrome hợp lệ
                if (config.TryGetValue("VerChrome", out string verChrome) && string.IsNullOrWhiteSpace(verChrome))
                {
                    _uiManager.AppendLog("Giá trị VerChrome không hợp lệ, sử dụng giá trị mặc định", UIManager.LogLevel.Warning);
                    config["VerChrome"] = "129.0.6533.73";
                }

                var lines = new List<string>();
                foreach (var kvp in config)
                {
                    lines.Add($"{kvp.Key}={kvp.Value}");
                }

                await File.WriteAllLinesAsync(_configFilePath, lines);

                _uiManager.AppendLog($"Đã lưu cấu hình vào {_configFilePath}", UIManager.LogLevel.Info);
                if (config.ContainsKey("VerChrome"))
                {
                    _uiManager.AppendLog($"Đã lưu VerChrome: {config["VerChrome"]}", UIManager.LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi lưu cấu hình: {ex.Message}", UIManager.LogLevel.Error);
                throw;
            }

            var endTime = DateTime.Now;
            _uiManager.AppendLog($"SaveConfigAsync hoàn tất trong {(endTime - startTime).TotalSeconds:F2} giây", UIManager.LogLevel.Info);
        }

        /// <summary>
        /// Lấy giá trị cấu hình theo key
        /// </summary>
        /// <param name="config">Dictionary chứa các cặp key-value của cấu hình</param>
        /// <param name="key">Key cần lấy giá trị</param>
        /// <param name="defaultValue">Giá trị mặc định nếu không tìm thấy key</param>
        /// <returns>Giá trị của key hoặc giá trị mặc định</returns>
        public string GetConfigValue(Dictionary<string, string> config, string key, string defaultValue = "")
        {
            if (config.TryGetValue(key, out string value) && !string.IsNullOrEmpty(value))
            {
                return value;
            }
            _uiManager.AppendLog($"Không tìm thấy giá trị cho {key}, sử dụng mặc định: {defaultValue}", UIManager.LogLevel.Debug);
            return defaultValue;
        }

        /// <summary>
        /// Lấy giá trị cấu hình số nguyên theo key
        /// </summary>
        /// <param name="config">Dictionary chứa các cặp key-value của cấu hình</param>
        /// <param name="key">Key cần lấy giá trị</param>
        /// <param name="defaultValue">Giá trị mặc định nếu không tìm thấy key hoặc không thể chuyển đổi</param>
        /// <returns>Giá trị số nguyên của key hoặc giá trị mặc định</returns>
        public int GetConfigIntValue(Dictionary<string, string> config, string key, int defaultValue = 0)
        {
            if (config.TryGetValue(key, out string value) && int.TryParse(value, out int result))
            {
                return result;
            }
            _uiManager.AppendLog($"Không tìm thấy hoặc không thể chuyển đổi giá trị cho {key}, sử dụng mặc định: {defaultValue}", UIManager.LogLevel.Debug);
            return defaultValue;
        }
    }
}