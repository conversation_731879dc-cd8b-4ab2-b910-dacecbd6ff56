@echo off
echo Setting up ConfuserEx for AutoGameBai...

REM Create tools directory
if not exist "tools" mkdir tools
cd tools

REM Download ConfuserEx if not exists
if not exist "ConfuserEx" (
    echo Downloading ConfuserEx...
    powershell -Command "Invoke-WebRequest -Uri 'https://github.com/mkaring/ConfuserEx/releases/download/v1.6.0/ConfuserEx-CLI.zip' -OutFile 'ConfuserEx-CLI.zip'"
    
    echo Extracting ConfuserEx...
    powershell -Command "Expand-Archive -Path 'ConfuserEx-CLI.zip' -DestinationPath 'ConfuserEx' -Force"
    
    del ConfuserEx-CLI.zip
    echo ConfuserEx installed successfully!
) else (
    echo ConfuserEx already installed.
)

cd ..
echo Setup completed!
pause
