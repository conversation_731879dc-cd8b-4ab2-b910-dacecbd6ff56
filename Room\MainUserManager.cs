﻿using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace AutoGameBai.Room
{
    public class MainUserManager
    {
        private readonly GameClientManager _gameClient;
        private readonly UIManager _uiManager;
        private readonly ConcurrentBag<string> _successfulUsers;
        private string? _mainUserWithSeatZero;

        public MainUserManager(GameClientManager gameClient, UIManager uiManager)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _successfulUsers = new ConcurrentBag<string>();
        }

        public string? MainUserWithSeatZero
        {
            get => _mainUserWithSeatZero;
            set => _mainUserWithSeatZero = value;
        }

        public bool SetMainUser(string username)
        {
            try
            {
                if (string.IsNullOrEmpty(username))
                {
                    _uiManager.AppendLog("Không thể đặt MainUser: Tên người dùng trống", UIManager.LogLevel.Error);
                    return false;
                }

                _mainUserWithSeatZero = username;
                _uiManager.AppendLog($"Đã đặt {username} làm MainUser với seat 0", UIManager.LogLevel.Info);

                if (_gameClient.GetUsers().ContainsKey(username))
                {
                    foreach (var user in _gameClient.GetUsers().Values)
                    {
                        user.IsMainUser = false;
                    }

                    _gameClient.GetUsers()[username].IsMainUser = true;
                    _gameClient.UpdateTokenFile();
                    _uiManager.AppendLog($"Đã cập nhật trạng thái IsMainUser cho {username}", UIManager.LogLevel.Info);
                }
                else
                {
                    _uiManager.AppendLog($"Không tìm thấy {username} trong danh sách user, chỉ lưu MainUser tạm thời", UIManager.LogLevel.Warning);
                }

                return true;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi đặt MainUser: {ex.Message}", UIManager.LogLevel.Error);
                return false;
            }
        }

        public bool ClearMainUser()
        {
            try
            {
                if (string.IsNullOrEmpty(_mainUserWithSeatZero))
                {
                    _uiManager.AppendLog("Không có MainUser để xóa", UIManager.LogLevel.Warning);
                    return false;
                }

                string oldMainUser = _mainUserWithSeatZero;

                if (_gameClient.GetUsers().ContainsKey(oldMainUser))
                {
                    _gameClient.GetUsers()[oldMainUser].IsMainUser = false;
                    _gameClient.UpdateTokenFile();
                }

                _mainUserWithSeatZero = null;

                while (_successfulUsers.TryTake(out _)) { }

                _uiManager.AppendLog($"Đã xóa MainUser {oldMainUser}", UIManager.LogLevel.Info);
                return true;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi xóa MainUser: {ex.Message}", UIManager.LogLevel.Error);
                return false;
            }
        }

        public bool AddSuccessfulUser(string username)
        {
            if (!string.IsNullOrEmpty(username) && !_successfulUsers.Contains(username))
            {
                _successfulUsers.Add(username);
                _uiManager.AppendLog($"Đã thêm {username} vào danh sách thành công, tổng: {_successfulUsers.Count}", UIManager.LogLevel.Info);

                if (_successfulUsers.Count >= 2)
                {
                    _uiManager.AppendLog($"Đã đủ 2 người dùng tìm thấy MainUser {_mainUserWithSeatZero}, xóa MainUser", UIManager.LogLevel.Info);
                    ClearMainUser();
                    return true;
                }
            }
            return false;
        }

        public bool HandleUserFoundMainUser(string username)
        {
            if (string.IsNullOrEmpty(_mainUserWithSeatZero))
            {
                _uiManager.AppendLog($"Không có MainUser để tìm", UIManager.LogLevel.Warning);
                return false;
            }

            if (!string.IsNullOrEmpty(username) && !_successfulUsers.Contains(username))
            {
                _successfulUsers.Add(username);
                _uiManager.AppendLog($"User {username} đã vào bàn thành công với {_mainUserWithSeatZero}, foundCount = {_successfulUsers.Count}", UIManager.LogLevel.Info, username);

                if (_successfulUsers.Count >= 2)
                {
                    _uiManager.AppendLog($"Đã đủ 2 user tìm thấy mainUser {_mainUserWithSeatZero}, xóa mainUser và cập nhật IsMainUser", UIManager.LogLevel.Info);
                    ClearMainUser();
                    return true;
                }
            }
            return false;
        }

        public int GetSuccessfulUserCount()
        {
            return _successfulUsers.Count;
        }

        public void ClearSuccessfulUsers()
        {
            while (_successfulUsers.TryTake(out _)) { }
            _uiManager.AppendLog("Đã xóa danh sách người dùng thành công", UIManager.LogLevel.Info);
        }
    }
}