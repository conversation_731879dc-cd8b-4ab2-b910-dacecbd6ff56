using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using AutoGameBai.GameTienLen;

namespace AutoGameBai.WebSocket
{
    /// <summary>
    /// WebSocket handler cho game <PERSON><PERSON><PERSON><PERSON>
    /// <PERSON><PERSON> lý các cmd: 250, 251, 252, 253, 254
    /// </summary>
    public class TienLenWebSocketHandler : IGameWebSocketHandler
    {
        private readonly GameClientManager _gameClient;
        private readonly UIManager _uiManager;
        private readonly Dictionary<string, int> _playerCardCounts;
        private readonly List<int> _playedCardsTienLen;
        private string _currentPlayerTienLen;
        private int[] _lastPlayedCards;
        private readonly object _lock = new object();

        public HashSet<int> SupportedCommands { get; } = new HashSet<int> { 250, 251, 252, 253, 254 };

        public event Action? CardsUpdated;
        public event Action? PlayCardReceived;
        public event Action? GameEnded;
        public event Action? NewGameStarted;

        public TienLenWebSocketHandler(GameClientManager gameClient, UIManager uiManager)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));

            _playerCardCounts = new Dictionary<string, int>();
            _playedCardsTienLen = new List<int>();
            _currentPlayerTienLen = "";
            _lastPlayedCards = new int[0];
        }

        public void HandleCommand(JObject messageData, string username, int cmd)
        {
            switch (cmd)
            {
                case 250:
                    HandleCmd250(messageData, username);
                    break;
                case 251:
                    HandleCmd251(messageData, username);
                    break;
                case 252:
                    HandleCmd252(messageData, username);
                    break;
                case 253:
                    HandleCmd253(messageData, username);
                    break;
                case 254:
                    HandleCmd254(messageData, username);
                    break;
                default:
                    _uiManager.AppendLog($"Unsupported Tien Len command: {cmd}", UIManager.LogLevel.Warning, username);
                    break;
            }
        }

        /// <summary>
        /// Xử lý cmd 250 - Danh sách bài và thông tin game Tiến Lên
        /// </summary>
        private void HandleCmd250(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 250 (Tien Len game info)", UIManager.LogLevel.Info, username);

                // Parse player cards and counts
                if (messageData.ContainsKey("ps"))
                {
                    var playersArray = messageData["ps"] as JArray;
                    if (playersArray != null)
                    {
                        foreach (var player in playersArray)
                        {
                            var playerName = player["dn"]?.ToString();
                            var cardCount = player["cc"]?.Value<int>() ?? 0;

                            if (!string.IsNullOrEmpty(playerName))
                            {
                                lock (_lock)
                                {
                                    _playerCardCounts[playerName] = cardCount;
                                }
                                _uiManager.AppendLog($"Player {playerName} has {cardCount} cards", UIManager.LogLevel.Info, username);
                            }
                        }
                    }
                }

                // Parse current player
                if (messageData.ContainsKey("cp"))
                {
                    _currentPlayerTienLen = messageData["cp"]?.ToString() ?? "";
                    _uiManager.AppendLog($"Current player: {_currentPlayerTienLen}", UIManager.LogLevel.Info, username);
                }

                // Parse last played cards
                if (messageData.ContainsKey("lpc"))
                {
                    var lastPlayedCards = messageData["lpc"]?.ToObject<int[]>();
                    if (lastPlayedCards != null)
                    {
                        lock (_lock)
                        {
                            _lastPlayedCards = lastPlayedCards;
                            _playedCardsTienLen.AddRange(lastPlayedCards);
                        }

                        var cardInfos = lastPlayedCards.Select(id => new CardUtilityTienLen.CardInfo(id)).ToList();
                        var combo = CardUtilityTienLen.AnalyzeHand(cardInfos);
                        _uiManager.AppendLog($"Last played: {combo.Description}", UIManager.LogLevel.Info, username);
                    }
                }

                // Update TienLenSuggestionForm
                if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                {
                    _gameClient.TienLenSuggestionForm.UpdateGameInfo(_currentPlayerTienLen, _playerCardCounts, username);
                }

                CardsUpdated?.Invoke();
                NewGameStarted?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 250 for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Xử lý cmd 251 - Thông tin người chơi và bài đã đánh
        /// </summary>
        private void HandleCmd251(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 251 (Tien Len player info)", UIManager.LogLevel.Info, username);

                // Parse played cards from log data
                if (messageData.ContainsKey("log"))
                {
                    var logArray = messageData["log"] as JArray;
                    if (logArray != null)
                    {
                        foreach (var logEntry in logArray)
                        {
                            var action = logEntry["action"]?.ToString();
                            var playerName = logEntry["player"]?.ToString();
                            var cards = logEntry["cards"]?.ToObject<int[]>();

                            if (action == "play" && !string.IsNullOrEmpty(playerName) && cards != null)
                            {
                                lock (_lock)
                                {
                                    _playedCardsTienLen.AddRange(cards);
                                    if (_playerCardCounts.ContainsKey(playerName))
                                    {
                                        _playerCardCounts[playerName] -= cards.Length;
                                    }
                                }

                                var cardInfos = cards.Select(id => new CardUtilityTienLen.CardInfo(id)).ToList();
                                var combo = CardUtilityTienLen.AnalyzeHand(cardInfos);
                                _uiManager.AppendLog($"📜 Log: {playerName} played {combo.Description}", UIManager.LogLevel.Info, username);

                                // Update TienLenSuggestionForm
                                if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                                {
                                    int cardsLeftBefore = _playerCardCounts.GetValueOrDefault(playerName, 0) + cards.Length;
                                    int cardsLeftAfter = _playerCardCounts.GetValueOrDefault(playerName, 0);
                                    _gameClient.TienLenSuggestionForm.UpdateOpponentPlay(playerName, combo, cardsLeftBefore, cardsLeftAfter, true);
                                }
                            }
                        }
                    }
                }

                CardsUpdated?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 251 for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Xử lý cmd 252 - Kết thúc ván Tiến Lên với kết quả
        /// </summary>
        private void HandleCmd252(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 252 (Tien Len game end)", UIManager.LogLevel.Info, username);

                // Parse game results
                if (messageData.ContainsKey("results"))
                {
                    var resultsArray = messageData["results"] as JArray;
                    if (resultsArray != null)
                    {
                        foreach (var result in resultsArray)
                        {
                            var playerName = result["player"]?.ToString();
                            var rank = result["rank"]?.Value<int>() ?? 0;
                            var moneyChange = result["money"]?.Value<long>() ?? 0;
                            var finalCards = result["cards"]?.ToObject<int[]>();

                            if (!string.IsNullOrEmpty(playerName) && _gameClient.GetUsers().ContainsKey(playerName))
                            {
                                string resultText = $"Rank {rank}: {(moneyChange >= 0 ? "+" : "")}{moneyChange}";
                                _gameClient.GetUsers()[playerName].GameResult = resultText;
                                _uiManager.AppendLog($"Game result for {playerName}: {resultText}", UIManager.LogLevel.Info, playerName);

                                // Log final cards if available
                                if (finalCards != null && finalCards.Length > 0)
                                {
                                    try
                                    {
                                        var finalCardInfos = finalCards.Select(id => new CardUtilityTienLen.CardInfo(id)).ToList();
                                        if (finalCardInfos.Count > 0)
                                        {
                                            var finalCombo = CardUtilityTienLen.AnalyzeHand(finalCardInfos);
                                            _gameClient.TienLenSuggestionForm?.UpdateOpponentPlay(playerName, finalCombo, 1, 0, false);
                                        }
                                    }
                                    catch (Exception cardEx)
                                    {
                                        _uiManager.AppendLog($"Error processing final cards for {playerName}: {cardEx.Message}", UIManager.LogLevel.Error, username);
                                    }
                                }
                            }
                        }
                        _gameClient.RefreshUserList?.Invoke();
                    }
                }

                // Reset game data
                ResetGameData();
                _uiManager.AppendLog("🎮 Tien Len game ended!", UIManager.LogLevel.Info, username);
                GameEnded?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 252 for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Xử lý cmd 253 - Đánh lá bài Tiến Lên
        /// </summary>
        private void HandleCmd253(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 253 (Tien Len play cards)", UIManager.LogLevel.Info, username);

                var playedCards = messageData["cards"]?.ToObject<int[]>();
                var fromPlayerName = messageData["player"]?.ToString();

                if (playedCards != null && !string.IsNullOrEmpty(fromPlayerName))
                {
                    try
                    {
                        // Analyze played combo
                        var playedCardInfos = playedCards.Select(id => new CardUtilityTienLen.CardInfo(id)).ToList();
                        if (playedCardInfos.Count > 0)
                        {
                            var combo = CardUtilityTienLen.AnalyzeHand(playedCardInfos);

                            // Update card counts
                            int cardsLeftBefore = _playerCardCounts.ContainsKey(fromPlayerName) ? _playerCardCounts[fromPlayerName] : 13;
                            int cardsLeftAfter = cardsLeftBefore - playedCards.Length;

                            lock (_lock)
                            {
                                _playerCardCounts[fromPlayerName] = cardsLeftAfter;
                                _playedCardsTienLen.AddRange(playedCards);
                                _lastPlayedCards = playedCards;
                            }

                            // Update TienLenSuggestionForm
                            if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                            {
                                // Cập nhật bài đã đánh
                                _gameClient.TienLenSuggestionForm.UpdatePlayedCards(playedCards);

                                // Cập nhật thông tin game (current player sẽ được cập nhật từ cmd khác)
                                _gameClient.TienLenSuggestionForm.UpdateGameInfo(_currentPlayerTienLen, _playerCardCounts, username);

                                // Cập nhật phân tích đối thủ
                                _gameClient.TienLenSuggestionForm.UpdateOpponentPlay(fromPlayerName, combo, cardsLeftBefore, cardsLeftAfter, false);
                            }

                            _uiManager.AppendLog($"🎯 {fromPlayerName} played: {combo.Description} (cards left: {cardsLeftAfter})", UIManager.LogLevel.Info, username);
                        }
                    }
                    catch (Exception cardEx)
                    {
                        _uiManager.AppendLog($"Error processing played cards for {fromPlayerName}: {cardEx.Message}", UIManager.LogLevel.Error, username);
                    }
                }

                CardsUpdated?.Invoke();
                PlayCardReceived?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 253 for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Xử lý cmd 254 - Bỏ lượt Tiến Lên
        /// </summary>
        private void HandleCmd254(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 254 (Tien Len pass turn)", UIManager.LogLevel.Info, username);

                var playerName = messageData["player"]?.ToString() ?? username;
                _uiManager.AppendLog($"{playerName} passed turn", UIManager.LogLevel.Info, username);

                // Update TienLenSuggestionForm with pass info
                if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                {
                    _gameClient.TienLenSuggestionForm.UpdatePlayedCards(null); // null = pass
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 254 for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        public void ResetGameData()
        {
            lock (_lock)
            {
                _playerCardCounts.Clear();
                _playedCardsTienLen.Clear();
                _currentPlayerTienLen = "";
                _lastPlayedCards = new int[0];
            }

            // Reset TienLenSuggestionForm
            try
            {
                if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                {
                    _gameClient.TienLenSuggestionForm.ResetGameData();
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error resetting Tien Len game data: {ex.Message}", UIManager.LogLevel.Error);
            }

            _uiManager.AppendLog("Reset Tien Len game data", UIManager.LogLevel.Info);
        }

        public bool IsTeamMode()
        {
            return _playerCardCounts.Count >= 3;
        }

        public void UpdateUserCards(string username, int[] cards)
        {
            if (string.IsNullOrEmpty(username) || cards == null)
            {
                _uiManager.AppendLog($"Cannot update Tien Len cards for {username}: Invalid data", UIManager.LogLevel.Error);
                return;
            }

            // Tien Len can have variable card counts
            lock (_lock)
            {
                _playerCardCounts[username] = cards.Length;
                string cardsString = CardUtilityTienLen.ConvertCardsToString(cards);
                _uiManager.AppendLog($"Updated Tien Len cards for {username}: {cardsString}", UIManager.LogLevel.Info);
            }

            CardsUpdated?.Invoke();
        }

        // Getters for Tien Len specific data
        public Dictionary<string, int> GetPlayerCardCounts() => _playerCardCounts.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        public List<int> GetPlayedCardsTienLen() => _playedCardsTienLen.ToList();
        public string GetCurrentPlayerTienLen() => _currentPlayerTienLen;
        public int[] GetLastPlayedCards() => _lastPlayedCards?.ToArray() ?? new int[0];
    }
}
