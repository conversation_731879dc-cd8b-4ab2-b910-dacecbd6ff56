using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using AutoGameBai.GameTienLen;

namespace AutoGameBai.WebSocket
{
    /// <summary>
    /// WebSocket handler cho game <PERSON><PERSON><PERSON><PERSON>
    /// <PERSON><PERSON> lý các cmd: 250, 251, 252, 253, 254
    /// </summary>
    public class TienLenWebSocketHandler : IGameWebSocketHandler
    {
        private readonly GameClientManager _gameClient;
        private readonly UIManager _uiManager;
        private readonly Dictionary<string, int> _playerCardCounts;
        private readonly List<int> _playedCardsTienLen;
        private string _currentPlayerTienLen;
        private int[] _lastPlayedCards;
        private readonly object _lock = new object();

        public HashSet<int> SupportedCommands { get; } = new HashSet<int> { 250, 251, 252, 253, 254 };

        public event Action? CardsUpdated;
        public event Action? PlayCardReceived;
        public event Action? GameEnded;
        public event Action? NewGameStarted;

        public TienLenWebSocketHandler(GameClientManager gameClient, UIManager uiManager)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));

            _playerCardCounts = new Dictionary<string, int>();
            _playedCardsTienLen = new List<int>();
            _currentPlayerTienLen = "";
            _lastPlayedCards = new int[0];
        }

        public void HandleCommand(JObject messageData, string username, int cmd)
        {
            switch (cmd)
            {
                case 250:
                    HandleCmd250(messageData, username);
                    break;
                case 251:
                    HandleCmd251(messageData, username);
                    break;
                case 252:
                    HandleCmd252(messageData, username);
                    break;
                case 253:
                    HandleCmd253(messageData, username);
                    break;
                case 254:
                    HandleCmd254(messageData, username);
                    break;
                default:
                    _uiManager.AppendLog($"Unsupported Tien Len command: {cmd}", UIManager.LogLevel.Warning, username);
                    break;
            }
        }

        /// <summary>
        /// Xử lý cmd 250 - Danh sách bài và thông tin game Tiến Lên
        /// Format: {"cs":[8,11,13,22,23,28,29,30,33,41,46,2,4],"ps":[{"uid":"1_203998551","m":17498},{"uid":"1_229699688","m":53011}],"lpi":["1_203998551","1_229699688"],"cmd":250,"tP":{"uid":"1_229699688","dn":"nhatrang345"},"m":53011}
        /// </summary>
        private void HandleCmd250(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 250 (Tien Len game start)", UIManager.LogLevel.Info, username);

                // Parse main user cards (cs) - CHỈ LOG, KHÔNG CẬP NHẬT INDEX BÀI
                if (messageData.ContainsKey("cs"))
                {
                    var userCards = messageData["cs"]?.ToObject<int[]>();
                    if (userCards != null)
                    {
                        _uiManager.AppendLog($"📝 {username} nhận {userCards.Length} lá bài: {string.Join(",", userCards)}", UIManager.LogLevel.Info, username);

                        // KHÔNG cập nhật index bài cho user - chỉ cập nhật cho đối thủ
                        // Index bài chỉ hiển thị bài đối thủ đã đánh
                        _uiManager.AppendLog($"ℹ️ Index bài chỉ hiển thị bài đối thủ, không hiển thị bài của {username}", UIManager.LogLevel.Debug, username);
                    }
                }

                // Parse players info (ps) - để biết số lá của đối thủ
                if (messageData.ContainsKey("ps"))
                {
                    var playersArray = messageData["ps"] as JArray;
                    if (playersArray != null)
                    {
                        lock (_lock)
                        {
                            _playerCardCounts.Clear();
                        }

                        foreach (var player in playersArray)
                        {
                            var playerUid = player["uid"]?.ToString();
                            var playerMoney = player["m"]?.Value<long>() ?? 0;

                            if (!string.IsNullOrEmpty(playerUid))
                            {
                                // Mỗi player có 13 lá ở đầu game
                                lock (_lock)
                                {
                                    _playerCardCounts[playerUid] = 13;
                                }
                                _uiManager.AppendLog($"👤 Player {playerUid}: 13 lá, tiền: {playerMoney}", UIManager.LogLevel.Info, username);
                            }
                        }
                    }
                }

                // Parse first player (tP)
                if (messageData.ContainsKey("tP"))
                {
                    var firstPlayer = messageData["tP"];
                    var firstPlayerName = firstPlayer["dn"]?.ToString();
                    var firstPlayerUid = firstPlayer["uid"]?.ToString();

                    if (!string.IsNullOrEmpty(firstPlayerName))
                    {
                        lock (_lock)
                        {
                            _currentPlayerTienLen = firstPlayerName;
                        }
                        _uiManager.AppendLog($"🎯 User đi trước: {firstPlayerName} ({firstPlayerUid})", UIManager.LogLevel.Info, username);
                    }
                }

                // Update TienLenSuggestionForm
                if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                {
                    _gameClient.TienLenSuggestionForm.UpdateGameInfo(_currentPlayerTienLen, _playerCardCounts, username);
                }

                CardsUpdated?.Invoke();
                NewGameStarted?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 250 for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Xử lý cmd 251 - Thông tin người chơi và bài đã đánh
        /// Format 1: {"fP":{"uid":"1_203998551","pS":1,"dCs":[49,50],"dn":"cungnam567"},"cmd":251,"tP":{"uid":"1_229699688","dn":"nhatrang345"}}
        /// Format 2: {"fP":{"uid":"1_203998551","pS":2,"dn":"cungnam567"},"cmd":251,"tP":{"uid":"1_229699688","dn":"nhatrang345"}}
        /// </summary>
        private void HandleCmd251(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 251 (Tien Len player action)", UIManager.LogLevel.Info, username);

                // Parse from player (fP)
                if (messageData.ContainsKey("fP"))
                {
                    var fromPlayer = messageData["fP"];
                    var playerName = fromPlayer["dn"]?.ToString();
                    var playerUid = fromPlayer["uid"]?.ToString();
                    var playStatus = fromPlayer["pS"]?.Value<int>() ?? 0;
                    var playedCards = fromPlayer["dCs"]?.ToObject<int[]>();

                    if (!string.IsNullOrEmpty(playerName))
                    {
                        if (playStatus == 1 && playedCards != null && playedCards.Length > 0)
                        {
                            // Player đánh bài
                            lock (_lock)
                            {
                                _playedCardsTienLen.AddRange(playedCards);
                                _lastPlayedCards = playedCards;

                                // Cập nhật số lá còn lại
                                if (_playerCardCounts.ContainsKey(playerUid))
                                {
                                    _playerCardCounts[playerUid] -= playedCards.Length;
                                }
                                else if (_playerCardCounts.ContainsKey(playerName))
                                {
                                    _playerCardCounts[playerName] -= playedCards.Length;
                                }
                            }

                            var cardInfos = playedCards.Select(id => new CardUtilityTienLen.CardInfo(id)).ToList();
                            var combo = CardUtilityTienLen.AnalyzeHand(cardInfos);

                            var cardsLeft = _playerCardCounts.GetValueOrDefault(playerUid, _playerCardCounts.GetValueOrDefault(playerName, 0));
                            _uiManager.AppendLog($"🎯 {playerName} đánh {playedCards.Length} lá: {string.Join(",", playedCards)} - {combo.Description} (còn {cardsLeft} lá)", UIManager.LogLevel.Info, username);

                            // Update TienLenSuggestionForm
                            if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                            {
                                int cardsLeftBefore = cardsLeft + playedCards.Length;
                                _gameClient.TienLenSuggestionForm.UpdateOpponentPlay(playerName, combo, cardsLeftBefore, cardsLeft, false);
                                _gameClient.TienLenSuggestionForm.UpdatePlayedCards(playedCards);
                            }
                        }
                        else if (playStatus == 2)
                        {
                            // Player bỏ lượt
                            _uiManager.AppendLog($"⏭️ {playerName} bỏ lượt", UIManager.LogLevel.Info, username);

                            // Update TienLenSuggestionForm
                            if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                            {
                                _gameClient.TienLenSuggestionForm.UpdatePlayedCards(null); // null = pass
                            }
                        }
                    }
                }

                // Parse next player (tP)
                if (messageData.ContainsKey("tP"))
                {
                    var nextPlayer = messageData["tP"];
                    var nextPlayerName = nextPlayer["dn"]?.ToString();

                    if (!string.IsNullOrEmpty(nextPlayerName))
                    {
                        lock (_lock)
                        {
                            _currentPlayerTienLen = nextPlayerName;
                        }
                        _uiManager.AppendLog($"🔄 Lượt tiếp theo: {nextPlayerName}", UIManager.LogLevel.Info, username);
                    }
                }

                // Update TienLenSuggestionForm with current player
                if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                {
                    _gameClient.TienLenSuggestionForm.UpdateGameInfo(_currentPlayerTienLen, _playerCardCounts, username);
                }

                CardsUpdated?.Invoke();
                PlayCardReceived?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 251 for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Xử lý cmd 252 - Kết thúc ván Tiến Lên với kết quả
        /// Format: {"ps":[{"cs":[36,40],"uid":"1_203998551","dn":"1_203998551","mX":-200,"m":17298}],"fP":{"uid":"1_229699688","pS":1,"dCs":[33],"dn":"nhatrang345","mX":196,"m":53207},"cmd":252}
        /// </summary>
        private void HandleCmd252(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 252 (Tien Len game end)", UIManager.LogLevel.Info, username);

                // Parse final player action (fP) - người thắng cuối cùng
                if (messageData.ContainsKey("fP"))
                {
                    var finalPlayer = messageData["fP"];
                    var playerName = finalPlayer["dn"]?.ToString();
                    var playerUid = finalPlayer["uid"]?.ToString();
                    var playStatus = finalPlayer["pS"]?.Value<int>() ?? 0;
                    var finalCards = finalPlayer["dCs"]?.ToObject<int[]>();
                    var moneyChange = finalPlayer["mX"]?.Value<long>() ?? 0;
                    var totalMoney = finalPlayer["m"]?.Value<long>() ?? 0;

                    if (!string.IsNullOrEmpty(playerName))
                    {
                        if (playStatus == 1 && finalCards != null && finalCards.Length > 0)
                        {
                            // Người thắng đánh lá cuối
                            var cardInfos = finalCards.Select(id => new CardUtilityTienLen.CardInfo(id)).ToList();
                            var combo = CardUtilityTienLen.AnalyzeHand(cardInfos);

                            _uiManager.AppendLog($"🏆 {playerName} THẮNG! Đánh lá cuối: {string.Join(",", finalCards)} - {combo.Description}", UIManager.LogLevel.Info, username);
                            _uiManager.AppendLog($"💰 {playerName}: {(moneyChange >= 0 ? "+" : "")}{moneyChange} (tổng: {totalMoney})", UIManager.LogLevel.Info, username);

                            // Update TienLenSuggestionForm
                            if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                            {
                                _gameClient.TienLenSuggestionForm.UpdateOpponentPlay(playerName, combo, finalCards.Length, 0, false);
                            }

                            // Update user result
                            if (_gameClient.GetUsers().ContainsKey(playerName))
                            {
                                _gameClient.GetUsers()[playerName].GameResult = $"🏆 THẮNG: +{moneyChange}";
                            }
                        }
                    }
                }

                // Parse other players results (ps)
                if (messageData.ContainsKey("ps"))
                {
                    var playersArray = messageData["ps"] as JArray;
                    if (playersArray != null)
                    {
                        foreach (var player in playersArray)
                        {
                            var playerName = player["dn"]?.ToString();
                            var playerUid = player["uid"]?.ToString();
                            var remainingCards = player["cs"]?.ToObject<int[]>();
                            var moneyChange = player["mX"]?.Value<long>() ?? 0;
                            var totalMoney = player["m"]?.Value<long>() ?? 0;

                            if (!string.IsNullOrEmpty(playerName))
                            {
                                _uiManager.AppendLog($"💸 {playerName}: {(moneyChange >= 0 ? "+" : "")}{moneyChange} (tổng: {totalMoney})", UIManager.LogLevel.Info, username);

                                // Log remaining cards
                                if (remainingCards != null && remainingCards.Length > 0)
                                {
                                    _uiManager.AppendLog($"📋 {playerName} còn {remainingCards.Length} lá: {string.Join(",", remainingCards)}", UIManager.LogLevel.Info, username);

                                    // Update TienLenSuggestionForm with final cards
                                    if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                                    {
                                        var cardInfos = remainingCards.Select(id => new CardUtilityTienLen.CardInfo(id)).ToList();
                                        var combo = CardUtilityTienLen.AnalyzeHand(cardInfos);
                                        _gameClient.TienLenSuggestionForm.UpdateOpponentPlay(playerName, combo, remainingCards.Length, remainingCards.Length, false);
                                    }
                                }

                                // Update user result
                                if (_gameClient.GetUsers().ContainsKey(playerName))
                                {
                                    string resultText = moneyChange >= 0 ? $"✅ +{moneyChange}" : $"❌ {moneyChange}";
                                    _gameClient.GetUsers()[playerName].GameResult = resultText;
                                }
                            }
                        }
                    }
                }

                // Refresh user list
                _gameClient.RefreshUserList?.Invoke();

                // Reset game data
                ResetGameData();
                _uiManager.AppendLog("🎮 Kết thúc ván Tiến Lên!", UIManager.LogLevel.Info, username);
                GameEnded?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 252 for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Xử lý cmd 253 - Đánh lá bài Tiến Lên
        /// </summary>
        private void HandleCmd253(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 253 (Tien Len play cards)", UIManager.LogLevel.Info, username);

                var playedCards = messageData["cards"]?.ToObject<int[]>();
                var fromPlayerName = messageData["player"]?.ToString();

                if (playedCards != null && !string.IsNullOrEmpty(fromPlayerName))
                {
                    try
                    {
                        // Analyze played combo
                        var playedCardInfos = playedCards.Select(id => new CardUtilityTienLen.CardInfo(id)).ToList();
                        if (playedCardInfos.Count > 0)
                        {
                            var combo = CardUtilityTienLen.AnalyzeHand(playedCardInfos);

                            // Update card counts
                            int cardsLeftBefore = _playerCardCounts.ContainsKey(fromPlayerName) ? _playerCardCounts[fromPlayerName] : 13;
                            int cardsLeftAfter = cardsLeftBefore - playedCards.Length;

                            lock (_lock)
                            {
                                _playerCardCounts[fromPlayerName] = cardsLeftAfter;
                                _playedCardsTienLen.AddRange(playedCards);
                                _lastPlayedCards = playedCards;
                            }

                            // Update TienLenSuggestionForm
                            if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                            {
                                // Cập nhật bài đã đánh
                                _gameClient.TienLenSuggestionForm.UpdatePlayedCards(playedCards);

                                // Cập nhật thông tin game (current player sẽ được cập nhật từ cmd khác)
                                _gameClient.TienLenSuggestionForm.UpdateGameInfo(_currentPlayerTienLen, _playerCardCounts, username);

                                // Cập nhật phân tích đối thủ
                                _gameClient.TienLenSuggestionForm.UpdateOpponentPlay(fromPlayerName, combo, cardsLeftBefore, cardsLeftAfter, false);
                            }

                            _uiManager.AppendLog($"🎯 {fromPlayerName} played: {combo.Description} (cards left: {cardsLeftAfter})", UIManager.LogLevel.Info, username);
                        }
                    }
                    catch (Exception cardEx)
                    {
                        _uiManager.AppendLog($"Error processing played cards for {fromPlayerName}: {cardEx.Message}", UIManager.LogLevel.Error, username);
                    }
                }

                CardsUpdated?.Invoke();
                PlayCardReceived?.Invoke();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 253 for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// Xử lý cmd 254 - Bỏ lượt Tiến Lên
        /// </summary>
        private void HandleCmd254(JObject messageData, string username)
        {
            try
            {
                _uiManager.AppendLog($"[WebSocket] {username}: Processing cmd 254 (Tien Len pass turn)", UIManager.LogLevel.Info, username);

                var playerName = messageData["player"]?.ToString() ?? username;
                _uiManager.AppendLog($"{playerName} passed turn", UIManager.LogLevel.Info, username);

                // Update TienLenSuggestionForm with pass info
                if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                {
                    _gameClient.TienLenSuggestionForm.UpdatePlayedCards(null); // null = pass
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error processing cmd 254 for {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        public void ResetGameData()
        {
            lock (_lock)
            {
                _playerCardCounts.Clear();
                _playedCardsTienLen.Clear();
                _currentPlayerTienLen = "";
                _lastPlayedCards = new int[0];
            }

            // Reset TienLenSuggestionForm
            try
            {
                if (_gameClient.TienLenSuggestionForm != null && !_gameClient.TienLenSuggestionForm.IsDisposed)
                {
                    _gameClient.TienLenSuggestionForm.ResetGameData();
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Error resetting Tien Len game data: {ex.Message}", UIManager.LogLevel.Error);
            }

            _uiManager.AppendLog("Reset Tien Len game data", UIManager.LogLevel.Info);
        }

        public bool IsTeamMode()
        {
            return _playerCardCounts.Count >= 3;
        }

        public void UpdateUserCards(string username, int[] cards)
        {
            if (string.IsNullOrEmpty(username) || cards == null)
            {
                _uiManager.AppendLog($"Cannot update Tien Len cards for {username}: Invalid data", UIManager.LogLevel.Error);
                return;
            }

            // Tien Len can have variable card counts
            lock (_lock)
            {
                _playerCardCounts[username] = cards.Length;
                string cardsString = CardUtilityTienLen.ConvertCardsToString(cards);
                _uiManager.AppendLog($"Updated Tien Len cards for {username}: {cardsString}", UIManager.LogLevel.Info);
            }

            CardsUpdated?.Invoke();
        }

        // Getters for Tien Len specific data
        public Dictionary<string, int> GetPlayerCardCounts() => _playerCardCounts.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        public List<int> GetPlayedCardsTienLen() => _playedCardsTienLen.ToList();
        public string GetCurrentPlayerTienLen() => _currentPlayerTienLen;
        public int[] GetLastPlayedCards() => _lastPlayedCards?.ToArray() ?? new int[0];
    }
}
