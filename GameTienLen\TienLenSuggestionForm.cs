using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace AutoGameBai.GameTienLen
{
    /// <summary>
    /// Form gợi ý cho game Tiến Lên
    /// </summary>
    public partial class TienLenSuggestionForm : Form
    {
        private readonly UIManager _uiManager;
        private readonly GameClientManager _gameClient;
        private readonly TienLenStrategyEngine _strategyEngine;
        private readonly TienLenOpponentAnalyzer _opponentAnalyzer;

        // UI Components
        private Panel _playedCardsPanel;
        private Panel _suggestionPanel;
        private Label _strategyLabel;
        private Label _opponentInfoLabel;
        private Label _remainingCardsLabel;

        // Game Data
        private List<CardUtilityTienLen.CardInfo> _myCards = new List<CardUtilityTienLen.CardInfo>();
        private List<CardUtilityTienLen.CardInfo> _playedCards = new List<CardUtilityTienLen.CardInfo>();
        private CardUtilityTienLen.ComboInfo _lastPlay = null;
        private Dictionary<string, int> _playerCardCounts = new Dictionary<string, int>();
        private string _currentPlayer = "";
        private string _myUsername = "";

        public TienLenSuggestionForm(UIManager uiManager, GameClientManager gameClient)
        {
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _strategyEngine = new TienLenStrategyEngine(uiManager);
            _opponentAnalyzer = new TienLenOpponentAnalyzer(uiManager);

            InitializeComponent();
            SetupForm();

            _uiManager.AppendLog("🎮 Đã khởi tạo TienLenSuggestionForm với Strategy Engine", UIManager.LogLevel.Info);
        }

        private void InitializeComponent()
        {
            SuspendLayout();

            // Form properties - Tăng chiều cao, giảm chiều rộng
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(600, 800); // 600x800 thay vì 800x600
            Text = "Gợi Ý Tiến Lên";
            StartPosition = FormStartPosition.CenterScreen;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;

            ResumeLayout(false);
        }

        private void SetupForm()
        {
            // Main layout - vertical
            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4,
                Padding = new Padding(10)
            };

            // Row heights - Điều chỉnh cho form cao hơn
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 150)); // Played cards - tăng lên
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));  // Info labels - tăng lên
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 150)); // Suggestion cards - tăng lên
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100));  // Strategy info - phần còn lại

            // 1. Played Cards Panel
            _playedCardsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.LightGray
            };

            var playedLabel = new Label
            {
                Text = "Bài đối thủ đã đánh:",
                Font = new Font("Arial", 10, FontStyle.Bold),
                Location = new Point(5, 5),
                AutoSize = true
            };
            _playedCardsPanel.Controls.Add(playedLabel);

            // Thêm label số lá đối thủ ngay kế bên
            _remainingCardsLabel = new Label
            {
                Text = "Số lá Đối Thủ: --",
                Font = new Font("Arial", 10, FontStyle.Bold),
                Location = new Point(200, 5), // Kế bên label "Bài đối thủ đã đánh"
                AutoSize = true,
                ForeColor = Color.DarkRed,
                BackColor = Color.LightYellow,
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(5, 2, 5, 2)
            };
            _playedCardsPanel.Controls.Add(_remainingCardsLabel);

            // 2. Info Labels Panel
            var infoPanel = new Panel { Dock = DockStyle.Fill };

            _opponentInfoLabel = new Label
            {
                Text = "Thông tin đối thủ: Chưa có dữ liệu",
                Font = new Font("Arial", 9),
                Location = new Point(5, 5), // Đưa lên đầu vì _remainingCardsLabel đã chuyển lên trên
                AutoSize = true,
                ForeColor = Color.DarkGreen
            };

            infoPanel.Controls.Add(_opponentInfoLabel);

            // 3. Suggestion Panel
            _suggestionPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.LightBlue
            };

            var suggestionLabel = new Label
            {
                Text = "Gợi ý đánh bài:",
                Font = new Font("Arial", 10, FontStyle.Bold),
                Location = new Point(5, 5),
                AutoSize = true
            };
            _suggestionPanel.Controls.Add(suggestionLabel);

            // 4. Strategy Label
            _strategyLabel = new Label
            {
                Dock = DockStyle.Fill,
                Text = "Chiến thuật: Chờ dữ liệu game...",
                Font = new Font("Arial", 9),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.LightYellow,
                Padding = new Padding(10),
                TextAlign = ContentAlignment.TopLeft
            };

            // Add to main layout
            mainLayout.Controls.Add(_playedCardsPanel, 0, 0);
            mainLayout.Controls.Add(infoPanel, 0, 1);
            mainLayout.Controls.Add(_suggestionPanel, 0, 2);
            mainLayout.Controls.Add(_strategyLabel, 0, 3);

            Controls.Add(mainLayout);
        }

        /// <summary>
        /// Cập nhật danh sách bài của người chơi (alias cho UpdateUserCards)
        /// </summary>
        public void UpdateUserCards(int[] cardIds)
        {
            UpdateMyCards(cardIds);
        }

        /// <summary>
        /// Cập nhật danh sách bài của người chơi
        /// </summary>
        public void UpdateMyCards(int[] cardIds)
        {
            try
            {
                if (cardIds == null || cardIds.Length == 0)
                {
                    _uiManager.AppendLog("❌ UpdateMyCards: Không có bài", UIManager.LogLevel.Warning);
                    return;
                }

                _myCards = cardIds.Select(id => new CardUtilityTienLen.CardInfo(id)).ToList();
                _uiManager.AppendLog($"🎴 Cập nhật bài của tôi: {CardUtilityTienLen.ConvertCardsToString(cardIds)}", UIManager.LogLevel.Info);

                // CHỈ cập nhật gợi ý nếu đến lượt mình
                if (_currentPlayer == _myUsername)
                {
                    _uiManager.AppendLog($"🎯 Có bài mới và đến lượt tôi - cập nhật gợi ý", UIManager.LogLevel.Info);
                    UpdateSuggestion();
                }
                else
                {
                    _uiManager.AppendLog($"🎴 Có bài mới nhưng chưa đến lượt - chờ...", UIManager.LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi UpdateMyCards: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Cập nhật thông tin game từ WebSocket
        /// </summary>
        public void UpdateGameInfo(string currentPlayer, Dictionary<string, int> playerCardCounts, string myUsername)
        {
            try
            {
                _uiManager.AppendLog($"🔄 UpdateGameInfo: currentPlayer={currentPlayer}, myUsername={myUsername}, playerCount={playerCardCounts.Count}", UIManager.LogLevel.Info);

                _currentPlayer = currentPlayer;
                _playerCardCounts = playerCardCounts;
                _myUsername = myUsername;

                _uiManager.AppendLog($"📊 Player card counts: {string.Join(", ", playerCardCounts.Select(kvp => $"{kvp.Key}:{kvp.Value}"))}", UIManager.LogLevel.Info);

                UpdateRemainingCardsDisplay();
                UpdateOpponentAnalysisDisplay();

                // CHỈ cập nhật gợi ý khi đến lượt của mình
                if (_currentPlayer == _myUsername && _myCards.Count > 0)
                {
                    _uiManager.AppendLog($"🎯 Đến lượt của tôi - cập nhật gợi ý. Số bài của tôi: {_myCards.Count}", UIManager.LogLevel.Info);
                    UpdateSuggestion();
                }
                else if (_currentPlayer != _myUsername)
                {
                    _uiManager.AppendLog($"⏳ Chờ {_currentPlayer} đánh bài", UIManager.LogLevel.Info);
                    // Clear suggestions khi không phải lượt mình
                    ClearSuggestions();
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi UpdateGameInfo: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Clear suggestions khi không phải lượt mình
        /// </summary>
        private void ClearSuggestions()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(ClearSuggestions));
                    return;
                }

                // Clear suggestion panel
                var existingCards = _suggestionPanel.Controls.OfType<PictureBox>().ToList();
                foreach (var card in existingCards)
                {
                    _suggestionPanel.Controls.Remove(card);
                    card.Dispose();
                }

                // Clear labels
                var existingLabels = _suggestionPanel.Controls.OfType<Label>().ToList();
                foreach (var label in existingLabels)
                {
                    _suggestionPanel.Controls.Remove(label);
                    label.Dispose();
                }

                // Add waiting message
                var waitingLabel = new Label
                {
                    Text = "Chờ lượt của bạn...",
                    Location = new Point(10, 30),
                    AutoSize = true,
                    Font = new Font("Arial", 10, FontStyle.Italic),
                    ForeColor = Color.Gray
                };
                _suggestionPanel.Controls.Add(waitingLabel);

                _strategyLabel.Text = "Chiến thuật: Chờ lượt của bạn...";
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi ClearSuggestions: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Cập nhật bài đã đánh
        /// </summary>
        public void UpdatePlayedCards(int[] cardIds)
        {
            try
            {
                if (cardIds == null || cardIds.Length == 0)
                {
                    _uiManager.AppendLog("🔄 Bỏ lượt", UIManager.LogLevel.Info);
                    return;
                }

                var playedCards = cardIds.Select(id => new CardUtilityTienLen.CardInfo(id)).ToList();
                _playedCards.AddRange(playedCards);
                _lastPlay = CardUtilityTienLen.AnalyzeHand(playedCards);

                _uiManager.AppendLog($"🎴 Bài vừa đánh: {CardUtilityTienLen.ConvertCardsToString(cardIds)} - {_lastPlay.Description}", UIManager.LogLevel.Info);

                UpdatePlayedCardsDisplay();
                UpdateOpponentInfo();

                // Nếu đến lượt mình sau khi đối thủ đánh, cập nhật gợi ý
                if (_currentPlayer == _myUsername && _myCards.Count > 0)
                {
                    _uiManager.AppendLog($"🎯 Đối thủ đã đánh xong, đến lượt tôi - cập nhật gợi ý", UIManager.LogLevel.Info);
                    UpdateSuggestion();
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi UpdatePlayedCards: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        private void UpdatePlayedCardsDisplay()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(UpdatePlayedCardsDisplay));
                    return;
                }

                // Clear existing cards
                var existingCards = _playedCardsPanel.Controls.OfType<PictureBox>().ToList();
                foreach (var card in existingCards)
                {
                    _playedCardsPanel.Controls.Remove(card);
                    card.Dispose();
                }

                if (_playedCards.Count == 0) return;

                // Display last 14 played cards in 2 rows (7 cards per row)
                var recentCards = _playedCards.TakeLast(14).ToList();
                int cardWidth = 40;  // Giảm kích thước để vừa 7 lá
                int cardHeight = 60;
                int spacing = 2;     // Giảm spacing
                int startX = 10;
                int startY = 30;
                int cardsPerRow = 7; // 7 lá mỗi hàng

                for (int i = 0; i < recentCards.Count; i++)
                {
                    var card = recentCards[i];
                    int row = i / cardsPerRow;
                    int col = i % cardsPerRow;

                    var cardPanel = new PictureBox
                    {
                        Size = new Size(cardWidth, cardHeight),
                        Location = new Point(startX + col * (cardWidth + spacing), startY + row * (cardHeight + spacing + 5)),
                        BorderStyle = BorderStyle.FixedSingle,
                        BackColor = Color.White,
                        SizeMode = PictureBoxSizeMode.StretchImage
                    };

                    // Load card image
                    string imagePath = $"card/{card.Id}.png";
                    if (System.IO.File.Exists(imagePath))
                    {
                        cardPanel.Image = Image.FromFile(imagePath);
                    }
                    else
                    {
                        // Fallback text
                        cardPanel.BackColor = Color.LightGray;
                        var label = new Label
                        {
                            Text = $"{CardUtilityTienLen.GetRankName(card.Rank)}\n{CardUtilityTienLen.GetSuitName(card.Suit)}",
                            Dock = DockStyle.Fill,
                            TextAlign = ContentAlignment.MiddleCenter,
                            Font = new Font("Arial", 8, FontStyle.Bold)
                        };
                        cardPanel.Controls.Add(label);
                    }

                    _playedCardsPanel.Controls.Add(cardPanel);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi UpdatePlayedCardsDisplay: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        private void UpdateRemainingCardsDisplay()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(UpdateRemainingCardsDisplay));
                    return;
                }

                _uiManager.AppendLog($"📊 UpdateRemainingCardsDisplay: {_playerCardCounts.Count} players", UIManager.LogLevel.Info);

                // Hiển thị đơn giản chỉ số lá đối thủ
                var opponents = _playerCardCounts.Where(kvp => kvp.Key != _myUsername).ToList();
                if (opponents.Count > 0)
                {
                    var opponentCards = opponents.Select(kvp => kvp.Value.ToString()).ToList();
                    var displayText = $"Số lá Đối Thủ: {string.Join(", ", opponentCards)}";
                    _remainingCardsLabel.Text = displayText;
                }
                else
                {
                    _remainingCardsLabel.Text = "Số lá Đối Thủ: --";
                }

                _uiManager.AppendLog($"📊 Hiển thị: {_remainingCardsLabel.Text}", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi UpdateRemainingCardsDisplay: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        private void UpdateOpponentInfo()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(UpdateOpponentInfo));
                    return;
                }

                var opponentAnalysis = AnalyzeOpponents();
                _opponentInfoLabel.Text = $"Phân tích đối thủ: {opponentAnalysis}";
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi UpdateOpponentInfo: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        private void UpdateSuggestion()
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(UpdateSuggestion));
                    return;
                }

                _uiManager.AppendLog($"🎯 UpdateSuggestion: Số bài của tôi: {_myCards.Count}", UIManager.LogLevel.Info);

                if (_myCards.Count == 0)
                {
                    _uiManager.AppendLog("⚠️ Không có bài để phân tích", UIManager.LogLevel.Warning);
                    _strategyLabel.Text = "Chiến thuật: Chưa có bài để phân tích";
                    return;
                }

                _uiManager.AppendLog($"🃏 Bài của tôi: {CardUtilityTienLen.ConvertCardsToString(_myCards.Select(c => c.Id).ToArray())}", UIManager.LogLevel.Info);
                _uiManager.AppendLog($"🎴 Last play: {(_lastPlay?.Description ?? "Lượt đầu tiên")}", UIManager.LogLevel.Info);

                // Sử dụng Strategy Engine để phân tích tình huống
                var situation = new TienLenStrategyEngine.GameSituation
                {
                    MyCards = _myCards,
                    LastPlay = _lastPlay,
                    PlayerCardCounts = _playerCardCounts,
                    PlayedCards = _playedCards,
                    CurrentPlayer = _currentPlayer,
                    MyUsername = _myUsername,
                    IsFirstPlay = _lastPlay == null || _lastPlay.Cards.Count == 0,
                    IsEndGame = _playerCardCounts.Values.Any(count => count <= 5)
                };

                var strategyResult = _strategyEngine.AnalyzeSituation(situation);
                _uiManager.AppendLog($"🧠 Chiến thuật: {strategyResult.Strategy} (Aggressive: {strategyResult.AggressiveScore:F1})", UIManager.LogLevel.Info);

                // Hiển thị gợi ý ưu tiên từ strategy engine
                var prioritySuggestions = strategyResult.PrioritySuggestions;
                if (prioritySuggestions.Count == 0)
                {
                    // Fallback to basic suggestions
                    prioritySuggestions = CardUtilityTienLen.FindPlayableHands(_myCards, _lastPlay);
                }

                _uiManager.AppendLog($"💡 Tìm được {prioritySuggestions.Count} gợi ý ưu tiên", UIManager.LogLevel.Info);

                if (prioritySuggestions.Count > 0)
                {
                    _uiManager.AppendLog($"🎯 Gợi ý tốt nhất: {prioritySuggestions.First().Description}", UIManager.LogLevel.Info);
                }

                DisplaySuggestions(prioritySuggestions);

                // Hiển thị chiến thuật chi tiết
                _strategyLabel.Text = $"🎯 {strategyResult.Strategy}\n\n{strategyResult.Details}\n\n💭 {strategyResult.Reasoning}";
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi UpdateSuggestion: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        private void DisplaySuggestions(List<CardUtilityTienLen.ComboInfo> suggestions)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action<List<CardUtilityTienLen.ComboInfo>>(DisplaySuggestions), suggestions);
                    return;
                }

                // Clear existing suggestions
                var existingCards = _suggestionPanel.Controls.OfType<PictureBox>().ToList();
                foreach (var card in existingCards)
                {
                    _suggestionPanel.Controls.Remove(card);
                    card.Dispose();
                }

                if (suggestions.Count == 0)
                {
                    var noSuggestionLabel = new Label
                    {
                        Text = "Không có bài nào có thể đánh",
                        Location = new Point(10, 30),
                        AutoSize = true,
                        Font = new Font("Arial", 10, FontStyle.Bold),
                        ForeColor = Color.Red
                    };
                    _suggestionPanel.Controls.Add(noSuggestionLabel);
                    return;
                }

                // Display best suggestion - layout 7x2
                var bestSuggestion = suggestions.First();
                int cardWidth = 40;  // Giảm kích thước để vừa 7 lá
                int cardHeight = 60;
                int spacing = 2;     // Giảm spacing
                int startX = 10;
                int startY = 30;
                int cardsPerRow = 7; // 7 lá mỗi hàng

                for (int i = 0; i < bestSuggestion.Cards.Count; i++)
                {
                    var card = bestSuggestion.Cards[i];
                    int row = i / cardsPerRow;
                    int col = i % cardsPerRow;

                    var cardPanel = new PictureBox
                    {
                        Size = new Size(cardWidth, cardHeight),
                        Location = new Point(startX + col * (cardWidth + spacing), startY + row * (cardHeight + spacing + 5)),
                        BorderStyle = BorderStyle.FixedSingle,
                        BackColor = Color.White,
                        SizeMode = PictureBoxSizeMode.StretchImage
                    };

                    // Load card image
                    string imagePath = $"card/{card.Id}.png";
                    if (System.IO.File.Exists(imagePath))
                    {
                        cardPanel.Image = Image.FromFile(imagePath);
                    }
                    else
                    {
                        // Fallback text
                        cardPanel.BackColor = Color.LightGreen;
                        var label = new Label
                        {
                            Text = $"{CardUtilityTienLen.GetRankName(card.Rank)}\n{CardUtilityTienLen.GetSuitName(card.Suit)}",
                            Dock = DockStyle.Fill,
                            TextAlign = ContentAlignment.MiddleCenter,
                            Font = new Font("Arial", 8, FontStyle.Bold)
                        };
                        cardPanel.Controls.Add(label);
                    }

                    _suggestionPanel.Controls.Add(cardPanel);
                }

                // Add suggestion description - điều chỉnh vị trí cho layout mới
                int maxRows = (bestSuggestion.Cards.Count - 1) / cardsPerRow + 1;
                var descLabel = new Label
                {
                    Text = $"Gợi ý: {bestSuggestion.Description}",
                    Location = new Point(startX, startY + maxRows * (cardHeight + spacing + 5) + 10),
                    AutoSize = true,
                    Font = new Font("Arial", 9, FontStyle.Bold),
                    ForeColor = Color.DarkBlue
                };
                _suggestionPanel.Controls.Add(descLabel);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi DisplaySuggestions: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        private string GenerateStrategy(List<CardUtilityTienLen.ComboInfo> suggestions)
        {
            try
            {
                if (suggestions.Count == 0)
                {
                    return "Bỏ lượt - Không có bài nào có thể đánh";
                }

                var bestSuggestion = suggestions.First();
                var strategy = $"Đánh {bestSuggestion.Description}. ";

                // Phân tích chiến thuật
                if (_lastPlay == null)
                {
                    strategy += "Lượt đầu tiên - nên đánh bài nhỏ để thăm dò.";
                }
                else
                {
                    switch (bestSuggestion.Type)
                    {
                        case CardUtilityTienLen.HandType.Single:
                            strategy += "Đánh lá đơn để giữ combo mạnh.";
                            break;
                        case CardUtilityTienLen.HandType.Pair:
                            strategy += "Đánh đôi để áp lực đối thủ.";
                            break;
                        case CardUtilityTienLen.HandType.Triple:
                            strategy += "Đánh xám - combo mạnh!";
                            break;
                        case CardUtilityTienLen.HandType.Straight:
                            strategy += "Đánh sảnh để kiểm soát ván bài.";
                            break;
                        case CardUtilityTienLen.HandType.Quadruple:
                            strategy += "Tứ quý - ăn chắc!";
                            break;
                    }
                }

                // Thêm thông tin về bài còn lại
                var remainingCards = _myCards.Count - bestSuggestion.Cards.Count;
                strategy += $" Còn lại {remainingCards} lá.";

                // Phân tích đối thủ
                var opponentAnalysis = AnalyzeOpponents();
                if (!string.IsNullOrEmpty(opponentAnalysis))
                {
                    strategy += $" {opponentAnalysis}";
                }

                return strategy;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GenerateStrategy: {ex.Message}", UIManager.LogLevel.Error);
                return "Lỗi phân tích chiến thuật";
            }
        }

        private string AnalyzeOpponents()
        {
            try
            {
                if (_playerCardCounts.Count <= 1) return "";

                var opponents = _playerCardCounts.Where(kvp => kvp.Key != _myUsername).ToList();
                if (opponents.Count == 0) return "";

                var minCards = opponents.Min(kvp => kvp.Value);
                var dangerousOpponents = opponents.Where(kvp => kvp.Value <= 3).ToList();

                var analysis = "";

                if (dangerousOpponents.Any())
                {
                    var names = string.Join(", ", dangerousOpponents.Select(kvp => kvp.Key));
                    analysis += $"Cảnh báo: {names} sắp hết bài! ";
                }

                if (minCards <= 1)
                {
                    analysis += "Có người chỉ còn 1 lá - cần chặn! ";
                }
                else if (minCards <= 3)
                {
                    analysis += "Có người sắp thắng - cần cẩn thận! ";
                }

                // Phân tích bài đã đánh
                if (_playedCards.Count > 0)
                {
                    var recentPlays = _playedCards.TakeLast(5).ToList();
                    var highCards = recentPlays.Where(c => CardUtilityTienLen.GetTienLenValue(c.Rank) >= 10).Count();

                    if (highCards >= 3)
                    {
                        analysis += "Nhiều bài lớn đã ra - có thể đánh mạnh. ";
                    }
                }

                return analysis.Trim();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi AnalyzeOpponents: {ex.Message}", UIManager.LogLevel.Error);
                return "";
            }
        }

        /// <summary>
        /// Cập nhật khi đối thủ đánh bài (phân tích thông minh)
        /// </summary>
        public void UpdateOpponentPlay(string username, CardUtilityTienLen.ComboInfo combo, int cardsLeftBefore, int cardsLeftAfter, bool isFirstPlay = false)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action<string, CardUtilityTienLen.ComboInfo, int, int, bool>(UpdateOpponentPlay),
                           username, combo, cardsLeftBefore, cardsLeftAfter, isFirstPlay);
                    return;
                }

                // Cập nhật thông tin cơ bản
                _playerCardCounts[username] = cardsLeftAfter;

                // Phân tích đối thủ thông minh
                _opponentAnalyzer.UpdateOpponentPlay(username, combo, cardsLeftBefore, cardsLeftAfter, isFirstPlay);

                // Cập nhật bài đã đánh
                foreach (var card in combo.Cards)
                {
                    if (!_playedCards.Any(c => c.Id == card.Id))
                    {
                        _playedCards.Add(card);
                    }
                }

                _uiManager.AppendLog($"🎯 {username} đánh: {combo.Description} (còn {cardsLeftAfter} lá)", UIManager.LogLevel.Info);

                // Cập nhật hiển thị bài đã đánh
                UpdatePlayedCardsDisplay();
                UpdateRemainingCardsDisplay();
                UpdateOpponentAnalysisDisplay();
                UpdateSuggestion();
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi UpdateOpponentPlay: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Cập nhật hiển thị phân tích đối thủ thông minh
        /// </summary>
        private void UpdateOpponentAnalysisDisplay()
        {
            try
            {
                var analyses = _opponentAnalyzer.AnalyzeAllOpponents(_myCards, _playedCards);

                if (analyses.Count == 0)
                {
                    _opponentInfoLabel.Text = "Thông tin đối thủ: Chưa có dữ liệu";
                    return;
                }

                var displayText = "🔍 Phân tích đối thủ:\n";

                foreach (var analysis in analyses.Take(3)) // Top 3 đối thủ nguy hiểm nhất
                {
                    displayText += $"• {analysis.Username}: {analysis.Style} ";
                    displayText += $"(Đe dọa: {analysis.ThreatLevel:F0}%) ";
                    displayText += $"- {analysis.Prediction}\n";
                }

                // Thêm chiến thuật tổng thể
                var mostDangerous = analyses.FirstOrDefault();
                if (mostDangerous != null && mostDangerous.ThreatLevel > 50)
                {
                    displayText += $"\n🎯 Chiến thuật: {mostDangerous.Strategy}";
                }

                _opponentInfoLabel.Text = displayText;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi UpdateOpponentAnalysisDisplay: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Reset form khi bắt đầu game mới
        /// </summary>
        public void ResetGame()
        {
            try
            {
                _myCards.Clear();
                _playedCards.Clear();
                _lastPlay = null;
                _playerCardCounts.Clear();
                _currentPlayer = "";
                _myUsername = "";

                // Reset opponent analyzer
                _opponentAnalyzer.Reset();

                // Clear UI
                var playedCards = _playedCardsPanel.Controls.OfType<PictureBox>().ToList();
                foreach (var card in playedCards)
                {
                    _playedCardsPanel.Controls.Remove(card);
                    card.Dispose();
                }

                var suggestionCards = _suggestionPanel.Controls.OfType<PictureBox>().ToList();
                foreach (var card in suggestionCards)
                {
                    _suggestionPanel.Controls.Remove(card);
                    card.Dispose();
                }

                _remainingCardsLabel.Text = "Số lá Đối Thủ: --";
                _opponentInfoLabel.Text = "Thông tin đối thủ: Chưa có dữ liệu";
                _strategyLabel.Text = "Chiến thuật: Chờ dữ liệu game...";

                _uiManager.AppendLog("🔄 Đã reset TienLenSuggestionForm", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi ResetGame: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Reset game data - alias cho ResetGame để tương thích với WebSocketHandler
        /// </summary>
        public void ResetGameData()
        {
            ResetGame();
        }

        /// <summary>
        /// Cập nhật thông tin đối thủ đánh bài (từ WebSocket)
        /// </summary>
        public void UpdateOpponentPlay(string playerName, CardUtilityTienLen.ComboInfo combo, int cardsLeftBefore, int cardsLeftAfter, bool isFromLog)
        {
            try
            {
                _uiManager.AppendLog($"🎯 UpdateOpponentPlay: {playerName} - {combo.Description} ({cardsLeftBefore}→{cardsLeftAfter})", UIManager.LogLevel.Info);

                // Cập nhật số lá của player
                if (!string.IsNullOrEmpty(playerName))
                {
                    lock (_playerCardCounts)
                    {
                        _playerCardCounts[playerName] = cardsLeftAfter;
                    }
                }

                // Cập nhật bài đã đánh
                if (combo.Cards != null && combo.Cards.Count > 0)
                {
                    _playedCards.AddRange(combo.Cards);
                    _lastPlay = combo;
                    UpdatePlayedCardsDisplay();
                }

                // Cập nhật hiển thị
                UpdateRemainingCardsDisplay();
                UpdateOpponentAnalysisDisplay();

                _uiManager.AppendLog($"✅ Đã cập nhật thông tin {playerName}: {cardsLeftAfter} lá còn lại", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi UpdateOpponentPlay: {ex.Message}", UIManager.LogLevel.Error);
            }
        }
    }
}
