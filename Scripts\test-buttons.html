<!DOCTYPE html>
<html>
<head>
    <title>Test JavaScript Buttons</title>
    <style>
        .button-container {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 99999;
            display: flex;
            gap: 5px;
        }
        
        .profile-button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: bold;
            min-width: 70px;
            height: 28px;
            margin: 3px;
        }
        
        .get-key-btn {
            background-color: #007bff;
            color: white;
        }
        
        .get-key-btn:hover {
            background-color: #0056b3;
        }
        
        .get-key-btn.stop {
            background-color: #dc3545;
            color: white;
        }
        
        .get-key-btn.stop:hover {
            background-color: #c82333;
        }
        
        .leave-room-btn {
            background-color: #dc3545 !important;
            color: white !important;
        }
        
        .leave-room-btn:hover {
            background-color: #c82333 !important;
        }
    </style>
</head>
<body>
    <h1>Test JavaScript Buttons</h1>
    <p>Buttons should appear in top-right corner</p>
    
    <script>
        console.log('🧪 Starting button test...');
        
        // Tạo container
        const container = document.createElement('div');
        container.className = 'button-container';
        
        // Tạo Get Key button
        const getKeyBtn = document.createElement('button');
        getKeyBtn.textContent = 'Get Key';
        getKeyBtn.className = 'profile-button get-key-btn';
        getKeyBtn.onclick = function() {
            alert('🔑 Get Key button clicked!');
            console.log('🔑 Get Key clicked');
        };
        
        // Tạo Leave Room button với force style
        const leaveBtn = document.createElement('button');
        leaveBtn.textContent = 'Thoát bàn';
        leaveBtn.className = 'profile-button leave-room-btn';
        leaveBtn.style.cssText = 'background-color: #dc3545 !important; color: white !important; padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 11px; font-weight: bold; min-width: 70px; height: 28px; margin: 3px;';
        leaveBtn.onclick = function() {
            alert('🚪 Leave Room button clicked! Background should be RED');
            console.log('🚪 Leave Room clicked');
            console.log('Background color:', getComputedStyle(this).backgroundColor);
        };
        
        // Thêm hover effects
        leaveBtn.onmouseover = function() { 
            this.style.backgroundColor = '#c82333'; 
        };
        leaveBtn.onmouseout = function() { 
            this.style.backgroundColor = '#dc3545'; 
        };
        
        // Thêm vào container
        container.appendChild(getKeyBtn);
        container.appendChild(leaveBtn);
        
        // Thêm vào body
        document.body.appendChild(container);
        
        console.log('✅ Test buttons created');
        console.log('Leave button background:', getComputedStyle(leaveBtn).backgroundColor);
        
        // Test sau 1 giây
        setTimeout(() => {
            console.log('🔍 Testing after 1 second...');
            console.log('Container exists:', !!document.querySelector('.button-container'));
            console.log('Get Key button exists:', !!document.querySelector('.get-key-btn'));
            console.log('Leave Room button exists:', !!document.querySelector('.leave-room-btn'));
            
            const testLeaveBtn = document.querySelector('.leave-room-btn');
            if (testLeaveBtn) {
                console.log('Leave button computed background:', getComputedStyle(testLeaveBtn).backgroundColor);
                console.log('Leave button inline style:', testLeaveBtn.style.backgroundColor);
            }
        }, 1000);
    </script>
</body>
</html>
