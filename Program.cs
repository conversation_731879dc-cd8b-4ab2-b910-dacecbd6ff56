﻿using System;
using System.IO;
using System.Windows.Forms;
using Serilog;
using AutoGameBai.Forms;

namespace AutoGameBai
{
    static class Program
    {
        [STAThread]
        static void Main()
        {
            try
            {
                // T<PERSON>o thư mục logs
                string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                Directory.CreateDirectory(logDirectory);

                // Cấu hình Serilog
                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Debug()
                    .WriteTo.File(Path.Combine(logDirectory, $"app_{DateTime.Now:yyyyMMdd}.log"),
                        rollingInterval: RollingInterval.Day,
                        fileSizeLimitBytes: 10 * 1024 * 1024, // 10MB
                        retainedFileCountLimit: 7) // Giữ 7 ngày
                    .CreateLogger();

                Log.Information("Starting AutoGameBai application...");

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // Hiển thị form chọn game trước
                using (var gameSelectionForm = new GameSelectionForm())
                {
                    var result = gameSelectionForm.ShowDialog();

                    if (result == DialogResult.OK)
                    {
                        // Lấy thông tin đã chọn
                        string selectedWebGame = gameSelectionForm.SelectedWebGame;
                        string selectedGame = gameSelectionForm.SelectedGame;

                        Log.Information($"User selected: {selectedWebGame} - {selectedGame}");

                        // Mở form chính với thông tin đã chọn
                        Application.Run(new Form1(selectedWebGame, selectedGame));

                        Log.Information("Application started successfully.");
                    }
                    else
                    {
                        Log.Information("User cancelled game selection, exiting application.");
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Unhandled exception in Main");
                MessageBox.Show($"Lỗi khởi động ứng dụng: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }
    }
}