﻿using System;
using System.Drawing;
using System.Windows.Forms;
using AutoGameBai.UI;

namespace AutoGameBai
{
    public class DataGridViewInitializer
    {
        private readonly UIManager _uiManager;

        public DataGridViewInitializer(UIManager uiManager)
        {
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
        }

        public void InitializeDataGridView(DataGridView gridView)
        {
            try
            {
                _uiManager?.AppendLog($"Bắt đầu khởi tạo cột cho {gridView.Name}", UIManager.LogLevel.Debug);
                gridView.Columns.Clear();
                gridView.RowHeadersVisible = false;
                gridView.AllowUserToAddRows = false;
                gridView.AllowUserToResizeRows = false;
                gridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                gridView.BackgroundColor = SystemColors.Window;
                gridView.BorderStyle = BorderStyle.Fixed3D;
                gridView.EnableHeadersVisualStyles = true;

                gridView.ColumnHeadersHeight = 28;
                gridView.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9.5f, FontStyle.Bold);
                gridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
                gridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.Black;
                gridView.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;

                gridView.DefaultCellStyle.Font = new Font("Segoe UI", 9.0f);
                gridView.DefaultCellStyle.Padding = new Padding(3);
                gridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 120, 215);
                gridView.DefaultCellStyle.SelectionForeColor = Color.White;

                gridView.GridColor = Color.LightGray;
                gridView.RowTemplate.Height = 26;
                gridView.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
                gridView.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(245, 245, 245);

                gridView.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "ID",
                    HeaderText = "ID",
                    DataPropertyName = "Username",
                    Width = 30,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Alignment = DataGridViewContentAlignment.MiddleCenter,
                        Font = new Font("Segoe UI", 9.5f, FontStyle.Bold),
                        BackColor = Color.FromArgb(240, 248, 255)
                    }
                });

                var webButtonColumn = new DataGridViewButtonColumn
                {
                    Name = "WebButton",
                    HeaderText = "Web",
                    Width = 80,
                    FlatStyle = FlatStyle.Flat,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Alignment = DataGridViewContentAlignment.MiddleCenter,
                        BackColor = Color.FromArgb(46, 204, 113),
                        ForeColor = Color.White,
                        SelectionBackColor = Color.FromArgb(39, 174, 96),
                        SelectionForeColor = Color.White,
                        Font = new Font("Segoe UI", 9, FontStyle.Regular)
                    }
                };
                gridView.Columns.Add(webButtonColumn);

                var nickButtonColumn = new DataGridViewButtonColumn
                {
                    Name = "NickChinhButton",
                    HeaderText = "Nick Chính",
                    Width = 80,
                    FlatStyle = FlatStyle.Flat,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Alignment = DataGridViewContentAlignment.MiddleCenter,
                        BackColor = Color.FromArgb(52, 152, 219),
                        ForeColor = Color.White,
                        SelectionBackColor = Color.FromArgb(41, 128, 185),
                        SelectionForeColor = Color.White,
                        Font = new Font("Segoe UI", 9, FontStyle.Regular)
                    }
                };
                gridView.Columns.Add(nickButtonColumn);

                gridView.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Username",
                    HeaderText = "Tên",
                    DataPropertyName = "Username",
                    Width = 140,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Alignment = DataGridViewContentAlignment.MiddleLeft,
                        Padding = new Padding(10, 0, 0, 0),
                        Font = new Font("Segoe UI", 9.5f, FontStyle.Bold),
                        ForeColor = Color.FromArgb(0, 0, 128)
                    }
                });




                var actionButtonColumn = new DataGridViewButtonColumn
                {
                    Name = "ActionButton",
                    HeaderText = "Hành động",
                    Width = 90,
                    FlatStyle = FlatStyle.Flat,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Alignment = DataGridViewContentAlignment.MiddleCenter,
                        BackColor = Color.FromArgb(192, 57, 43),
                        ForeColor = Color.White,
                        SelectionBackColor = Color.FromArgb(165, 42, 42),
                        SelectionForeColor = Color.White,
                        Font = new Font("Segoe UI", 9, FontStyle.Regular)
                    }
                };
                gridView.Columns.Add(actionButtonColumn);

                _uiManager?.AppendLog($"Đã khởi tạo cột cho {gridView.Name}", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager?.AppendLog($"Lỗi khi khởi tạo cột cho {gridView.Name}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }
    }
}