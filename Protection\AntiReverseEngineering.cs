using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;

namespace AutoGameBai.Protection
{
    /// <summary>
    /// Advanced anti-reverse engineering protection
    /// </summary>
    public static class AntiReverseEngineering
    {
        private static readonly System.Threading.Timer _protectionTimer;
        private static readonly string _expectedHash;

        static AntiReverseEngineering()
        {
            _expectedHash = CalculateExpectedHash();
            _protectionTimer = new System.Threading.Timer(PerformPeriodicChecks, null, 5000, 10000); // Check every 10 seconds
        }

        /// <summary>
        /// Initialize protection system
        /// </summary>
        public static void Initialize()
        {
            Task.Run(() =>
            {
                PerformInitialChecks();
                StartContinuousMonitoring();
            });
        }

        /// <summary>
        /// Perform initial security checks
        /// </summary>
        private static void PerformInitialChecks()
        {
            if (IsDebuggerPresent() || IsProfilerAttached() || IsVirtualMachine() || IsAnalysisEnvironment())
            {
                TriggerAntiTamperResponse();
            }

            if (!VerifyAssemblyIntegrity())
            {
                TriggerAntiTamperResponse();
            }
        }

        /// <summary>
        /// Advanced debugger detection
        /// </summary>
        private static bool IsDebuggerPresent()
        {
            // Multiple detection methods
            if (Debugger.IsAttached)
                return true;

            if (IsDebuggerPresentWin32())
                return true;

            if (CheckRemoteDebugger())
                return true;

            if (CheckEnvironmentVariables())
                return true;

            return false;
        }

        [DllImport("kernel32.dll", SetLastError = true, ExactSpelling = true)]
        private static extern bool IsDebuggerPresentWin32API();

        private static bool IsDebuggerPresentWin32()
        {
            try
            {
                return IsDebuggerPresentWin32API();
            }
            catch
            {
                return false;
            }
        }

        private static bool CheckRemoteDebugger()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                return process.ProcessName.ToLower().Contains("debug") ||
                       process.MainWindowTitle.ToLower().Contains("debug");
            }
            catch
            {
                return false;
            }
        }

        private static bool CheckEnvironmentVariables()
        {
            string[] suspiciousVars = {
                "COR_ENABLE_PROFILING",
                "COR_PROFILER",
                "_NT_SYMBOL_PATH",
                "_NT_ALT_SYMBOL_PATH"
            };

            foreach (var varName in suspiciousVars)
            {
                if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable(varName)))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Check if running under profiler
        /// </summary>
        private static bool IsProfilerAttached()
        {
            try
            {
                var profilerEnv = Environment.GetEnvironmentVariable("COR_ENABLE_PROFILING");
                return profilerEnv == "1";
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Virtual machine detection
        /// </summary>
        private static bool IsVirtualMachine()
        {
            try
            {
                var computerName = Environment.MachineName.ToLower();
                var userName = Environment.UserName.ToLower();

                string[] vmIndicators = {
                    "vmware", "vbox", "virtualbox", "qemu", "xen",
                    "sandbox", "malware", "virus", "analysis"
                };

                foreach (var indicator in vmIndicators)
                {
                    if (computerName.Contains(indicator) || userName.Contains(indicator))
                        return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Analysis environment detection
        /// </summary>
        private static bool IsAnalysisEnvironment()
        {
            try
            {
                // Check for analysis tools
                string[] analysisTools = {
                    "ollydbg", "x64dbg", "ida", "ghidra", "radare2",
                    "dnspy", "ilspy", "reflexil", "de4dot"
                };

                var processes = Process.GetProcesses();
                foreach (var process in processes)
                {
                    var processName = process.ProcessName.ToLower();
                    foreach (var tool in analysisTools)
                    {
                        if (processName.Contains(tool))
                            return true;
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Verify assembly integrity
        /// </summary>
        private static bool VerifyAssemblyIntegrity()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var location = assembly.Location;

                if (!File.Exists(location))
                    return false;

                var currentHash = CalculateFileHash(location);
                return currentHash == _expectedHash;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Calculate expected hash (to be updated after obfuscation)
        /// </summary>
        private static string CalculateExpectedHash()
        {
            // This will be updated with actual hash after obfuscation
            return "PLACEHOLDER_HASH";
        }

        /// <summary>
        /// Calculate file hash
        /// </summary>
        private static string CalculateFileHash(string filePath)
        {
            try
            {
                using (var sha256 = SHA256.Create())
                {
                    var fileBytes = File.ReadAllBytes(filePath);
                    var hash = sha256.ComputeHash(fileBytes);
                    return Convert.ToBase64String(hash);
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Periodic security checks
        /// </summary>
        private static void PerformPeriodicChecks(object state)
        {
            if (IsDebuggerPresent() || !VerifyAssemblyIntegrity())
            {
                TriggerAntiTamperResponse();
            }
        }

        /// <summary>
        /// Start continuous monitoring
        /// </summary>
        private static void StartContinuousMonitoring()
        {
            Task.Run(async () =>
            {
                while (true)
                {
                    await Task.Delay(15000); // Check every 15 seconds

                    if (IsAnalysisEnvironment())
                    {
                        TriggerAntiTamperResponse();
                    }
                }
            });
        }

        /// <summary>
        /// Anti-tamper response
        /// </summary>
        private static void TriggerAntiTamperResponse()
        {
            try
            {
                // Graceful exit without obvious error message
                Environment.Exit(0);
            }
            catch
            {
                // Force exit if graceful exit fails
                Process.GetCurrentProcess().Kill();
            }
        }

        /// <summary>
        /// Dispose protection timer
        /// </summary>
        public static void Dispose()
        {
            _protectionTimer?.Dispose();
        }
    }
}
