@echo off
echo ========================================
echo   AutoGameBai Protected Debug Creator
echo ========================================

echo.
echo Step 1: Building debug version...
call debug-mode.bat

echo.
echo Step 2: Applying protection...
call apply-protection.bat

echo.
echo Step 3: Creating distribution folder...
if exist "Distribution-Protected" rmdir /s /q "Distribution-Protected"
mkdir "Distribution-Protected"
mkdir "Distribution-Protected\AutoGameBai"

echo.
echo Step 4: Copying protected debug files...
copy "bin\Debug\net8.0-windows\AutoGameBai.exe" "Distribution-Protected\AutoGameBai\"
copy "bin\Debug\net8.0-windows\AutoGameBai.dll" "Distribution-Protected\AutoGameBai\"
copy "bin\Debug\net8.0-windows\AutoGameBai.runtimeconfig.json" "Distribution-Protected\AutoGameBai\"
copy "bin\Debug\net8.0-windows\AutoGameBai.deps.json" "Distribution-Protected\AutoGameBai\"

echo.
echo Step 5: Copying required dependencies...
xcopy "bin\Debug\net8.0-windows\*.dll" "Distribution-Protected\AutoGameBai\" /Y /Q
xcopy "bin\Debug\net8.0-windows\*.exe" "Distribution-Protected\AutoGameBai\" /Y /Q
xcopy "bin\Debug\net8.0-windows\runtimes" "Distribution-Protected\AutoGameBai\runtimes\" /E /I /Y /Q

echo.
echo Step 6: Copying essential resources...
echo Copying card images...
if exist "bin\Debug\net8.0-windows\card" xcopy "bin\Debug\net8.0-windows\card" "Distribution-Protected\AutoGameBai\card\" /E /I /Y /Q
echo Copying game images...
if exist "bin\Debug\net8.0-windows\img" xcopy "bin\Debug\net8.0-windows\img" "Distribution-Protected\AutoGameBai\img\" /E /I /Y /Q
echo Copying selenium-manager...
if exist "bin\Debug\net8.0-windows\selenium-manager" xcopy "bin\Debug\net8.0-windows\selenium-manager" "Distribution-Protected\AutoGameBai\selenium-manager\" /E /I /Y /Q

echo.
echo Step 7: Copying configuration files...
if exist "bin\Debug\net8.0-windows\*.txt" copy "bin\Debug\net8.0-windows\*.txt" "Distribution-Protected\AutoGameBai\"
if exist "bin\Debug\net8.0-windows\config.json" copy "bin\Debug\net8.0-windows\config.json" "Distribution-Protected\AutoGameBai\"
if exist "*.txt" copy "*.txt" "Distribution-Protected\AutoGameBai\"
if exist "config.json" copy "config.json" "Distribution-Protected\AutoGameBai\"

echo.
echo Step 8: Creating README for users...
echo AutoGameBai - Game Assistant Tool (PROTECTED DEBUG) > "Distribution-Protected\AutoGameBai\README.txt"
echo. >> "Distribution-Protected\AutoGameBai\README.txt"
echo VERSION: Protected Debug Build >> "Distribution-Protected\AutoGameBai\README.txt"
echo BUILD DATE: %date% %time% >> "Distribution-Protected\AutoGameBai\README.txt"
echo PROTECTION: ENABLED >> "Distribution-Protected\AutoGameBai\README.txt"
echo. >> "Distribution-Protected\AutoGameBai\README.txt"
echo REQUIREMENTS: >> "Distribution-Protected\AutoGameBai\README.txt"
echo - Windows 10/11 >> "Distribution-Protected\AutoGameBai\README.txt"
echo - .NET 8.0 Runtime >> "Distribution-Protected\AutoGameBai\README.txt"
echo - Chrome browser >> "Distribution-Protected\AutoGameBai\README.txt"
echo. >> "Distribution-Protected\AutoGameBai\README.txt"
echo USAGE: >> "Distribution-Protected\AutoGameBai\README.txt"
echo 1. Extract all files to a folder >> "Distribution-Protected\AutoGameBai\README.txt"
echo 2. Run AutoGameBai.exe >> "Distribution-Protected\AutoGameBai\README.txt"
echo 3. Select your game and start playing >> "Distribution-Protected\AutoGameBai\README.txt"

echo.
echo Step 9: Creating ZIP package...
powershell -command "Compress-Archive -Path 'Distribution-Protected\AutoGameBai\*' -DestinationPath 'Distribution-Protected\AutoGameBai-PROTECTED-v1.0.zip' -Force"

echo.
echo ✅ Protected Debug distribution package created successfully!
echo.
echo Files created:
echo - Distribution-Protected\AutoGameBai\ (folder with all files)
echo - Distribution-Protected\AutoGameBai-PROTECTED-v1.0.zip (ready to distribute)
echo.
echo Package contents:
dir "Distribution-Protected\AutoGameBai" /B

echo.
echo 📦 Ready to distribute: Distribution-Protected\AutoGameBai-PROTECTED-v1.0.zip
echo 🔒 Protection level: HIGH (cannot be easily reverse engineered)
echo 📁 Package size:
powershell -command "(Get-Item 'Distribution-Protected\AutoGameBai-PROTECTED-v1.0.zip').Length / 1MB | ForEach-Object { '{0:N2} MB' -f $_ }"

echo.
echo ✅ This version has strong protection and can be safely distributed.
echo 🛡️  Anti-reverse engineering measures are active.

echo.
pause
