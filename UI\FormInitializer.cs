﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace AutoGameBai.UI
{
    public class FormInitializer
    {
        private readonly Form _form;
        private readonly UIManager _uiManager;
        private readonly GameClientManager _gameClient;
        private readonly List<string> _selectedUsers;
        private Label _loadingLabel;

        public FormInitializer(Form form, UIManager uiManager = null, GameClientManager gameClient = null, List<string> selectedUsers = null)
        {
            _form = form ?? throw new ArgumentNullException(nameof(form));
            _uiManager = uiManager;
            _gameClient = gameClient;
            _selectedUsers = selectedUsers;
        }

        public void InitializeBasicUI()
        {
            _form.BackColor = SystemColors.Control;
            _form.Font = new Font("Segoe UI", 9f, FontStyle.Regular);
        }

        public Label CreateLoadingLabel()
        {
            _loadingLabel = new Label
            {
                Text = "Đang tải...",
                Location = new Point((_form.Width - 120) / 2, (_form.Height - 25) / 2),
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 9, FontStyle.Regular),
                ForeColor = Color.Black,
                BackColor = Color.LightGray,
                BorderStyle = BorderStyle.None,
                Visible = true
            };

            _form.Controls.Add(_loadingLabel);
            return _loadingLabel;
        }

        public void InitializeDataGridViewColumns(DataGridView gridView)
        {
            try
            {
                _uiManager?.AppendLog($"Bắt đầu khởi tạo cột cho {gridView.Name}", UIManager.LogLevel.Debug);
                gridView.Columns.Clear();
                gridView.RowHeadersVisible = false;
                gridView.AllowUserToAddRows = false;
                gridView.AllowUserToResizeRows = false;
                gridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                gridView.BackgroundColor = SystemColors.Window;
                gridView.BorderStyle = BorderStyle.Fixed3D;
                gridView.EnableHeadersVisualStyles = true;

                gridView.ColumnHeadersHeight = 28;
                gridView.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9.5f, FontStyle.Bold);
                gridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
                gridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.Black;
                gridView.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;

                gridView.DefaultCellStyle.Font = new Font("Segoe UI", 9.0f);
                gridView.DefaultCellStyle.Padding = new Padding(3);
                gridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 120, 215);
                gridView.DefaultCellStyle.SelectionForeColor = Color.White;

                gridView.GridColor = Color.LightGray;
                gridView.RowTemplate.Height = 26;
                gridView.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
                gridView.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(245, 245, 245);

                gridView.CellFormatting += DataGridView_CellFormatting;

                gridView.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "ID",
                    HeaderText = "ID",
                    DataPropertyName = "Username",
                    Width = 60,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Alignment = DataGridViewContentAlignment.MiddleCenter,
                        Font = new Font("Segoe UI", 9.5f, FontStyle.Bold),
                        BackColor = Color.FromArgb(240, 248, 255)
                    }
                });

                var webButtonColumn = new DataGridViewButtonColumn
                {
                    Name = "WebButton",
                    HeaderText = "Web",
                    Width = 100,
                    FlatStyle = FlatStyle.Flat,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Alignment = DataGridViewContentAlignment.MiddleCenter,
                        BackColor = Color.FromArgb(46, 204, 113),
                        ForeColor = Color.White,
                        SelectionBackColor = Color.FromArgb(39, 174, 96),
                        SelectionForeColor = Color.White,
                        Font = new Font("Segoe UI", 9, FontStyle.Regular)
                    }
                };
                gridView.Columns.Add(webButtonColumn);

                var nickButtonColumn = new DataGridViewButtonColumn
                {
                    Name = "NickChinhButton",
                    HeaderText = "Nick Chính",
                    Width = 100,
                    FlatStyle = FlatStyle.Flat,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Alignment = DataGridViewContentAlignment.MiddleCenter,
                        BackColor = Color.FromArgb(52, 152, 219),
                        ForeColor = Color.White,
                        SelectionBackColor = Color.FromArgb(41, 128, 185),
                        SelectionForeColor = Color.White,
                        Font = new Font("Segoe UI", 9, FontStyle.Regular)
                    }
                };
                gridView.Columns.Add(nickButtonColumn);

                gridView.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Username",
                    HeaderText = "Tên",
                    DataPropertyName = "Username",
                    Width = 150,
                    ReadOnly = true,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Alignment = DataGridViewContentAlignment.MiddleLeft,
                        Padding = new Padding(10, 0, 0, 0),
                        Font = new Font("Segoe UI", 9.5f, FontStyle.Bold),
                        ForeColor = Color.FromArgb(0, 0, 128)
                    }
                });




                var actionButtonColumn = new DataGridViewButtonColumn
                {
                    Name = "ActionButton",
                    HeaderText = "Hành động",
                    Width = 100,
                    FlatStyle = FlatStyle.Flat,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Alignment = DataGridViewContentAlignment.MiddleCenter,
                        BackColor = Color.FromArgb(192, 57, 43),
                        ForeColor = Color.White,
                        SelectionBackColor = Color.FromArgb(165, 42, 42),
                        SelectionForeColor = Color.White,
                        Font = new Font("Segoe UI", 9, FontStyle.Regular)
                    }
                };
                gridView.Columns.Add(actionButtonColumn);

                _uiManager?.AppendLog($"Đã khởi tạo cột cho {gridView.Name}", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager?.AppendLog($"Lỗi khi khởi tạo cột cho {gridView.Name}: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        private void DataGridView_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
        {
            if (sender is DataGridView gridView && e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                try
                {
                    string? username = gridView.Rows[e.RowIndex].Tag?.ToString();
                    bool isProfileOpen = false;
                    bool isInRoom = false;
                    bool isMainUser = false;

                    if (!string.IsNullOrEmpty(username) && _gameClient != null)
                    {
                        isProfileOpen = _gameClient.GetProfileManager().IsProfileOpen(username);
                        isInRoom = _gameClient.GetUserRooms().ContainsKey(username);
                        if (_gameClient.GetUsers().TryGetValue(username, out var user))
                        {
                            isMainUser = user.IsMainUser;
                        }
                    }

                    if (e.ColumnIndex < gridView.Columns.Count && gridView.Columns[e.ColumnIndex] is DataGridViewButtonColumn)
                    {
                        var buttonColumn = (DataGridViewButtonColumn)gridView.Columns[e.ColumnIndex];
                        var cell = gridView.Rows[e.RowIndex].Cells[e.ColumnIndex];

                        if (buttonColumn.Name == "WebButton")
                        {
                            e.Value = isProfileOpen ? "Đóng Web" : "Mở Web";
                            cell.Style.BackColor = isProfileOpen ? Color.FromArgb(231, 76, 60) : Color.FromArgb(46, 204, 113);
                            cell.Style.ForeColor = Color.White;
                        }
                        else if (buttonColumn.Name == "NickChinhButton")
                        {
                            e.Value = isMainUser ? "Bỏ Chính" : "Đặt Chính";
                            cell.Style.BackColor = isMainUser ? Color.FromArgb(52, 152, 219) : Color.FromArgb(236, 240, 241);
                            cell.Style.ForeColor = isMainUser ? Color.White : Color.Black;
                        }

                        else if (buttonColumn.Name == "ActionButton")
                        {
                            e.Value = "Xóa";
                            cell.Style.BackColor = Color.FromArgb(192, 57, 43);
                            cell.Style.ForeColor = Color.White;
                        }
                    }

                    if (gridView.Columns[e.ColumnIndex].Name == "ID")
                    {
                        var cell = gridView.Rows[e.RowIndex].Cells[e.ColumnIndex];
                        if (e.Value == null || string.IsNullOrEmpty(e.Value.ToString()))
                        {
                            e.Value = (e.RowIndex + 1).ToString();
                            _uiManager?.AppendLog($"Cập nhật ID cho hàng {e.RowIndex} thành {e.Value}", UIManager.LogLevel.Debug);
                        }

                        cell.Style.BackColor = _selectedUsers != null && _selectedUsers.Contains(username)
                            ? Color.FromArgb(0, 120, 215)
                            : Color.FromArgb(245, 245, 245);
                        cell.Style.ForeColor = _selectedUsers != null && _selectedUsers.Contains(username)
                            ? Color.White
                            : Color.Black;
                        cell.Style.Font = new Font("Segoe UI", 9.5f, FontStyle.Bold);
                        cell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    }

                    if (gridView.Columns[e.ColumnIndex].Name == "Username")
                    {
                        var cell = gridView.Rows[e.RowIndex].Cells[e.ColumnIndex];
                        if (e.Value == null || string.IsNullOrEmpty(e.Value.ToString()))
                        {
                            if (!string.IsNullOrEmpty(username))
                            {
                                e.Value = username;
                                _uiManager?.AppendLog($"Cập nhật Username cho hàng {e.RowIndex} thành {e.Value}", UIManager.LogLevel.Debug);
                            }
                        }

                        cell.Style.BackColor = Color.FromArgb(245, 245, 245);
                        cell.Style.ForeColor = Color.FromArgb(0, 0, 128);
                        cell.Style.Font = new Font("Segoe UI", 9.5f, FontStyle.Bold);
                        cell.Style.Padding = new Padding(10, 0, 0, 0);
                    }
                }
                catch (Exception ex)
                {
                    _uiManager?.AppendLog($"Lỗi trong DataGridView_CellFormatting: {ex.Message}", UIManager.LogLevel.Error);
                }
            }
        }

        public void StyleButton(Button button, Color color)
        {
            button.FlatStyle = FlatStyle.System;
            button.UseVisualStyleBackColor = true;
            button.Font = new Font("Segoe UI", 9, FontStyle.Regular);
            button.Cursor = Cursors.Default;
        }

        public void HideLoadingLabel()
        {
            if (_loadingLabel != null)
            {
                _loadingLabel.Visible = false;
            }
        }
    }
}