﻿using OpenQA.Selenium;
using OpenCvSharp;
using OpenCvSharp.Extensions; // Thêm namespace
using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace AutoGameBai
{
    public class ImageChecker
    {
        private readonly UIManager _uiManager;

        public ImageChecker(UIManager uiManager)
        {
            _uiManager = uiManager;
        }

        public bool IsImagePresent(IWebDriver driver, string imagePath, double similarityThreshold = 0.9)
        {
            try
            {
                // Kiểm tra driver có còn hoạt động không
                try
                {
                    var title = driver.Title; // Test connection
                }
                catch (WebDriverException ex)
                {
                    _uiManager.AppendLog($"WebDriver đã bị đóng hoặc mất kết nối: {ex.Message}", UIManager.LogLevel.Error);
                    return false;
                }

                // Đường dẫn ảnh nằm trong img/ ngang hàng với AutoGameBai.exe
                string fullImagePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, imagePath);
                if (!File.Exists(fullImagePath))
                {
                    _uiManager.AppendLog($"Không tìm thấy file ảnh: {fullImagePath}", UIManager.LogLevel.Error);
                    return false;
                }

                // Chụp màn hình
                var screenshot = ((ITakesScreenshot)driver).GetScreenshot();
                using var ms = new MemoryStream(screenshot.AsByteArray);
                using var screenImage = Image.FromStream(ms);
                using var screenBitmap = new Bitmap(screenImage);

                // Tải ảnh mẫu
                using var templateImage = Image.FromFile(fullImagePath);
                using var templateBitmap = new Bitmap(templateImage);

                // Chuyển đổi ảnh sang Mat
                using var screenMat = BitmapConverter.ToMat(screenBitmap); // Sử dụng Extensions
                using var templateMat = BitmapConverter.ToMat(templateBitmap);

                // Chuyển sang ảnh xám để so sánh
                using var screenGray = screenMat.CvtColor(ColorConversionCodes.BGR2GRAY);
                using var templateGray = templateMat.CvtColor(ColorConversionCodes.BGR2GRAY);

                // So sánh ảnh bằng Template Matching
                using var result = new Mat();
                Cv2.MatchTemplate(screenGray, templateGray, result, TemplateMatchModes.CCoeffNormed);

                // Tìm giá trị tương đồng tối đa
                Cv2.MinMaxLoc(result, out _, out double maxVal, out _, out OpenCvSharp.Point maxLoc);

                // Sử dụng System.Drawing.Point để log
                var position = new System.Drawing.Point(maxLoc.X, maxLoc.Y);

                _uiManager.AppendLog($"Kích thước ảnh chụp: {screenImage.Width}x{screenImage.Height}, Kích thước {imagePath}: {templateImage.Width}x{templateImage.Height}", UIManager.LogLevel.Info);
                _uiManager.AppendLog($"Tìm thấy {imagePath} trong ảnh chụp: {maxVal >= similarityThreshold}, Giá trị tương đồng: {maxVal}, Vị trí: ({position.X}, {position.Y})", UIManager.LogLevel.Info);

                return maxVal >= similarityThreshold;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi kiểm tra ảnh {imagePath}: {ex.Message}", UIManager.LogLevel.Error);
                return false;
            }
        }
    }
}