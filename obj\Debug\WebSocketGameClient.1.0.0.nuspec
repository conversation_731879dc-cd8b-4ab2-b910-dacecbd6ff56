﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>WebSocketGameClient</id>
    <version>1.0.0</version>
    <authors>WebSocketGameClient</authors>
    <description>Package Description</description>
    <repository type="git" />
    <dependencies>
      <group targetFramework="net8.0-windows7.0">
        <dependency id="Microsoft.Extensions.Configuration.Binder" version="9.0.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Configuration.Json" version="9.0.3" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection" version="9.0.3" exclude="Build,Analyzers" />
        <dependency id="Newtonsoft.Json" version="13.0.3" exclude="Build,Analyzers" />
        <dependency id="Selenium.WebDriver" version="4.30.0" exclude="Build,Analyzers" />
        <dependency id="Selenium.WebDriver.ChromeDriver" version="134.0.6998.16500" exclude="Build,Analyzers" />
        <dependency id="Serilog" version="4.2.0" exclude="Build,Analyzers" />
        <dependency id="Serilog.Sinks.File" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Data.SQLite" version="1.0.119" exclude="Build,Analyzers" />
        <dependency id="System.Net.Http.Json" version="9.0.3" exclude="Build,Analyzers" />
        <dependency id="System.Net.WebSockets" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="WebSocketSharp-netstandard" version="1.0.1" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkReferences>
      <group targetFramework="net8.0-windows7.0">
        <frameworkReference name="Microsoft.WindowsDesktop.App.WindowsForms" />
      </group>
    </frameworkReferences>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\WebSocketGameClient.runtimeconfig.json" target="lib\net8.0-windows7.0\WebSocketGameClient.runtimeconfig.json" />
    <file src="C:\Users\<USER>\source\repos\WebSocketGameClient\WebSocketGameClient\bin\Debug\net8.0-windows\WebSocketGameClient.dll" target="lib\net8.0-windows7.0\WebSocketGameClient.dll" />
  </files>
</package>