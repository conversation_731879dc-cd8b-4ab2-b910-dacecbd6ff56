﻿using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AutoGameBai.UI;

namespace AutoGameBai
{
    public class DataGridViewFormatter
    {
        private readonly UIManager _uiManager;
        private readonly GameClientManager _gameClient;
        private readonly List<string> _selectedUsers;

        public DataGridViewFormatter(UIManager uiManager, GameClientManager gameClient, List<string> selectedUsers)
        {
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _selectedUsers = selectedUsers ?? throw new ArgumentNullException(nameof(selectedUsers));
        }

        public void FormatCell(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (sender is DataGridView gridView && e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                try
                {
                    string? username = gridView.Rows[e.RowIndex].Tag?.ToString();
                    bool isProfileOpen = false;
                    bool isInRoom = false;
                    bool isMainUser = false;

                    if (!string.IsNullOrEmpty(username))
                    {
                        isProfileOpen = _gameClient.GetProfileManager().IsProfileOpen(username);
                        isInRoom = _gameClient.GetUserRooms().ContainsKey(username);
                        if (_gameClient.GetUsers().TryGetValue(username, out var user))
                        {
                            isMainUser = user.IsMainUser;
                        }
                    }

                    if (e.ColumnIndex < gridView.Columns.Count && gridView.Columns[e.ColumnIndex] is DataGridViewButtonColumn)
                    {
                        var buttonColumn = (DataGridViewButtonColumn)gridView.Columns[e.ColumnIndex];
                        var cell = gridView.Rows[e.RowIndex].Cells[e.ColumnIndex];

                        if (buttonColumn.Name == "WebButton")
                        {
                            e.Value = isProfileOpen ? "Đóng Web" : "Mở Web";
                            cell.Style.BackColor = isProfileOpen ? Color.FromArgb(231, 76, 60) : Color.FromArgb(46, 204, 113);
                            cell.Style.ForeColor = Color.White;
                        }
                        else if (buttonColumn.Name == "NickChinhButton")
                        {
                            e.Value = isMainUser ? "Bỏ Chính" : "Đặt Chính";
                            cell.Style.BackColor = isMainUser ? Color.FromArgb(52, 152, 219) : Color.FromArgb(236, 240, 241);
                            cell.Style.ForeColor = isMainUser ? Color.White : Color.Black;
                        }
                        else if (buttonColumn.Name == "TableButton")
                        {
                            e.Value = isInRoom ? "Dừng" : "Vào Phòng";
                            cell.Style.BackColor = !isProfileOpen ? Color.FromArgb(149, 165, 166) : Color.FromArgb(230, 126, 34);
                            cell.Style.ForeColor = Color.White;
                        }
                        else if (buttonColumn.Name == "ActionButton")
                        {
                            e.Value = "Xóa";
                            cell.Style.BackColor = Color.FromArgb(192, 57, 43);
                            cell.Style.ForeColor = Color.White;
                        }
                    }

                    if (gridView.Columns[e.ColumnIndex].Name == "ID")
                    {
                        var cell = gridView.Rows[e.RowIndex].Cells[e.ColumnIndex];
                        if (e.Value == null || string.IsNullOrEmpty(e.Value.ToString()))
                        {
                            e.Value = (e.RowIndex + 1).ToString();
                            _uiManager?.AppendLog($"Cập nhật ID cho hàng {e.RowIndex} thành {e.Value}", UIManager.LogLevel.Debug);
                        }

                        cell.Style.BackColor = _selectedUsers.Contains(username) ? Color.FromArgb(0, 120, 215) : Color.FromArgb(245, 245, 245);
                        cell.Style.ForeColor = _selectedUsers.Contains(username) ? Color.White : Color.Black;
                        cell.Style.Font = new Font("Segoe UI", 9.5f, FontStyle.Bold);
                        cell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    }

                    if (gridView.Columns[e.ColumnIndex].Name == "Username")
                    {
                        var cell = gridView.Rows[e.RowIndex].Cells[e.ColumnIndex];
                        if (e.Value == null || string.IsNullOrEmpty(e.Value.ToString()))
                        {
                            if (!string.IsNullOrEmpty(username))
                            {
                                e.Value = username;
                                _uiManager?.AppendLog($"Cập nhật Username cho hàng {e.RowIndex} thành {e.Value}", UIManager.LogLevel.Debug);
                            }
                        }

                        cell.Style.BackColor = Color.FromArgb(245, 245, 245);
                        cell.Style.ForeColor = Color.FromArgb(0, 0, 128);
                        cell.Style.Font = new Font("Segoe UI", 9.5f, FontStyle.Bold);
                        cell.Style.Padding = new Padding(10, 0, 0, 0);
                    }
                }
                catch (Exception ex)
                {
                    _uiManager?.AppendLog($"Lỗi trong DataGridView_CellFormatting: {ex.Message}", UIManager.LogLevel.Error);
                }
            }
        }
    }
}