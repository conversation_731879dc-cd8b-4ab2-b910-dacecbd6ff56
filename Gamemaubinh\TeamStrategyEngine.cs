using System;
using System.Collections.Generic;
using System.Linq;

namespace AutoGameBai.Gamemaubinh
{
    /// <summary>
    /// Engine tính toán chiến thuật team (3 users vs Gà)
    /// </summary>
    public class TeamStrategyEngine
    {
        private readonly UIManager _uiManager;
        private readonly MauBinhEngine _engine;

        public TeamStrategyEngine(UIManager uiManager)
        {
            _uiManager = uiManager;
            _engine = new MauBinhEngine(uiManager);
        }

        #region Data Structures

        /// <summary>
        /// Thông tin user
        /// </summary>
        public class UserInfo
        {
            public string Username { get; set; } = "";
            public int[] Cards { get; set; } = Array.Empty<int>();
            public List<MauBinhEngine.MauBinhSuggestion> Suggestions { get; set; } = new();
            public int Index { get; set; }
        }

        /// <summary>
        /// Thông tin Gà
        /// </summary>
        public class ChickenInfo
        {
            public int[] Cards { get; set; } = Array.Empty<int>();
            public List<MauBinhEngine.MauBinhSuggestion> Suggestions { get; set; } = new();
            public int TotalLossPoints { get; set; }
        }

        /// <summary>
        /// Kết quả so sánh
        /// </summary>
        public class ComparisonResult
        {
            public string UserName { get; set; } = "";
            public int Chi1Result { get; set; } // 1: thắng, 0: hòa, -1: thua
            public int Chi2Result { get; set; }
            public int Chi3Result { get; set; }
            public int TotalWins { get; set; }
            public bool IsSapLang { get; set; } // Thắng cả 3 chi
        }

        /// <summary>
        /// Kết quả team
        /// </summary>
        public class TeamResult
        {
            public List<ComparisonResult> UserResults { get; set; } = new();
            public int ChickenTotalLoss { get; set; }
            public bool IsChickenDefeated { get; set; } // Gà thua ít nhất 2/3 users mỗi chi
            public List<string> SapLangUsers { get; set; } = new();
        }

        #endregion

        #region Main Methods

        /// <summary>
        /// Tính toán chiến thuật team
        /// </summary>
        public TeamResult CalculateTeamStrategy(List<UserInfo> users)
        {
            try
            {
                _uiManager.AppendLog("🎯 Bắt đầu tính toán chiến thuật team", UIManager.LogLevel.Info);

                // 1. Tính bài Gà
                var chicken = CalculateChickenCards(users);
                
                // 2. Tạo gợi ý cho tất cả
                GenerateAllSuggestions(users, chicken);

                // 3. Tìm combination tốt nhất
                var bestResult = FindBestTeamCombination(users, chicken);

                _uiManager.AppendLog($"✅ Hoàn thành tính toán team strategy", UIManager.LogLevel.Info);
                return bestResult;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi tính toán team: {ex.Message}", UIManager.LogLevel.Error);
                return new TeamResult();
            }
        }

        /// <summary>
        /// Tính bài Gà từ 52 lá - 39 lá của 3 users
        /// </summary>
        private ChickenInfo CalculateChickenCards(List<UserInfo> users)
        {
            var allCards = Enumerable.Range(1, 52).ToList();
            var usedCards = users.SelectMany(u => u.Cards).ToList();
            var chickenCards = allCards.Except(usedCards).ToArray();

            _uiManager.AppendLog($"🐔 Tính bài Gà: {chickenCards.Length} lá", UIManager.LogLevel.Info);

            return new ChickenInfo
            {
                Cards = chickenCards
            };
        }

        /// <summary>
        /// Tạo gợi ý cho tất cả users và Gà
        /// </summary>
        private void GenerateAllSuggestions(List<UserInfo> users, ChickenInfo chicken)
        {
            // Tạo gợi ý cho users
            foreach (var user in users)
            {
                user.Suggestions = _engine.GenerateSuggestions(user.Cards);
                _uiManager.AppendLog($"👤 {user.Username}: {user.Suggestions.Count} gợi ý", UIManager.LogLevel.Info);
            }

            // Tạo gợi ý cho Gà
            chicken.Suggestions = _engine.GenerateSuggestions(chicken.Cards);
            _uiManager.AppendLog($"🐔 Gà: {chicken.Suggestions.Count} gợi ý", UIManager.LogLevel.Info);
        }

        /// <summary>
        /// Tìm combination tốt nhất để đánh bại Gà
        /// </summary>
        private TeamResult FindBestTeamCombination(List<UserInfo> users, ChickenInfo chicken)
        {
            var bestResult = new TeamResult();
            var maxChickenLoss = 0;

            // Thử tất cả combinations của gợi ý
            foreach (var chickenSuggestion in chicken.Suggestions)
            {
                foreach (var user1Suggestion in users[0].Suggestions)
                {
                    foreach (var user2Suggestion in users[1].Suggestions)
                    {
                        foreach (var user3Suggestion in users[2].Suggestions)
                        {
                            var result = EvaluateCombination(
                                users,
                                new[] { user1Suggestion, user2Suggestion, user3Suggestion },
                                chickenSuggestion
                            );

                            if (result.ChickenTotalLoss > maxChickenLoss)
                            {
                                maxChickenLoss = result.ChickenTotalLoss;
                                bestResult = result;
                            }
                        }
                    }
                }
            }

            return bestResult;
        }

        /// <summary>
        /// Đánh giá một combination cụ thể
        /// </summary>
        private TeamResult EvaluateCombination(
            List<UserInfo> users,
            MauBinhEngine.MauBinhSuggestion[] userSuggestions,
            MauBinhEngine.MauBinhSuggestion chickenSuggestion)
        {
            var result = new TeamResult();
            var chickenLossPoints = 0;

            for (int i = 0; i < users.Count; i++)
            {
                var userResult = CompareUserVsChicken(users[i], userSuggestions[i], chickenSuggestion);
                result.UserResults.Add(userResult);

                // Tính điểm thua của Gà
                if (userResult.Chi1Result == 1) chickenLossPoints += 1;
                if (userResult.Chi2Result == 1) chickenLossPoints += 1;
                if (userResult.Chi3Result == 1) chickenLossPoints += 1;

                // Kiểm tra sập làng
                if (userResult.IsSapLang)
                {
                    result.SapLangUsers.Add(userResult.UserName);
                }
            }

            result.ChickenTotalLoss = chickenLossPoints;

            // Kiểm tra Gà có bị đánh bại không (thua ít nhất 2/3 users mỗi chi)
            result.IsChickenDefeated = IsChickenDefeated(result.UserResults);

            return result;
        }

        /// <summary>
        /// So sánh 1 user vs Gà
        /// </summary>
        private ComparisonResult CompareUserVsChicken(
            UserInfo user,
            MauBinhEngine.MauBinhSuggestion userSuggestion,
            MauBinhEngine.MauBinhSuggestion chickenSuggestion)
        {
            var result = new ComparisonResult
            {
                UserName = user.Username
            };

            // So sánh từng chi
            result.Chi1Result = CompareHands(userSuggestion.Chi1, chickenSuggestion.Chi1);
            result.Chi2Result = CompareHands(userSuggestion.Chi2, chickenSuggestion.Chi2);
            result.Chi3Result = CompareHands(userSuggestion.Chi3, chickenSuggestion.Chi3);

            // Tính tổng thắng
            result.TotalWins = (result.Chi1Result == 1 ? 1 : 0) +
                              (result.Chi2Result == 1 ? 1 : 0) +
                              (result.Chi3Result == 1 ? 1 : 0);

            // Kiểm tra sập làng (thắng cả 3 chi)
            result.IsSapLang = result.TotalWins == 3;

            return result;
        }

        /// <summary>
        /// So sánh 2 hand
        /// </summary>
        private int CompareHands(int[] hand1, int[] hand2)
        {
            var strength1 = CardUtilityMaubinh.GetChiStrength(hand1.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());
            var strength2 = CardUtilityMaubinh.GetChiStrength(hand2.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());

            if (strength1 > strength2) return 1;  // hand1 thắng
            if (strength1 < strength2) return -1; // hand1 thua
            return 0; // hòa
        }

        /// <summary>
        /// Kiểm tra Gà có bị đánh bại không
        /// </summary>
        private bool IsChickenDefeated(List<ComparisonResult> userResults)
        {
            // Đếm số user thắng mỗi chi
            var chi1Wins = userResults.Count(r => r.Chi1Result == 1);
            var chi2Wins = userResults.Count(r => r.Chi2Result == 1);
            var chi3Wins = userResults.Count(r => r.Chi3Result == 1);

            // Gà thua nếu bị ít nhất 2/3 users thắng ở mỗi chi
            return chi1Wins >= 2 && chi2Wins >= 2 && chi3Wins >= 2;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Format kết quả để hiển thị
        /// </summary>
        public string FormatTeamResult(TeamResult result)
        {
            var lines = new List<string>();
            
            lines.Add("🎯 KẾT QUẢ TEAM STRATEGY:");
            lines.Add($"🐔 Gà thua tổng: {result.ChickenTotalLoss} chi");
            lines.Add($"✅ Gà bị đánh bại: {(result.IsChickenDefeated ? "CÓ" : "KHÔNG")}");

            if (result.SapLangUsers.Any())
            {
                lines.Add($"💥 SẬP LÀNG: {string.Join(", ", result.SapLangUsers)}");
            }

            lines.Add("");
            lines.Add("📊 CHI TIẾT:");
            foreach (var userResult in result.UserResults)
            {
                var chi1 = userResult.Chi1Result == 1 ? "✅" : userResult.Chi1Result == 0 ? "⚖️" : "❌";
                var chi2 = userResult.Chi2Result == 1 ? "✅" : userResult.Chi2Result == 0 ? "⚖️" : "❌";
                var chi3 = userResult.Chi3Result == 1 ? "✅" : userResult.Chi3Result == 0 ? "⚖️" : "❌";
                
                lines.Add($"👤 {userResult.UserName}: {chi1} {chi2} {chi3} ({userResult.TotalWins}/3)");
            }

            return string.Join("\n", lines);
        }

        #endregion
    }
}
