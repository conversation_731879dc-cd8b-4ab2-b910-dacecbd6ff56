@echo off
echo Building AutoGameBai in Release mode with optimizations...

REM Clean previous builds
dotnet clean --configuration Release

REM Build in Release mode with optimizations
dotnet build --configuration Release --no-restore ^
    -p:DebugType=None ^
    -p:DebugSymbols=false ^
    -p:Optimize=true ^
    -p:TrimUnusedDependencies=true ^
    -p:PublishTrimmed=true

REM Publish single file
dotnet publish --configuration Release --no-build ^
    --self-contained true ^
    --runtime win-x64 ^
    -p:PublishSingleFile=true ^
    -p:IncludeNativeLibrariesForSelfExtract=true ^
    -p:PublishTrimmed=true ^
    -p:TrimMode=link

echo Build completed!
echo Output: bin\Release\net8.0-windows\win-x64\publish\AutoGameBai.exe

REM Optional: Run obfuscation tool
REM echo Running obfuscation...
REM ConfuserEx.exe -n project.crproj

pause
