
using AutoGameBai.Gamemaubinh;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Forms;
using OpenQA.Selenium;

namespace AutoGameBai.Gamemaubinh
{
    // COMMENTED OUT - Legacy form needs refactoring
    public partial class MauBinhSuggestionForm : Form
    {
        private readonly GameClientManager _gameClient;
        private readonly UIManager _uiManager;
        private readonly MauBinhSuggestionHandler _suggestionHandler;
        private readonly CardDragHandlerMaubinh _cardDragHandler;
        private readonly MauBinhCardManager _cardManager;
        // Removed UltimateMauBinhEngine - using OptimizedMauBinhEngine only
        private readonly TeamStrategyManager _teamStrategyManager;
        private readonly CardDisplayManager _displayManager;
        private readonly Dictionary<string, int[]> _userCards;
        private readonly Dictionary<string, List<(int[] Chi1, int[] Chi2, int[] Chi3)>> _userSuggestions;
        private readonly Dictionary<string, (int[] Chi1, int[] Chi2, int[] Chi3)?> _selectedSuggestions;
        private int[] _opponentCards;
        private bool _isScanned;
        private bool _isGameActive;

        public MauBinhSuggestionForm(GameClientManager gameClient, UIManager uiManager, MauBinhSuggestionHandler suggestionHandler)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _suggestionHandler = suggestionHandler ?? throw new ArgumentNullException(nameof(suggestionHandler));
            _cardDragHandler = new CardDragHandlerMaubinh(gameClient);
            _cardManager = new MauBinhCardManager(uiManager, gameClient);
            // Removed UltimateMauBinhEngine initialization
            // Sử dụng trực tiếp _cardManager thay vì SuggestionGenerator
            _teamStrategyManager = new TeamStrategyManager(uiManager, _cardManager);
            _displayManager = new CardDisplayManager(uiManager, this);
            _userCards = new Dictionary<string, int[]>();
            _userSuggestions = new Dictionary<string, List<(int[] Chi1, int[] Chi2, int[] Chi3)>>();
            _selectedSuggestions = new Dictionary<string, (int[] Chi1, int[] Chi2, int[] Chi3)?>();
            _opponentCards = Array.Empty<int>();
            _isScanned = false;
            _isGameActive = false;

            InitializeComponent();
            _displayManager.InitializeUI();
            // _teamStrategyManager.StrategyChanged += _displayManager.OnStrategyChanged;

            _uiManager.AppendLog("Đã khởi tạo MauBinhSuggestionForm", UIManager.LogLevel.Debug);
        }

        private void InitializeComponent()
        {
            SuspendLayout();
            //
            // MauBinhSuggestionForm
            //
            ClientSize = new Size(500, 1250); // Tăng height (1170->1250) do tăng chiều cao index và spacing thêm
            FormBorderStyle = FormBorderStyle.FixedSingle;
            MaximizeBox = false;
            Name = "MauBinhSuggestionForm";
            Text = "Mậu Binh Gợi Ý";
            ResumeLayout(false);
        }

        public async Task ScanButton_Click(object sender, EventArgs e)
        {
            try
            {
                _userCards.Clear();
                _userSuggestions.Clear();
                _selectedSuggestions.Clear();
                _opponentCards = Array.Empty<int>();
                _isScanned = false;
                _isGameActive = true;

                var users = _gameClient.GetUsers();
                _uiManager.AppendLog($"Bắt đầu quét bài, số user: {users.Count}", UIManager.LogLevel.Debug);

                List<string> usernames = new List<string>();
                int cardCount = 0;

                // Sử dụng script JavaScript để quét bài
                foreach (var user in users.Keys)
                {
                    try
                    {
                        if (!_gameClient.GetDrivers().ContainsKey(user))
                        {
                            _uiManager.AppendLog($"Không tìm thấy driver cho {user}", UIManager.LogLevel.Error);
                            continue;
                        }

                        var driver = _gameClient.GetDrivers()[user];
                        string scanCardScript = @"
                        function scanMauBinhCards() {
                            try {
                                if (typeof cc === 'undefined' || !cc.director || !cc.director._scene) {
                                    return { success: false, message: 'Không tìm thấy scene game' };
                                }

                                console.log('Bắt đầu quét scene để tìm bài...');

                                const scene = cc.director.getScene();
                                const allNodes = [];
                                const findAllNodes = (node) => {
                                    allNodes.push(node);
                                    const children = node.children || [];
                                    for (let i = 0; i < children.length; i++) {
                                        findAllNodes(children[i]);
                                    }
                                };

                                findAllNodes(scene);
                                console.log('Tìm thấy ' + allNodes.length + ' nodes trong scene');

                                // Tìm node MauBinhController
                                const mauBinhNode = allNodes.find(node => node.name === 'MauBinhController');
                                if (mauBinhNode) {
                                    console.log('Tìm thấy node MauBinhController');
                                    const components = mauBinhNode._components || [];
                                    for (const comp of components) {
                                        if (comp && comp._thisPlayerView && comp._thisPlayerView.cards) {
                                            const cards = comp._thisPlayerView.cards;
                                            const cardCodes = cards.map(card => card.serverCode).filter(code => typeof code === 'number');
                                            if (cardCodes.length === cards.length && cardCodes.length === 13) {
                                                console.log('Mã bài đã tìm thấy (MauBinhController): ' + JSON.stringify(cardCodes));
                                                return { success: true, cards: cardCodes, method: 'MauBinhController' };
                                            }
                                        }
                                    }
                                }

                                // Nếu không tìm thấy, quét tất cả các node
                                for (const node of allNodes) {
                                    const components = node._components || [];
                                    for (const comp of components) {
                                        if (comp && comp._thisPlayerView && comp._thisPlayerView.cards) {
                                            console.log('Tìm thấy _thisPlayerView.cards trong component của node ' + node.name);
                                            const cards = comp._thisPlayerView.cards;
                                            const cardCodes = [];
                                            for (let i = 0; i < cards.length; i++) {
                                                const card = cards[i];
                                                if (card) {
                                                    if (typeof card.serverCode === 'number') cardCodes.push(card.serverCode);
                                                    else if (typeof card.code === 'number') cardCodes.push(card.code);
                                                    else if (typeof card.id === 'number') cardCodes.push(card.id);
                                                    else if (typeof card.value === 'number') cardCodes.push(card.value);
                                                }
                                            }

                                            if (cardCodes.length === cards.length && cardCodes.length === 13) {
                                                console.log('Mã bài đã tìm thấy (Node ' + node.name + '): ' + JSON.stringify(cardCodes));
                                                return { success: true, cards: cardCodes, method: node.name };
                                            }
                                        }
                                    }
                                }

                                // Tìm kiếm trong các biến toàn cục
                                if (typeof window.mauBinhGame !== 'undefined' && window.mauBinhGame && window.mauBinhGame.cards) {
                                    const cards = window.mauBinhGame.cards;
                                    if (Array.isArray(cards) && cards.length === 13) {
                                        console.log('Mã bài đã tìm thấy (window.mauBinhGame): ' + JSON.stringify(cards));
                                        return { success: true, cards: cards, method: 'window.mauBinhGame' };
                                    }
                                }

                                return { success: false, message: 'Không tìm thấy bài trong scene' };
                            } catch (error) {
                                console.error('Lỗi khi quét bài:', error);
                                return { success: false, message: 'Lỗi: ' + error.message };
                            }
                        }

                        return scanMauBinhCards();";

                        var result = ((IJavaScriptExecutor)driver).ExecuteScript(scanCardScript);

                        if (result != null && result is Dictionary<string, object> resultDict)
                        {
                            bool success = Convert.ToBoolean(resultDict["success"]);

                            if (success && resultDict.ContainsKey("cards"))
                            {
                                var cardsObj = resultDict["cards"];
                                var method = resultDict.ContainsKey("method") ? resultDict["method"].ToString() : "unknown";

                                if (cardsObj is IEnumerable<object> cardObjects)
                                {
                                    var cards = cardObjects.Select(c => Convert.ToInt32(c)).ToArray();

                                    if (cards.Length == 13)
                                    {
                                        _userCards[user] = cards;
                                        usernames.Add(user);
                                        cardCount += 13;

                                        // Cập nhật dictionary trong WebSocketHandler để đảm bảo tính nhất quán
                                        _gameClient.GetWebSocketManager().GetMauBinhHandler().UpdateUserCards(user, cards);

                                        _uiManager.AppendLog($"Đã quét bài cho {user} bằng script ({method}): {CardUtilityMaubinh.ConvertCardsToString(cards)}", UIManager.LogLevel.Info, user);
                                    }
                                    else
                                    {
                                        _uiManager.AppendLog($"Số lượng bài không hợp lệ cho {user}: {cards.Length} lá (yêu cầu 13)", UIManager.LogLevel.Warning, user);
                                    }
                                }
                            }
                            else
                            {
                                string message = resultDict.ContainsKey("message") ? resultDict["message"].ToString() : "Không tìm thấy bài";
                                _uiManager.AppendLog($"Không tìm thấy bài cho {user}: {message}", UIManager.LogLevel.Warning, user);
                            }
                        }
                        else
                        {
                            _uiManager.AppendLog($"Kết quả không hợp lệ khi quét bài cho {user}", UIManager.LogLevel.Warning, user);
                        }
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Lỗi khi quét bài cho {user}: {ex.Message}", UIManager.LogLevel.Error, user);
                    }
                }

                var usernameDict = usernames.Select((name, index) => new { name, index })
                                           .ToDictionary(x => x.name, x => $"user{x.index}");
                _displayManager.UpdateUsernameLabels(usernameDict);

                if (_userCards.Count == 0)
                {
                    _uiManager.AppendLog("Không tìm thấy bài hợp lệ cho bất kỳ user nào", UIManager.LogLevel.Error);
                    MessageBox.Show("Không tìm thấy bài hợp lệ cho bất kỳ user nào", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    _isGameActive = false;
                    return;
                }

                if (_userCards.Count < users.Count)
                {
                    _uiManager.AppendLog($"Chỉ tìm thấy bài hợp lệ cho {_userCards.Count}/{users.Count} user", UIManager.LogLevel.Warning);
                }

                if (_userCards.Count == 3)
                {
                    var allCards = Enumerable.Range(0, 52).ToList();
                    var usedCards = _userCards.Values.SelectMany(c => c).ToList();
                    _opponentCards = allCards.Except(usedCards).ToArray();
                    if (_opponentCards.Length != 13)
                    {
                        _uiManager.AppendLog($"Số lá bài đối thủ không hợp lệ: {_opponentCards.Length}", UIManager.LogLevel.Error);
                        _opponentCards = Array.Empty<int>();
                    }
                    else
                    {
                        _uiManager.AppendLog($"Đã tính toán bài đối thủ: {CardUtilityMaubinh.ConvertCardsToString(_opponentCards)}", UIManager.LogLevel.Info);
                    }
                }
                else
                {
                    _uiManager.AppendLog($"Số user hợp lệ ({_userCards.Count}) không đủ 3 để tính bài đối thủ", UIManager.LogLevel.Warning);
                }

                // Update card display for each user
                foreach (var kvp in _userCards)
                {
                    _displayManager.UpdateCardDisplay(kvp.Key, kvp.Value);
                }
                _isScanned = true;
                _uiManager.AppendLog($"Quét bài thành công, số user: {_userCards.Count}, tổng số lá: {cardCount}", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi quét bài: {ex.Message}", UIManager.LogLevel.Error);
                MessageBox.Show($"Lỗi khi quét bài: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                _isGameActive = false;
            }
        }

        public async Task SuggestButton_Click(object sender, EventArgs e)
        {
            if (!_isScanned)
            {
                _uiManager.AppendLog("Vui lòng quét bài mới trước khi gợi ý", UIManager.LogLevel.Error);
                MessageBox.Show("Vui lòng quét bài mới trước khi gợi ý", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                _userSuggestions.Clear();
                _selectedSuggestions.Clear();

                _displayManager.ResetListBoxColors();

                Dictionary<string, string> usernameToIndexKey = _displayManager.GetUsernameToIndexKey();

                if (_userCards.Count == 3 && _opponentCards.Length == 13)
                {
                    var teamSuggestions = _teamStrategyManager.GenerateTeamSuggestions(_userCards);
                    foreach (var user in _userCards.Keys)
                    {
                        if (usernameToIndexKey.TryGetValue(user, out string indexKey))
                        {
                            var userSuggestion = teamSuggestions.FirstOrDefault(ts => ts.User == user);
                            if (userSuggestion.User != null)
                            {
                                var suggestions = new List<(int[] Chi1, int[] Chi2, int[] Chi3)>
                                {
                                    (userSuggestion.Chi1, userSuggestion.Chi2, userSuggestion.Chi3)
                                };
                                _userSuggestions[indexKey] = suggestions;
                                _uiManager.AppendLog($"Đã tạo {suggestions.Count} gợi ý tối ưu cho {user} (indexKey: {indexKey})", UIManager.LogLevel.Info, user);
                                foreach (var suggestion in suggestions)
                                {
                                    _suggestionHandler.AddSuggestion(user, suggestion);
                                }
                            }
                            else
                            {
                                _uiManager.AppendLog($"Không tìm thấy gợi ý cho {user} (indexKey: {indexKey}) trong teamSuggestions", UIManager.LogLevel.Warning, user);
                            }
                        }
                    }

                    var opponentSuggestions = _cardManager.GenerateSuggestions(_opponentCards, isOpponent: true);
                    _userSuggestions["opponent"] = opponentSuggestions;
                    _uiManager.AppendLog($"Đã tạo {opponentSuggestions.Count} gợi ý cho đối thủ", UIManager.LogLevel.Info);
                }
                else
                {
                    foreach (var user in _userCards.Keys)
                    {
                        if (usernameToIndexKey.TryGetValue(user, out string indexKey))
                        {
                            var suggestions = _cardManager.GenerateSuggestions(_userCards[user]);
                            _userSuggestions[indexKey] = suggestions;
                            _uiManager.AppendLog($"Đã tạo {suggestions.Count} gợi ý cho {user} (indexKey: {indexKey})", UIManager.LogLevel.Info, user);
                            foreach (var suggestion in suggestions)
                            {
                                _suggestionHandler.AddSuggestion(user, suggestion);
                            }
                        }
                    }
                }

                _displayManager.UpdateSuggestionListBoxes();
                _displayManager.UpdateWinRatesForTeam();
                _uiManager.AppendLog("Đã cập nhật danh sách gợi ý", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi tạo gợi ý: {ex.Message}", UIManager.LogLevel.Error);
                MessageBox.Show($"Lỗi khi tạo gợi ý: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public async Task ArrangeAllButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (!_isGameActive)
                {
                    _uiManager.AppendLog("Không thể xếp bài: Trò chơi chưa hoạt động hoặc chưa quét bài mới", UIManager.LogLevel.Error);
                    MessageBox.Show("Vui lòng quét bài mới trước khi xếp", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var tasks = new List<Task>();
                foreach (var user in _userCards.Keys)
                {
                    var userIndex = Array.IndexOf(_userCards.Keys.ToArray(), user);
                    string indexKey = $"user{userIndex}";
                    if (!_selectedSuggestions.ContainsKey(indexKey) || _selectedSuggestions[indexKey] == null)
                    {
                        _uiManager.AppendLog($"Bỏ qua {user} vì chưa chọn gợi ý", UIManager.LogLevel.Warning);
                        continue;
                    }
                    tasks.Add(ArrangeButton_Click(indexKey));
                }

                if (!tasks.Any())
                {
                    _uiManager.AppendLog("Không có user nào có bài hợp lệ để xếp", UIManager.LogLevel.Error);
                    MessageBox.Show("Không có user nào có bài hợp lệ để xếp", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                await Task.WhenAll(tasks);
                _uiManager.AppendLog($"Đã xếp bài cho {tasks.Count} user", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi xếp bài đồng thời: {ex.Message}", UIManager.LogLevel.Error);
                MessageBox.Show($"Lỗi khi xếp bài: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public async Task ArrangeButton_Click(string indexKey)
        {
            try
            {
                if (!_isGameActive)
                {
                    _uiManager.AppendLog($"Không thể xếp bài cho {indexKey}: Trò chơi chưa hoạt động", UIManager.LogLevel.Error);
                    MessageBox.Show("Vui lòng quét bài mới trước khi xếp", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (!_selectedSuggestions.ContainsKey(indexKey) || _selectedSuggestions[indexKey] == null)
                {
                    _uiManager.AppendLog($"Chưa chọn gợi ý cho {indexKey}", UIManager.LogLevel.Error);
                    MessageBox.Show($"Vui lòng chọn một gợi ý cho {indexKey}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var userIndex = int.Parse(indexKey.Replace("user", ""));
                var user = _userCards.Keys.ElementAt(userIndex);
                var targetChis = _selectedSuggestions[indexKey].Value; // Sử dụng .Value để lấy giá trị từ nullable tuple

                // Chuyển đổi tuple thành mảng đơn để hiển thị log
                int[] allCards = targetChis.Chi1.Concat(targetChis.Chi2).Concat(targetChis.Chi3).ToArray();

                _uiManager.AppendLog($"Bắt đầu xếp bài cho {user} theo gợi ý: {CardUtilityMaubinh.ConvertCardsToString(allCards)}", UIManager.LogLevel.Info, user);

                if (!_gameClient.GetDrivers().ContainsKey(user))
                {
                    _uiManager.AppendLog($"Không tìm thấy driver cho {user}", UIManager.LogLevel.Error);
                    MessageBox.Show($"Không tìm thấy driver cho {user}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var driver = _gameClient.GetDrivers()[user];

                // Sử dụng phương thức mới để xếp bài bằng script thay vì kéo thả
                await _cardDragHandler.ArrangeCardsWithScriptAsync(targetChis.Chi1, targetChis.Chi2, targetChis.Chi3);

                // Chuyển đổi tuple thành mảng đơn để hiển thị log
                _uiManager.AppendLog($"Đã xếp bài cho {user} theo gợi ý: {CardUtilityMaubinh.ConvertCardsToString(allCards)}", UIManager.LogLevel.Info, user);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi xếp bài: {ex.Message}", UIManager.LogLevel.Error);
                MessageBox.Show($"Lỗi khi xếp bài: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public async Task SendButton_Click(string indexKey)
        {
            try
            {
                if (!_isGameActive)
                {
                    _uiManager.AppendLog($"Không thể gửi bài cho {indexKey}: Trò chơi chưa hoạt động", UIManager.LogLevel.Error);
                    MessageBox.Show("Vui lòng quét bài mới trước khi gửi", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (!_selectedSuggestions.ContainsKey(indexKey) || _selectedSuggestions[indexKey] == null)
                {
                    _uiManager.AppendLog($"Chưa chọn gợi ý cho {indexKey}", UIManager.LogLevel.Error);
                    MessageBox.Show($"Vui lòng chọn một gợi ý cho {indexKey}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Tìm username từ indexKey giống như trong SendAllButton_Click
                string user = null;
                var userIndex = int.Parse(indexKey.Replace("user", ""));
                var userArray = _userCards.Keys.ToArray();
                if (userIndex >= 0 && userIndex < userArray.Length)
                {
                    user = userArray[userIndex];
                }
                else
                {
                    _uiManager.AppendLog($"Index {userIndex} không hợp lệ cho {indexKey}", UIManager.LogLevel.Error);
                    MessageBox.Show($"Index {userIndex} không hợp lệ cho {indexKey}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (!_gameClient.GetDrivers().TryGetValue(user, out var driver) || !_gameClient.GetDriverManager().IsDriverActive(user))
                {
                    _uiManager.AppendLog($"Driver cho {user} không hoạt động", UIManager.LogLevel.Error);
                    MessageBox.Show($"Driver cho {user} không hoạt động", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Sử dụng script giống như SendAllButton_Click để đảm bảo tính nhất quán
                string callOnXepXongScript = @"
                    try {
                        // Tìm tất cả các component game
                        const scene = cc.director.getScene();
                        if (!scene) {
                            return 'Không tìm thấy scene';
                        }

                        // Tìm các component có thể chứa hàm callOnXepXong
                        const allComponents = [];
                        const findAllComponents = (node) => {
                            if (node._components) {
                                allComponents.push(...node._components);
                            }
                            const children = node.children || [];
                            for (let i = 0; i < children.length; i++) {
                                findAllComponents(children[i]);
                            }
                        };

                        findAllComponents(scene);

                        // Tìm và gọi hàm callOnXepXong hoặc các hàm tương tự
                        for (const comp of allComponents) {
                            // Kiểm tra callOnXepXong
                            if (typeof comp.callOnXepXong === 'function') {
                                comp.callOnXepXong();
                                return 'Đã gọi callOnXepXong thành công';
                            }

                            // Kiểm tra các hàm tương tự
                            for (const key in comp) {
                                if (typeof comp[key] === 'function' &&
                                    (key.includes('XepXong') || key.includes('xepXong') ||
                                     key.includes('GuiBai') || key.includes('guiBai') ||
                                     key.includes('Submit') || key.includes('submit') ||
                                     key.includes('Finish') || key.includes('finish'))) {
                                    try {
                                        comp[key]();
                                        return `Đã gọi hàm ${key} thành công`;
                                    } catch (err) {
                                        console.log(`Lỗi khi gọi hàm ${key}:`, err);
                                    }
                                }
                            }
                        }

                        // Nếu không tìm thấy hàm nào, thử tìm trong window
                        if (typeof window.callOnXepXong === 'function') {
                            window.callOnXepXong();
                            return 'Đã gọi window.callOnXepXong thành công';
                        }

                        // Nếu không tìm thấy, thử tìm các hàm tương tự trong window
                        const possibleFunctions = [
                            'onXepXong', 'xepXong', 'guiBai', 'sendCards', 'finishArrange',
                            'onFinishArrange', 'submitCards', 'onSubmitCards'
                        ];

                        for (const funcName of possibleFunctions) {
                            if (typeof window[funcName] === 'function') {
                                window[funcName]();
                                return `Đã gọi window.${funcName} thành công`;
                            }
                        }

                        // Nếu không tìm thấy hàm nào, trả về thông báo lỗi
                        return 'Không tìm thấy hàm callOnXepXong hoặc tương tự';
                    } catch (error) {
                        return 'Lỗi khi gọi callOnXepXong: ' + error.message;
                    }";

                var result = ((IJavaScriptExecutor)driver).ExecuteScript(callOnXepXongScript)?.ToString();
                if (result.StartsWith("Đã gọi"))
                {
                    _uiManager.AppendLog($"Đã gửi bài cho {user}: {result}", UIManager.LogLevel.Info, user);
                }
                else
                {
                    _uiManager.AppendLog($"Không thể gửi bài cho {user}: {result}", UIManager.LogLevel.Warning, user);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi gửi bài cho {indexKey}: {ex.Message}", UIManager.LogLevel.Error);
                MessageBox.Show($"Lỗi khi gửi bài: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public async Task SendAllButton_Click(object sender, EventArgs e)
        {
            try
            {
                bool allUsersArranged = true;
                foreach (var user in _userCards.Keys)
                {
                    var userIndex = Array.IndexOf(_userCards.Keys.ToArray(), user);
                    string indexKey = $"user{userIndex}";
                    if (!_selectedSuggestions.ContainsKey(indexKey) || _selectedSuggestions[indexKey] == null)
                    {
                        _uiManager.AppendLog($"User {user} chưa xếp bài", UIManager.LogLevel.Warning);
                        allUsersArranged = false;
                        break;
                    }
                }

                if (!allUsersArranged)
                {
                    _uiManager.AppendLog("Vui lòng xếp bài cho tất cả user có bài hợp lệ trước khi gửi", UIManager.LogLevel.Error);
                    MessageBox.Show("Vui lòng xếp bài cho tất cả user có bài hợp lệ trước khi gửi", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                foreach (var user in _userCards.Keys)
                {
                    if (!_gameClient.GetDrivers().TryGetValue(user, out var driver) || !_gameClient.GetDriverManager().IsDriverActive(user))
                    {
                        _uiManager.AppendLog($"Driver cho {user} không hoạt động, bỏ qua", UIManager.LogLevel.Warning, user);
                        continue;
                    }

                    try
                    {
                        // Chỉ sử dụng callOnXepXong()
                        string callOnXepXongScript = @"
                            try {
                                // Tìm tất cả các component game
                                const scene = cc.director.getScene();
                                if (!scene) {
                                    return 'Không tìm thấy scene';
                                }

                                // Tìm các component có thể chứa hàm callOnXepXong
                                const allComponents = [];
                                const findAllComponents = (node) => {
                                    if (node._components) {
                                        allComponents.push(...node._components);
                                    }
                                    const children = node.children || [];
                                    for (let i = 0; i < children.length; i++) {
                                        findAllComponents(children[i]);
                                    }
                                };

                                findAllComponents(scene);

                                // Tìm và gọi hàm callOnXepXong hoặc các hàm tương tự
                                for (const comp of allComponents) {
                                    // Kiểm tra callOnXepXong
                                    if (typeof comp.callOnXepXong === 'function') {
                                        comp.callOnXepXong();
                                        return 'Đã gọi callOnXepXong thành công';
                                    }

                                    // Kiểm tra các hàm tương tự
                                    for (const key in comp) {
                                        if (typeof comp[key] === 'function' &&
                                            (key.includes('XepXong') || key.includes('xepXong') ||
                                             key.includes('GuiBai') || key.includes('guiBai') ||
                                             key.includes('Submit') || key.includes('submit') ||
                                             key.includes('Finish') || key.includes('finish'))) {
                                            try {
                                                comp[key]();
                                                return `Đã gọi hàm ${key} thành công`;
                                            } catch (err) {
                                                console.log(`Lỗi khi gọi hàm ${key}:`, err);
                                            }
                                        }
                                    }
                                }

                                // Nếu không tìm thấy hàm nào, thử tìm trong window
                                if (typeof window.callOnXepXong === 'function') {
                                    window.callOnXepXong();
                                    return 'Đã gọi window.callOnXepXong thành công';
                                }

                                // Nếu không tìm thấy, thử tìm các hàm tương tự trong window
                                const possibleFunctions = [
                                    'onXepXong', 'xepXong', 'guiBai', 'sendCards', 'finishArrange',
                                    'onFinishArrange', 'submitCards', 'onSubmitCards'
                                ];

                                for (const funcName of possibleFunctions) {
                                    if (typeof window[funcName] === 'function') {
                                        window[funcName]();
                                        return `Đã gọi window.${funcName} thành công`;
                                    }
                                }

                                // Nếu không tìm thấy hàm nào, trả về thông báo lỗi
                                return 'Không tìm thấy hàm callOnXepXong hoặc tương tự';
                            } catch (error) {
                                return 'Lỗi khi gọi callOnXepXong: ' + error.message;
                            }";

                        var result = ((IJavaScriptExecutor)driver).ExecuteScript(callOnXepXongScript)?.ToString();
                        if (result.StartsWith("Đã gọi"))
                        {
                            _uiManager.AppendLog($"Đã gửi bài cho {user}: {result}", UIManager.LogLevel.Info, user);
                        }
                        else
                        {
                            _uiManager.AppendLog($"Không thể gửi bài cho {user}: {result}", UIManager.LogLevel.Warning, user);
                        }

                        await Task.Delay(200);
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"Lỗi khi gửi bài cho {user}: {ex.Message}", UIManager.LogLevel.Error, user);
                    }
                }

                _uiManager.AppendLog("Đã gửi bài cho tất cả user có bài hợp lệ", UIManager.LogLevel.Info);
                _isGameActive = false;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi gửi bài: {ex.Message}", UIManager.LogLevel.Error);
                MessageBox.Show($"Lỗi khi gửi bài: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public async Task ScanCardsSolo()
        {
            try
            {
                _uiManager.AppendLog("Bắt đầu quét bài tự động", UIManager.LogLevel.Info);
                await ScanButton_Click(null, EventArgs.Empty);
                if (_isScanned)
                {
                    await SuggestButton_Click(null, EventArgs.Empty);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi quét bài tự động: {ex.Message}", UIManager.LogLevel.Error);
                MessageBox.Show($"Lỗi khi quét bài tự động: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                _isGameActive = false;
            }
        }

        public void ClearCardDisplay()
        {
            _userCards.Clear();
            _userSuggestions.Clear();
            _selectedSuggestions.Clear();
            _opponentCards = Array.Empty<int>();
            _isScanned = false;
            _isGameActive = false;
            _displayManager.ClearCardDisplay();
        }

        public void ListBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                var listBox = sender as ListBox;
                if (listBox == null)
                {
                    _uiManager.AppendLog("Sender không phải ListBox", UIManager.LogLevel.Error);
                    return;
                }

                var indexKey = listBox.Tag?.ToString();
                if (string.IsNullOrEmpty(indexKey))
                {
                    _uiManager.AppendLog("indexKey không hợp lệ", UIManager.LogLevel.Error);
                    return;
                }

                if (listBox.SelectedIndex >= 0 && _userSuggestions.ContainsKey(indexKey))
                {
                    var selectedSuggestion = _userSuggestions[indexKey][listBox.SelectedIndex];
                    _selectedSuggestions[indexKey] = selectedSuggestion;

                    var cards = selectedSuggestion.Chi1.Concat(selectedSuggestion.Chi2).Concat(selectedSuggestion.Chi3).ToArray();
                    _displayManager.UpdateCardDisplay(indexKey, cards);

                    if (indexKey == "opponent")
                    {
                        CalculateOptimalTeamSuggestions(selectedSuggestion);

                        // Tính tổng số điểm team thắng Gà
                        int totalPoints = 0;
                        foreach (var userKey in new[] { "user0", "user1", "user2" })
                        {
                            if (_selectedSuggestions.ContainsKey(userKey) && _selectedSuggestions[userKey] != null)
                            {
                                var userSuggestion = _selectedSuggestions[userKey].Value;
                                var (chi1WinRate, chi2WinRate, chi3WinRate) = _cardManager.CalculateWinRates(userSuggestion, selectedSuggestion);
                                if (chi1WinRate > 50) totalPoints++;
                                if (chi2WinRate > 50) totalPoints++;
                                if (chi3WinRate > 50) totalPoints++;
                            }
                        }

                        _displayManager.UpdateTeamScoreLabel(totalPoints);
                        _uiManager.AppendLog($"Tổng số điểm team thắng Gà: {totalPoints}", UIManager.LogLevel.Info);
                    }
                    else if (indexKey.StartsWith("user") && _userCards.Count >= 2)
                    {
                        // Team mode: Tự động chọn gợi ý tối ưu cho các user khác
                        AutoSelectTeamSuggestions(indexKey, listBox.SelectedIndex);
                    }

                    _displayManager.UpdateWinRatesForTeam();
                    _uiManager.AppendLog($"Đã chọn gợi ý {listBox.SelectedIndex + 1} cho {indexKey}", UIManager.LogLevel.Info);
                }
                else
                {
                    _uiManager.AppendLog($"Không tìm thấy gợi ý cho {indexKey} hoặc SelectedIndex không hợp lệ: {listBox.SelectedIndex}", UIManager.LogLevel.Warning);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi chọn gợi ý: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        private void CalculateOptimalTeamSuggestions((int[] Chi1, int[] Chi2, int[] Chi3) opponentSuggestion)
        {
            try
            {
                double bestTeamScore = double.MinValue; // Tổng điểm chi đội thắng
                int bestNumLosingChis = 0; // Số chi Gà thua ≥ 2 user
                int bestNumAllLosingChis = 0; // Số chi Gà thua cả 3 user
                int bestOpponentPoints = int.MaxValue; // Điểm trung bình của Gà
                Dictionary<string, (int[] Chi1, int[] Chi2, int[] Chi3)> bestSuggestions = new Dictionary<string, (int[] Chi1, int[] Chi2, int[] Chi3)>();
                Dictionary<string, int> bestSuggestionIndices = new Dictionary<string, int>();

                if (!_userSuggestions.ContainsKey("user0") || !_userSuggestions.ContainsKey("user1") || !_userSuggestions.ContainsKey("user2"))
                {
                    _uiManager.AppendLog("Không đủ gợi ý cho các user để tính toán tổ hợp tối ưu", UIManager.LogLevel.Error);
                    MessageBox.Show("Không đủ gợi ý cho các user để tính toán tổ hợp tối ưu", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Tính độ mạnh của các chi của Gà
                var opponentChi1Strength = CardUtilityMaubinh.GetChiStrength(opponentSuggestion.Chi1.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());
                var opponentChi2Strength = CardUtilityMaubinh.GetChiStrength(opponentSuggestion.Chi2.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());
                var opponentChi3Strength = CardUtilityMaubinh.GetChiStrength(opponentSuggestion.Chi3.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList());

                // Tăng số gợi ý mỗi người chơi lên 10 để kiểm tra thêm tổ hợp
                _uiManager.AppendLog($"Bắt đầu tính toán tổ hợp tối ưu với {_userSuggestions["user0"].Count} x {_userSuggestions["user1"].Count} x {_userSuggestions["user2"].Count} tổ hợp", UIManager.LogLevel.Info);

                // Kiểm tra trường hợp đặc biệt trước
                foreach (var indexKey in new[] { "user0", "user1", "user2" })
                {
                    foreach (var suggestion in _userSuggestions[indexKey])
                    {
                        var cards = suggestion.Chi1.Concat(suggestion.Chi2).Concat(suggestion.Chi3).ToList();
                        var cardInfos = cards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();
                        int specialPoints = _cardManager.CalculateSpecialPoints(cardInfos);
                        if (specialPoints > 0)
                        {
                            // Ưu tiên gợi ý có bài đặc biệt
                            bestSuggestions.Clear();
                            bestSuggestions[indexKey] = suggestion;
                            bestSuggestionIndices[indexKey] = _userSuggestions[indexKey].IndexOf(suggestion);
                            bestTeamScore = specialPoints;
                            bestOpponentPoints = -specialPoints; // Gà mất điểm lớn
                            bestNumLosingChis = 3; // Giả định thắng cả 3 chi
                            bestNumAllLosingChis = 3;

                            // Chọn gợi ý mặc định cho các user khác
                            foreach (var otherKey in new[] { "user0", "user1", "user2" }.Where(k => k != indexKey))
                            {
                                var defaultSuggestion = _userSuggestions[otherKey].FirstOrDefault();
                                bestSuggestions[otherKey] = defaultSuggestion;
                                bestSuggestionIndices[otherKey] = 0;
                            }

                            _uiManager.AppendLog($"Phát hiện bài đặc biệt cho {indexKey} (điểm: {specialPoints}), ưu tiên tổ hợp này", UIManager.LogLevel.Info);
                            goto UpdateSuggestions; // Nhảy tới bước cập nhật gợi ý
                        }
                    }
                }

                // Duyệt qua tất cả tổ hợp gợi ý
                foreach (var suggestion0 in _userSuggestions["user0"].Take(10))
                {
                    foreach (var suggestion1 in _userSuggestions["user1"].Take(10))
                    {
                        foreach (var suggestion2 in _userSuggestions["user2"].Take(10))
                        {
                            var suggestions = new Dictionary<string, (int[] Chi1, int[] Chi2, int[] Chi3)>
                            {
                                { "user0", suggestion0 },
                                { "user1", suggestion1 },
                                { "user2", suggestion2 }
                            };

                            double teamScore = 0; // Tổng điểm chi đội thắng
                            int numLosingChis = 0; // Số chi Gà thua ≥ 2 user
                            int numAllLosingChis = 0; // Số chi Gà thua cả 3 user
                            int opponentPoints = 0; // Tổng điểm của Gà
                            int chi1Wins = 0, chi2Wins = 0, chi3Wins = 0;

                            // Tính điểm cho từng user
                            foreach (var indexKey in new[] { "user0", "user1", "user2" })
                            {
                                var userSuggestion = suggestions[indexKey];
                                var (chi1WinRate, chi2WinRate, chi3WinRate) = _cardManager.CalculateWinRates(userSuggestion, opponentSuggestion);

                                // Kiểm tra trường hợp đặc biệt
                                var cards = userSuggestion.Chi1.Concat(userSuggestion.Chi2).Concat(userSuggestion.Chi3).ToList();
                                var cardInfos = cards.Select(id => new CardUtilityMaubinh.CardInfo(id)).ToList();
                                int specialPoints = _cardManager.CalculateSpecialPoints(cardInfos);
                                if (specialPoints > 0)
                                {
                                    teamScore += specialPoints;
                                    opponentPoints -= specialPoints;
                                }
                                else
                                {
                                    // Tính điểm chi với trọng số (Chi 2 x2, Chi 1 x1, Chi 3 x0.5)
                                    if (chi1WinRate > 50)
                                    {
                                        chi1Wins++;
                                        var userChi1Type = CardUtilityMaubinh.EvaluateHand(userSuggestion.Chi1);
                                        int chiPoints = userChi1Type switch
                                        {
                                            CardUtilityMaubinh.HandType.StraightFlush => 10,
                                            CardUtilityMaubinh.HandType.FourOfAKind => 8,
                                            _ => 1
                                        };
                                        teamScore += chiPoints;
                                        opponentPoints -= chiPoints;
                                    }

                                    if (chi2WinRate > 50)
                                    {
                                        chi2Wins++;
                                        var userChi2Type = CardUtilityMaubinh.EvaluateHand(userSuggestion.Chi2);
                                        int chiPoints = userChi2Type switch
                                        {
                                            CardUtilityMaubinh.HandType.StraightFlush => 20,
                                            CardUtilityMaubinh.HandType.FourOfAKind => 16,
                                            CardUtilityMaubinh.HandType.FullHouse => 4,
                                            _ => 1
                                        };
                                        teamScore += chiPoints * 2; // Trọng số Chi 2
                                        opponentPoints -= chiPoints;
                                    }

                                    if (chi3WinRate > 50)
                                    {
                                        chi3Wins++;
                                        var userChi3Type = CardUtilityMaubinh.EvaluateHand(userSuggestion.Chi3);
                                        int chiPoints = userChi3Type switch
                                        {
                                            CardUtilityMaubinh.HandType.ThreeOfAKind => 6,
                                            _ => 1
                                        };
                                        teamScore += chiPoints * 0.5; // Trọng số Chi 3
                                        opponentPoints -= chiPoints;
                                    }
                                }
                            }

                            // Tính số chi Gà thua ≥ 2 user
                            if (chi1Wins >= 2) numLosingChis++;
                            if (chi2Wins >= 2) numLosingChis++;
                            if (chi3Wins >= 2) numLosingChis++;

                            // Tính số chi Gà thua cả 3 user
                            if (chi1Wins == 3 && suggestions.All(u => _cardManager.CalculateWinRates(u.Value, opponentSuggestion).Chi1WinRate > 80))
                                numAllLosingChis++;
                            if (chi2Wins == 3 && suggestions.All(u => _cardManager.CalculateWinRates(u.Value, opponentSuggestion).Chi2WinRate > 80))
                                numAllLosingChis++;
                            if (chi3Wins == 3 && suggestions.All(u => _cardManager.CalculateWinRates(u.Value, opponentSuggestion).Chi3WinRate > 80))
                                numAllLosingChis++;

                            // Kiểm tra điều kiện 2/3
                            bool meetsTwoThirds = chi1Wins >= 2 && chi2Wins >= 2 && chi3Wins >= 2;

                            // So sánh và chọn tổ hợp tốt nhất
                            if (opponentPoints < bestOpponentPoints ||
                                (opponentPoints == bestOpponentPoints && meetsTwoThirds && numLosingChis > bestNumLosingChis) ||
                                (opponentPoints == bestOpponentPoints && meetsTwoThirds && numLosingChis == bestNumLosingChis && numAllLosingChis > bestNumAllLosingChis) ||
                                (opponentPoints == bestOpponentPoints && meetsTwoThirds && numLosingChis == bestNumLosingChis && numAllLosingChis == bestNumAllLosingChis && teamScore > bestTeamScore))
                            {
                                bestTeamScore = teamScore;
                                bestNumLosingChis = numLosingChis;
                                bestNumAllLosingChis = numAllLosingChis;
                                bestOpponentPoints = opponentPoints;
                                bestSuggestions.Clear();
                                bestSuggestions["user0"] = suggestion0;
                                bestSuggestions["user1"] = suggestion1;
                                bestSuggestions["user2"] = suggestion2;

                                bestSuggestionIndices["user0"] = _userSuggestions["user0"].IndexOf(suggestion0);
                                bestSuggestionIndices["user1"] = _userSuggestions["user1"].IndexOf(suggestion1);
                                bestSuggestionIndices["user2"] = _userSuggestions["user2"].IndexOf(suggestion2);

                                _uiManager.AppendLog($"Cập nhật tổ hợp tốt nhất: Điểm Gà={bestOpponentPoints}, numLosingChis={bestNumLosingChis}, numAllLosingChis={bestNumAllLosingChis}, Tổng điểm chi={bestTeamScore}", UIManager.LogLevel.Debug);
                            }
                        }
                    }
                }

            UpdateSuggestions:
                // Cập nhật gợi ý cho từng user
                foreach (var indexKey in new[] { "user0", "user1", "user2" })
                {
                    if (bestSuggestions.ContainsKey(indexKey) && bestSuggestionIndices.ContainsKey(indexKey))
                    {
                        _selectedSuggestions[indexKey] = bestSuggestions[indexKey];
                        _displayManager.UpdateListBoxSelection(bestSuggestionIndices[indexKey]);

                        var cards = bestSuggestions[indexKey].Chi1.Concat(bestSuggestions[indexKey].Chi2).Concat(bestSuggestions[indexKey].Chi3).ToArray();
                        _displayManager.UpdateCardDisplay(indexKey, cards);
                        _uiManager.AppendLog($"Đã chọn gợi ý tối ưu cho {indexKey} với index {bestSuggestionIndices[indexKey]}", UIManager.LogLevel.Info);
                    }
                }

                _displayManager.UpdateTeamWinRateLabel(bestTeamScore, $"{bestNumLosingChis} chi Gà thua ≥ 2 user");

                if (bestNumLosingChis == 0)
                {
                    _uiManager.AppendLog("Không tìm được tổ hợp khiến Gà thua ≥ 2 user ở bất kỳ chi nào", UIManager.LogLevel.Warning);
                }
                else
                {
                    _uiManager.AppendLog($"Đã tìm được tổ hợp tối ưu với điểm Gà={bestOpponentPoints}, {bestNumLosingChis} chi Gà thua ≥ 2 user, tổng điểm: {bestTeamScore}", UIManager.LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi tính toán gợi ý tối ưu cho đội: {ex.Message}", UIManager.LogLevel.Error);
                MessageBox.Show($"Lỗi khi tính toán gợi ý: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Tự động chọn gợi ý tối ưu cho team khi user chọn gợi ý
        /// </summary>
        private void AutoSelectTeamSuggestions(string selectedIndexKey, int selectedIndex)
        {
            try
            {
                _uiManager.AppendLog($"🤝 Team mode: Tự động chọn gợi ý tối ưu cho team dựa trên {selectedIndexKey} index {selectedIndex}", UIManager.LogLevel.Info);

                // Tạo dictionary bài của team hiện tại
                var teamCards = new Dictionary<string, int[]>();
                var indexToUser = new Dictionary<string, string>();

                int userIndex = 0;
                foreach (var kvp in _userCards)
                {
                    string indexKey = $"user{userIndex}";
                    teamCards[kvp.Key] = kvp.Value;
                    indexToUser[indexKey] = kvp.Key;
                    userIndex++;
                }

                // Tạo gợi ý team tối ưu cho từng user
                _uiManager.AppendLog($"✅ Tạo gợi ý team tối ưu cho {teamCards.Count} users", UIManager.LogLevel.Info);

                // Áp dụng gợi ý team cho các user khác
                int currentUserIndex = 0;
                foreach (var kvp in indexToUser)
                {
                    string indexKey = kvp.Key;
                    string username = kvp.Value;

                    if (indexKey == selectedIndexKey)
                    {
                        currentUserIndex++;
                        continue; // Bỏ qua user đã chọn
                    }

                    if (teamCards.ContainsKey(username))
                    {
                        var userCards = teamCards[username];
                        var userSuggestions = _cardManager.GenerateSuggestions(userCards);

                        if (userSuggestions.Any())
                        {
                            var bestSuggestion = userSuggestions.First(); // Lấy gợi ý tốt nhất
                            var legacySuggestion = (bestSuggestion.Chi1, bestSuggestion.Chi2, bestSuggestion.Chi3);

                            // Tìm gợi ý tương ứng trong listbox
                            if (_userSuggestions.ContainsKey(indexKey))
                            {
                                int bestMatchIndex = FindBestMatchingSuggestion(_userSuggestions[indexKey], legacySuggestion);

                                if (bestMatchIndex >= 0)
                                {
                                    // Cập nhật selection
                                    _selectedSuggestions[indexKey] = _userSuggestions[indexKey][bestMatchIndex];

                                    // Cập nhật UI listbox
                                    _displayManager.UpdateListBoxSelection(bestMatchIndex);

                                    // Cập nhật card display
                                    var cards = _userSuggestions[indexKey][bestMatchIndex].Chi1
                                        .Concat(_userSuggestions[indexKey][bestMatchIndex].Chi2)
                                        .Concat(_userSuggestions[indexKey][bestMatchIndex].Chi3).ToArray();
                                    _displayManager.UpdateCardDisplay(indexKey, cards);

                                    _uiManager.AppendLog($"🎯 Auto-selected suggestion {bestMatchIndex + 1} for {username} (team optimization)", UIManager.LogLevel.Info);
                                }
                            }
                        }
                        else
                        {
                            _uiManager.AppendLog($"⚠️ Không tạo được gợi ý cho {username}", UIManager.LogLevel.Warning);
                        }
                    }
                    currentUserIndex++;
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi auto-select team suggestions: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        /// <summary>
        /// Tìm gợi ý khớp nhất trong danh sách
        /// </summary>
        private int FindBestMatchingSuggestion(List<(int[] Chi1, int[] Chi2, int[] Chi3)> suggestions, (int[] Chi1, int[] Chi2, int[] Chi3) target)
        {
            int bestMatch = -1;
            int maxMatches = 0;

            for (int i = 0; i < suggestions.Count; i++)
            {
                var suggestion = suggestions[i];
                int matches = 0;

                // Đếm số lá bài khớp
                matches += suggestion.Chi1.Intersect(target.Chi1).Count();
                matches += suggestion.Chi2.Intersect(target.Chi2).Count();
                matches += suggestion.Chi3.Intersect(target.Chi3).Count();

                if (matches > maxMatches)
                {
                    maxMatches = matches;
                    bestMatch = i;
                }
            }

            return bestMatch;
        }
    }
}

