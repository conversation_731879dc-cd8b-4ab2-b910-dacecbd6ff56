using System;
using System.Collections.Generic;
using System.Linq;

namespace AutoGameBai.Gamemaubinh
{
    /// <summary>
    /// Temporary stub for TeamStrategyManager to maintain compatibility
    /// This class delegates to the new TeamStrategyEngine
    /// </summary>
    public class TeamStrategyManager
    {
        private readonly UIManager _uiManager;
        private readonly TeamStrategyEngine _teamEngine;

        public TeamStrategyManager(UIManager uiManager, MauBinhCardManager cardManager)
        {
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _teamEngine = new TeamStrategyEngine(uiManager);
        }

        /// <summary>
        /// Calculate team strategy using new engine
        /// </summary>
        public List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)> CalculateTeamStrategy(Dictionary<string, int[]> userCards)
        {
            try
            {
                var users = userCards.Select((kvp, index) => new TeamStrategyEngine.UserInfo
                {
                    Username = kvp.Key,
                    Cards = kvp.Value,
                    Index = index
                }).ToList();

                var result = _teamEngine.CalculateTeamStrategy(users);

                // Convert to legacy format - use best suggestions for each user
                var teamSuggestions = new List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)>();
                foreach (var user in users)
                {
                    // Use MauBinhEngine to get best suggestion for each user
                    var engine = new MauBinhEngine(_uiManager);
                    var suggestions = engine.GenerateSuggestions(user.Cards);
                    if (suggestions.Any())
                    {
                        var best = suggestions.First();
                        teamSuggestions.Add((user.Username, best.Chi1, best.Chi2, best.Chi3));
                    }
                }

                return teamSuggestions;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi TeamStrategyManager: {ex.Message}", UIManager.LogLevel.Error);
                return new List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)>();
            }
        }

        /// <summary>
        /// Analyze team performance
        /// </summary>
        public string AnalyzeTeamPerformance(Dictionary<string, int[]> userCards, int[] opponentCards)
        {
            try
            {
                var users = userCards.Select((kvp, index) => new TeamStrategyEngine.UserInfo
                {
                    Username = kvp.Key,
                    Cards = kvp.Value,
                    Index = index
                }).ToList();

                var result = _teamEngine.CalculateTeamStrategy(users);

                return $"Điểm thua Gà: {result.ChickenTotalLoss}, Sập làng: {result.SapLangUsers.Count} users";
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi AnalyzeTeamPerformance: {ex.Message}", UIManager.LogLevel.Error);
                return "Lỗi phân tích team";
            }
        }

        /// <summary>
        /// Get team optimization score
        /// </summary>
        public double GetOptimizationScore(Dictionary<string, int[]> userCards)
        {
            try
            {
                var users = userCards.Select((kvp, index) => new TeamStrategyEngine.UserInfo
                {
                    Username = kvp.Key,
                    Cards = kvp.Value,
                    Index = index
                }).ToList();

                var result = _teamEngine.CalculateTeamStrategy(users);

                // Return negative chicken loss as optimization score (higher is better)
                return -result.ChickenTotalLoss;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GetOptimizationScore: {ex.Message}", UIManager.LogLevel.Error);
                return 0;
            }
        }

        /// <summary>
        /// Generate team suggestions (alias for CalculateTeamStrategy)
        /// </summary>
        public List<(string User, int[] Chi1, int[] Chi2, int[] Chi3)> GenerateTeamSuggestions(Dictionary<string, int[]> userCards)
        {
            return CalculateTeamStrategy(userCards);
        }

        /// <summary>
        /// Event for strategy changed (stub implementation)
        /// </summary>
        public event Action StrategyChanged = delegate { };
    }
}
