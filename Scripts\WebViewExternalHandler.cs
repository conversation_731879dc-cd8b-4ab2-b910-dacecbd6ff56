using System;
using System.Runtime.InteropServices;
using System.Threading.Tasks;

namespace AutoGameBai.Scripts
{
    /// <summary>
    /// Handler cho WebView external calls từ JavaScript
    /// </summary>
    [ComVisible(true)]
    public class WebViewExternalHandler
    {
        private readonly GameClientManager _gameClient;
        private readonly UIManager _uiManager;
        private readonly string _username;

        public WebViewExternalHandler(GameClientManager gameClient, UIManager uiManager, string username)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _username = username ?? throw new ArgumentNullException(nameof(username));
        }

        /// <summary>
        /// GetKeyRoom method được gọi từ JavaScript
        /// </summary>
        public async void GetKeyRoom(string username)
        {
            try
            {
                _uiManager.AppendLog($"🔑 JavaScript gọi GetKeyRoom cho {username}", UIManager.LogLevel.Info, username);

                // Validate username
                if (string.IsNullOrEmpty(username) || username != _username)
                {
                    _uiManager.AppendLog($"❌ Username không hợp lệ: {username} (expected: {_username})", UIManager.LogLevel.Error, username);
                    return;
                }

                // Gọi GetKeyRoom từ RoomStateManager (tạo RoomManager mới)
                var roomManager = new Room.RoomManager(_gameClient, _uiManager, null);
                if (roomManager != null)
                {
                    // Default parameters
                    string roomId = "100";
                    int maxAttempts = 10;
                    int attemptDelay = 200;

                    _uiManager.AppendLog($"🚀 Bắt đầu GetKeyRoom cho {username} (roomId: {roomId}, maxAttempts: {maxAttempts})", UIManager.LogLevel.Info, username);

                    // Gọi async method
                    Task.Run(async () =>
                    {
                        try
                        {
                            bool success = await roomManager.GetKeyRoomAsync(username, roomId, maxAttempts, attemptDelay, System.Threading.CancellationToken.None);

                            if (success)
                            {
                                _uiManager.AppendLog($"✅ GetKeyRoom thành công cho {username}", UIManager.LogLevel.Info, username);
                            }
                            else
                            {
                                _uiManager.AppendLog($"❌ GetKeyRoom thất bại cho {username}", UIManager.LogLevel.Warning, username);
                            }
                        }
                        catch (Exception ex)
                        {
                            _uiManager.AppendLog($"❌ Lỗi trong GetKeyRoom cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                        }
                    });
                }
                else
                {
                    _uiManager.AppendLog($"❌ RoomManager không khả dụng cho {username}", UIManager.LogLevel.Error, username);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi trong WebView GetKeyRoom cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// LeaveRoom method được gọi từ JavaScript
        /// </summary>
        public async void LeaveRoom(string username)
        {
            try
            {
                _uiManager.AppendLog($"🚪 JavaScript gọi LeaveRoom cho {username}", UIManager.LogLevel.Info, username);

                // Validate username
                if (string.IsNullOrEmpty(username) || username != _username)
                {
                    _uiManager.AppendLog($"❌ Username không hợp lệ: {username} (expected: {_username})", UIManager.LogLevel.Error, username);
                    return;
                }

                // Gọi LeaveRoom từ GameClient
                _uiManager.AppendLog($"🚀 Bắt đầu LeaveRoom cho {username}", UIManager.LogLevel.Info, username);

                // Gọi async method
                Task.Run(async () =>
                {
                    try
                    {
                        await _gameClient.LeaveRoomAsync(username, 200);
                        _uiManager.AppendLog($"✅ LeaveRoom thành công cho {username}", UIManager.LogLevel.Info, username);
                    }
                    catch (Exception ex)
                    {
                        _uiManager.AppendLog($"❌ Lỗi trong LeaveRoom cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                    }
                });
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi trong WebView LeaveRoom cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }

        /// <summary>
        /// GetUsername method để JavaScript lấy username hiện tại
        /// </summary>
        public string GetUsername()
        {
            return _username;
        }

        /// <summary>
        /// IsInRoom method để kiểm tra user có đang trong phòng không
        /// </summary>
        public bool IsInRoom()
        {
            try
            {
                return _gameClient.GetUserRooms().ContainsKey(_username);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi kiểm tra IsInRoom cho {_username}: {ex.Message}", UIManager.LogLevel.Error, _username);
                return false;
            }
        }

        /// <summary>
        /// GetRoomInfo method để lấy thông tin phòng hiện tại
        /// </summary>
        public string GetRoomInfo()
        {
            try
            {
                var userRooms = _gameClient.GetUserRooms();
                if (userRooms.ContainsKey(_username))
                {
                    return userRooms[_username].ToString();
                }
                return "Không trong phòng";
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi lấy RoomInfo cho {_username}: {ex.Message}", UIManager.LogLevel.Error, _username);
                return "Lỗi";
            }
        }
    }
}
