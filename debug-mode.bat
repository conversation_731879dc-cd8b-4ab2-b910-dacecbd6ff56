@echo off
echo ========================================
echo   AutoGameBai Debug Mode Helper
echo ========================================

echo.
echo Current configuration:
echo - Debug Mode: ENABLED (Protection DISABLED)
echo - Build Configuration: Debug
echo - Anti-Debug Protection: OFF
echo - String Encryption: OFF
echo - Debugger Attachment: ALLOWED

echo.
echo Building in Debug mode...
dotnet build AutoGameBai.csproj --configuration Debug --no-restore

if %ERRORLEVEL% equ 0 (
    echo.
    echo ✅ Debug build completed successfully!
    echo.
    echo You can now:
    echo 1. Set breakpoints in Visual Studio
    echo 2. Use F5 to start debugging
    echo 3. Attach debugger to running process
    echo 4. Use debug tools without restrictions
    echo.
    echo Output: bin\Debug\net8.0-windows\AutoGameBai.exe
) else (
    echo.
    echo ❌ Debug build failed!
    echo Check the error messages above.
)

echo.
pause
