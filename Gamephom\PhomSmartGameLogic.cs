using System;
using System.Collections.Generic;
using System.Linq;

namespace AutoGameBai.Gamephom
{
    /// <summary>
    /// Thuật toán Phỏm thông minh - đánh giá điểm số và khả năng thắng
    /// </summary>
    public class PhomSmartGameLogic
    {
        private readonly GameClientManager _gameClient;
        private readonly PhomCardManager _phomCardManager;
        private readonly UIManager _uiManager;

        // GLOBAL VARIABLES - Lưu trữ trạng thái game để phối hợp team thông minh
        private static List<int> _listCardDaDanh = new List<int>(); // Tất cả lá đã đánh
        private static List<int> _listCardGoiYDoiThu = new List<int>(); // Bài team + bài đã đánh
        private static List<int> _listCardChuaBiet = new List<int>(); // Bài ẩn số

        public PhomSmartGameLogic(GameClientManager gameClient, PhomCardManager phomCardManager, UIManager uiManager)
        {
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _phomCardManager = phomCardManager ?? throw new ArgumentNullException(nameof(phomCardManager));
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
        }

        /// <summary>
        /// Reset tất cả biến global khi kết thúc ván bài
        /// </summary>
        public static void ResetGameState()
        {
            _listCardDaDanh.Clear();
            _listCardGoiYDoiThu.Clear();
            _listCardChuaBiet.Clear();
        }

        /// <summary>
        /// Update danh sách bài đã đánh
        /// </summary>
        public void UpdatePlayedCards(List<int> playedCards)
        {
            _listCardDaDanh = playedCards.ToList();
            _uiManager.AppendLog($"📋 Updated _listCardDaDanh: {_listCardDaDanh.Count} lá", UIManager.LogLevel.Debug);
        }

        /// <summary>
        /// Gợi ý lá bài thông minh dựa trên điểm số và khả năng thắng
        /// </summary>
        public (int suggestedCard, string analysis) SuggestSmartCard(string username, bool isFirstPlayer)
        {
            try
            {
                var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
                if (!userCardsDict.ContainsKey(username))
                {
                    return (-1, "Không tìm thấy bài của user");
                }

                var userCards = userCardsDict[username];
                bool isSolo = userCardsDict.Count == 1;

                _uiManager.AppendLog($"🧠 Smart analysis cho {username}: {(isSolo ? "Solo" : "Team")} mode", UIManager.LogLevel.Info);

                // Phân tích bài của user
                var cardAnalysis = AnalyzeUserCards(userCards);
                _uiManager.AppendLog($"📊 {username}: {cardAnalysis.phoms.Count} phỏm, {cardAnalysis.potentialPhoms.Count} cạ, {cardAnalysis.wasteCards.Count} rác", UIManager.LogLevel.Info);

                if (isSolo)
                {
                    return SuggestSoloPlay(username, userCards, cardAnalysis);
                }
                else
                {
                    // Update played cards cho global tracking
                    UpdatePlayedCards(_gameClient.GetPlayedCards());

                    // Kiểm tra next player có phải team không
                    string nextPlayer = GetNextPlayer(username);
                    bool isNextPlayerTeam = IsNextPlayerTeam(username, nextPlayer);

                    if (isNextPlayerTeam)
                    {
                        // Sử dụng GoiYbaiTeam logic mới
                        return SuggestGoiYbaiTeam(username, nextPlayer, userCards, cardAnalysis);
                    }
                    else
                    {
                        // Sử dụng GoiYDoiThu logic mới
                        return SuggestGoiYDoiThu(username, userCards, cardAnalysis);
                    }
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi Smart analysis: {ex.Message}", UIManager.LogLevel.Error);
                return (-1, $"Lỗi: {ex.Message}");
            }
        }

        /// <summary>
        /// Phân tích bài của user
        /// </summary>
        private (List<List<int>> phoms, List<List<int>> potentialPhoms, List<int> wasteCards, int totalScore) AnalyzeUserCards(int[] userCards)
        {
            var cardsInfo = userCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();
            var phoms = new List<List<int>>();
            var potentialPhoms = new List<List<int>>();
            var usedCards = new HashSet<int>();

            // 1. Tìm các phỏm hoàn chỉnh (3+ lá)
            FindCompletePhoms(cardsInfo, phoms, usedCards);

            // 2. Tìm các cạ (2 lá có thể tạo phỏm)
            var remainingCards = cardsInfo.Where(c => !usedCards.Contains(c.Id)).ToList();
            FindPotentialPhoms(remainingCards, potentialPhoms, usedCards);

            // 3. Lá bài rác (không thuộc phỏm hay cạ)
            var wasteCards = userCards.Where(c => !usedCards.Contains(c)).ToList();

            // 4. Tính điểm
            int totalScore = CalculateScore(phoms, potentialPhoms, wasteCards);

            return (phoms, potentialPhoms, wasteCards, totalScore);
        }

        /// <summary>
        /// Tìm các phỏm hoàn chỉnh
        /// </summary>
        private void FindCompletePhoms(List<CardUtilityPhom.CardInfo> cards, List<List<int>> phoms, HashSet<int> usedCards)
        {
            // Tìm phỏm sảnh (3+ lá liên tiếp cùng chất)
            var suitGroups = cards.GroupBy(c => c.Suit);
            foreach (var suitGroup in suitGroups)
            {
                var sortedCards = suitGroup.Where(c => !usedCards.Contains(c.Id)).OrderBy(c => c.Rank).ToList();
                FindStraightPhoms(sortedCards, phoms, usedCards);
            }

            // Tìm phỏm đôi/ba/tứ (3+ lá cùng rank)
            var rankGroups = cards.Where(c => !usedCards.Contains(c.Id)).GroupBy(c => c.Rank);
            foreach (var rankGroup in rankGroups)
            {
                if (rankGroup.Count() >= 3)
                {
                    var groupCards = rankGroup.Select(c => c.Id).ToList();
                    phoms.Add(groupCards);
                    foreach (var cardId in groupCards)
                    {
                        usedCards.Add(cardId);
                    }
                }
            }
        }

        /// <summary>
        /// Tìm phỏm sảnh
        /// </summary>
        private void FindStraightPhoms(List<CardUtilityPhom.CardInfo> sortedCards, List<List<int>> phoms, HashSet<int> usedCards)
        {
            for (int i = 0; i <= sortedCards.Count - 3; i++)
            {
                var consecutiveCards = new List<CardUtilityPhom.CardInfo> { sortedCards[i] };

                for (int j = i + 1; j < sortedCards.Count; j++)
                {
                    if (sortedCards[j].Rank == consecutiveCards.Last().Rank + 1)
                    {
                        consecutiveCards.Add(sortedCards[j]);
                    }
                    else
                    {
                        break;
                    }
                }

                if (consecutiveCards.Count >= 3)
                {
                    var phomCards = consecutiveCards.Select(c => c.Id).ToList();
                    phoms.Add(phomCards);
                    foreach (var cardId in phomCards)
                    {
                        usedCards.Add(cardId);
                    }
                    i += consecutiveCards.Count - 1; // Skip processed cards
                }
            }
        }

        /// <summary>
        /// Tìm các cạ (potential phoms)
        /// </summary>
        private void FindPotentialPhoms(List<CardUtilityPhom.CardInfo> cards, List<List<int>> potentialPhoms, HashSet<int> usedCards)
        {
            // Cạ đôi (2 lá cùng rank)
            var rankGroups = cards.GroupBy(c => c.Rank).Where(g => g.Count() == 2);
            foreach (var rankGroup in rankGroups)
            {
                var pairCards = rankGroup.Select(c => c.Id).ToList();
                potentialPhoms.Add(pairCards);
                foreach (var cardId in pairCards)
                {
                    usedCards.Add(cardId);
                }
            }

            // Cạ sảnh (2 lá liên tiếp cùng chất)
            var remainingCards = cards.Where(c => !usedCards.Contains(c.Id)).ToList();
            var suitGroups = remainingCards.GroupBy(c => c.Suit);
            foreach (var suitGroup in suitGroups)
            {
                var sortedCards = suitGroup.OrderBy(c => c.Rank).ToList();
                for (int i = 0; i < sortedCards.Count - 1; i++)
                {
                    if (sortedCards[i + 1].Rank - sortedCards[i].Rank == 1)
                    {
                        var straightPair = new List<int> { sortedCards[i].Id, sortedCards[i + 1].Id };
                        potentialPhoms.Add(straightPair);
                        usedCards.Add(sortedCards[i].Id);
                        usedCards.Add(sortedCards[i + 1].Id);
                        i++; // Skip next card
                    }
                }
            }
        }

        /// <summary>
        /// Tính điểm số
        /// </summary>
        private int CalculateScore(List<List<int>> phoms, List<List<int>> potentialPhoms, List<int> wasteCards)
        {
            int score = 0;

            // Phỏm hoàn chỉnh: +10 điểm mỗi phỏm
            score += phoms.Count * 10;

            // Cạ: +5 điểm mỗi cạ
            score += potentialPhoms.Count * 5;

            // Lá rác: -1 điểm mỗi lá (ưu tiên đánh lá có điểm cao)
            foreach (var cardId in wasteCards)
            {
                var cardInfo = new CardUtilityPhom.CardInfo(cardId);
                score -= Math.Max(1, cardInfo.Rank - 10); // Lá cao trừ điểm nhiều hơn
            }

            return score;
        }

        /// <summary>
        /// Gợi ý cho chế độ solo - TRÁNH đánh cạ
        /// </summary>
        private (int suggestedCard, string analysis) SuggestSoloPlay(string username, int[] userCards,
            (List<List<int>> phoms, List<List<int>> potentialPhoms, List<int> wasteCards, int totalScore) analysis)
        {
            // Tìm tất cả lá bài KHÔNG được đánh (phỏm + cạ)
            var protectedCards = new HashSet<int>();

            // Thêm tất cả lá trong phỏm hoàn chỉnh
            foreach (var phom in analysis.phoms)
            {
                foreach (var card in phom)
                {
                    protectedCards.Add(card);
                }
            }

            // Thêm tất cả lá trong cạ (potential phoms)
            foreach (var ca in analysis.potentialPhoms)
            {
                foreach (var card in ca)
                {
                    protectedCards.Add(card);
                }
            }

            // Tìm lá bài có thể đánh (không phải phỏm hay cạ)
            var playableCards = userCards.Where(c => !protectedCards.Contains(c)).ToArray();

            // Tính toán bài đã biết (bao gồm cả bài hạ phỏm)
            var playedCards = _gameClient.GetPlayedCards();
            var drawnCards = _gameClient.GetDrawnCards();
            var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();

            var knownCards = new HashSet<int>(playedCards);
            knownCards.UnionWith(drawnCards);
            foreach (var kvp in userCardsDict)
            {
                knownCards.UnionWith(kvp.Value);
            }

            // Tính hidden cards (52 - known cards)
            int hiddenCardsCount = 52 - knownCards.Count;

            _uiManager.AppendLog($"🔍 Solo analysis: {userCards.Length} tổng, {protectedCards.Count} bảo vệ, {playableCards.Length} có thể đánh, {hiddenCardsCount} ẩn", UIManager.LogLevel.Info);

            if (playableCards.Any())
            {
                // Đánh lá rác có điểm cao nhất
                var bestWasteCard = playableCards
                    .Select(c => new { Id = c, Info = new CardUtilityPhom.CardInfo(c) })
                    .OrderByDescending(c => c.Info.Rank)
                    .First();

                return (bestWasteCard.Id,
                    $"⚔️ Solo: Đánh lá rác cao ({CardUtilityPhom.ConvertCardsToString(new[] { bestWasteCard.Id })}) - Bảo vệ {protectedCards.Count} lá - Ẩn: {hiddenCardsCount}");
            }

            // Nếu không có lá rác, đánh từ cạ ít triển vọng nhất
            if (analysis.potentialPhoms.Any())
            {
                var worstCa = analysis.potentialPhoms
                    .OrderBy(ca => CalculateCaPotential(ca))
                    .First();

                var cardToPlay = worstCa
                    .Select(c => new { Id = c, Info = new CardUtilityPhom.CardInfo(c) })
                    .OrderByDescending(c => c.Info.Rank)
                    .First().Id;

                return (cardToPlay,
                    $"🎯 Solo: Đánh từ cạ ít triển vọng ({CardUtilityPhom.ConvertCardsToString(new[] { cardToPlay })}) - Điểm: {analysis.totalScore}");
            }

            // Fallback cuối cùng - đánh lá cao nhất
            var fallbackCard = userCards.OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
            return (fallbackCard, $"⚔️ Solo: Đánh lá cao nhất (fallback) - Điểm: {analysis.totalScore}");
        }

        /// <summary>
        /// Gợi ý cho chế độ team
        /// </summary>
        private (int suggestedCard, string analysis) SuggestTeamPlay(string username, int[] userCards,
            (List<List<int>> phoms, List<List<int>> potentialPhoms, List<int> wasteCards, int totalScore) analysis)
        {
            var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
            string nextPlayer = GetNextPlayer(username);
            bool nextPlayerIsTeammate = userCardsDict.ContainsKey(nextPlayer);

            if (nextPlayerIsTeammate)
            {
                // Gợi ý cho teammate
                return SuggestForTeammate(username, userCards, analysis, nextPlayer);
            }
            else
            {
                // Chống đối thủ
                return SuggestAgainstOpponent(username, userCards, analysis);
            }
        }



        /// <summary>
        /// Tính toán tiềm năng của cạ (càng cao càng có triển vọng)
        /// </summary>
        private int CalculateCaPotential(List<int> ca)
        {
            if (ca.Count != 2) return 0;

            var card1 = new CardUtilityPhom.CardInfo(ca[0]);
            var card2 = new CardUtilityPhom.CardInfo(ca[1]);

            // Cạ đôi (cùng rank) - tiềm năng cao hơn
            if (card1.Rank == card2.Rank)
            {
                // Rank thấp hơn = tiềm năng cao hơn (dễ tạo phỏm)
                return 100 - card1.Rank;
            }

            // Cạ sảnh (liên tiếp cùng chất) - tiềm năng trung bình
            if (card1.Suit == card2.Suit && Math.Abs(card1.Rank - card2.Rank) == 1)
            {
                // Sảnh ở giữa có tiềm năng cao hơn (có thể mở rộng 2 phía)
                int minRank = Math.Min(card1.Rank, card2.Rank);
                int maxRank = Math.Max(card1.Rank, card2.Rank);

                // Sảnh ở giữa (3-4, 4-5, ..., 12-13) có tiềm năng cao
                if (minRank >= 3 && maxRank <= 13)
                {
                    return 50 + (7 - Math.Abs(7 - minRank)); // Ưu tiên sảnh gần giữa
                }

                return 30; // Sảnh ở đầu/cuối
            }

            return 0; // Không phải cạ hợp lệ
        }

        /// <summary>
        /// Cải thiện gợi ý cho team mode
        /// </summary>
        private (int suggestedCard, string analysis) SuggestForTeammate(string username, int[] userCards,
            (List<List<int>> phoms, List<List<int>> potentialPhoms, List<int> wasteCards, int totalScore) analysis, string teammate)
        {
            // Tìm lá bài được bảo vệ (phỏm + cạ)
            var protectedCards = new HashSet<int>();

            foreach (var phom in analysis.phoms)
                foreach (var card in phom)
                    protectedCards.Add(card);

            foreach (var ca in analysis.potentialPhoms)
                foreach (var card in ca)
                    protectedCards.Add(card);

            var playableCards = userCards.Where(c => !protectedCards.Contains(c)).ToArray();

            // Tính toán bài đã biết (bao gồm cả bài hạ phỏm)
            var playedCards = _gameClient.GetPlayedCards();
            var drawnCards = _gameClient.GetDrawnCards();
            var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();

            var knownCards = new HashSet<int>(playedCards);
            knownCards.UnionWith(drawnCards);
            foreach (var kvp in userCardsDict)
            {
                knownCards.UnionWith(kvp.Value);
            }

            // Tính hidden cards (52 - known cards)
            int hiddenCardsCount = 52 - knownCards.Count;

            if (playableCards.Any())
            {
                // Đánh lá rác trung bình (không quá cao, không quá thấp)
                var mediumWasteCard = playableCards
                    .Select(c => new { Id = c, Info = new CardUtilityPhom.CardInfo(c) })
                    .OrderBy(c => Math.Abs(c.Info.Rank - 7)) // Gần rank 7
                    .First();

                return (mediumWasteCard.Id,
                    $"🤝 Team: Đánh lá trung bình cho {teammate} ({CardUtilityPhom.ConvertCardsToString(new[] { mediumWasteCard.Id })}) - Bảo vệ {protectedCards.Count} lá - Ẩn: {hiddenCardsCount}");
            }

            // Fallback: đánh từ cạ ít triển vọng
            if (analysis.potentialPhoms.Any())
            {
                var worstCa = analysis.potentialPhoms
                    .OrderBy(ca => CalculateCaPotential(ca))
                    .First();

                var cardToPlay = worstCa.First();
                return (cardToPlay, $"🤝 Team: Đánh từ cạ ít triển vọng cho {teammate}");
            }

            var fallbackCard = userCards.OrderBy(c => new CardUtilityPhom.CardInfo(c).Rank).First();
            return (fallbackCard, $"🤝 Team: Đánh lá nhỏ cho {teammate}");
        }

        /// <summary>
        /// Cải thiện gợi ý chống đối thủ
        /// </summary>
        private (int suggestedCard, string analysis) SuggestAgainstOpponent(string username, int[] userCards,
            (List<List<int>> phoms, List<List<int>> potentialPhoms, List<int> wasteCards, int totalScore) analysis)
        {
            // Tìm lá bài được bảo vệ
            var protectedCards = new HashSet<int>();

            foreach (var phom in analysis.phoms)
                foreach (var card in phom)
                    protectedCards.Add(card);

            foreach (var ca in analysis.potentialPhoms)
                foreach (var card in ca)
                    protectedCards.Add(card);

            var playableCards = userCards.Where(c => !protectedCards.Contains(c)).ToArray();

            // Tính toán bài đã biết (bao gồm cả bài hạ phỏm)
            var playedCards = _gameClient.GetPlayedCards();
            var drawnCards = _gameClient.GetDrawnCards();
            var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();

            var knownCards = new HashSet<int>(playedCards);
            knownCards.UnionWith(drawnCards);
            foreach (var kvp in userCardsDict)
            {
                knownCards.UnionWith(kvp.Value);
            }

            // Tính hidden cards (52 - known cards)
            int hiddenCardsCount = 52 - knownCards.Count;

            if (playableCards.Any())
            {
                // Đánh lá rác cao nhất để tránh cho đối thủ lá tốt
                var highestWasteCard = playableCards
                    .Select(c => new { Id = c, Info = new CardUtilityPhom.CardInfo(c) })
                    .OrderByDescending(c => c.Info.Rank)
                    .First();

                return (highestWasteCard.Id,
                    $"⚔️ Vs Opponent: Đánh lá rác cao ({CardUtilityPhom.ConvertCardsToString(new[] { highestWasteCard.Id })}) - Bảo vệ {protectedCards.Count} lá - Ẩn: {hiddenCardsCount}");
            }

            // Fallback: đánh từ cạ ít triển vọng
            if (analysis.potentialPhoms.Any())
            {
                var worstCa = analysis.potentialPhoms
                    .OrderBy(ca => CalculateCaPotential(ca))
                    .First();

                var cardToPlay = worstCa
                    .Select(c => new { Id = c, Info = new CardUtilityPhom.CardInfo(c) })
                    .OrderByDescending(c => c.Info.Rank)
                    .First().Id;

                return (cardToPlay, $"⚔️ Vs Opponent: Đánh từ cạ ít triển vọng - Ẩn: {hiddenCardsCount}");
            }

            var fallbackCard = userCards.OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
            return (fallbackCard, $"⚔️ Vs Opponent: Đánh lá cao nhất - Ẩn: {hiddenCardsCount}");
        }

        /// <summary>
        /// Lấy người chơi tiếp theo
        /// </summary>
        private string GetNextPlayer(string currentPlayer)
        {
            var playerOrder = _gameClient.GetWebSocketManager().GetPhomHandler().GetPlayerOrder();
            if (playerOrder.Count == 0) return "";

            int currentIndex = playerOrder.IndexOf(currentPlayer);
            if (currentIndex == -1) return "";

            int nextIndex = (currentIndex + 1) % playerOrder.Count;
            return playerOrder[nextIndex];
        }

        /// <summary>
        /// Kiểm tra next player có phải team không
        /// </summary>
        private bool IsNextPlayerTeam(string currentPlayer, string nextPlayer)
        {
            var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();

            // Nếu next player có trong userCardsDict thì là team
            bool isTeam = userCardsDict.ContainsKey(nextPlayer);

            _uiManager.AppendLog($"🤝 Next player {nextPlayer} is {(isTeam ? "TEAM" : "OPPONENT")}", UIManager.LogLevel.Info);

            return isTeam;
        }

        /// <summary>
        /// LOGIC GOIYBAI TEAM - Gợi ý để nhường bài cho team
        /// </summary>
        private (int suggestedCard, string analysis) SuggestGoiYbaiTeam(string username, string nextPlayer, int[] userCards,
            (List<List<int>> phoms, List<List<int>> potentialPhoms, List<int> wasteCards, int totalScore) userAnalysis)
        {
            try
            {
                _uiManager.AppendLog($"🤝 Bắt đầu GoiYbaiTeam: {username} → {nextPlayer}", UIManager.LogLevel.Info);

                // BƯỚC 1: Lấy bài của team player
                var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
                if (!userCardsDict.ContainsKey(nextPlayer))
                {
                    return (-1, "Không tìm thấy bài của team player");
                }

                var teamPlayerCards = userCardsDict[nextPlayer];
                _uiManager.AppendLog($"📋 Team player {nextPlayer} có {teamPlayerCards.Length} lá bài", UIManager.LogLevel.Info);

                // BƯỚC 2: Tìm cạ của team player
                var teamPlayerCa = FindCaInCards(teamPlayerCards.ToList());
                _uiManager.AppendLog($"🔍 Tìm thấy {teamPlayerCa.Count} cạ của team player {nextPlayer}", UIManager.LogLevel.Info);

                // BƯỚC 3: Tìm lá bài user có thể nhường để tạo phỏm cho team player
                var (cardToHelp, isUserCa, helpAnalysis) = FindCardToHelpTeamPlayer(userCards, teamPlayerCa, userAnalysis.potentialPhoms);
                if (cardToHelp != -1)
                {
                    string warningText = isUserCa ? "Lá Bài 'Cạ'!" : "";
                    return (cardToHelp, $"Đánh {CardUtilityPhom.ConvertCardsToString(new[] { cardToHelp })}, Nhường bài tạo phỏm cho {nextPlayer}|{warningText}");
                }

                // BƯỚC 4: Fallback - Gợi ý lá rác cao nhất không phải cạ
                var wasteCard = FindBestWasteCardForTeam(userCards, userAnalysis);
                return (wasteCard.cardId, $"Đánh {CardUtilityPhom.ConvertCardsToString(new[] { wasteCard.cardId })}, Lá rác cao cho team|{(wasteCard.isUserCa ? "Lá Bài 'Cạ'!" : "")}");
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GoiYbaiTeam: {ex.Message}", UIManager.LogLevel.Error);
                return (-1, $"Lỗi: {ex.Message}");
            }
        }

        /// <summary>
        /// Tìm lá bài tạo phỏm cho team player (ưu tiên lá không phải cạ của user, CHỈ TỪ CHỐI KHI CÓ 2 PHỎM SẮP Ù)
        /// </summary>
        private (int cardId, bool isUserCa, string analysis) FindCardToHelpTeamPlayer(int[] userCards, List<CardUtilityPhom.CardInfo> teamPlayerCa, List<List<int>> userCa)
        {
            var userCardsInfo = userCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();
            var userCaFlat = userCa.SelectMany(ca => ca).ToHashSet();

            // Kiểm tra user có 2 phỏm sắp ù không (chỉ khi đó mới từ chối nhường cạ)
            bool userHasTwoNearWinPhoms = userCa.Count >= 2;
            _uiManager.AppendLog($"🤝 User có {userCa.Count} cạ, {(userHasTwoNearWinPhoms ? "CÓ THỂ từ chối nhường cạ" : "SẴN SÀNG nhường cạ")}", UIManager.LogLevel.Info);

            var candidateCards = new List<(int cardId, bool isUserCa, string reason)>();

            foreach (var userCard in userCardsInfo)
            {
                foreach (var ca in teamPlayerCa)
                {
                    // Kiểm tra có thể tạo phỏm ngang (đôi/ba)
                    if (userCard.Rank == ca.Rank)
                    {
                        bool isUserCa = userCaFlat.Contains(userCard.Id);
                        candidateCards.Add((userCard.Id, isUserCa, $"Tạo phỏm ngang với {CardUtilityPhom.GetRankName(ca.Rank)} của team"));
                    }

                    // Kiểm tra có thể tạo phỏm dọc (sảnh)
                    if (userCard.Suit == ca.Suit)
                    {
                        var value1 = CardUtilityPhom.GetCardValue(userCard.Rank);
                        var value2 = CardUtilityPhom.GetCardValue(ca.Rank);
                        var diff = Math.Abs(value2 - value1);

                        if (diff == 1 || diff == 2 || (value1 == 1 && value2 == 13) || (value1 == 13 && value2 == 1))
                        {
                            bool isUserCa = userCaFlat.Contains(userCard.Id);
                            candidateCards.Add((userCard.Id, isUserCa, $"Tạo phỏm dọc với {CardUtilityPhom.GetRankName(ca.Rank)} của team"));
                        }
                    }
                }
            }

            if (candidateCards.Any())
            {
                // LOGIC MỚI: Ưu tiên lá KHÔNG phải cạ của user
                var nonUserCa = candidateCards.Where(c => !c.isUserCa).ToList();
                if (nonUserCa.Any())
                {
                    var selected = nonUserCa.First();
                    _uiManager.AppendLog($"🤝 Chọn lá nhường team (không phải cạ): {CardUtilityPhom.ConvertCardsToString(new[] { selected.cardId })}", UIManager.LogLevel.Info);
                    return (selected.cardId, selected.isUserCa, selected.reason);
                }

                // LOGIC MỚI: Nếu user có 2+ phỏm sắp ù thì từ chối nhường cạ
                if (userHasTwoNearWinPhoms)
                {
                    _uiManager.AppendLog($"🤝 TỪ CHỐI nhường cạ vì user có {userCa.Count} phỏm sắp ù", UIManager.LogLevel.Info);
                    return (-1, false, "Từ chối nhường cạ - user sắp ù");
                }

                // Nếu user chỉ có ít hơn 2 phỏm sắp ù thì vẫn nhường cạ cho team
                var selected2 = candidateCards.First();
                _uiManager.AppendLog($"🤝 Chọn lá nhường team (là cạ user - nhưng vẫn nhường vì chưa đủ 2 phỏm): {CardUtilityPhom.ConvertCardsToString(new[] { selected2.cardId })}", UIManager.LogLevel.Info);
                return (selected2.cardId, selected2.isUserCa, selected2.reason);
            }

            return (-1, false, "");
        }

        /// <summary>
        /// Tìm lá rác tốt nhất cho team (rank cao nhất, ưu tiên không phải cạ của user)
        /// </summary>
        private (int cardId, bool isUserCa) FindBestWasteCardForTeam(int[] userCards, (List<List<int>> phoms, List<List<int>> potentialPhoms, List<int> wasteCards, int totalScore) userAnalysis)
        {
            var userCaFlat = userAnalysis.potentialPhoms.SelectMany(ca => ca).ToHashSet();
            var protectedCards = new HashSet<int>();

            // Thêm tất cả lá trong phỏm hoàn chỉnh
            foreach (var phom in userAnalysis.phoms)
                foreach (var card in phom)
                    protectedCards.Add(card);

            // Tìm lá rác (không thuộc phỏm)
            var wasteCards = userCards.Where(c => !protectedCards.Contains(c)).ToList();

            if (wasteCards.Any())
            {
                // Ưu tiên lá rác KHÔNG phải cạ của user, rank cao nhất
                var nonUserCa = wasteCards.Where(c => !userCaFlat.Contains(c)).ToList();
                if (nonUserCa.Any())
                {
                    var selected = nonUserCa.OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
                    _uiManager.AppendLog($"🤝 Chọn lá rác cho team (không phải cạ): {CardUtilityPhom.ConvertCardsToString(new[] { selected })}", UIManager.LogLevel.Info);
                    return (selected, false);
                }

                // Nếu không có, chọn lá rác là cạ của user, rank cao nhất
                var selected2 = wasteCards.OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
                _uiManager.AppendLog($"🤝 Chọn lá rác cho team (là cạ user): {CardUtilityPhom.ConvertCardsToString(new[] { selected2 })}", UIManager.LogLevel.Info);
                return (selected2, userCaFlat.Contains(selected2));
            }

            // Fallback: chọn từ cạ của user
            if (userAnalysis.potentialPhoms.Any())
            {
                var caCard = userAnalysis.potentialPhoms.First().OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
                _uiManager.AppendLog($"🤝 Fallback chọn từ cạ user: {CardUtilityPhom.ConvertCardsToString(new[] { caCard })}", UIManager.LogLevel.Info);
                return (caCard, true);
            }

            // Fallback cuối: lá cao nhất
            var fallback = userCards.OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
            _uiManager.AppendLog($"🤝 Fallback cuối: {CardUtilityPhom.ConvertCardsToString(new[] { fallback })}", UIManager.LogLevel.Info);
            return (fallback, userCaFlat.Contains(fallback));
        }

        /// <summary>
        /// Tạo ListCardGoiYDoiThu: bài đã đánh (all users) + bài user hiện tại + bài team (nếu có)
        /// </summary>
        private List<int> BuildListCardGoiYDoiThu(string username)
        {
            var listCardGoiYDoiThu = new List<int>();

            // 1. Bài đã đánh của tất cả users (sử dụng global variable)
            listCardGoiYDoiThu.AddRange(_listCardDaDanh);

            // 2. Bài của user hiện tại
            var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();
            if (userCardsDict.ContainsKey(username))
            {
                listCardGoiYDoiThu.AddRange(userCardsDict[username]);
            }

            // 3. Bài của team (nếu có)
            foreach (var kvp in userCardsDict)
            {
                if (kvp.Key != username) // Bài của teammates
                {
                    listCardGoiYDoiThu.AddRange(kvp.Value);
                }
            }

            // Update global variable
            _listCardGoiYDoiThu = listCardGoiYDoiThu.Distinct().ToList();

            _uiManager.AppendLog($"📋 BuildListCardGoiYDoiThu: {_listCardDaDanh.Count} đã đánh + {userCardsDict[username].Length} user + {userCardsDict.Where(x => x.Key != username).Sum(x => x.Value.Length)} team = {_listCardGoiYDoiThu.Count} tổng", UIManager.LogLevel.Debug);

            return _listCardGoiYDoiThu;
        }

        /// <summary>
        /// Tạo ListCardChuaBiet: 52 lá - ListCardGoiYDoiThu
        /// </summary>
        private List<int> BuildListCardChuaBiet(List<int> listCardGoiYDoiThu)
        {
            var allCards = Enumerable.Range(1, 52).ToList();
            var listCardChuaBiet = allCards.Except(listCardGoiYDoiThu).ToList();

            // Update global variable
            _listCardChuaBiet = listCardChuaBiet;

            _uiManager.AppendLog($"🎲 BuildListCardChuaBiet: 52 - {listCardGoiYDoiThu.Count} = {_listCardChuaBiet.Count} lá chưa biết", UIManager.LogLevel.Debug);

            return _listCardChuaBiet;
        }

        /// <summary>
        /// Tìm cạ trong danh sách bài - LOGIC MỚI: TÌM TẤT CẢ CẠ NGANG VÀ CẠ DỌC
        /// </summary>
        private List<CardUtilityPhom.CardInfo> FindCaInCards(List<int> cards)
        {
            var cardInfos = cards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();
            var allCaCards = new List<CardUtilityPhom.CardInfo>();

            _uiManager.AppendLog($"🔍 Tìm cạ trong {cards.Count} lá: {string.Join(",", cards)}", UIManager.LogLevel.Debug);

            // 1. CẠ NGANG (đôi - 2 lá cùng rank) - TÌM TẤT CẢ CẶP
            var rankGroups = cardInfos.GroupBy(c => c.Rank).ToList();
            foreach (var rankGroup in rankGroups)
            {
                if (rankGroup.Count() >= 2)
                {
                    var cardsInRank = rankGroup.ToList();

                    // Tìm tất cả cặp trong cùng rank
                    for (int i = 0; i < cardsInRank.Count - 1; i++)
                    {
                        for (int j = i + 1; j < cardsInRank.Count; j++)
                        {
                            allCaCards.Add(cardsInRank[i]);
                            allCaCards.Add(cardsInRank[j]);
                            _uiManager.AppendLog($"✅ Cạ ngang: {CardUtilityPhom.GetRankName(rankGroup.Key)} {GetSuitSymbol(cardsInRank[i].Suit)}-{GetSuitSymbol(cardsInRank[j].Suit)}", UIManager.LogLevel.Debug);
                        }
                    }
                }
            }

            // 2. CẠ DỌC (sảnh - 2 lá có thể tạo sảnh cùng chất) - TÌM TẤT CẢ CẶP
            var suitGroups = cardInfos.GroupBy(c => c.Suit).ToList();
            foreach (var suitGroup in suitGroups)
            {
                if (suitGroup.Count() >= 2)
                {
                    var sortedCards = suitGroup.OrderBy(c => CardUtilityPhom.GetCardValue(c.Rank)).ToList();

                    // Tìm tất cả cặp có thể tạo sảnh trong cùng suit
                    for (int i = 0; i < sortedCards.Count - 1; i++)
                    {
                        for (int j = i + 1; j < sortedCards.Count; j++)
                        {
                            var card1 = sortedCards[i];
                            var card2 = sortedCards[j];

                            var value1 = CardUtilityPhom.GetCardValue(card1.Rank);
                            var value2 = CardUtilityPhom.GetCardValue(card2.Rank);
                            var diff = Math.Abs(value2 - value1);

                            bool canFormSequence = false;
                            string sequenceType = "";

                            if (diff == 1)
                            {
                                canFormSequence = true;
                                sequenceType = "liên tiếp";
                            }
                            else if (diff == 2)
                            {
                                canFormSequence = true;
                                sequenceType = "cách 1 lá";
                            }
                            else if ((value1 == 1 && value2 == 13) || (value1 == 13 && value2 == 1))
                            {
                                canFormSequence = true;
                                sequenceType = "A-K";
                            }

                            if (canFormSequence)
                            {
                                allCaCards.Add(card1);
                                allCaCards.Add(card2);
                                _uiManager.AppendLog($"✅ Cạ dọc ({sequenceType}): {GetSuitSymbol(card1.Suit)} {CardUtilityPhom.GetRankName(card1.Rank)}-{CardUtilityPhom.GetRankName(card2.Rank)}", UIManager.LogLevel.Debug);
                            }
                        }
                    }
                }
            }

            var uniqueCaCards = allCaCards.Distinct().ToList();
            _uiManager.AppendLog($"🔍 Tổng cộng tìm thấy {uniqueCaCards.Count} lá thuộc cạ", UIManager.LogLevel.Debug);

            return uniqueCaCards;
        }

        /// <summary>
        /// Helper method để lấy ký hiệu suit
        /// </summary>
        private string GetSuitSymbol(int suit)
        {
            return suit switch
            {
                0 => "♠",
                1 => "♥",
                2 => "♦",
                3 => "♣",
                _ => "?"
            };
        }

        /// <summary>
        /// Tìm lá bài tạo phỏm cho các cạ (ưu tiên lá không phải cạ của user) - VỚI DEBUG LOGS CHI TIẾT
        /// </summary>
        private (int cardId, bool isUserCa, string analysis) FindCardToHelpCa(int[] userCards, List<CardUtilityPhom.CardInfo> caCards, List<List<int>> userCa)
        {
            var userCardsInfo = userCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();
            var userCaFlat = userCa.SelectMany(ca => ca).ToHashSet();

            _uiManager.AppendLog($"🔍 FindCardToHelpCa: Kiểm tra {userCards.Length} lá user với {caCards.Count} lá cạ đối thủ", UIManager.LogLevel.Info);

            var candidateCards = new List<(int cardId, bool isUserCa, string reason)>();

            foreach (var userCard in userCardsInfo)
            {
                foreach (var ca in caCards)
                {
                    // Kiểm tra có thể tạo phỏm ngang (đôi/ba)
                    if (userCard.Rank == ca.Rank)
                    {
                        bool isUserCa = userCaFlat.Contains(userCard.Id);
                        string cardName = $"{CardUtilityPhom.GetRankName(userCard.Rank)}{GetSuitSymbol(userCard.Suit)}";
                        string caName = $"{CardUtilityPhom.GetRankName(ca.Rank)}{GetSuitSymbol(ca.Suit)}";
                        candidateCards.Add((userCard.Id, isUserCa, $"Tạo phỏm ngang với {caName}"));
                        _uiManager.AppendLog($"⚠️ PHÁT HIỆN: {cardName} tạo phỏm ngang với cạ {caName} của đối thủ!", UIManager.LogLevel.Info);
                    }

                    // Kiểm tra có thể tạo phỏm dọc (sảnh)
                    if (userCard.Suit == ca.Suit)
                    {
                        var value1 = CardUtilityPhom.GetCardValue(userCard.Rank);
                        var value2 = CardUtilityPhom.GetCardValue(ca.Rank);
                        var diff = Math.Abs(value2 - value1);

                        if (diff == 1 || diff == 2 || (value1 == 1 && value2 == 13) || (value1 == 13 && value2 == 1))
                        {
                            bool isUserCa = userCaFlat.Contains(userCard.Id);
                            string cardName = $"{CardUtilityPhom.GetRankName(userCard.Rank)}{GetSuitSymbol(userCard.Suit)}";
                            string caName = $"{CardUtilityPhom.GetRankName(ca.Rank)}{GetSuitSymbol(ca.Suit)}";
                            string diffType = diff == 1 ? "liên tiếp" : diff == 2 ? "cách 1 lá" : "A-K";
                            candidateCards.Add((userCard.Id, isUserCa, $"Tạo phỏm dọc với {caName}"));
                            _uiManager.AppendLog($"⚠️ PHÁT HIỆN: {cardName} tạo phỏm dọc ({diffType}) với cạ {caName} của đối thủ!", UIManager.LogLevel.Info);
                        }
                    }
                }
            }

            if (candidateCards.Any())
            {
                _uiManager.AppendLog($"⚠️ Tổng cộng {candidateCards.Count} lá user có thể giúp đối thủ tạo phỏm!", UIManager.LogLevel.Info);

                // Ưu tiên lá KHÔNG phải cạ của user
                var nonUserCa = candidateCards.Where(c => !c.isUserCa).ToList();
                if (nonUserCa.Any())
                {
                    var selected = nonUserCa.First();
                    string cardName = CardUtilityPhom.ConvertCardsToString(new[] { selected.cardId });
                    _uiManager.AppendLog($"🎯 Chọn lá giúp đối thủ (không phải cạ user): {cardName} - {selected.reason}", UIManager.LogLevel.Info);
                    return (selected.cardId, selected.isUserCa, selected.reason);
                }

                // Nếu không có, chọn lá cạ của user
                var selected2 = candidateCards.First();
                string cardName2 = CardUtilityPhom.ConvertCardsToString(new[] { selected2.cardId });
                _uiManager.AppendLog($"🎯 Chọn lá giúp đối thủ (từ cạ user): {cardName2} - {selected2.reason}", UIManager.LogLevel.Info);
                return (selected2.cardId, selected2.isUserCa, selected2.reason);
            }

            _uiManager.AppendLog($"✅ Không tìm thấy lá nào giúp đối thủ tạo phỏm", UIManager.LogLevel.Info);
            return (-1, false, "");
        }

        /// <summary>
        /// Tìm lá bài KHÔNG tạo phỏm cho các cạ ẩn (ưu tiên lá không phải cạ của user)
        /// </summary>
        private (int cardId, bool isUserCa, string analysis) FindCardToAvoidCa(int[] userCards, List<CardUtilityPhom.CardInfo> caCards, List<List<int>> userCa)
        {
            var userCardsInfo = userCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();
            var userCaFlat = userCa.SelectMany(ca => ca).ToHashSet();

            var safeCards = new List<(int cardId, bool isUserCa)>();

            foreach (var userCard in userCardsInfo)
            {
                bool isSafe = true;

                foreach (var ca in caCards)
                {
                    // Kiểm tra có tạo phỏm ngang không
                    if (userCard.Rank == ca.Rank)
                    {
                        isSafe = false;
                        break;
                    }

                    // Kiểm tra có tạo phỏm dọc không
                    if (userCard.Suit == ca.Suit)
                    {
                        var value1 = CardUtilityPhom.GetCardValue(userCard.Rank);
                        var value2 = CardUtilityPhom.GetCardValue(ca.Rank);
                        var diff = Math.Abs(value2 - value1);

                        if (diff == 1 || diff == 2 || (value1 == 1 && value2 == 13) || (value1 == 13 && value2 == 1))
                        {
                            isSafe = false;
                            break;
                        }
                    }
                }

                if (isSafe)
                {
                    bool isUserCa = userCaFlat.Contains(userCard.Id);
                    safeCards.Add((userCard.Id, isUserCa));
                }
            }

            if (safeCards.Any())
            {
                // Ưu tiên lá KHÔNG phải cạ của user
                var nonUserCa = safeCards.Where(c => !c.isUserCa).ToList();
                if (nonUserCa.Any())
                {
                    var selected = nonUserCa.OrderByDescending(c => new CardUtilityPhom.CardInfo(c.cardId).Rank).First();
                    return (selected.cardId, selected.isUserCa, "An toàn, không tạo phỏm cho cạ ẩn");
                }

                // Nếu không có, chọn lá cạ của user có rank cao nhất
                var selected2 = safeCards.OrderByDescending(c => new CardUtilityPhom.CardInfo(c.cardId).Rank).First();
                return (selected2.cardId, selected2.isUserCa, "An toàn, không tạo phỏm cho cạ ẩn");
            }

            return (-1, false, "");
        }

        /// <summary>
        /// Loại bỏ cạ không tiềm năng dựa trên bài đã đánh VÀ PHỎM CỦA OPPONENTS
        /// </summary>
        private List<List<int>> FilterViableCa(List<List<int>> userCa, string currentUsername)
        {
            var viableCa = new List<List<int>>();

            // BƯỚC 1: Tính toán listcard theo vị trí player
            var listCardToCheck = BuildListCardToCheckForViability(currentUsername);
            _uiManager.AppendLog($"🔍 Checking viability against {listCardToCheck.Count} cards", UIManager.LogLevel.Debug);

            foreach (var ca in userCa)
            {
                if (ca.Count < 2) continue;

                var card1 = new CardUtilityPhom.CardInfo(ca[0]);
                var card2 = new CardUtilityPhom.CardInfo(ca[1]);

                bool isViable = true;
                string reason = "";

                // Kiểm tra cạ ngang (đôi)
                if (card1.Rank == card2.Rank)
                {
                    isViable = CheckViabilityForPairCa(card1.Rank, listCardToCheck, out reason);
                }
                // Kiểm tra cạ dọc (sảnh)
                else if (card1.Suit == card2.Suit)
                {
                    isViable = CheckViabilityForSequenceCa(card1, card2, listCardToCheck, out reason);
                }

                if (isViable)
                {
                    viableCa.Add(ca);
                    _uiManager.AppendLog($"✅ Cạ khả thi: {string.Join(",", ca.Select(c => CardUtilityPhom.ConvertCardsToString(new[] { c })))}", UIManager.LogLevel.Debug);
                }
                else
                {
                    _uiManager.AppendLog($"❌ Loại bỏ cạ: {reason}", UIManager.LogLevel.Debug);
                }
            }

            return viableCa;
        }

        /// <summary>
        /// Tính toán listcard để kiểm tra viability theo vị trí player
        /// </summary>
        private List<int> BuildListCardToCheckForViability(string currentUsername)
        {
            var listCardToCheck = new List<int>();
            var playerOrder = _gameClient.GetWebSocketManager().GetPhomHandler().GetPlayerOrder();
            var userCardsDict = _gameClient.GetWebSocketManager().GetPhomHandler().GetUserCards();

            if (playerOrder.Count == 0) return listCardToCheck;

            int currentIndex = playerOrder.IndexOf(currentUsername);
            if (currentIndex == -1) return listCardToCheck;

            _uiManager.AppendLog($"🎯 Current player: {currentUsername} (index {currentIndex})", UIManager.LogLevel.Debug);

            // 1. Luôn thêm bài đã đánh
            listCardToCheck.AddRange(_listCardDaDanh);

            // 2. Thêm bài của players KHÔNG THỂ thả bài cho current user
            for (int i = 0; i < playerOrder.Count; i++)
            {
                string playerName = playerOrder[i];

                // Skip current user
                if (playerName == currentUsername) continue;

                // Tính toán xem player này có thể thả bài cho current user không
                bool canGiveCard = CanPlayerGiveCardTo(i, currentIndex, playerOrder.Count);

                if (!canGiveCard)
                {
                    // Thêm bài của player này vào list để check
                    if (userCardsDict.ContainsKey(playerName))
                    {
                        listCardToCheck.AddRange(userCardsDict[playerName]);
                        _uiManager.AppendLog($"🚫 Player {playerName} KHÔNG THỂ thả bài cho {currentUsername} - thêm vào check list", UIManager.LogLevel.Debug);
                    }
                }
                else
                {
                    _uiManager.AppendLog($"✅ Player {playerName} CÓ THỂ thả bài cho {currentUsername} - kiểm tra phỏm", UIManager.LogLevel.Debug);

                    // Kiểm tra phỏm của player này và thêm lá trong phỏm vào check list
                    if (userCardsDict.ContainsKey(playerName))
                    {
                        var playerCards = userCardsDict[playerName];
                        var playerPhoms = DetectPlayerPhoms(playerCards);

                        foreach (var phom in playerPhoms)
                        {
                            listCardToCheck.AddRange(phom);
                            _uiManager.AppendLog($"🔒 Phỏm của {playerName}: {string.Join(",", phom.Select(c => CardUtilityPhom.ConvertCardsToString(new[] { c })))} - KHÔNG THỂ thả", UIManager.LogLevel.Debug);
                        }
                    }
                }
            }

            return listCardToCheck.Distinct().ToList();
        }

        /// <summary>
        /// Kiểm tra player có thể thả bài cho target player không (theo thứ tự chơi)
        /// </summary>
        private bool CanPlayerGiveCardTo(int playerIndex, int targetIndex, int totalPlayers)
        {
            // Player chỉ có thể thả bài cho player TIẾP THEO trong thứ tự chơi
            int nextIndex = (playerIndex + 1) % totalPlayers;
            return nextIndex == targetIndex;
        }

        /// <summary>
        /// Phát hiện phỏm của player (3+ lá liên tiếp hoặc cùng rank)
        /// </summary>
        private List<List<int>> DetectPlayerPhoms(int[] playerCards)
        {
            var phoms = new List<List<int>>();
            var cardInfos = playerCards.Select(c => new CardUtilityPhom.CardInfo(c)).ToList();

            // Tìm phỏm ngang (3+ lá cùng rank)
            var rankGroups = cardInfos.GroupBy(c => c.Rank).ToList();
            foreach (var rankGroup in rankGroups)
            {
                if (rankGroup.Count() >= 3)
                {
                    phoms.Add(rankGroup.Select(c => c.Id).ToList());
                }
            }

            // Tìm phỏm dọc (3+ lá liên tiếp cùng suit)
            var suitGroups = cardInfos.GroupBy(c => c.Suit).ToList();
            foreach (var suitGroup in suitGroups)
            {
                if (suitGroup.Count() >= 3)
                {
                    var sortedCards = suitGroup.OrderBy(c => CardUtilityPhom.GetCardValue(c.Rank)).ToList();
                    var sequences = FindSequences(sortedCards);
                    phoms.AddRange(sequences);
                }
            }

            return phoms;
        }

        /// <summary>
        /// Tìm sequences trong sorted cards
        /// </summary>
        private List<List<int>> FindSequences(List<CardUtilityPhom.CardInfo> sortedCards)
        {
            var sequences = new List<List<int>>();
            var currentSequence = new List<CardUtilityPhom.CardInfo>();

            for (int i = 0; i < sortedCards.Count; i++)
            {
                if (currentSequence.Count == 0)
                {
                    currentSequence.Add(sortedCards[i]);
                }
                else
                {
                    var lastCard = currentSequence.Last();
                    var currentCard = sortedCards[i];
                    var lastValue = CardUtilityPhom.GetCardValue(lastCard.Rank);
                    var currentValue = CardUtilityPhom.GetCardValue(currentCard.Rank);

                    if (currentValue == lastValue + 1)
                    {
                        currentSequence.Add(currentCard);
                    }
                    else
                    {
                        if (currentSequence.Count >= 3)
                        {
                            sequences.Add(currentSequence.Select(c => c.Id).ToList());
                        }
                        currentSequence = new List<CardUtilityPhom.CardInfo> { currentCard };
                    }
                }
            }

            if (currentSequence.Count >= 3)
            {
                sequences.Add(currentSequence.Select(c => c.Id).ToList());
            }

            return sequences;
        }

        /// <summary>
        /// Kiểm tra viability cho cạ ngang (đôi)
        /// </summary>
        private bool CheckViabilityForPairCa(int rank, List<int> listCardToCheck, out string reason)
        {
            reason = "";

            // Đếm số lá cùng rank chưa bị block
            int availableCount = 0;
            for (int suit = 0; suit < 4; suit++)
            {
                int cardId = (rank - 1) * 4 + suit + 1;
                if (!listCardToCheck.Contains(cardId))
                {
                    availableCount++;
                }
            }

            if (availableCount < 3) // Cần ít nhất 3 lá (2 trong tay + 1 available)
            {
                reason = $"Cạ ngang {CardUtilityPhom.GetRankName(rank)} không tiềm năng - chỉ còn {availableCount} lá available";
                return false;
            }

            return true;
        }

        /// <summary>
        /// Kiểm tra viability cho cạ dọc (sảnh)
        /// </summary>
        private bool CheckViabilityForSequenceCa(CardUtilityPhom.CardInfo card1, CardUtilityPhom.CardInfo card2, List<int> listCardToCheck, out string reason)
        {
            reason = "";

            var value1 = CardUtilityPhom.GetCardValue(card1.Rank);
            var value2 = CardUtilityPhom.GetCardValue(card2.Rank);
            var diff = Math.Abs(value2 - value1);

            if (diff == 1) // Liên tiếp - cần thêm 1 lá ở đầu hoặc cuối
            {
                int minValue = Math.Min(value1, value2);
                int maxValue = Math.Max(value1, value2);

                // Kiểm tra lá trước (minValue - 1)
                bool canExtendBefore = false;
                if (minValue > 1)
                {
                    int beforeRank = minValue - 1;
                    int beforeCardId = (beforeRank - 1) * 4 + card1.Suit + 1;
                    canExtendBefore = !listCardToCheck.Contains(beforeCardId);
                }

                // Kiểm tra lá sau (maxValue + 1)
                bool canExtendAfter = false;
                if (maxValue < 13)
                {
                    int afterRank = maxValue + 1;
                    int afterCardId = (afterRank - 1) * 4 + card1.Suit + 1;
                    canExtendAfter = !listCardToCheck.Contains(afterCardId);
                }

                if (!canExtendBefore && !canExtendAfter)
                {
                    reason = $"Cạ dọc liên tiếp {GetSuitSymbol(card1.Suit)} {CardUtilityPhom.GetRankName(card1.Rank)}-{CardUtilityPhom.GetRankName(card2.Rank)} không tiềm năng - không thể mở rộng";
                    return false;
                }
            }
            else if (diff == 2) // Cách 1 lá - cần lá giữa
            {
                int minValue = Math.Min(value1, value2);
                int maxValue = Math.Max(value1, value2);
                int middleRank = (minValue + maxValue) / 2;
                int middleCardId = (middleRank - 1) * 4 + card1.Suit + 1;

                if (listCardToCheck.Contains(middleCardId))
                {
                    reason = $"Cạ dọc cách 1 lá {GetSuitSymbol(card1.Suit)} {CardUtilityPhom.GetRankName(card1.Rank)}-{CardUtilityPhom.GetRankName(card2.Rank)} không tiềm năng - lá giữa {CardUtilityPhom.GetRankName(middleRank)} bị block";
                    return false;
                }
            }
            else if ((value1 == 1 && value2 == 13) || (value1 == 13 && value2 == 1)) // A-K
            {
                // Kiểm tra Q có available không
                int queenCardId = (12 - 1) * 4 + card1.Suit + 1; // Q = rank 12
                if (listCardToCheck.Contains(queenCardId))
                {
                    reason = $"Cạ dọc A-K {GetSuitSymbol(card1.Suit)} không tiềm năng - Q bị block";
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Tìm lá thay thế để tránh giúp đối thủ
        /// </summary>
        private int FindAlternativeCardToAvoidHelping(int[] userCards, int cardToAvoid, (List<List<int>> phoms, List<List<int>> potentialPhoms, List<int> wasteCards, int totalScore) userAnalysis)
        {
            var protectedCards = new HashSet<int>();

            // Thêm tất cả lá trong phỏm hoàn chỉnh
            foreach (var phom in userAnalysis.phoms)
                foreach (var card in phom)
                    protectedCards.Add(card);

            // Thêm lá cần tránh
            protectedCards.Add(cardToAvoid);

            // Tìm lá có thể đánh (không thuộc phỏm và không phải lá cần tránh)
            var availableCards = userCards.Where(c => !protectedCards.Contains(c)).ToList();

            if (availableCards.Any())
            {
                // Ưu tiên lá không phải cạ của user
                var userCaFlat = userAnalysis.potentialPhoms.SelectMany(ca => ca).ToHashSet();
                var nonCaCards = availableCards.Where(c => !userCaFlat.Contains(c)).ToList();

                if (nonCaCards.Any())
                {
                    var selected = nonCaCards.OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
                    _uiManager.AppendLog($"🔄 Chọn lá thay thế (không phải cạ): {CardUtilityPhom.ConvertCardsToString(new[] { selected })}", UIManager.LogLevel.Info);
                    return selected;
                }

                // Nếu không có, chọn từ cạ của user
                var selected2 = availableCards.OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
                _uiManager.AppendLog($"🔄 Chọn lá thay thế (từ cạ): {CardUtilityPhom.ConvertCardsToString(new[] { selected2 })}", UIManager.LogLevel.Info);
                return selected2;
            }

            _uiManager.AppendLog($"⚠️ Không tìm thấy lá thay thế, buộc phải đánh lá giúp đối thủ", UIManager.LogLevel.Warning);
            return -1;
        }

        /// <summary>
        /// Tìm lá rác tốt nhất (rank cao nhất, ưu tiên không phải cạ của user) - SỬ DỤNG CẠ ĐÃ LỌC
        /// </summary>
        private (int cardId, bool isUserCa) FindBestWasteCard(int[] userCards, (List<List<int>> phoms, List<List<int>> potentialPhoms, List<int> wasteCards, int totalScore) userAnalysis, string currentUsername)
        {
            // Lọc cạ khả thi trước khi xử lý
            var viableCa = FilterViableCa(userAnalysis.potentialPhoms, currentUsername);
            var viableCaFlat = viableCa.SelectMany(ca => ca).ToHashSet();

            var protectedCards = new HashSet<int>();

            // Thêm tất cả lá trong phỏm hoàn chỉnh
            foreach (var phom in userAnalysis.phoms)
                foreach (var card in phom)
                    protectedCards.Add(card);

            // Tìm lá rác (không thuộc phỏm và không thuộc cạ khả thi)
            var wasteCards = userCards.Where(c => !protectedCards.Contains(c) && !viableCaFlat.Contains(c)).ToList();

            if (wasteCards.Any())
            {
                // Chọn lá rác có rank cao nhất
                var selected = wasteCards.OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
                _uiManager.AppendLog($"🗑️ Chọn lá rác (không phải cạ khả thi): {CardUtilityPhom.ConvertCardsToString(new[] { selected })}", UIManager.LogLevel.Info);
                return (selected, false);
            }

            // Nếu không có lá rác thuần túy, chọn từ cạ không khả thi
            var nonViableCaCards = userAnalysis.potentialPhoms.SelectMany(ca => ca).Where(c => !viableCaFlat.Contains(c)).ToList();
            if (nonViableCaCards.Any())
            {
                var selected = nonViableCaCards.OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
                _uiManager.AppendLog($"🗑️ Chọn từ cạ không khả thi: {CardUtilityPhom.ConvertCardsToString(new[] { selected })}", UIManager.LogLevel.Info);
                return (selected, true);
            }

            // Fallback: chọn từ cạ khả thi (rank cao nhất)
            if (viableCa.Any())
            {
                var caCard = viableCa.First().OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
                _uiManager.AppendLog($"🗑️ Fallback từ cạ khả thi: {CardUtilityPhom.ConvertCardsToString(new[] { caCard })}", UIManager.LogLevel.Info);
                return (caCard, true);
            }

            // Fallback cuối: lá cao nhất
            var fallback = userCards.OrderByDescending(c => new CardUtilityPhom.CardInfo(c).Rank).First();
            _uiManager.AppendLog($"🗑️ Fallback cuối: {CardUtilityPhom.ConvertCardsToString(new[] { fallback })}", UIManager.LogLevel.Info);
            return (fallback, viableCaFlat.Contains(fallback));
        }

        /// <summary>
        /// LOGIC GOIYDOITHU - Gợi ý thông minh theo yêu cầu mới
        /// </summary>
        private (int suggestedCard, string analysis) SuggestGoiYDoiThu(string username, int[] userCards,
            (List<List<int>> phoms, List<List<int>> potentialPhoms, List<int> wasteCards, int totalScore) userAnalysis)
        {
            try
            {
                _uiManager.AppendLog($"🎯 Bắt đầu GoiYDoiThu cho {username}", UIManager.LogLevel.Info);

                // BƯỚC 1: Tạo ListCardGoiYDoiThu
                var listCardGoiYDoiThu = BuildListCardGoiYDoiThu(username);
                _uiManager.AppendLog($"📋 ListCardGoiYDoiThu: {listCardGoiYDoiThu.Count} lá", UIManager.LogLevel.Info);

                // BƯỚC 2: Tìm cạ trong ListCardGoiYDoiThu
                var caInGoiYDoiThu = FindCaInCards(listCardGoiYDoiThu);
                _uiManager.AppendLog($"🔍 Tìm thấy {caInGoiYDoiThu.Count} cạ trong ListCardGoiYDoiThu", UIManager.LogLevel.Info);

                if (caInGoiYDoiThu.Any())
                {
                    string caDetails = string.Join(", ", caInGoiYDoiThu.Select(c => $"{CardUtilityPhom.GetRankName(c.Rank)}{GetSuitSymbol(c.Suit)}"));
                    _uiManager.AppendLog($"📋 Chi tiết cạ đối thủ: {caDetails}", UIManager.LogLevel.Info);
                }

                // BƯỚC 3: Ưu tiên gợi ý lá tạo phỏm cho các cạ đó (LOGIC NGƯỢC: TRÁNH ĐÁNH LÁ TẠO PHỎM CHO ĐỐI THỦ)
                var (cardToHelp, isUserCa, helpAnalysis) = FindCardToHelpCa(userCards, caInGoiYDoiThu, userAnalysis.potentialPhoms);
                if (cardToHelp != -1)
                {
                    // LOGIC NGƯỢC: Nếu tìm thấy lá tạo phỏm cho đối thủ → TRÁNH đánh lá đó
                    _uiManager.AppendLog($"⚠️ PHÁT HIỆN lá {CardUtilityPhom.ConvertCardsToString(new[] { cardToHelp })} tạo phỏm cho đối thủ - TRÁNH đánh!", UIManager.LogLevel.Info);

                    // Tìm lá khác để đánh thay thế
                    var alternativeCard = FindAlternativeCardToAvoidHelping(userCards, cardToHelp, userAnalysis);
                    if (alternativeCard != -1)
                    {
                        string warningText = userAnalysis.potentialPhoms.SelectMany(ca => ca).Contains(alternativeCard) ? "Lá Bài 'Cạ'!" : "";
                        return (alternativeCard, $"Đánh {CardUtilityPhom.ConvertCardsToString(new[] { alternativeCard })}, Tránh đối thủ ăn bài! vì tạo phỏm cho listcard_goiydoithu|{warningText}");
                    }
                }

                // BƯỚC 4: Fallback - Tính xác suất bài ẩn
                var listCardChuaBiet = BuildListCardChuaBiet(listCardGoiYDoiThu);
                _uiManager.AppendLog($"🎲 ListCardChuaBiet: {listCardChuaBiet.Count} lá", UIManager.LogLevel.Info);

                var caInChuaBiet = FindCaInCards(listCardChuaBiet);
                _uiManager.AppendLog($"🔍 Tìm thấy {caInChuaBiet.Count} cạ trong ListCardChuaBiet", UIManager.LogLevel.Info);

                // BƯỚC 5: Gợi ý lá không tạo phỏm cho cạ ẩn
                var (cardToAvoid, isUserCaAvoid, avoidAnalysis) = FindCardToAvoidCa(userCards, caInChuaBiet, userAnalysis.potentialPhoms);
                if (cardToAvoid != -1)
                {
                    string warningText = isUserCaAvoid ? "Lá Bài 'Cạ'!" : "";
                    return (cardToAvoid, $"Đánh {CardUtilityPhom.ConvertCardsToString(new[] { cardToAvoid })}, Tránh đối thủ ăn bài! vì tạo phỏm cho listcard_goiydoithu|{warningText}");
                }

                // BƯỚC 6: Fallback cuối - Gợi ý lá rác
                var wasteCard = FindBestWasteCard(userCards, userAnalysis, username);
                return (wasteCard.cardId, $"Đánh {CardUtilityPhom.ConvertCardsToString(new[] { wasteCard.cardId })}, Lá rác cao nhất|{(wasteCard.isUserCa ? "Lá Bài 'Cạ'!" : "")}");
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi GoiYDoiThu: {ex.Message}", UIManager.LogLevel.Error);
                return (-1, $"Lỗi: {ex.Message}");
            }
        }
    }
}
