﻿﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AutoGameBai.UI
{
    /// <summary>
    /// Lớp hỗ trợ tối ưu hiệu suất cho các Form
    /// </summary>
    public static class FormOptimizer
    {
        /// <summary>
        /// Tối ưu hiệu suất cho Form bằng cách tắt các hiệu ứng và tính năng không cần thiết
        /// </summary>
        public static void OptimizeFormPerformance(Form form)
        {
            if (form == null) return;

            // Tắt double buffering cho form
            typeof(Form).GetProperty("DoubleBuffered",
                System.Reflection.BindingFlags.NonPublic |
                System.Reflection.BindingFlags.Instance)
                ?.SetValue(form, true);

            // Tắt các hiệu ứng không cần thiết
            form.AutoValidate = AutoValidate.Disable;

            // Tối ưu hiệu suất cho tất cả DataGridView trong form
            foreach (var control in GetAllControls(form))
            {
                if (control is DataGridView dgv)
                {
                    OptimizeDataGridView(dgv);
                }
            }
        }

        /// <summary>
        /// Tối ưu hiệu suất cho DataGridView
        /// </summary>
        public static void OptimizeDataGridView(DataGridView dgv)
        {
            if (dgv == null) return;

            // Tắt double buffering cho DataGridView
            typeof(DataGridView).GetProperty("DoubleBuffered",
                System.Reflection.BindingFlags.NonPublic |
                System.Reflection.BindingFlags.Instance)
                ?.SetValue(dgv, true);

            // Tối ưu hiệu suất render
            dgv.RowHeadersVisible = false;
            dgv.EnableHeadersVisualStyles = false;
            dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            dgv.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.None;
            dgv.VirtualMode = true;
        }

        /// <summary>
        /// Đo thời gian thực thi của một hành động
        /// </summary>
        public static TimeSpan MeasureExecutionTime(Action action)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            action();
            stopwatch.Stop();
            return stopwatch.Elapsed;
        }

        /// <summary>
        /// Đo thời gian thực thi của một hành động bất đồng bộ
        /// </summary>
        public static async Task<TimeSpan> MeasureExecutionTimeAsync(Func<Task> asyncAction)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            await asyncAction();
            stopwatch.Stop();
            return stopwatch.Elapsed;
        }

        /// <summary>
        /// Lấy tất cả các control trong form (bao gồm cả control con)
        /// </summary>
        private static IEnumerable<Control> GetAllControls(Control control)
        {
            var controls = control.Controls.Cast<Control>();
            return controls.SelectMany(ctrl => GetAllControls(ctrl))
                .Concat(controls);
        }

        /// <summary>
        /// Tối ưu hiệu suất cho việc tải dữ liệu vào DataGridView
        /// </summary>
        public static void OptimizedDataLoad<T>(DataGridView dgv, IEnumerable<T> data, Action<DataGridView, IEnumerable<T>> loadAction)
        {
            if (dgv == null || data == null || loadAction == null) return;

            dgv.SuspendLayout();

            try
            {
                loadAction(dgv, data);
            }
            finally
            {
                dgv.ResumeLayout();
            }
        }
    }
}
