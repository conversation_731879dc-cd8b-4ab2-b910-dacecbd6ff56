﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace AutoGameBai.Gamemaubinh
{
    public static class CardUtilityMaubinh
    {
        public enum HandType
        {
            HighCard,
            OnePair,
            TwoPair,
            ThreeOfAKind,
            Straight,
            Flush,
            FullHouse,
            FourOfAKind,
            StraightFlush
        }

        public class CardInfo
        {
            public int Id { get; }
            public int Rank { get; }
            public int Suit { get; }
            public int OriginalIndex { get; set; }

            public CardInfo(int id)
            {
                Id = id;
                Rank = (id / 4) + 1; // 1=A, 2=2, ..., 13=K
                Suit = id % 4; // 0=Spades, 1=Clubs, 2=Diamonds, 3=Hearts
            }
        }

        public static int GetCardValue(int rank)
        {
            return rank == 1 ? 14 : rank; // Át (1) có giá trị cao nhất (14)
        }

        // Tính điểm mạnh của chi theo luật mậu binh - Sử dụng logic game gốc
        public static double GetChiStrength(List<CardInfo> cards)
        {
            if (cards == null || cards.Count == 0)
                return 0;

            // Sử dụng logic game gốc để tính điểm chính xác
            int gameScore = GetGameOriginalScore(cards);

            // Chuyển đổi điểm game gốc thành double để tương thích
            // Thêm bonus cho Chi 3 (3 lá) để phù hợp với logic Mau Binh
            if (cards.Count == 3)
            {
                // Chi 3 có bonus để ưu tiên đôi/xám
                if (gameScore > 204) // Xám
                    return gameScore * 1.8; // Bonus cao cho xám ở Chi 3
                else if (gameScore > 68) // Đôi
                    return gameScore * 1.5; // Bonus cho đôi ở Chi 3
                else // Mậu thầu
                    return gameScore * 0.8; // Giảm điểm mậu thầu ở Chi 3
            }

            return (double)gameScore;
        }

        /// <summary>
        /// Tính điểm Chi theo logic game gốc (overload cho array)
        /// </summary>
        public static double GetChiStrength(int[] cardIds)
        {
            if (cardIds == null || cardIds.Length == 0)
                return 0;

            var cards = cardIds.Select(id => new CardInfo(id)).ToList();
            return GetChiStrength(cards);
        }

        public static HandType EvaluateHand(int[] cardIds)
        {
            var cards = cardIds.Select(id => new CardInfo(id)).ToList();
            var ranks = cards.Select(c => GetCardValue(c.Rank)).OrderByDescending(v => v).ToList();
            var suits = cards.Select(c => c.Suit).ToList();
            var rankGroups = ranks.GroupBy(v => v).OrderByDescending(g => g.Count()).ThenByDescending(g => g.Key).ToList();
            var suitGroups = suits.GroupBy(s => s).ToList();

            bool isFlush = suitGroups.Any(g => g.Count() == cards.Count);
            bool isStraight = ranks.Distinct().Count() == cards.Count &&
                             (ranks.Max() - ranks.Min() == cards.Count - 1 ||
                              ranks.OrderBy(v => v).SequenceEqual(new[] { 2, 3, 4, 5, 14 }.Take(cards.Count)));

            if (cards.Count == 5)
            {
                if (isFlush && isStraight)
                    return HandType.StraightFlush;
                if (rankGroups.Any(g => g.Count() >= 4))
                    return HandType.FourOfAKind;
                if (rankGroups.Any(g => g.Count() >= 3))
                {
                    var threeRank = rankGroups.First(g => g.Count() >= 3).Key;
                    var remainingRanks = ranks.Where(r => r != threeRank).ToList();
                    if (remainingRanks.GroupBy(r => r).Any(g => g.Count() >= 2))
                        return HandType.FullHouse;
                    return HandType.ThreeOfAKind;
                }
                if (isFlush)
                    return HandType.Flush;
                if (isStraight)
                    return HandType.Straight;
                if (rankGroups.Count(g => g.Count() >= 2) >= 2)
                    return HandType.TwoPair;
                if (rankGroups.Any(g => g.Count() >= 2))
                    return HandType.OnePair;
                return HandType.HighCard;
            }
            else if (cards.Count == 3)
            {
                if (isFlush && isStraight)
                    return HandType.StraightFlush;
                if (isFlush)
                    return HandType.Flush;
                if (rankGroups.Any(g => g.Count() >= 3))
                    return HandType.ThreeOfAKind;
                if (rankGroups.Any(g => g.Count() >= 2))
                    return HandType.OnePair;
                return HandType.HighCard;
            }

            return HandType.HighCard;
        }

        public static string VietHoaHandType(HandType handType)
        {
            return handType switch
            {
                HandType.HighCard => "Mậu Thầu",
                HandType.OnePair => "Đôi",
                HandType.TwoPair => "Thú",
                HandType.ThreeOfAKind => "Xám",
                HandType.Straight => "Sảnh",
                HandType.Flush => "Thùng",
                HandType.FullHouse => "Cù Lũ",
                HandType.FourOfAKind => "Tứ Quý",
                HandType.StraightFlush => "Thùng Phá Sảnh",
                _ => "Không xác định"
            };
        }

        public static string GetCardImagePath(CardInfo card)
        {
            return $"card/{card.Id}.png";
        }

        public static string ConvertCardsToString(int[] cardIds)
        {
            if (cardIds == null || cardIds.Length == 0)
                return "[]";

            var cards = cardIds.Select(id => new CardInfo(id)).ToList();
            var cardStrings = cards.Select(c =>
            {
                string rank = c.Rank switch
                {
                    1 => "A",
                    11 => "J",
                    12 => "Q",
                    13 => "K",
                    _ => c.Rank.ToString()
                };
                string suit = c.Suit switch
                {
                    0 => "♠",
                    1 => "♣",
                    2 => "♦",
                    3 => "♥",
                    _ => ""
                };
                return $"{rank}{suit}";
            });
            return $"[{string.Join(", ", cardStrings)}]";
        }

        /// <summary>
        /// Get display name for a single card
        /// </summary>
        public static string GetCardDisplayName(int cardId)
        {
            var card = new CardInfo(cardId);
            string rank = card.Rank switch
            {
                1 => "A",
                11 => "J",
                12 => "Q",
                13 => "K",
                _ => card.Rank.ToString()
            };
            string suit = card.Suit switch
            {
                0 => "♠",
                1 => "♣",
                2 => "♦",
                3 => "♥",
                _ => ""
            };
            return $"{rank}{suit}";
        }

        /// <summary>
        /// Check if card is red (Diamond or Heart)
        /// </summary>
        public static bool IsRedCard(int cardId)
        {
            var card = new CardInfo(cardId);
            return card.Suit == 2 || card.Suit == 3; // Diamond or Heart
        }

        /// <summary>
        /// Get card rank from ID
        /// </summary>
        public static int GetCardRank(int cardId)
        {
            return (cardId / 4) + 1;
        }

        /// <summary>
        /// Get card suit from ID
        /// </summary>
        public static int GetCardSuit(int cardId)
        {
            return cardId % 4;
        }

        public static HandType GetHandType(List<CardInfo> cards)
        {
            if (cards == null || cards.Count == 0)
                return HandType.HighCard;

            return EvaluateHand(cards.Select(c => c.Id).ToArray());
        }



        #region Special Cases Detection - Tập trung logic đặc biệt

        /// <summary>
        /// Kiểm tra có 6 đôi không
        /// </summary>
        public static bool HasSixPairs(List<CardInfo> cards)
        {
            if (cards == null || cards.Count != 13) return false;

            var rankGroups = cards.GroupBy(c => c.Rank).ToList();
            var pairCount = rankGroups.Count(g => g.Count() >= 2);
            return pairCount >= 6;
        }

        /// <summary>
        /// Kiểm tra có 3 thùng không
        /// </summary>
        public static bool HasThreeFlushes(List<CardInfo> cards)
        {
            if (cards == null || cards.Count != 13) return false;

            var suitGroups = cards.GroupBy(c => c.Suit).ToList();
            var flushCount = suitGroups.Count(g => g.Count() >= 5);
            return flushCount >= 1 && suitGroups.Count(g => g.Count() >= 3) >= 3;
        }

        /// <summary>
        /// Kiểm tra có sảnh rồng đồng hoa không (A-2-3-4-5-6-7-8-9-10-J-Q-K cùng chất)
        /// </summary>
        public static bool HasDragonStraightFlush(List<CardInfo> cards)
        {
            if (cards == null || cards.Count != 13) return false;

            var suitGroups = cards.GroupBy(c => c.Suit).ToList();
            foreach (var suitGroup in suitGroups)
            {
                if (suitGroup.Count() == 13)
                {
                    var ranks = suitGroup.Select(c => c.Rank).OrderBy(r => r).ToList();
                    var expectedRanks = Enumerable.Range(1, 13).ToList();
                    return ranks.SequenceEqual(expectedRanks);
                }
            }
            return false;
        }

        /// <summary>
        /// Kiểm tra có sảnh rồng không (A-2-3-4-5-6-7-8-9-10-J-Q-K)
        /// </summary>
        public static bool HasDragonStraight(List<CardInfo> cards)
        {
            if (cards == null || cards.Count != 13) return false;

            var ranks = cards.Select(c => c.Rank).OrderBy(r => r).ToList();
            var expectedRanks = Enumerable.Range(1, 13).ToList();
            return ranks.SequenceEqual(expectedRanks);
        }

        /// <summary>
        /// Kiểm tra có đồng hoa không (13 lá cùng chất)
        /// </summary>
        public static bool HasSameSuit(List<CardInfo> cards)
        {
            if (cards == null || cards.Count != 13) return false;

            var firstSuit = cards[0].Suit;
            return cards.All(c => c.Suit == firstSuit);
        }

        /// <summary>
        /// Kiểm tra có 5 đôi 1 xám không
        /// </summary>
        public static bool HasFivePairsOneThree(List<CardInfo> cards)
        {
            if (cards == null || cards.Count != 13) return false;

            var rankGroups = cards.GroupBy(c => c.Rank).ToList();
            var pairCount = rankGroups.Count(g => g.Count() == 2);
            var threeCount = rankGroups.Count(g => g.Count() == 3);
            return pairCount == 5 && threeCount == 1;
        }

        /// <summary>
        /// Kiểm tra có 3 sảnh không - dựa trên logic game gốc OPTIMIZED
        /// </summary>
        public static bool HasThreeStraights(List<CardInfo> cards)
        {
            if (cards == null || cards.Count != 13) return false;

            // Tối ưu: Kiểm tra nhanh trước khi thử combinations
            var ranks = cards.Select(c => c.Rank).OrderBy(r => r).ToList();
            var distinctRanks = ranks.Distinct().Count();

            // Cần ít nhất 9 rank khác nhau để có thể tạo 3 sảnh (có thể có Át trùng)
            if (distinctRanks < 9) return false;

            // Thử tất cả cách chia 13 lá thành 3 chi (5-5-3) - GIỚI HẠN để tránh quá chậm
            var allCards = cards.ToList();

            // Giới hạn số combinations để tránh quá chậm
            var chi1Combinations = GetCombinations(allCards, 5).Take(100); // Giới hạn 100 combinations

            foreach (var chi1 in chi1Combinations)
            {
                if (!IsStraight(chi1)) continue; // Kiểm tra sớm

                var remainingAfterChi1 = allCards.Except(chi1).ToList();
                var chi2Combinations = GetCombinations(remainingAfterChi1, 5).Take(50); // Giới hạn 50

                foreach (var chi2 in chi2Combinations)
                {
                    if (!IsStraight(chi2)) continue; // Kiểm tra sớm

                    var chi3 = remainingAfterChi1.Except(chi2).ToList();

                    // Kiểm tra chi 3 có phải sảnh không
                    if (chi3.Count == 3 && IsStraight(chi3))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Kiểm tra có phải sảnh không - logic game gốc
        /// </summary>
        private static bool IsStraight(List<CardInfo> cards)
        {
            if (cards.Count < 3) return false;

            var gameCards = cards.Select(c => new { N = c.Rank, S = c.Suit }).ToList();
            return CheckSanh(gameCards) > 0;
        }

        #endregion

        /// <summary>
        /// Tạo tất cả combinations của n phần tử từ list - OPTIMIZED
        /// </summary>
        private static IEnumerable<List<T>> GetCombinations<T>(List<T> list, int length)
        {
            if (length == 1)
                return list.Select(t => new List<T> { t });

            if (length == list.Count)
                return new List<List<T>> { list };

            if (length > list.Count)
                return new List<List<T>>();

            // Sử dụng thuật toán tối ưu hơn để tránh quá chậm
            return GetCombinationsIterative(list, length);
        }

        /// <summary>
        /// Thuật toán combinations tối ưu - tránh quá chậm
        /// </summary>
        private static IEnumerable<List<T>> GetCombinationsIterative<T>(List<T> list, int length)
        {
            if (length == 0)
                yield return new List<T>();
            else if (length <= list.Count)
            {
                for (int i = 0; i <= list.Count - length; i++)
                {
                    var head = list[i];
                    var tail = list.Skip(i + 1).ToList();

                    foreach (var combination in GetCombinationsIterative(tail, length - 1))
                    {
                        var result = new List<T> { head };
                        result.AddRange(combination);
                        yield return result;
                    }
                }
            }
        }

        /// <summary>
        /// Phân tích Chi 3 (3 lá) - sử dụng logic game gốc
        /// </summary>
        public static (string Type, int Strength) AnalyzeChi3(int[] cards)
        {
            if (cards == null || cards.Length != 3)
                return ("Không hợp lệ", 0);

            var cardInfos = cards.Select(id => new CardInfo(id)).ToList();

            // Sử dụng logic game gốc để tính điểm
            int gameScore = GetGameOriginalScore(cardInfos);

            if (gameScore > 204) // Xám (three of a kind)
            {
                int rank = gameScore - 204;
                return ($"Xám {GetRankName(rank)}", gameScore);
            }
            else if (gameScore > 68) // Đôi (pair)
            {
                int rank = gameScore - 68;
                return ($"Đôi {GetRankName(rank)}", gameScore);
            }
            else // Mậu thầu (high cards)
            {
                var sortedCards = cardInfos.OrderByDescending(c => GetCardValue(c.Rank))
                                          .ThenByDescending(c => c.Suit).ToList();
                return ($"Mậu thầu {GetRankName(sortedCards[0].Rank)}", gameScore);
            }
        }

        /// <summary>
        /// Tính điểm theo logic game gốc (dựa trên source code game)
        /// </summary>
        public static int GetGameOriginalScore(List<CardInfo> cards)
        {
            if (cards == null || cards.Count == 0) return 0;

            // Chuyển đổi sang format game gốc (N = rank, S = suit)
            var gameCards = cards.Select(c => new { N = c.Rank, S = c.Suit }).ToList();

            // Kiểm tra theo thứ tự ưu tiên như game gốc

            // 1. Kiểm tra Thùng phá sảnh (TPS)
            if (cards.Count >= 5)
            {
                var tpsScore = CheckStraightFlush(gameCards);
                if (tpsScore > 0) return 544 + tpsScore;
            }

            // 2. Kiểm tra Tứ quý
            var fourKindScore = CheckFourOfAKind(gameCards);
            if (fourKindScore > 0) return 476 + fourKindScore;

            // 3. Kiểm tra Cù lũ
            if (cards.Count >= 5)
            {
                var fullHouseScore = CheckFullHouse(gameCards);
                if (fullHouseScore > 0) return 408 + fullHouseScore;
            }

            // 4. Kiểm tra Thùng
            if (cards.Count >= 5)
            {
                var flushScore = CheckThung(gameCards);
                if (flushScore > 0) return flushScore;
            }

            // 5. Kiểm tra Sảnh
            if (cards.Count >= 5)
            {
                var straightScore = CheckSanh(gameCards);
                if (straightScore > 0) return straightScore;
            }

            // 6. Kiểm tra Xám (three of a kind)
            var threeKindScore = CheckThreeOfAKind(gameCards);
            if (threeKindScore > 0) return threeKindScore;

            // 7. Kiểm tra Thú (two pairs)
            if (cards.Count >= 4)
            {
                var twoPairScore = CheckTwoPairs(gameCards);
                if (twoPairScore > 0) return twoPairScore;
            }

            // 8. Kiểm tra Đôi (one pair)
            var pairScore = CheckPair(gameCards);
            if (pairScore > 0) return pairScore;

            // 9. Mậu thầu (high card)
            return GetHighCardScore(gameCards);
        }

        #region Game Original Logic Methods

        /// <summary>
        /// Sắp xếp vector theo logic game gốc - CHÍNH XÁC
        /// </summary>
        private static void SortVector(List<dynamic> cards)
        {
            for (int i = 0; i < cards.Count - 1; )
            {
                var currentCard = cards[i];
                int currentRank = currentCard.N;
                int j;

                for (j = i + 1; j < cards.Count; j++)
                {
                    var compareCard = cards[j];
                    if (currentRank > compareCard.N)
                    {
                        cards[i] = compareCard;
                        cards[j] = currentCard;
                        i = 0;
                        break;
                    }
                }

                if (i == 0 && j != cards.Count)
                    continue;
                else
                    i++;
            }
        }

        /// <summary>
        /// Sắp xếp vector theo suit - logic game gốc
        /// </summary>
        private static void SortVector2(List<dynamic> cards)
        {
            for (int i = 0; i < cards.Count - 1; )
            {
                var currentCard = cards[i];
                int currentRank = currentCard.N;
                int currentSuit = currentCard.S;
                int j;

                for (j = i + 1; j < cards.Count; j++)
                {
                    var compareCard = cards[j];
                    int compareRank = compareCard.N;
                    int compareSuit = compareCard.S;

                    if (currentSuit > compareSuit)
                    {
                        cards[i] = compareCard;
                        cards[j] = currentCard;
                        i = 0;
                        break;
                    }
                    if (currentSuit == compareSuit && currentRank > compareRank)
                    {
                        cards[i] = compareCard;
                        cards[j] = currentCard;
                        i = 0;
                        break;
                    }
                }

                if (i == 0 && j != cards.Count)
                    continue;
                else
                    i++;
            }
        }

        /// <summary>
        /// Tìm index của Át (14) - logic game gốc
        /// </summary>
        private static int IndexA(List<dynamic> cards)
        {
            for (int i = 0; i < cards.Count; i++)
            {
                if (cards[i].N == 14)
                {
                    return i;
                }
            }
            return 0;
        }

        /// <summary>
        /// Kiểm tra Thùng phá sảnh (TPS) - logic game gốc CHÍNH XÁC
        /// </summary>
        private static int CheckStraightFlush(dynamic gameCards)
        {
            if (gameCards.Count == 3) return 0; // Chi 3 không thể có TPS

            var cards = gameCards.ToList();
            SortVector(cards);

            var thungScore = CheckThung(gameCards);
            var sanhScore = CheckSanh(gameCards);

            if (thungScore > 0 && sanhScore > 0)
            {
                return 544 + cards[4].N; // TPS + lá cao nhất
            }

            return 0;
        }

        /// <summary>
        /// Kiểm tra Tứ quý (Four of a Kind) - logic game gốc CHÍNH XÁC
        /// </summary>
        private static int CheckFourOfAKind(dynamic gameCards)
        {
            if (gameCards.Count == 3) return 0; // Chi 3 không thể có tứ quý

            var cards = gameCards.ToList();
            SortVector(cards);

            int consecutiveCount = 0;
            for (int i = 0; i < cards.Count - 1; i++)
            {
                consecutiveCount = 0;
                for (int j = i + 1; j < cards.Count && (cards[i].N - cards[j].N) % 13 == 0; j++)
                {
                    consecutiveCount++;
                }

                if (consecutiveCount == 3) // Tìm thấy 4 lá cùng rank
                {
                    return 476 + cards[i].N;
                }
            }

            return 0;
        }

        /// <summary>
        /// Kiểm tra Cù lũ (Full House) - logic game gốc CHÍNH XÁC
        /// </summary>
        private static int CheckFullHouse(dynamic gameCards)
        {
            if (gameCards.Count == 3) return 0; // Chi 3 không thể có cù lũ

            var cards = gameCards.ToList();
            SortVector(cards);

            var threeOfAKindCards = new List<dynamic>();

            // Tìm xám (3 lá cùng rank)
            for (int i = 0; i < cards.Count - 1; i++)
            {
                int consecutiveCount = 0;
                for (int j = i + 1; j < cards.Count && cards[i].N == cards[j].N; j++)
                {
                    consecutiveCount++;
                }

                if (consecutiveCount == 2) // Tìm thấy xám
                {
                    threeOfAKindCards.Add(cards[i]);
                    threeOfAKindCards.Add(cards[i + 1]);
                    threeOfAKindCards.Add(cards[i + 2]);
                    break;
                }
            }

            if (threeOfAKindCards.Count == 3)
            {
                // Loại bỏ xám và tìm đôi trong các lá còn lại - tránh lambda với dynamic
                var remainingCards = new List<dynamic>();
                foreach (var card in cards)
                {
                    bool isThreeOfAKind = false;
                    foreach (var threeCard in threeOfAKindCards)
                    {
                        if (threeCard.N == card.N && threeCard.S == card.S)
                        {
                            isThreeOfAKind = true;
                            break;
                        }
                    }
                    if (!isThreeOfAKind)
                    {
                        remainingCards.Add(card);
                    }
                }

                for (int i = 0; i < remainingCards.Count - 1; i++)
                {
                    if (remainingCards[i].N == remainingCards[i + 1].N)
                    {
                        return 408 + threeOfAKindCards[0].N; // Cù lũ
                    }
                }
            }

            return 0;
        }

        /// <summary>
        /// Kiểm tra Thùng (Flush) - logic game gốc CHÍNH XÁC
        /// </summary>
        private static int CheckThung(dynamic gameCards)
        {
            if (gameCards.Count == 3) return 0; // Chi 3 không thể có thùng

            var cards = gameCards.ToList();
            SortVector2(cards);

            for (int i = 0; i < cards.Count - 1; i++)
            {
                int consecutiveCount = 0;
                var flushCards = new List<dynamic>();

                for (int j = i + 1; j < cards.Count && cards[i].S - cards[j].S == 0; j++)
                {
                    consecutiveCount++;
                }

                if (consecutiveCount == 4) // Tìm thấy 5 lá cùng suit
                {
                    for (int k = i; k <= i + 4; k++)
                    {
                        flushCards.Add(cards[k]);
                    }
                    return 340 + cards[4].N; // Thùng + lá cao nhất
                }
            }

            return 0;
        }

        /// <summary>
        /// Kiểm tra Sảnh (Straight) - logic game gốc CHÍNH XÁC
        /// </summary>
        private static int CheckSanh(dynamic gameCards)
        {
            if (gameCards.Count == 3) return 0; // Chi 3 không thể có sảnh

            var cards = gameCards.ToList();
            SortVector(cards);

            int consecutiveSum = 0;
            var straightCards = new List<dynamic>();

            if (cards.Count == 5)
            {
                // Logic cho 5 lá
                for (int i = 0; i < cards.Count - 1; i++)
                {
                    int diff = cards[i + 1].N - cards[i].N;

                    if (diff > 1)
                    {
                        consecutiveSum = 0;
                        straightCards.Clear();
                    }
                    else
                    {
                        consecutiveSum += diff;
                        if (diff == 1)
                        {
                            straightCards.Add(cards[i]);
                            if (i == cards.Count - 2)
                            {
                                straightCards.Add(cards[i + 1]);
                                consecutiveSum++;
                            }
                        }
                    }

                    // Kiểm tra sảnh đặc biệt A-2-3-4 với Át ở cuối
                    if (consecutiveSum == 3 && straightCards.Count >= 3 && straightCards[2].N == 4 && IndexA(cards) > 0)
                    {
                        straightCards.Add(cards[i + 1]);
                        straightCards.Add(cards[IndexA(cards)]);
                        consecutiveSum = 5;
                        break;
                    }

                    if (consecutiveSum == 4 && straightCards.Count >= 4 && straightCards[3].N == 5 && IndexA(cards) > 0)
                    {
                        straightCards.Add(cards[IndexA(cards)]);
                        consecutiveSum++;
                        break;
                    }

                    if (consecutiveSum == 5)
                        break;
                }

                if (consecutiveSum == 5)
                {
                    int aceIndex = IndexA(cards);
                    if (aceIndex > 0)
                    {
                        if (straightCards[0].N == 10)
                            return 286; // 10-J-Q-K-A
                        else if (straightCards[0].N == 2)
                            return 277; // A-2-3-4-5
                        else
                            return 272 + straightCards[4].N;
                    }
                    else
                    {
                        return 272 + straightCards[4].N;
                    }
                }
            }
            else
            {
                // Logic cho nhiều hơn 5 lá (tương tự nhưng linh hoạt hơn)
                for (int i = 0; i < cards.Count - 1; i++)
                {
                    int diff = cards[i + 1].N - cards[i].N;

                    if (diff > 1)
                    {
                        consecutiveSum = 0;
                        straightCards.Clear();
                    }
                    else
                    {
                        consecutiveSum += diff;
                        if (diff == 1)
                        {
                            straightCards.Add(cards[i]);
                            if (i == cards.Count - 2)
                            {
                                straightCards.Add(cards[i + 1]);
                                consecutiveSum++;
                            }
                        }
                    }

                    // Kiểm tra sảnh đặc biệt
                    if (consecutiveSum == 3 && straightCards.Count >= 3 && straightCards[2].N == 4 && IndexA(cards) > 0)
                    {
                        straightCards.Add(cards[i + 1]);
                        straightCards.Add(cards[IndexA(cards)]);
                        consecutiveSum = 5;
                        break;
                    }

                    if (consecutiveSum == 4)
                    {
                        if (straightCards.Count >= 4 && straightCards[3].N == 5 && IndexA(cards) > 0)
                        {
                            straightCards.Add(cards[IndexA(cards)]);
                            consecutiveSum++;
                            break;
                        }
                        straightCards.Add(cards[i + 1]);
                        consecutiveSum++;
                        break;
                    }

                    if (consecutiveSum == 5)
                        break;
                }

                if (consecutiveSum == 5)
                {
                    int aceIndex = IndexA(cards);
                    if (aceIndex > 0)
                    {
                        if (straightCards[0].N == 10)
                            return 286; // 10-J-Q-K-A
                        else if (straightCards[0].N == 2)
                            return 277; // A-2-3-4-5
                        else
                            return 272 + straightCards[4].N;
                    }
                    else
                    {
                        return 272 + straightCards[4].N;
                    }
                }
            }

            return 0;
        }

        /// <summary>
        /// Kiểm tra Xám (Three of a Kind) - logic game gốc CHÍNH XÁC
        /// </summary>
        private static int CheckThreeOfAKind(dynamic gameCards)
        {
            var cards = gameCards.ToList();
            SortVector(cards);

            for (int i = 0; i < cards.Count - 1; i++)
            {
                int consecutiveCount = 0;
                for (int j = i + 1; j < cards.Count && cards[i].N == cards[j].N; j++)
                {
                    consecutiveCount++;
                }

                if (consecutiveCount == 2) // Tìm thấy xám
                {
                    return 204 + cards[i].N;
                }
            }

            return 0;
        }

        /// <summary>
        /// Kiểm tra Thú (Two Pairs) - logic game gốc CHÍNH XÁC
        /// </summary>
        private static int CheckTwoPairs(dynamic gameCards)
        {
            if (gameCards.Count == 3) return 0; // Chi 3 không thể có thú

            var cards = gameCards.ToList();
            SortVector(cards);

            var firstPairCards = new List<dynamic>();

            // Tìm đôi đầu tiên
            for (int i = 0; i < cards.Count - 1; i++)
            {
                int consecutiveCount = 0;
                for (int j = i + 1; j < cards.Count && cards[i].N == cards[j].N; j++)
                {
                    consecutiveCount++;
                }

                if (consecutiveCount == 1) // Tìm thấy đôi
                {
                    firstPairCards.Add(cards[i]);
                    firstPairCards.Add(cards[i + 1]);
                    break;
                }
            }

            if (firstPairCards.Count == 2)
            {
                // Loại bỏ đôi đầu và tìm đôi thứ hai
                var remainingCards = new List<dynamic>();
                foreach (var card in cards)
                {
                    bool isFirstPair = false;
                    foreach (var pairCard in firstPairCards)
                    {
                        if (pairCard.N == card.N && pairCard.S == card.S)
                        {
                            isFirstPair = true;
                            break;
                        }
                    }
                    if (!isFirstPair)
                    {
                        remainingCards.Add(card);
                    }
                }

                for (int i = 0; i < remainingCards.Count - 1; i++)
                {
                    int consecutiveCount = 0;
                    int secondPairIndex = -1;
                    for (int j = i + 1; j < remainingCards.Count && (remainingCards[i].N == remainingCards[j].N && remainingCards[i].N != firstPairCards[0].N); j++)
                    {
                        consecutiveCount++;
                        secondPairIndex = j;
                    }

                    if (consecutiveCount == 1) // Tìm thấy đôi thứ hai
                    {
                        return 136 + remainingCards[secondPairIndex].N; // Thú + đôi cao nhất
                    }
                }
            }

            return 0;
        }

        /// <summary>
        /// Kiểm tra Đôi (One Pair) - logic game gốc CHÍNH XÁC
        /// </summary>
        private static int CheckPair(dynamic gameCards)
        {
            var cards = gameCards.ToList();
            SortVector(cards);

            for (int i = 0; i < cards.Count - 1; i++)
            {
                if (cards[i].N == cards[i + 1].N)
                {
                    return 68 + cards[i].N;
                }
            }

            return 0;
        }

        /// <summary>
        /// Tính điểm Mậu thầu (High Card) - logic game gốc
        /// </summary>
        private static int GetHighCardScore(dynamic gameCards)
        {
            if (gameCards.Count == 0) return 0;

            // Sắp xếp theo rank giảm dần - tránh lambda với dynamic
            var cards = gameCards.ToList();
            var sortedCards = new List<dynamic>();

            // Sắp xếp thủ công để tránh lambda với dynamic
            for (int i = 0; i < cards.Count; i++)
            {
                int insertIndex = 0;
                for (int j = 0; j < sortedCards.Count; j++)
                {
                    if (cards[i].N > sortedCards[j].N)
                    {
                        insertIndex = j;
                        break;
                    }
                    insertIndex = j + 1;
                }
                sortedCards.Insert(insertIndex, cards[i]);
            }

            // Tính điểm dựa trên 3 lá cao nhất (cho Chi 3)
            int score = 0;
            for (int i = 0; i < Math.Min(3, sortedCards.Count); i++)
            {
                score += sortedCards[i].N * (int)Math.Pow(15, 2 - i);
            }

            return Math.Min(score, 67); // Đảm bảo < 68 (threshold của đôi)
        }

        /// <summary>
        /// Kiểm tra các bài đặc biệt Mau Binh - dựa trên logic game gốc
        /// </summary>
        public static int CheckMauBinhSpecialHands(List<CardInfo> cards)
        {
            if (cards == null || cards.Count != 13) return 0;

            // Sắp xếp theo ID để dễ kiểm tra
            var sortedIds = cards.Select(c => c.Id).OrderBy(id => id).ToList();

            // 15. Sảnh rồng đồng hoa (chưa implement trong game gốc)
            if (CheckSanhRongDongHoa(sortedIds))
                return 15;

            // 14. Sảnh rồng
            if (CheckSanhRong(sortedIds))
                return 14;

            // 13. Đồng hoa
            if (CheckDongHoa(sortedIds))
                return 13;

            // 16. 5 đôi 1 xám
            var specialSixPairs = Check5Doi1Xam(sortedIds);
            if (specialSixPairs == 16) return 16;

            // 12. 6 đôi
            if (specialSixPairs == 12) return 12;

            // 11. 3 thùng
            if (Check3Thung(cards))
                return 11;

            // 10. 3 sảnh
            if (HasThreeStraights(cards))
                return 10;

            return 0;
        }

        /// <summary>
        /// Kiểm tra Sảnh rồng (A-2-3-4-5-6-7-8-9-10-J-Q-K) - logic game gốc
        /// </summary>
        private static bool CheckSanhRong(List<int> cardIds)
        {
            var ranks = cardIds.Select(id => (id % 52) / 4 + 1).OrderBy(r => r).ToList();

            // Kiểm tra có đủ 13 rank khác nhau từ A đến K
            var expectedRanks = Enumerable.Range(1, 13).ToList();
            return ranks.SequenceEqual(expectedRanks);
        }

        /// <summary>
        /// Kiểm tra Đồng hoa (tất cả cùng màu đỏ hoặc đen) - logic game gốc
        /// </summary>
        private static bool CheckDongHoa(List<int> cardIds)
        {
            // Kiểm tra màu: 0,1 = đen (♠,♣), 2,3 = đỏ (♦,♥)
            var suits = cardIds.Select(id => (id % 52) % 4).ToList();

            bool allBlack = suits.All(s => s == 0 || s == 1);
            bool allRed = suits.All(s => s == 2 || s == 3);

            return allBlack || allRed;
        }

        /// <summary>
        /// Kiểm tra Sảnh rồng đồng hoa - logic game gốc
        /// </summary>
        private static bool CheckSanhRongDongHoa(List<int> cardIds)
        {
            return CheckSanhRong(cardIds) && CheckDongHoa(cardIds);
        }

        /// <summary>
        /// Kiểm tra 6 đôi hoặc 5 đôi 1 xám - logic game gốc
        /// </summary>
        private static int Check5Doi1Xam(List<int> cardIds)
        {
            var ranks = cardIds.Select(id => (id % 52) / 4 + 1).ToList();
            var rankGroups = ranks.GroupBy(r => r).ToList();

            int pairCount = 0;
            int threeCount = 0;
            int fourCount = 0;

            foreach (var group in rankGroups)
            {
                int count = group.Count();
                if (count == 2) pairCount++;
                else if (count == 3) threeCount++;
                else if (count >= 4) fourCount++;
                else if (count > 4) return 0; // Không hợp lệ
            }

            // 5 đôi 1 xám
            if (pairCount == 5 && threeCount == 1)
                return 16;

            // 6 đôi
            if (pairCount == 6 && threeCount == 0)
                return 12;

            // 6 đôi với tứ quý (tính như 6 đôi)
            if (pairCount >= 4 && fourCount >= 1)
                return 12;

            return 0;
        }

        /// <summary>
        /// Kiểm tra 3 thùng - logic game gốc OPTIMIZED
        /// </summary>
        private static bool Check3Thung(List<CardInfo> cards)
        {
            if (cards == null || cards.Count != 13) return false;

            // Tối ưu: Kiểm tra nhanh trước khi thử combinations
            var suitGroups = cards.GroupBy(c => c.Suit).ToList();

            // Cần ít nhất 2 suit có đủ lá để tạo thùng
            var validSuits = suitGroups.Where(g => g.Count() >= 3).ToList();
            if (validSuits.Count < 2) return false;

            // Thử tất cả cách chia 13 lá thành 3 chi (5-5-3) - GIỚI HẠN để tránh quá chậm
            var allCards = cards.ToList();

            // Giới hạn số combinations để tránh quá chậm
            var chi1Combinations = GetCombinations(allCards, 5).Take(100); // Giới hạn 100 combinations

            foreach (var chi1 in chi1Combinations)
            {
                if (!IsFlush(chi1)) continue; // Kiểm tra sớm

                var remainingAfterChi1 = allCards.Except(chi1).ToList();
                var chi2Combinations = GetCombinations(remainingAfterChi1, 5).Take(50); // Giới hạn 50

                foreach (var chi2 in chi2Combinations)
                {
                    if (!IsFlush(chi2)) continue; // Kiểm tra sớm

                    var chi3 = remainingAfterChi1.Except(chi2).ToList();

                    // Kiểm tra chi 3 có phải thùng không
                    if (chi3.Count == 3 && IsFlush(chi3))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Kiểm tra có phải thùng không - logic game gốc
        /// </summary>
        private static bool IsFlush(List<CardInfo> cards)
        {
            if (cards.Count < 3) return false;

            var gameCards = cards.Select(c => new { N = c.Rank, S = c.Suit }).ToList();
            return CheckThung(gameCards) > 0;
        }

        #endregion

        public static string GetRankName(int rank)
        {
            return rank switch
            {
                1 => "A",
                11 => "J",
                12 => "Q",
                13 => "K",
                _ => rank.ToString()
            };
        }
    }
}