﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net8.0-windows</TargetFramework>
		<Nullable>enable</Nullable>
		<UseWindowsForms>true</UseWindowsForms>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="HIC.System.Windows.Forms.DataVisualization" Version="1.0.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.4" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="OpenCVSharp4" Version="4.10.0.20241108" />
		<PackageReference Include="OpenCvSharp4.Extensions" Version="4.10.0.20241108" />
		<PackageReference Include="OpenCVSharp4.runtime.win" Version="4.10.0.20241108" />
		<PackageReference Include="python" Version="3.13.3" />
		<PackageReference Include="Python.Runtime" Version="2.7.9" />
		<PackageReference Include="pythonnet" Version="3.0.5" />
		<PackageReference Include="Selenium.WebDriver" Version="4.32.0" />
		<PackageReference Include="Selenium.WebDriver.ChromeDriver" Version="135.0.7049.11400" />
		<PackageReference Include="Serilog" Version="4.2.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
		<PackageReference Include="SocketIOClient" Version="3.1.2" />
		<PackageReference Include="System.Data.SQLite" Version="1.0.119" />
		<PackageReference Include="System.Net.Http.Json" Version="9.0.4" />
		<PackageReference Include="System.Text.Json" Version="9.0.4" />
		<PackageReference Include="WebSocketSharp-netstandard" Version="1.0.1" />
	</ItemGroup>
	<ItemGroup>
		<Compile Update="Properties\Resources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Resources.resx</DependentUpon>
		</Compile>
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Update="Properties\Resources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>Resources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>
	<ItemGroup>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="debug-log.txt">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="game_history.txt">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="room-status.txt">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="token.txt">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>