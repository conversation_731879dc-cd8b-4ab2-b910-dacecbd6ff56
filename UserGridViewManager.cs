﻿﻿﻿﻿﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using AutoGameBai.Models;

namespace AutoGameBai
{
    public class UserGridViewManager
    {
        private readonly DataGridView _dataGridView;
        private readonly GameClientManager _gameClient;
        private readonly List<string> _selectedUsers;

        public UserGridViewManager(DataGridView dataGridView, GameClientManager gameClient, List<string> selectedUsers)
        {
            _dataGridView = dataGridView ?? throw new ArgumentNullException(nameof(dataGridView));
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
            _selectedUsers = selectedUsers ?? throw new ArgumentNullException(nameof(selectedUsers));
        }

        public async Task LoadUsersAsync()
        {
            try
            {
                await Task.Run(() => LoadUsers());
            }
            catch (Exception ex)
            {
                _gameClient.GetUIManager().AppendLog($"Lỗi khi tải danh sách user: {ex.Message}\nStackTrace: {ex.StackTrace}", UIManager.LogLevel.Error);
                throw;
            }
        }

        public void LoadUsers()
        {
            try
            {
                var previouslySelectedUsers = new List<string>(_selectedUsers);

                if (_dataGridView.InvokeRequired)
                {
                    _dataGridView.Invoke(new Action(LoadUsers));
                    return;
                }

                _dataGridView.Rows.Clear();

                // Kiểm tra số cột - đảm bảo có đủ cột (giảm từ 5 xuống 4 do xóa TableButton)
                if (_dataGridView.Columns.Count < 4)
                {
                    _gameClient.GetUIManager().AppendLog($"Số cột DataGridView không đủ: {_dataGridView.Columns.Count}/4", UIManager.LogLevel.Error);
                    throw new InvalidOperationException("Số cột DataGridView không đủ");
                }

                var users = _gameClient.GetUsers();
                if (users == null)
                {
                    _gameClient.GetUIManager().AppendLog("Danh sách user rỗng", UIManager.LogLevel.Error);
                    return;
                }

                int id = 1;
                foreach (var user in users.Values)
                {
                    if (user == null || string.IsNullOrEmpty(user.Username)) continue;

                    bool isProfileOpen = _gameClient.GetProfileManager().IsProfileOpen(user.Username);
                    _gameClient.GetUIManager().AppendLog($"Kiểm tra trạng thái profile cho {user.Username}: {(isProfileOpen ? "Mở" : "Đóng")}", UIManager.LogLevel.Info, user.Username);

                    bool isInRoom = _gameClient.GetUserRooms().ContainsKey(user.Username);

                    // Đặt giá trị mặc định cho các cột
                    object[] rowValues = new object[5]; // Tạo mảng với 5 phần tử (xóa TableButton)

                    // Đặt giá trị cho từng cột (xóa TableButton)
                    rowValues[0] = id.ToString();                           // ID (index 0)
                    rowValues[1] = isProfileOpen ? "Đóng Web" : "Mở Web";   // WebButton (index 1)
                    rowValues[2] = user.IsMainUser ? "Bỏ Chính" : "Đặt Chính"; // NickChinhButton (index 2)
                    rowValues[3] = user.Username;                           // Username (index 3)
                    rowValues[4] = "Xóa";                                    // ActionButton (index 4)

                    // Ghi log để kiểm tra giá trị
                    _gameClient.GetUIManager().AppendLog($"Row values: ID={rowValues[0]}, Username={rowValues[3]}", UIManager.LogLevel.Debug);

                    var row = new DataGridViewRow();
                    row.CreateCells(_dataGridView, rowValues);
                    row.Tag = user.Username;
                    _dataGridView.Rows.Add(row);

                    // Sử dụng chỉ số cột thay vì tên cột, và kiểm tra số cột trước khi truy cập
                    int columnCount = row.Cells.Count;
                    _gameClient.GetUIManager().AppendLog($"Số cột trong hàng: {columnCount}", UIManager.LogLevel.Debug);

                    // Không cần đặt style cho các cột ở đây
                    // Style sẽ được áp dụng trong sự kiện DataGridView_CellFormatting

                    // Đánh dấu các user được chọn
                    if (_selectedUsers.Contains(user.Username) && columnCount > 0)
                    {
                        // ID (Cột 0) - đổi màu nền khi được chọn
                        row.Cells[0].Style.BackColor = Color.FromArgb(0, 120, 215);
                        row.Cells[0].Style.ForeColor = Color.White;
                    }

                    id++;
                }

                _dataGridView.ClearSelection();
                _selectedUsers.Clear();
                _selectedUsers.AddRange(users.Values
                    .Where(u => u != null && u.Username != null && previouslySelectedUsers.Contains(u.Username))
                    .Select(u => u.Username));

                // Đảm bảo các button được hiển thị đúng
                _dataGridView.Refresh();
                _dataGridView.Update();

                // Không thể gọi trực tiếp sự kiện CellFormatting vì nó là protected
                // Thay vào đó, chúng ta sẽ gọi lại sự kiện CellFormatting bằng cách gọi Invalidate cho từng hàng
                for (int rowIndex = 0; rowIndex < _dataGridView.Rows.Count; rowIndex++)
                {
                    // Gọi InvalidateRow để khôi phục lại toàn bộ hàng, hiệu quả hơn gọi InvalidateCell cho từng cell
                    _dataGridView.InvalidateRow(rowIndex);
                }

                _dataGridView.Refresh();
                _gameClient.GetUIManager().AppendLog("Tải danh sách user thành công", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                _gameClient.GetUIManager().AppendLog($"Lỗi khi tải danh sách user: {ex.Message}\nStackTrace: {ex.StackTrace}", UIManager.LogLevel.Error);
                throw;
            }
        }
    }
}