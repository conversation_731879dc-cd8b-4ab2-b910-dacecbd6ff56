# Profile Buttons - Get Key Room & Leave Room

## 📋 Tổng quan

Hệ thống Profile Buttons tự động inject 2 buttons vào mỗi profile được mở:
- **🔑 Get Key**: Tìm bàn trống cho user hiện tại
- **🚪 Thoát bàn**: Tho<PERSON>t khỏi phòng hiện tại

## 🚀 Cách hoạt động

### 1. **Auto Injection**
- Khi mở profile, JavaScript sẽ được inject tự động sau 3-5 giây
- Buttons xuất hiện ở **góc phải trên** của trang web
- Nếu injection thất bại, hệ thống sẽ tự động thử **force inject** với script đơn giản hơn

### 2. **Get Key Button**
- **Trạng thái ban đầu**: `Get Key` (màu xanh)
- **Khi bấm**: <PERSON><PERSON><PERSON><PERSON> thành `Stop` (màu đỏ)
- **Chức năng**: <PERSON><PERSON><PERSON> GetKeyRoomAsync cho user hiện tại
- **Auto-stop**: Tự động chuyển về `Get Key` khi tìm thấy bàn trống hoặc đạt maxAttempts

### 3. **Leave Room Button**
- **Màu**: Xám (#6c757d)
- **Chức năng**: Gọi LeaveRoomAsync cho user hiện tại
- **Luôn sẵn sàng** sử dụng

### 4. **F5 Reload Support**
- Buttons sẽ **tự động xuất hiện lại** sau khi F5 reload trang
- Sử dụng **MutationObserver** để detect DOM changes
- **SessionStorage** để maintain state

## 🔧 Technical Implementation

### **Files được tạo/sửa:**

1. **`Scripts/JavaScriptInjector.cs`** - Class quản lý injection
2. **`Scripts/profile-buttons.js`** - JavaScript chính
3. **`Scripts/test-injection.html`** - Test page
4. **`DriverManager.cs`** - Tích hợp injection vào driver initialization

### **Methods quan trọng:**

```csharp
// Inject buttons tự động
await jsInjector.InjectProfileButtons(driver, username);

// Force inject nếu thất bại
jsInjector.ForceInjectSimpleButtons(driver, username);

// Setup auto-reload handler
jsInjector.SetupPageReloadHandler(driver, username);

// Public method để force inject từ bên ngoài
driverManager.ForceInjectButtons(username);
```

## 🎯 Cách sử dụng

### **1. Mở Profile**
```csharp
// Khi mở profile, buttons sẽ tự động xuất hiện
string remoteDebuggingAddress = await profileManager.OpenProfile(username, null, groupName);
var driver = driverManager.InitializeDriver(username, remoteDebuggingAddress, gameUrl, user);
// Buttons sẽ được inject tự động sau 3-5 giây
```

### **2. Force Inject (nếu cần)**
```csharp
// Nếu buttons không xuất hiện, có thể force inject
driverManager.ForceInjectButtons(username);
```

### **3. Reinject sau F5**
```csharp
// Nếu cần inject lại sau reload
await driverManager.ReinjectJavaScript(username);
```

## 🧪 Testing

### **Test với HTML file:**
1. Mở `Scripts/test-injection.html` trong browser
2. Bấm "Inject Profile Buttons"
3. Kiểm tra buttons ở góc phải trên
4. Test functionality của từng button

### **Test trong ứng dụng:**
1. Mở profile bất kỳ
2. Đợi 3-5 giây để buttons xuất hiện
3. Test Get Key functionality
4. Test Leave Room functionality
5. Test F5 reload (buttons phải xuất hiện lại)

## 🔍 Troubleshooting

### **Buttons không xuất hiện:**
1. Kiểm tra log để xem có lỗi injection không
2. Thử force inject: `driverManager.ForceInjectButtons(username)`
3. Kiểm tra Console của browser (F12) để xem JavaScript errors

### **Buttons mất sau F5:**
1. Kiểm tra MutationObserver có hoạt động không
2. Thử reinject: `await driverManager.ReinjectJavaScript(username)`

### **Buttons không hoạt động:**
1. Kiểm tra Console để xem click events
2. Kiểm tra WebView message communication
3. Verify username detection logic

## 📝 Logs

Hệ thống sẽ log các events sau:
- `✅ Đã inject profile buttons thành công cho {username}`
- `🚀 Force inject simple buttons cho {username}: thành công`
- `⚠️ Inject script thành công nhưng buttons chưa xuất hiện`
- `❌ Lỗi khi inject profile buttons cho {username}`

## 🎨 Styling

Buttons được style với:
- **Position**: Fixed, top-right corner
- **Z-index**: 99999 (luôn ở trên cùng)
- **Size**: 80px width, 32px height
- **Colors**: Blue (#007bff) cho Get Key, Gray (#6c757d) cho Leave Room
- **Hover effects**: Darker shades khi hover

## 🔄 Auto-reload Logic

```javascript
// Setup MutationObserver để detect DOM changes
const observer = new MutationObserver(function(mutations) {
    // Kiểm tra nếu buttons bị mất thì tạo lại
    if (!document.querySelector('.button-container')) {
        initButtons();
    }
});
```

## 🎯 Next Steps

1. **Tích hợp với GetKeyRoom logic** trong C#
2. **WebView message handling** cho communication
3. **Username detection** từ profile context
4. **Error handling** và retry mechanisms
5. **UI feedback** cho user actions
