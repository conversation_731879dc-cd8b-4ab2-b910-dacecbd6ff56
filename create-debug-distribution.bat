@echo off
echo ========================================
echo   AutoGameBai Debug Distribution Creator
echo ========================================

echo.
echo Step 1: Building debug version with protection...
call debug-mode.bat
echo Applying protection to debug build...
call apply-protection.bat

echo.
echo Step 2: Creating distribution folder...
if exist "Distribution-Debug" rmdir /s /q "Distribution-Debug"
mkdir "Distribution-Debug"
mkdir "Distribution-Debug\AutoGameBai"

echo.
echo Step 3: Copying debug files...
copy "bin\Debug\net8.0-windows\AutoGameBai.exe" "Distribution-Debug\AutoGameBai\"
copy "bin\Debug\net8.0-windows\AutoGameBai.dll" "Distribution-Debug\AutoGameBai\"
copy "bin\Debug\net8.0-windows\AutoGameBai.pdb" "Distribution-Debug\AutoGameBai\"
copy "bin\Debug\net8.0-windows\AutoGameBai.runtimeconfig.json" "Distribution-Debug\AutoGameBai\"

echo.
echo Step 4: Copying required dependencies...
xcopy "bin\Debug\net8.0-windows\*.dll" "Distribution-Debug\AutoGameBai\" /Y /Q
xcopy "bin\Debug\net8.0-windows\*.exe" "Distribution-Debug\AutoGameBai\" /Y /Q
xcopy "bin\Debug\net8.0-windows\runtimes" "Distribution-Debug\AutoGameBai\runtimes\" /E /I /Y /Q

echo.
echo Step 5: Copying essential resources...
xcopy "bin\Debug\net8.0-windows\card" "Distribution-Debug\AutoGameBai\card\" /E /I /Y /Q
xcopy "bin\Debug\net8.0-windows\img" "Distribution-Debug\AutoGameBai\img\" /E /I /Y /Q
xcopy "bin\Debug\net8.0-windows\selenium-manager" "Distribution-Debug\AutoGameBai\selenium-manager\" /E /I /Y /Q

echo.
echo Step 6: Copying configuration files...
if exist "bin\Debug\net8.0-windows\*.txt" copy "bin\Debug\net8.0-windows\*.txt" "Distribution-Debug\AutoGameBai\"
if exist "bin\Debug\net8.0-windows\config.json" copy "bin\Debug\net8.0-windows\config.json" "Distribution-Debug\AutoGameBai\"
if exist "*.txt" copy "*.txt" "Distribution-Debug\AutoGameBai\"
if exist "config.json" copy "config.json" "Distribution-Debug\AutoGameBai\"

echo.
echo Step 7: Creating README for users...
echo AutoGameBai - Game Assistant Tool (DEBUG VERSION) > "Distribution-Debug\AutoGameBai\README.txt"
echo. >> "Distribution-Debug\AutoGameBai\README.txt"
echo ⚠️  WARNING: This is a DEBUG version! >> "Distribution-Debug\AutoGameBai\README.txt"
echo - Protection systems are DISABLED >> "Distribution-Debug\AutoGameBai\README.txt"
echo - Can be debugged and analyzed >> "Distribution-Debug\AutoGameBai\README.txt"
echo - For testing purposes only >> "Distribution-Debug\AutoGameBai\README.txt"
echo. >> "Distribution-Debug\AutoGameBai\README.txt"
echo INSTALLATION: >> "Distribution-Debug\AutoGameBai\README.txt"
echo 1. Extract all files to a folder >> "Distribution-Debug\AutoGameBai\README.txt"
echo 2. Run AutoGameBai.exe >> "Distribution-Debug\AutoGameBai\README.txt"
echo 3. Follow the setup instructions >> "Distribution-Debug\AutoGameBai\README.txt"
echo. >> "Distribution-Debug\AutoGameBai\README.txt"
echo REQUIREMENTS: >> "Distribution-Debug\AutoGameBai\README.txt"
echo - Windows 10/11 >> "Distribution-Debug\AutoGameBai\README.txt"
echo - .NET 8.0 Runtime (will auto-install if needed) >> "Distribution-Debug\AutoGameBai\README.txt"
echo - Chrome browser >> "Distribution-Debug\AutoGameBai\README.txt"
echo. >> "Distribution-Debug\AutoGameBai\README.txt"
echo FEATURES: >> "Distribution-Debug\AutoGameBai\README.txt"
echo - Phom game assistance >> "Distribution-Debug\AutoGameBai\README.txt"
echo - Mau Binh game assistance >> "Distribution-Debug\AutoGameBai\README.txt"
echo - Tien Len game assistance >> "Distribution-Debug\AutoGameBai\README.txt"
echo - Smart card suggestions >> "Distribution-Debug\AutoGameBai\README.txt"
echo - Team coordination >> "Distribution-Debug\AutoGameBai\README.txt"

echo.
echo Step 8: Creating ZIP package...
powershell -command "Compress-Archive -Path 'Distribution-Debug\AutoGameBai\*' -DestinationPath 'Distribution-Debug\AutoGameBai-Debug-v1.0.zip' -Force"

echo.
echo ✅ Debug distribution package created successfully!
echo.
echo Files created:
echo - Distribution-Debug\AutoGameBai\ (folder with all files)
echo - Distribution-Debug\AutoGameBai-Debug-v1.0.zip (ready to test)
echo.
echo Package contents:
dir "Distribution-Debug\AutoGameBai" /B

echo.
echo 📦 Ready to test: Distribution-Debug\AutoGameBai-Debug-v1.0.zip
echo 🔒 Protection level: ENABLED (with debug symbols)
echo 📁 Package size:
powershell -command "(Get-Item 'Distribution-Debug\AutoGameBai-Debug-v1.0.zip').Length / 1MB | ForEach-Object { '{0:N2} MB' -f $_ }"

echo.
echo ✅ This debug version has protection enabled but keeps debug symbols for testing.
echo ⚠️  For maximum protection, use create-distribution.bat for production builds.

echo.
pause
