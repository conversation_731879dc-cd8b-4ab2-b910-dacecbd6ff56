﻿namespace AutoGameBai.Forms
{
    partial class AddUserForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing) // Thêm từ khóa override
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            txtUsername = new TextBox();
            btnAdd = new Button();
            btnCancel = new Button();
            label1 = new Label();
            label2 = new Label();
            lblTitle = new Label();
            SuspendLayout();

            // Form properties - Modern style như GameSelectionForm
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(420, 220);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "AddUserForm";
            Text = "AutoGameBai - Thêm User";
            StartPosition = FormStartPosition.CenterScreen;
            BackColor = Color.White;

            // Title label - Modern style
            lblTitle = new Label();
            lblTitle.Text = "👤 Thêm User Mới";
            lblTitle.Font = new Font("Segoe UI", 16, FontStyle.Bold);
            lblTitle.ForeColor = Color.FromArgb(0, 120, 215);
            lblTitle.Location = new Point(20, 20);
            lblTitle.Size = new Size(380, 35);
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            Controls.Add(lblTitle);

            // Username label - Modern style
            label1.AutoSize = false;
            label1.Location = new Point(30, 80);
            label1.Name = "label1";
            label1.Size = new Size(100, 25);
            label1.TabIndex = 4;
            label1.Text = "📝 Username:";
            label1.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            label1.ForeColor = Color.FromArgb(64, 64, 64);
            label1.TextAlign = ContentAlignment.MiddleLeft;

            // Username textbox - Modern style
            txtUsername.Location = new Point(140, 80);
            txtUsername.Name = "txtUsername";
            txtUsername.Size = new Size(250, 25);
            txtUsername.TabIndex = 0;
            txtUsername.Font = new Font("Segoe UI", 10);
            txtUsername.BorderStyle = BorderStyle.FixedSingle;

            // Info label - Modern style
            label2.AutoSize = false;
            label2.Font = new Font("Segoe UI", 9, FontStyle.Italic);
            label2.Location = new Point(30, 115);
            label2.Name = "label2";
            label2.Size = new Size(360, 40);
            label2.TabIndex = 5;
            label2.Text = "💡 Lưu ý: Đây là username tài khoản hiển thị trong game.\nVui lòng nhập chính xác để tránh lỗi kết nối.";
            label2.ForeColor = Color.FromArgb(108, 117, 125);
            label2.TextAlign = ContentAlignment.TopLeft;

            // Add button - Modern style như GameSelectionForm
            btnAdd.Location = new Point(120, 170);
            btnAdd.Name = "btnAdd";
            btnAdd.Size = new Size(90, 35);
            btnAdd.TabIndex = 2;
            btnAdd.Text = "✅ Thêm";
            btnAdd.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            btnAdd.BackColor = Color.FromArgb(0, 120, 215);
            btnAdd.ForeColor = Color.White;
            btnAdd.FlatStyle = FlatStyle.Flat;
            btnAdd.UseVisualStyleBackColor = false;
            btnAdd.FlatAppearance.BorderSize = 0;
            btnAdd.Click += BtnAdd_Click;

            // Cancel button - Modern style như GameSelectionForm
            btnCancel.Location = new Point(230, 170);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(90, 35);
            btnCancel.TabIndex = 3;
            btnCancel.Text = "❌ Hủy";
            btnCancel.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            btnCancel.BackColor = Color.FromArgb(220, 53, 69);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.UseVisualStyleBackColor = false;
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;

            // Add controls to form
            Controls.Add(label2);
            Controls.Add(label1);
            Controls.Add(btnCancel);
            Controls.Add(btnAdd);
            Controls.Add(txtUsername);

            ResumeLayout(false);
            PerformLayout();
        }

        private System.Windows.Forms.TextBox txtUsername;
        private System.Windows.Forms.Button btnAdd;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label lblTitle;
    }
}