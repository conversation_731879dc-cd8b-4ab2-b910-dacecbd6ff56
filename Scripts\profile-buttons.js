// Profile Buttons Script - Get Key Room & Leave Room
// Tạo 2 buttons trên profile: Get Key và Thoát bàn

(function() {
    'use strict';

    // Tránh load multiple lần
    if (window.profileButtonsLoaded) {
        console.log('Profile buttons đã được load, skip...');
        return;
    }
    window.profileButtonsLoaded = true;

    let getKeyButton = null;
    let leaveRoomButton = null;
    let isGetKeyRunning = false;
    let getKeyInterval = null;

    // Tạo CSS styles cho buttons
    function createStyles() {
        // Kiểm tra nếu style đã tồn tại
        if (document.getElementById('profile-buttons-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'profile-buttons-styles';
        style.textContent = `
            .profile-button {
                padding: 6px 12px;
                margin: 3px;
                border: none;
                border-radius: 4px;
                font-size: 11px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 70px;
                height: 28px;
            }

            .get-key-btn {
                background-color: #007bff;
                color: white;
            }

            .get-key-btn:hover {
                background-color: #0056b3;
            }

            .get-key-btn.stop {
                background-color: #dc3545;
                color: white;
            }

            .get-key-btn.stop:hover {
                background-color: #c82333;
            }

            .leave-room-btn {
                background-color: #dc3545;
                color: white;
            }

            .leave-room-btn:hover {
                background-color: #c82333;
            }

            .button-container {
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 99999;
                display: flex;
                gap: 5px;
            }
        `;
        document.head.appendChild(style);
    }

    // Tạo Get Key button
    function createGetKeyButton() {
        getKeyButton = document.createElement('button');
        getKeyButton.className = 'profile-button get-key-btn';
        getKeyButton.textContent = 'Get Key';

        // Gán sự kiện click với test alert
        getKeyButton.onclick = function() {
            const username = getCurrentUsername();
            console.log('🔑 Get Key button clicked for:', username);
            alert('🔑 Get Key button hoạt động! Username: ' + username);
            toggleGetKey();
        };

        return getKeyButton;
    }

    // Tạo Leave Room button
    function createLeaveRoomButton() {
        leaveRoomButton = document.createElement('button');
        leaveRoomButton.className = 'profile-button leave-room-btn';
        leaveRoomButton.textContent = 'Thoát bàn';

        // Gán sự kiện click với test alert
        leaveRoomButton.onclick = function() {
            const username = getCurrentUsername();
            console.log('🚪 Leave Room button clicked for:', username);
            alert('🚪 Leave Room button hoạt động! Username: ' + username);
            leaveRoom();
        };

        return leaveRoomButton;
    }

    // Toggle Get Key functionality
    function toggleGetKey() {
        if (!isGetKeyRunning) {
            startGetKey();
        } else {
            stopGetKey();
        }
    }

    // Bắt đầu Get Key
    function startGetKey() {
        isGetKeyRunning = true;
        getKeyButton.textContent = 'Stop';
        getKeyButton.classList.add('stop');

        console.log('🔑 Bắt đầu Get Key Room...');

        // Gọi API GetKeyRoom
        callGetKeyRoom();

        // Thiết lập interval để kiểm tra trạng thái
        getKeyInterval = setInterval(() => {
            if (isGetKeyRunning) {
                callGetKeyRoom();
            }
        }, 1000); // Kiểm tra mỗi giây
    }

    // Dừng Get Key
    function stopGetKey() {
        isGetKeyRunning = false;
        getKeyButton.textContent = 'Get Key';
        getKeyButton.classList.remove('stop');

        if (getKeyInterval) {
            clearInterval(getKeyInterval);
            getKeyInterval = null;
        }

        console.log('🛑 Đã dừng Get Key Room');
    }

    // Gọi API GetKeyRoom
    function callGetKeyRoom() {
        try {
            // Lấy username từ profile hiện tại
            const username = getCurrentUsername();
            if (!username) {
                console.error('Không tìm thấy username');
                stopGetKey();
                return;
            }

            console.log(`🔑 Bắt đầu GetKeyRoom cho ${username}`);

            // Gọi C# method thông qua window object
            if (window.chrome && window.chrome.webview) {
                window.chrome.webview.postMessage({
                    action: 'getKeyRoom',
                    username: username,
                    roomId: '100', // Default room
                    maxAttempts: 10,
                    attemptDelay: 200
                });
                console.log(`📤 Đã gửi message GetKeyRoom cho ${username}`);
            } else {
                console.log(`🔑 GetKeyRoom called for ${username} (WebView không khả dụng)`);
                // Fallback: thử gọi trực tiếp nếu có method
                if (window.gameClient && window.gameClient.getKeyRoom) {
                    window.gameClient.getKeyRoom(username);
                } else {
                    console.warn('Không tìm thấy WebView hoặc gameClient method');
                }
            }
        } catch (error) {
            console.error('Lỗi khi gọi GetKeyRoom:', error);
            stopGetKey();
        }
    }

    // Thoát phòng
    function leaveRoom() {
        try {
            const username = getCurrentUsername();
            if (!username) {
                console.error('Không tìm thấy username');
                return;
            }

            console.log(`🚪 Bắt đầu thoát phòng cho ${username}`);

            // Gọi C# method thông qua window object
            if (window.chrome && window.chrome.webview) {
                window.chrome.webview.postMessage({
                    action: 'leaveRoom',
                    username: username
                });
                console.log(`📤 Đã gửi message LeaveRoom cho ${username}`);
            } else {
                console.log(`🚪 LeaveRoom called for ${username} (WebView không khả dụng)`);
                // Fallback: thử gọi trực tiếp nếu có method
                if (window.gameClient && window.gameClient.leaveRoom) {
                    window.gameClient.leaveRoom(username);
                } else {
                    console.warn('Không tìm thấy WebView hoặc gameClient method');
                }
            }
        } catch (error) {
            console.error('Lỗi khi thoát phòng:', error);
        }
    }

    // Lấy username từ profile hiện tại
    function getCurrentUsername() {
        // Thử các cách khác nhau để lấy username

        // Cách 1: Từ URL
        const urlParams = new URLSearchParams(window.location.search);
        let username = urlParams.get('username') || urlParams.get('user');

        // Cách 2: Từ localStorage
        if (!username) {
            username = localStorage.getItem('username') || localStorage.getItem('currentUser');
        }

        // Cách 3: Từ sessionStorage
        if (!username) {
            username = sessionStorage.getItem('username') || sessionStorage.getItem('currentUser');
        }

        // Cách 4: Từ title hoặc các element trên page
        if (!username) {
            const titleMatch = document.title.match(/Profile:\s*(.+)/i);
            if (titleMatch) {
                username = titleMatch[1].trim();
            }
        }

        // Cách 5: Từ các element có class hoặc id chứa username
        if (!username) {
            const usernameElements = document.querySelectorAll('[class*="username"], [id*="username"], [class*="user"], [id*="user"]');
            for (const element of usernameElements) {
                if (element.textContent && element.textContent.trim()) {
                    username = element.textContent.trim();
                    break;
                }
            }
        }

        return username;
    }

    // Khởi tạo buttons
    function initButtons() {
        // Xóa buttons cũ nếu có
        const existingContainer = document.querySelector('.button-container');
        if (existingContainer) {
            existingContainer.remove();
        }

        // Tạo styles
        createStyles();

        // Tạo container
        const container = document.createElement('div');
        container.className = 'button-container';

        // Tạo và thêm buttons
        container.appendChild(createGetKeyButton());
        container.appendChild(createLeaveRoomButton());

        // Thêm vào body
        document.body.appendChild(container);

        console.log('✅ Profile buttons đã được khởi tạo');
    }

    // Lắng nghe messages từ C#
    function setupMessageListener() {
        if (window.chrome && window.chrome.webview) {
            window.chrome.webview.addEventListener('message', function(event) {
                const data = event.data;

                if (data.action === 'getKeyRoomResult') {
                    if (data.success) {
                        console.log('✅ Tìm thấy bàn trống!');
                        stopGetKey();
                    } else if (data.maxAttemptsReached) {
                        console.log('❌ Đã đạt số lần thử tối đa');
                        stopGetKey();
                    }
                } else if (data.action === 'stopGetKey') {
                    stopGetKey();
                }
            });
        }
    }

    // Khởi tạo khi DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            initButtons();
            setupMessageListener();
        });
    } else {
        initButtons();
        setupMessageListener();
    }

    // Setup auto-reload detection
    function setupAutoReload() {
        // Lưu trạng thái vào sessionStorage
        sessionStorage.setItem('profileButtonsActive', 'true');

        // Setup event listener cho beforeunload
        window.addEventListener('beforeunload', function() {
            sessionStorage.removeItem('profileButtonsActive');
        });

        // Setup MutationObserver để detect DOM changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Kiểm tra nếu buttons bị mất thì tạo lại
                    setTimeout(() => {
                        if (!document.querySelector('.button-container')) {
                            initButtons();
                        }
                    }, 1000);
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Export functions for external access
    window.profileButtons = {
        startGetKey: startGetKey,
        stopGetKey: stopGetKey,
        leaveRoom: leaveRoom,
        isGetKeyRunning: () => isGetKeyRunning,
        reinit: initButtons
    };

    // Setup auto-reload
    setupAutoReload();

})();
