using System;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace AutoGameBai.Protection
{
    /// <summary>
    /// Helper class to reduce antivirus false positives
    /// </summary>
    public static class AntivirusCompatibility
    {
        /// <summary>
        /// Gentle process termination instead of Kill()
        /// </summary>
        public static async Task<bool> GentleTerminateProcess(Process process, int timeoutMs = 5000)
        {
            try
            {
                // Try graceful close first
                if (!process.HasExited)
                {
                    process.CloseMainWindow();
                    
                    // Wait for graceful exit
                    if (await WaitForExitAsync(process, timeoutMs / 2))
                    {
                        return true;
                    }
                }

                // If still running, try terminate
                if (!process.HasExited)
                {
                    process.Kill();
                    return await WaitForExitAsync(process, timeoutMs / 2);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Async wait for process exit
        /// </summary>
        private static async Task<bool> WaitForExitAsync(Process process, int timeoutMs)
        {
            return await Task.Run(() =>
            {
                try
                {
                    return process.WaitForExit(timeoutMs);
                }
                catch
                {
                    return false;
                }
            });
        }

        /// <summary>
        /// Safe debugger check without suspicious patterns
        /// </summary>
        public static bool IsDebuggingEnvironment()
        {
            try
            {
                // Less suspicious way to check debugging
                return System.Diagnostics.Debugger.IsAttached;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Safe file operations with proper error handling
        /// </summary>
        public static async Task<bool> SafeFileOperation(Func<Task> operation, string operationName)
        {
            try
            {
                await operation();
                return true;
            }
            catch (UnauthorizedAccessException)
            {
                // Log but don't crash - might be antivirus blocking
                Console.WriteLine($"Access denied for {operationName} - possibly blocked by antivirus");
                return false;
            }
            catch (IOException ex)
            {
                // Handle file in use scenarios
                Console.WriteLine($"File operation failed for {operationName}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error in {operationName}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Create whitelist instructions for users
        /// </summary>
        public static string GetWhitelistInstructions()
        {
            return @"
🛡️ HƯỚNG DẪN THÊM VÀO WHITELIST ANTIVIRUS:

📌 Windows Defender:
1. Mở Windows Security → Virus & threat protection
2. Chọn 'Manage settings' under Virus & threat protection settings
3. Chọn 'Add or remove exclusions'
4. Thêm folder chứa AutoGameBai.exe

📌 Avast/AVG:
1. Mở Avast → Settings → General → Exceptions
2. Thêm đường dẫn đến AutoGameBai.exe

📌 Kaspersky:
1. Mở Kaspersky → Settings → Additional → Threats and Exclusions
2. Thêm exclusion cho AutoGameBai.exe

📌 Norton:
1. Mở Norton → Settings → Antivirus → Scans and Risks
2. Thêm exclusion cho file hoặc folder

⚠️ LƯU Ý: Phần mềm này an toàn 100%, chỉ dùng để automation game.
Antivirus có thể báo nhầm do các tính năng automation.
";
        }

        /// <summary>
        /// Check if running in sandbox environment
        /// </summary>
        public static bool IsRunningSandboxed()
        {
            try
            {
                // Simple sandbox detection
                var userName = Environment.UserName?.ToLower();
                var computerName = Environment.MachineName?.ToLower();

                // Common sandbox indicators
                string[] sandboxIndicators = {
                    "sandbox", "malware", "virus", "analysis", "cuckoo",
                    "vmware", "vbox", "virtualbox", "qemu", "wine"
                };

                foreach (var indicator in sandboxIndicators)
                {
                    if (userName?.Contains(indicator) == true || 
                        computerName?.Contains(indicator) == true)
                    {
                        return true;
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
