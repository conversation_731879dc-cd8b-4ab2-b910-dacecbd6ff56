using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace AutoGameBai.Protection
{
    /// <summary>
    /// Advanced string protection and encryption
    /// </summary>
    public static class StringProtection
    {
        // Dynamic keys generated at runtime
        private static readonly byte[] _key1 = GenerateKey(16);
        private static readonly byte[] _key2 = GenerateKey(24);
        private static readonly byte[] _iv = GenerateKey(16);

        /// <summary>
        /// Generate dynamic encryption key
        /// </summary>
        private static byte[] GenerateKey(int length)
        {
            var key = new byte[length];
            var seed = Environment.TickCount ^ Environment.ProcessorCount ^ DateTime.Now.Millisecond;
            var rng = new Random(seed);
            rng.NextBytes(key);
            return key;
        }

        /// <summary>
        /// Encrypt string with AES-256
        /// </summary>
        public static string Encrypt(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
                return plainText;

            try
            {
                using (var aes = Aes.Create())
                {
                    aes.Key = CombineKeys(_key1, _key2);
                    aes.IV = _iv;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    using (var encryptor = aes.CreateEncryptor())
                    using (var ms = new MemoryStream())
                    using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    using (var writer = new StreamWriter(cs))
                    {
                        writer.Write(plainText);
                        writer.Flush();
                        cs.FlushFinalBlock();
                        return Convert.ToBase64String(ms.ToArray());
                    }
                }
            }
            catch
            {
                return plainText; // Fallback
            }
        }

        /// <summary>
        /// Decrypt string with AES-256
        /// </summary>
        public static string Decrypt(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
                return cipherText;

            try
            {
                var buffer = Convert.FromBase64String(cipherText);
                
                using (var aes = Aes.Create())
                {
                    aes.Key = CombineKeys(_key1, _key2);
                    aes.IV = _iv;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    using (var decryptor = aes.CreateDecryptor())
                    using (var ms = new MemoryStream(buffer))
                    using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                    using (var reader = new StreamReader(cs))
                    {
                        return reader.ReadToEnd();
                    }
                }
            }
            catch
            {
                return cipherText; // Fallback
            }
        }

        /// <summary>
        /// Combine two keys into one
        /// </summary>
        private static byte[] CombineKeys(byte[] key1, byte[] key2)
        {
            var combined = new byte[32]; // AES-256 key size
            for (int i = 0; i < 16; i++)
            {
                combined[i] = key1[i];
                combined[i + 16] = key2[i];
            }
            return combined;
        }

        /// <summary>
        /// Protected constants - will be encrypted by ConfuserEx
        /// </summary>
        public static class ProtectedStrings
        {
            public static string Password => Decrypt("ENCRYPTED_PASSWORD_HERE");
            public static string ApiUrlPattern => Decrypt("ENCRYPTED_API_PATTERN_HERE");
            public static string ChromeDriverPath => Decrypt("ENCRYPTED_CHROMEDRIVER_PATH_HERE");
            public static string HitClubUrl => Decrypt("ENCRYPTED_HITCLUB_URL_HERE");
            public static string SunWinUrl => Decrypt("ENCRYPTED_SUNWIN_URL_HERE");
        }

        /// <summary>
        /// Obfuscated method names
        /// </summary>
        public static class ObfuscatedMethods
        {
            public const string SuggestCard = "A1B2C3D4";
            public const string FindBestCard = "E5F6G7H8";
            public const string AnalyzeCards = "I9J0K1L2";
            public const string OptimizeHand = "M3N4O5P6";
            public const string CalculateScore = "Q7R8S9T0";
        }
    }
}
