using System;

namespace AutoGameBai
{
    /// <summary>
    /// Debug configuration for development
    /// </summary>
    public static class DebugConfig
    {
        /// <summary>
        /// Enable/disable debug mode
        /// Set to true for debugging, false for production
        /// </summary>
        public static bool IsDebugMode => 
#if DEBUG
            true;
#else
            false;
#endif

        /// <summary>
        /// Enable/disable protection systems
        /// </summary>
        public static bool EnableProtection => !IsDebugMode;

        /// <summary>
        /// Enable/disable verbose logging
        /// </summary>
        public static bool EnableVerboseLogging => IsDebugMode;

        /// <summary>
        /// Enable/disable anti-debug protection
        /// </summary>
        public static bool EnableAntiDebug => EnableProtection;

        /// <summary>
        /// Enable/disable string encryption
        /// </summary>
        public static bool EnableStringEncryption => EnableProtection;

        /// <summary>
        /// Print debug configuration
        /// </summary>
        public static void PrintConfiguration()
        {
            Console.WriteLine("=== DEBUG CONFIGURATION ===");
            Console.WriteLine($"Debug Mode: {IsDebugMode}");
            Console.WriteLine($"Protection Enabled: {EnableProtection}");
            Console.WriteLine($"Verbose Logging: {EnableVerboseLogging}");
            Console.WriteLine($"Anti-Debug: {EnableAntiDebug}");
            Console.WriteLine($"String Encryption: {EnableStringEncryption}");
            Console.WriteLine("============================");
        }
    }
}
