﻿using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoGameBai.Models;

namespace AutoGameBai.Room
{
    public class RoomStateManager
    {
        private readonly Dictionary<string, List<User>> _roomPlayers = new();
        private readonly Dictionary<string, int> _roomTimes = new();
        private readonly Dictionary<string, int> _userSeats = new();
        private readonly Dictionary<string, int[]> _userCards = new();
        private readonly UIManager _uiManager;
        private readonly object _lock = new object();
        private readonly GameClientManager _gameClient;

        public RoomStateManager(UIManager uiManager, GameClientManager gameClient)
        {
            _uiManager = uiManager ?? throw new ArgumentNullException(nameof(uiManager));
            _gameClient = gameClient ?? throw new ArgumentNullException(nameof(gameClient));
        }

        public Dictionary<string, List<User>> GetRoomPlayers() => _roomPlayers;
        public Dictionary<string, int> GetRoomTimes() => _roomTimes;
        public Dictionary<string, int> GetUserSeats() => _userSeats;
        public Dictionary<string, int[]> GetUserCards() => _userCards;

        /// <summary>
        /// Lấy key room cho user cụ thể - tương tự GetEmptyTable nhưng chỉ cho 1 user
        /// </summary>
        public async Task<bool> GetKeyRoom(string username, string roomId, int maxAttempts, int attemptDelay, CancellationToken cancellationToken)
        {
            try
            {
                if (!_gameClient.GetDriverManager().IsDriverActive(username))
                {
                    _uiManager.AppendLog($"Driver cho {username} không hoạt động", UIManager.LogLevel.Error, username);
                    return false;
                }

                var driver = _gameClient.GetDriverManager().GetDrivers()[username];
                if (!_gameClient.IsInLobby(username, driver))
                {
                    _uiManager.AppendLog($"User {username} không ở giao diện vào phòng", UIManager.LogLevel.Error, username);
                    return false;
                }

                _uiManager.AppendLog($"🔑 Bắt đầu lấy key room cho {username}, roomId: {roomId}, maxAttempts: {maxAttempts}", UIManager.LogLevel.Info, username);

                for (int attempt = 1; attempt <= maxAttempts; attempt++)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    _uiManager.AppendLog($"🔍 Thử lần {attempt}/{maxAttempts} lấy key room cho {username}", UIManager.LogLevel.Info, username);

                    var (success, seat, playerCount) = await JoinRoom(username, roomId, driver, cancellationToken, attemptDelay);
                    if (success && seat == 0 && playerCount == 1)
                    {
                        _uiManager.AppendLog($"✅ Tìm thấy bàn trống hợp lệ cho {username} (sit: {seat}, số người: {playerCount})", UIManager.LogLevel.Info, username);
                        return true;
                    }
                    else if (success)
                    {
                        _uiManager.AppendLog($"❌ Bàn không hợp lệ cho {username} (sit: {seat}, số người: {playerCount}), thoát và thử lại", UIManager.LogLevel.Info, username);
                        await LeaveRoomWithJavaScript(username, driver);

                        // Đợi user về lobby trước khi thử lại
                        await WaitForLobby(username, driver, cancellationToken);

                        // Delay ngắn trước khi thử lại
                        if (attempt < maxAttempts)
                        {
                            await Task.Delay(attemptDelay, cancellationToken);
                        }
                    }
                    else
                    {
                        // Nếu không join được phòng, delay trước khi thử lại
                        if (attempt < maxAttempts)
                        {
                            await Task.Delay(attemptDelay, cancellationToken);
                        }
                    }
                }

                _uiManager.AppendLog($"❌ Không tìm thấy bàn trống sau {maxAttempts} lần thử cho {username}", UIManager.LogLevel.Warning, username);
                return false;
            }
            catch (OperationCanceledException)
            {
                _uiManager.AppendLog($"🛑 Đã hủy lấy key room cho {username}", UIManager.LogLevel.Info, username);
                return false;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi lấy key room cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                return false;
            }
        }

        public async Task<(bool Success, int Seat, int PlayerCount)> JoinRoom(string username, string roomId, ChromeDriver driver, CancellationToken cancellationToken, int attemptDelay = 200)
        {
            var startTime = DateTime.Now;
            var wait = new WebDriverWait(driver, TimeSpan.FromSeconds(10));

            try
            {
                if (!_gameClient.GetDriverManager().IsDriverActive(username))
                {
                    _uiManager.AppendLog($"Driver cho {username} không hoạt động, hủy vào phòng", UIManager.LogLevel.Error, username);
                    return (false, -1, 0);
                }

                // Thử sử dụng JavaScript function trước
                var jsResult = await TryJoinRoomWithJavaScript(username, roomId, driver, cancellationToken, attemptDelay);
                if (jsResult.Success)
                {
                    return jsResult;
                }

                // Fallback về click tọa độ nếu JavaScript không thành công
                _uiManager.AppendLog($"JavaScript join room không thành công, fallback về click tọa độ cho {username}", UIManager.LogLevel.Warning, username);
                return await JoinRoomWithCoordinates(username, roomId, driver, cancellationToken, attemptDelay);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi vào phòng cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                throw;
            }
            finally
            {
                _uiManager.AppendLog($"Hoàn thành JoinRoom cho {username}, thời gian: {(DateTime.Now - startTime).TotalMilliseconds}ms", UIManager.LogLevel.Info, username);
            }
        }

        /// <summary>
        /// Thử join room bằng JavaScript function
        /// </summary>
        private async Task<(bool Success, int Seat, int PlayerCount)> TryJoinRoomWithJavaScript(string username, string roomId, ChromeDriver driver, CancellationToken cancellationToken, int attemptDelay)
        {
            try
            {
                _uiManager.AppendLog($"🔧 Thử join room bằng JavaScript cho {username}, roomId: {roomId}", UIManager.LogLevel.Info, username);

                // Kiểm tra xem có function callGetKeyRoom hoặc tương tự không
                string checkFunctionScript = @"
                    (function() {
                        // Kiểm tra các function có thể dùng để join room
                        const functions = [];

                        if (typeof callGetKeyRoom === 'function') {
                            functions.push('callGetKeyRoom');
                        }
                        if (typeof requestJoinRoom === 'function') {
                            functions.push('requestJoinRoom');
                        }
                        if (window.csharpMethods && typeof window.csharpMethods.getKeyRoom === 'function') {
                            functions.push('csharpMethods.getKeyRoom');
                        }

                        return functions;
                    })();";

                var availableFunctions = ((IJavaScriptExecutor)driver).ExecuteScript(checkFunctionScript) as System.Collections.ObjectModel.ReadOnlyCollection<object>;

                if (availableFunctions != null && availableFunctions.Count > 0)
                {
                    _uiManager.AppendLog($"✅ Tìm thấy JavaScript functions: {string.Join(", ", availableFunctions)} cho {username}", UIManager.LogLevel.Info, username);

                    // Thử gọi function đầu tiên có sẵn
                    string functionName = availableFunctions[0].ToString();
                    string callScript = functionName switch
                    {
                        "callGetKeyRoom" => $"callGetKeyRoom('{roomId}');",
                        "requestJoinRoom" => $"requestJoinRoom('{roomId}', '', '', '', '');",
                        "csharpMethods.getKeyRoom" => $"window.csharpMethods.getKeyRoom('{username}');",
                        _ => null
                    };

                    if (!string.IsNullOrEmpty(callScript))
                    {
                        _uiManager.AppendLog($"🔧 Gọi JavaScript function: {callScript} cho {username}", UIManager.LogLevel.Info, username);

                        var tcs = new TaskCompletionSource<bool>();
                        _gameClient.GetWebSocketManager().SetRoomJoinedTcs(username, tcs);

                        ((IJavaScriptExecutor)driver).ExecuteScript(callScript);

                        var completedTask = await Task.WhenAny(tcs.Task, Task.Delay(5000, cancellationToken));
                        if (completedTask == tcs.Task && tcs.Task.IsCompletedSuccessfully)
                        {
                            bool joinResult = tcs.Task.Result;
                            if (joinResult)
                            {
                                lock (_lock)
                                {
                                    if (_userSeats.ContainsKey(username) && _roomPlayers.ContainsKey(username))
                                    {
                                        int seat = _userSeats[username];
                                        int playerCount = _roomPlayers[username].Count;
                                        _uiManager.AppendLog($"✅ JavaScript join room thành công cho {username} (sit: {seat}, số người: {playerCount})", UIManager.LogLevel.Info, username);
                                        return (true, seat, playerCount);
                                    }
                                }
                            }
                        }
                    }
                }

                _uiManager.AppendLog($"❌ Không tìm thấy JavaScript function phù hợp cho {username}", UIManager.LogLevel.Warning, username);
                return (false, -1, 0);
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi thử JavaScript join room cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                return (false, -1, 0);
            }
        }

        /// <summary>
        /// Join room bằng click tọa độ (phương pháp cũ)
        /// </summary>
        private async Task<(bool Success, int Seat, int PlayerCount)> JoinRoomWithCoordinates(string username, string roomId, ChromeDriver driver, CancellationToken cancellationToken, int attemptDelay)
        {
            var startTime = DateTime.Now;
            var wait = new WebDriverWait(driver, TimeSpan.FromSeconds(10));

            try
            {
                wait.Until(d => ((IJavaScriptExecutor)d).ExecuteScript("return document.readyState")?.ToString() == "complete");
                cancellationToken.ThrowIfCancellationRequested();

                string canvasXPath = "//canvas[@id='GameCanvas']";
                _uiManager.AppendLog($"Đang tìm canvas bằng XPath: {canvasXPath} cho {username}", UIManager.LogLevel.Info, username);

                var canvasElement = wait.Until(d => d.FindElement(By.XPath(canvasXPath)));
                _uiManager.AppendLog($"Đã tìm thấy canvas cho {username}", UIManager.LogLevel.Info, username);

                var canvasWidth = Convert.ToInt64(((IJavaScriptExecutor)driver).ExecuteScript("return document.getElementById('GameCanvas').width;"));
                var canvasHeight = Convert.ToInt64(((IJavaScriptExecutor)driver).ExecuteScript("return document.getElementById('GameCanvas').height;"));
                _uiManager.AppendLog($"Kích thước canvas cho {username}: {canvasWidth}x{canvasHeight}", UIManager.LogLevel.Info, username);

                // Tọa độ được điều chỉnh cho kích thước profile mới (700x500) - scale 1.08x (width) và 1.11x (height)
                Dictionary<string, Dictionary<string, (int x, int y)>> roomCoordinates = new()
                {
                    {
                        "HitClub", new Dictionary<string, (int x, int y)>
                        {
                            { "100", (200, 139) }, { "500", (336, 139) }, { "1K", (477, 143) }, { "2K", (201, 213) }, { "5K", (348, 213) }, { "10K", (481, 210) }, { "20K", (196, 296) }, { "50K", (340, 300) }, { "100K", (488, 289) }, { "200K", (204, 367) }, { "500K", (341, 369) }, { "1M", (488, 373) }
                        }
                    },
                    {
                        "SunWin", new Dictionary<string, (int x, int y)>
                        {
                            { "100", (201, 140) }, { "500", (348, 127) }, { "1K", (492, 129) }, { "2K", (205, 200) }, { "5K", (350, 199) }, { "10K", (495, 199) }, { "20K", (202, 267) }, { "50K", (347, 269) }, { "100K", (494, 269) }, { "200K", (207, 336) }, { "500K", (352, 337) }, { "1M", (491, 340) }
                        }
                    }
                };

                string game = driver.Url.Contains("hit.club") ? "HitClub" : "SunWin";
                var coordinates = roomCoordinates[game];
                var (x, y) = coordinates.ContainsKey(roomId) ? coordinates[roomId] : coordinates["100"];
                if (x >= canvasWidth || y >= canvasHeight)
                {
                    _uiManager.AppendLog($"Tọa độ ({x}, {y}) ngoài giới hạn canvas ({canvasWidth}x{canvasHeight}) cho {username}", UIManager.LogLevel.Error, username);
                    throw new Exception("Tọa độ click ngoài giới hạn canvas");
                }

                bool clickSuccessful = false;
                int maxAttempts = 2;
                int attempt = 0;
                int seat = -1;
                int playerCount = 0;

                var tcs = new TaskCompletionSource<bool>();
                _gameClient.GetWebSocketManager().SetRoomJoinedTcs(username, tcs);
                _uiManager.AppendLog($"Created TaskCompletionSource for {username} in JoinRoom at {DateTime.Now:HH:mm:ss.fff}", UIManager.LogLevel.Debug, username);

                try
                {
                    while (!clickSuccessful && attempt < maxAttempts)
                    {
                        if (cancellationToken.IsCancellationRequested)
                        {
                            _uiManager.AppendLog($"Đã hủy click vào phòng cho {username}", UIManager.LogLevel.Info, username);
                            throw new OperationCanceledException();
                        }

                        attempt++;
                        _uiManager.AppendLog($"Thử lần {attempt}/{maxAttempts} click vào phòng {roomId} cho {username}", UIManager.LogLevel.Info, username);

                        string clickScript = $@"
                            var canvas = document.getElementById('GameCanvas');
                            if (canvas) {{
                                var rect = canvas.getBoundingClientRect();
                                var scaleX = canvas.width / rect.width;
                                var scaleY = canvas.height / rect.height;
                                var adjustedX = {x} * scaleX;
                                var adjustedY = {y} * scaleY;
                                var mousedown = new MouseEvent('mousedown', {{
                                    view: window,
                                    bubbles: true,
                                    cancelable: true,
                                    clientX: rect.left + adjustedX,
                                    clientY: rect.top + adjustedY,
                                    button: 0
                                }});
                                canvas.dispatchEvent(mousedown);
                                var mouseup = new MouseEvent('mouseup', {{
                                    view: window,
                                    bubbles: true,
                                    cancelable: true,
                                    clientX: rect.left + adjustedX,
                                    clientY: rect.top + adjustedY,
                                    button: 0
                                }});
                                canvas.dispatchEvent(mouseup);
                                var click = new MouseEvent('click', {{
                                    view: window,
                                    bubbles: true,
                                    cancelable: true,
                                    clientX: rect.left + adjustedX,
                                    clientY: rect.top + adjustedY,
                                    button: 0
                                }});
                                canvas.dispatchEvent(click);
                                var pointerdown = new PointerEvent('pointerdown', {{
                                    view: window,
                                    bubbles: true,
                                    cancelable: true,
                                    clientX: rect.left + adjustedX,
                                    clientY: rect.top + adjustedY,
                                    pointerType: 'mouse',
                                    isPrimary: true
                                }});
                                canvas.dispatchEvent(pointerdown);
                                var pointerup = new PointerEvent('pointerup', {{
                                    view: window,
                                    bubbles: true,
                                    cancelable: true,
                                    clientX: rect.left + adjustedX,
                                    clientY: rect.top + adjustedY,
                                    pointerType: 'mouse',
                                    isPrimary: true
                                }});
                                canvas.dispatchEvent(pointerup);
                                return 'Click successful';
                            }} else {{
                                return 'Canvas not found';
                            }}";
                        var result = ((IJavaScriptExecutor)driver).ExecuteScript(clickScript)?.ToString();
                        if (result != "Click successful")
                        {
                            _uiManager.AppendLog($"Không thể click lần 1 vào phòng {roomId} tại tọa độ ({x}, {y}) cho {username}: {result}", UIManager.LogLevel.Warning, username);
                            if (attempt == maxAttempts)
                            {
                                return (false, -1, 0);
                            }
                            await Task.Delay(500, cancellationToken);
                            continue;
                        }
                        _uiManager.AppendLog($"Click lần 1 thành công vào phòng {roomId} tại tọa độ ({x}, {y}) cho {username}", UIManager.LogLevel.Info, username);

                        var completedTask = await Task.WhenAny(tcs.Task, Task.Delay(3000, cancellationToken));
                        if (completedTask == tcs.Task && tcs.Task.IsCompletedSuccessfully)
                        {
                            bool joinResult = tcs.Task.Result;
                            if (joinResult)
                            {
                                clickSuccessful = true;
                                _uiManager.AppendLog($"Nhận được cmd: 202 cho {username} (thời gian: {(DateTime.Now - startTime).TotalMilliseconds}ms)", UIManager.LogLevel.Info, username);

                                lock (_lock)
                                {
                                    if (!_userSeats.ContainsKey(username) || !_roomPlayers.ContainsKey(username))
                                    {
                                        _uiManager.AppendLog($"Dữ liệu phòng không hợp lệ sau cmd 202 cho {username}", UIManager.LogLevel.Warning, username);
                                        return (false, -1, 0);
                                    }
                                    seat = _userSeats[username];
                                    playerCount = _roomPlayers[username].Count;
                                    _uiManager.AppendLog($"Xác nhận trạng thái phòng cho {username}: sit={seat}, số người={playerCount}", UIManager.LogLevel.Debug, username);
                                    return (true, seat, playerCount);
                                }
                            }
                            else
                            {
                                _uiManager.AppendLog($"⚡ BaseWebSocketHandler đã tự động thoát phòng cho {username}, thử lại ngay", UIManager.LogLevel.Info, username);
                                // Không set clickSuccessful = true để tiếp tục thử lại ngay
                                // Không delay ở đây vì delay sẽ được xử lý ở GetKeyRoom
                                continue; // Thử lại ngay
                            }
                        }
                        else
                        {
                            _uiManager.AppendLog($"Timeout 3000ms khi chờ cmd: 202 cho {username}, thực hiện click 2 lần", UIManager.LogLevel.Warning, username);
                        }

                        if (attempt < maxAttempts && !clickSuccessful)
                        {
                            await Task.Delay(500, cancellationToken);
                        }
                    }

                    _uiManager.AppendLog($"Không thể vào phòng {roomId} sau {maxAttempts} lần thử cho {username}", UIManager.LogLevel.Error, username);
                    return (false, -1, 0);
                }
                finally
                {
                    // KHÔNG xóa tcs ở đây nữa, chỉ xóa ở WebSocketHandler sau khi TrySetResult
                }
            }
            catch (OperationCanceledException)
            {
                _uiManager.AppendLog($"Đã hủy vào phòng cho {username}", UIManager.LogLevel.Info, username);
                throw;
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi vào phòng cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                throw;
            }
            finally
            {
                _uiManager.AppendLog($"Hoàn thành JoinRoom cho {username}, thời gian: {(DateTime.Now - startTime).TotalMilliseconds}ms", UIManager.LogLevel.Info, username);
            }
        }

        public async Task LeaveRoomWithJavaScript(string username, ChromeDriver driver)
        {
            var startTime = DateTime.Now;
            try
            {
                if (!_gameClient.GetDriverManager().IsDriverActive(username))
                {
                    _uiManager.AppendLog($"Driver cho {username} không hoạt động, coi như đã thoát phòng", UIManager.LogLevel.Warning, username);
                    lock (_lock)
                    {
                        _roomPlayers.Remove(username);
                        _roomTimes.Remove(username);
                        _userSeats.Remove(username);
                        _userCards.Remove(username);
                    }
                    return;
                }

                lock (_lock)
                {
                    if (_userCards.ContainsKey(username))
                    {
                        _uiManager.AppendLog($"User {username} đã nhận bài (cmd 600), bỏ qua thoát phòng để tránh crash", UIManager.LogLevel.Warning, username);
                        return;
                    }
                }

                _uiManager.AppendLog($"Đang thử rời phòng bằng JavaScript cho {username}", UIManager.LogLevel.Info, username);

                string leaveRoomScript = @"
                (function() {
                    if (typeof cc === 'undefined' || !cc.director || !cc.director.getScene()) {
                        console.log('Không thể truy cập scene');
                        return false;
                    }

                    const scene = cc.director.getScene();

                    // Tìm MauBinhController
                    const findMauBinhController = (node) => {
                        if (!node) return null;

                        // Kiểm tra các component của node
                        const components = node._components || [];
                        for (let i = 0; i < components.length; i++) {
                            const comp = components[i];
                            if (comp && typeof comp.sendLeaveRoom === 'function') {
                                return comp;
                            }
                        }

                        // Tìm trong các node con
                        const children = node.children || [];
                        for (let i = 0; i < children.length; i++) {
                            const result = findMauBinhController(children[i]);
                            if (result) return result;
                        }

                        return null;
                    };

                    const controller = findMauBinhController(scene);

                    if (!controller) {
                        console.log('Không tìm thấy MauBinhController');
                        return false;
                    }

                    console.log('Đã tìm thấy MauBinhController, đang gọi sendLeaveRoom()');

                    try {
                        controller.sendLeaveRoom();
                        console.log('Đã gọi sendLeaveRoom() thành công');
                        return true;
                    } catch (e) {
                        console.log('Lỗi khi gọi sendLeaveRoom():', e);
                        return false;
                    }
                })();";

                var result = ((IJavaScriptExecutor)driver).ExecuteScript(leaveRoomScript);
                bool jsSuccess = result is bool && (bool)result;

                if (jsSuccess)
                {
                    _uiManager.AppendLog($"Đã rời phòng bằng JavaScript cho {username}", UIManager.LogLevel.Info, username);

                    // Đợi một khoảng thời gian để WebSocket xử lý
                    await Task.Delay(500);

                    lock (_lock)
                    {
                        _roomPlayers.Remove(username);
                        _roomTimes.Remove(username);
                        _userSeats.Remove(username);
                        _userCards.Remove(username);
                    }
                    return;
                }
                else
                {
                    _uiManager.AppendLog($"JavaScript không thành công, thử phương pháp click thông thường cho {username}", UIManager.LogLevel.Warning, username);
                    // Tiếp tục với phương pháp click thông thường

                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"Lỗi khi rời phòng bằng JavaScript cho {username}: {ex.Message}", UIManager.LogLevel.Error, username);
                // Thử phương pháp click thông thường nếu JavaScript thất bại

            }
            finally
            {
                _uiManager.AppendLog($"Hoàn thành LeaveRoomWithJavaScript cho {username}, thời gian: {(DateTime.Now - startTime).TotalMilliseconds}ms", UIManager.LogLevel.Info, username);
            }
        }

        /// <summary>
        /// Đợi user về lobby sau khi leave room
        /// </summary>
        private async Task WaitForLobby(string username, ChromeDriver driver, CancellationToken cancellationToken)
        {
            try
            {
                _uiManager.AppendLog($"🔄 Đợi {username} về lobby sau khi leave room", UIManager.LogLevel.Info, username);

                int maxWaitTime = 5000; // 5 giây
                int checkInterval = 200; // Check mỗi 200ms
                int elapsed = 0;

                while (elapsed < maxWaitTime && !cancellationToken.IsCancellationRequested)
                {
                    // Kiểm tra xem user đã về lobby chưa
                    if (_gameClient.IsInLobby(username, driver))
                    {
                        _uiManager.AppendLog($"✅ {username} đã về lobby sau {elapsed}ms", UIManager.LogLevel.Info, username);
                        return;
                    }

                    await Task.Delay(checkInterval, cancellationToken);
                    elapsed += checkInterval;
                }

                if (elapsed >= maxWaitTime)
                {
                    _uiManager.AppendLog($"⚠️ Timeout chờ {username} về lobby sau {maxWaitTime}ms", UIManager.LogLevel.Warning, username);
                }
            }
            catch (Exception ex)
            {
                _uiManager.AppendLog($"❌ Lỗi khi đợi {username} về lobby: {ex.Message}", UIManager.LogLevel.Error, username);
            }
        }
    }
}
