﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Forms;
using AutoGameBai.Forms;
using AutoGameBai.Gamemaubinh;
using AutoGameBai.Room;
using AutoGameBai.UI;


namespace AutoGameBai
{
    public partial class Form1 : Form
    {
        private GameClientManager? _gameClient;
        private UIManager? _uiManager;
        private RoomManager? _roomManager;
        private UserGridViewManager? _userGridViewManagerHitClub;
        private UserGridViewManager? _userGridViewManagerSunWin;
        private UserActionHandler? _userActionHandler;
        private MauBinhCardManager? _mauBinhCardManager;
        private MauBinhSuggestionHandler? _suggestionHandler;
        private FormInitializer? _formInitializer;
        private EventHandlers? _eventHandlers;
        private ConfigManager? _configManager;
        private DataGridViewInitializer? _dataGridViewInitializer;
        private DataGridViewFormatter? _dataGridViewFormatter;
        private List<string> _selectedUsers = new();
        private readonly Dictionary<string, CancellationTokenSource> _joinRoomCts = new();
        private Label _loadingLabel;
        private string _selectedWebGame = "HitClub";
        private string _selectedGame = "Mậu Binh";

        public Form1() : this("HitClub", "Mậu Binh")
        {
        }

        public Form1(string selectedWebGame, string selectedGame)
        {
            _selectedWebGame = selectedWebGame;
            _selectedGame = selectedGame;
            Serilog.Log.Information("Form1 constructor started.");
            var startTime = DateTime.Now;
            try
            {


                this.SetStyle(ControlStyles.OptimizedDoubleBuffer |
                             ControlStyles.AllPaintingInWmPaint |
                             ControlStyles.UserPaint, true);

                InitializeComponent();
                _uiManager = new UIManager(comboBoxRooms);
                _uiManager?.AppendLog("Gọi InitializeComponent", UIManager.LogLevel.Debug);

                _formInitializer = new FormInitializer(this);
                _formInitializer.InitializeBasicUI();
                _loadingLabel = _formInitializer.CreateLoadingLabel();

                var serviceProvider = ConfigureServices();
                _uiManager = serviceProvider.GetRequiredService<UIManager>();
                if (_uiManager == null)
                {
                    throw new InvalidOperationException("Không thể khởi tạo UIManager từ ServiceProvider");
                }
                _uiManager.AppendLog("Khởi tạo UIManager thành công", UIManager.LogLevel.Info);

                _configManager = new ConfigManager(_uiManager);
                _dataGridViewInitializer = new DataGridViewInitializer(_uiManager);
                _dataGridViewInitializer.InitializeDataGridView(dataGridViewUsers);
                _dataGridViewInitializer.InitializeDataGridView(dataGridViewSunWinUsers);

                SetupDefaultValues();

                var endTime = DateTime.Now;
                _uiManager.AppendLog($"Form1 constructor hoàn tất trong {(endTime - startTime).TotalSeconds:F2} giây", UIManager.LogLevel.Info);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Error in Form1 constructor");
                _uiManager?.AppendLog($"Lỗi khởi tạo Form1: {ex.Message}\nStackTrace: {ex.StackTrace}", UIManager.LogLevel.Error);
                UI.MessageBoxHelper.ShowMessageBox(this, $"Lỗi khởi tạo Form1: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }

        private async void Form1_OnLoad(object sender, EventArgs e)
        {
            try
            {
                if (!IsHandleCreated)
                {
                    _uiManager?.AppendLog("Form handle chưa được tạo, hoãn tải dữ liệu", UIManager.LogLevel.Warning);
                    return;
                }

                _uiManager?.AppendLog("Bắt đầu OnLoad", UIManager.LogLevel.Debug);

                var config = await _configManager!.LoadConfigAsync();
                await this.InvokeAsync(() =>
                {
                    txtApiUrl.Text = _configManager.GetConfigValue(config, "ApiUrl", "http://127.0.0.1:15312");
                    numberThuban.Text = _configManager.GetConfigValue(config, "NumberThuban", "10");
                    txtAttemptDelay.Text = _configManager.GetConfigValue(config, "AttemptDelay", "200");
                });

                string selectedGame = _selectedGame;
                _gameClient = new GameClientManager(_uiManager, selectedGame, true, this); // Always lock profile size

                // Set card game đã chọn từ GameSelectionForm
                _gameClient.SetSelectedCardGame(_selectedGame);

                _gameClient.GetProfileManager().SetApiUrl(txtApiUrl.Text);
                await _gameClient.InitializeAsync();

                _dataGridViewFormatter = new DataGridViewFormatter(_uiManager, _gameClient, _selectedUsers);

                _mauBinhCardManager = new MauBinhCardManager(_uiManager);
                _suggestionHandler = new MauBinhSuggestionHandler(_uiManager);
                _roomManager = new RoomManager(_gameClient, _uiManager, this);
                _userGridViewManagerHitClub = new UserGridViewManager(dataGridViewUsers, _gameClient, _selectedUsers);
                _userGridViewManagerSunWin = new UserGridViewManager(dataGridViewSunWinUsers, _gameClient, _selectedUsers);
                _userActionHandler = new UserActionHandler(_gameClient, _uiManager, _roomManager, _joinRoomCts, _selectedUsers,
                    LoadUsersToDataGridView, txtAttemptDelay, btnJumpTable,
                    text => btnGetEmptyTable.Text = text, this);

                _gameClient.RefreshUserList = LoadUsersToDataGridView;
                _gameClient.RefreshGameLogs = () => { };

                RegisterEventHandlers();
                _formInitializer!.StyleButton(btnAddUser, SystemColors.ButtonFace);
                _formInitializer.StyleButton(btnSaveConfig, SystemColors.ButtonFace);
                _formInitializer.StyleButton(btnOpenWebAll, SystemColors.ButtonFace);
                _formInitializer.StyleButton(btnSelectAll, SystemColors.ButtonFace);
                _formInitializer.StyleButton(BtnViewLog, SystemColors.ButtonFace);

                UpdateRoomList();
                await LoadUsersToDataGridViewAsync();

                _loadingLabel.Visible = false;
                _uiManager?.AppendLog("OnLoad hoàn tất", UIManager.LogLevel.Debug);
            }
            catch (Exception ex)
            {
                _uiManager?.AppendLog($"Lỗi trong OnLoad: {ex.Message}\nStackTrace: {ex.StackTrace}", UIManager.LogLevel.Error);
                UI.MessageBoxHelper.ShowMessageBox(this, $"Lỗi khi khởi tạo: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }

        private IServiceProvider ConfigureServices()
        {
            var services = new ServiceCollection();
            services.AddSingleton<UIManager>(sp => new UIManager(comboBoxRooms));
            return services.BuildServiceProvider();
        }

        private void SetupDefaultValues()
        {
            // Thiết lập web game đã chọn
            if (_selectedWebGame == "HitClub")
            {
                // Ẩn tab SunWin, chỉ hiện tab HitClub
                tabControl.TabPages.Remove(tabSunWinUsers);
                if (!tabControl.TabPages.Contains(tabUsers))
                {
                    tabControl.TabPages.Insert(0, tabUsers);
                }
            }
            else if (_selectedWebGame == "SunWin")
            {
                // Ẩn tab HitClub, chỉ hiện tab SunWin
                tabControl.TabPages.Remove(tabUsers);
                if (!tabControl.TabPages.Contains(tabSunWinUsers))
                {
                    tabControl.TabPages.Insert(0, tabSunWinUsers);
                }
            }

            // Fix button colors
            SetupButtonColors();

            if (string.IsNullOrEmpty(numberThuban.Text)) numberThuban.Text = "10";
            if (string.IsNullOrEmpty(txtAttemptDelay.Text)) txtAttemptDelay.Text = "200";
        }

        private void SetupButtonColors()
        {
            // Đảm bảo button colors hiển thị đúng
            btnAddUser.BackColor = Color.FromArgb(241, 196, 15);
            btnAddUser.FlatStyle = FlatStyle.Flat;
            btnAddUser.UseVisualStyleBackColor = false;

            btnSaveConfig.BackColor = Color.FromArgb(39, 174, 96);
            btnSaveConfig.FlatStyle = FlatStyle.Flat;
            btnSaveConfig.UseVisualStyleBackColor = false;

            BtnViewLog.BackColor = Color.Red;
            BtnViewLog.ForeColor = Color.White;
            BtnViewLog.FlatStyle = FlatStyle.Flat;
            BtnViewLog.UseVisualStyleBackColor = false;
        }

        private void RegisterEventHandlers()
        {
            _eventHandlers = new EventHandlers(this, _gameClient!, _uiManager!, _roomManager!, _userActionHandler!,
                _suggestionHandler ?? throw new InvalidOperationException("MauBinhSuggestionHandler is null"),
                _selectedUsers, _joinRoomCts);

            dataGridViewUsers.CellContentClick += _eventHandlers.DataGridViewUsers_CellContentClick;
            dataGridViewUsers.CellValueChanged += _eventHandlers.DataGridViewUsers_CellValueChanged;
            dataGridViewUsers.CellClick += _eventHandlers.DataGridViewUsers_CellClick;
            dataGridViewUsers.CellFormatting += _dataGridViewFormatter!.FormatCell;
            dataGridViewSunWinUsers.CellContentClick += _eventHandlers.DataGridViewSunWinUsers_CellContentClick;
            dataGridViewSunWinUsers.CellValueChanged += _eventHandlers.DataGridViewSunWinUsers_CellValueChanged;
            dataGridViewSunWinUsers.CellClick += _eventHandlers.DataGridViewSunWinUsers_CellClick;
            dataGridViewSunWinUsers.CellFormatting += _dataGridViewFormatter.FormatCell;

            this.Click += _eventHandlers.Form_Click;
            this.KeyDown += _eventHandlers.Form1_KeyDown;
            this.FormClosing += _eventHandlers.Form1_FormClosing;

            btnSelectAll.Click += _eventHandlers.BtnSelectAll_Click;
            btnAddUser.Click += _eventHandlers.BtnAddUser_Click;
            btnSaveConfig.Click += BtnSaveConfig_Click;
            btnOpenWebAll.Click += _eventHandlers.BtnOpenWebAll_Click;
            btnGetEmptyTable.Click += _eventHandlers.BtnGetEmptyTable_Click;
            btnJumpTable.Click += _eventHandlers.BtnJumpTable_Click;
            btnShowSuggestions.Click += _eventHandlers.BtnShowSuggestions_Click;
            tabControl.Selecting += _eventHandlers.TabControl_Selecting;

            BtnViewLog.Click += _eventHandlers.BtnViewLog_Click;

            this.KeyPreview = true;
        }

        private async void BtnSaveConfig_Click(object sender, EventArgs e)
        {
            try
            {
                var config = new Dictionary<string, string>
                {
                    ["ApiUrl"] = txtApiUrl.Text,
                    ["NumberThuban"] = numberThuban.Text,
                    ["AttemptDelay"] = txtAttemptDelay.Text
                };

                await _configManager!.SaveConfigAsync(config);
                _gameClient?.GetProfileManager().SetApiUrl(txtApiUrl.Text);
                _uiManager?.AppendLog("Lưu cấu hình thành công", UIManager.LogLevel.Info);
                UI.MessageBoxHelper.ShowMessageBox(this, "Lưu cấu hình thành công", "Thành công", MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _uiManager?.AppendLog($"Lỗi khi lưu cấu hình: {ex.Message}", UIManager.LogLevel.Error);
                UI.MessageBoxHelper.ShowMessageBox(this, $"Lỗi khi lưu cấu hình: {ex.Message}", "Lỗi", MessageBoxIcon.Error);
            }
        }

        public async Task LoadUsersToDataGridViewAsync()
        {
            for (int attempt = 1; attempt <= 3; attempt++)
            {
                try
                {
                    var startTime = DateTime.Now;
                    _uiManager?.AppendLog("Bắt đầu LoadUsersToDataGridViewAsync", UIManager.LogLevel.Debug);

                    if (dataGridViewUsers == null || dataGridViewSunWinUsers == null)
                    {
                        _uiManager?.AppendLog("DataGridView chưa được khởi tạo", UIManager.LogLevel.Error);
                        throw new InvalidOperationException("DataGridView chưa được khởi tạo");
                    }

                    string? apiUrl = _gameClient?.GetProfileManager().GetApiUrl();
                    if (string.IsNullOrEmpty(apiUrl))
                    {
                        _uiManager?.AppendLog("API URL chưa được thiết lập", UIManager.LogLevel.Error);
                        throw new InvalidOperationException("API URL chưa được thiết lập");
                    }

                    if (dataGridViewUsers.InvokeRequired || dataGridViewSunWinUsers.InvokeRequired)
                    {
                        await this.InvokeAsync(async () => await LoadUsersToDataGridViewAsync());
                        return;
                    }

                    var users = _gameClient?.GetUsers();
                    if (users == null || users.Count == 0)
                    {
                        _uiManager?.AppendLog("Danh sách người dùng trống", UIManager.LogLevel.Warning);
                        await this.InvokeAsync(() =>
                        {
                            dataGridViewUsers.SuspendLayout();
                            dataGridViewSunWinUsers.SuspendLayout();

                            dataGridViewUsers.Rows.Clear();
                            dataGridViewSunWinUsers.Rows.Clear();

                            var row = new DataGridViewRow();
                            row.CreateCells(dataGridViewUsers);
                            row.Cells[0].Value = "Không có người dùng";
                            row.DefaultCellStyle.ForeColor = SystemColors.GrayText;
                            dataGridViewUsers.Rows.Add(row);

                            var rowSunWin = new DataGridViewRow();
                            rowSunWin.CreateCells(dataGridViewSunWinUsers);
                            rowSunWin.Cells[0].Value = "Không có người dùng";
                            rowSunWin.DefaultCellStyle.ForeColor = SystemColors.GrayText;
                            dataGridViewSunWinUsers.Rows.Add(rowSunWin);

                            dataGridViewUsers.ResumeLayout();
                            dataGridViewSunWinUsers.ResumeLayout();
                        });
                        return;
                    }

                    _uiManager?.AppendLog($"Tải {users.Count} người dùng vào DataGridView", UIManager.LogLevel.Debug);

                    TimeSpan loadTime;

                    if (_selectedWebGame == "HitClub")
                    {
                        if (_userGridViewManagerHitClub == null)
                        {
                            _uiManager?.AppendLog("UserGridViewManagerHitClub chưa được khởi tạo", UIManager.LogLevel.Error);
                            throw new InvalidOperationException("UserGridViewManagerHitClub chưa được khởi tạo");
                        }

                        dataGridViewUsers.SuspendLayout();
                        loadTime = await UI.FormOptimizer.MeasureExecutionTimeAsync(async () =>
                            await _userGridViewManagerHitClub.LoadUsersAsync());
                        dataGridViewUsers.ResumeLayout();

                        dataGridViewUsers.Refresh();
                        dataGridViewUsers.Update();
                    }
                    else if (_selectedWebGame == "SunWin")
                    {
                        if (_userGridViewManagerSunWin == null)
                        {
                            _uiManager?.AppendLog("UserGridViewManagerSunWin chưa được khởi tạo", UIManager.LogLevel.Error);
                            throw new InvalidOperationException("UserGridViewManagerSunWin chưa được khởi tạo");
                        }

                        dataGridViewSunWinUsers.SuspendLayout();
                        loadTime = await UI.FormOptimizer.MeasureExecutionTimeAsync(async () =>
                            await _userGridViewManagerSunWin.LoadUsersAsync());
                        dataGridViewSunWinUsers.ResumeLayout();

                        dataGridViewSunWinUsers.Refresh();
                        dataGridViewSunWinUsers.Update();
                    }

                    var endTime = DateTime.Now;
                    _uiManager?.AppendLog($"LoadUsersToDataGridViewAsync hoàn tất trong {(endTime - startTime).TotalSeconds:F2} giây", UIManager.LogLevel.Info);
                    return;
                }
                catch (Exception ex)
                {
                    _uiManager?.AppendLog($"Lỗi khi tải danh sách user (lần {attempt}): {ex.Message}\nStackTrace: {ex.StackTrace}", UIManager.LogLevel.Error);
                    if (attempt == 3)
                    {
                        UI.MessageBoxHelper.ShowMessageBox(this, $"Lỗi khi tải danh sách user: {ex.Message}. Vui lòng kiểm tra file hitclub_token.txt/sunwin_token.txt.", "Lỗi", MessageBoxIcon.Error);
                        throw;
                    }
                    await Task.Delay(1000);
                }
            }
        }

        public void LoadUsersToDataGridView()
        {
            _ = LoadUsersToDataGridViewAsync();
        }

        private void UpdateRoomList()
        {
            try
            {
                var startTime = DateTime.Now;
                _uiManager?.AppendLog("Bắt đầu UpdateRoomList", UIManager.LogLevel.Debug);
                comboBoxRooms.Items.Clear();
                comboBoxRooms.Items.AddRange(new object[] { "100", "500", "1K", "2K", "5K", "10K", "20K", "50K", "100K", "200K", "500K", "1M" });
                comboBoxRooms.SelectedItem = "100";
                var endTime = DateTime.Now;
                _uiManager?.AppendLog($"UpdateRoomList hoàn tất trong {(endTime - startTime).TotalSeconds:F2} giây", UIManager.LogLevel.Debug);
            }
            catch (Exception ex)
            {
                _uiManager?.AppendLog($"Lỗi khi cập nhật danh sách phòng: {ex.Message}", UIManager.LogLevel.Error);
            }
        }

        private string? GetApiUrl() => _gameClient?.GetProfileManager().GetApiUrl();
    }
}
